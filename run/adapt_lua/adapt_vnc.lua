-- vnc.lua
local mapping = {
  from_proto_name = "vnc",
  to_proto_name = "vnc",
  rule_proto_name = "vnc",
  common_flag = true,
  field = {
    {from_name = "DesktopName"    ,to_name = "desk"          ,rule_name = "desktop"  ,tll = 1},
    {from_name = "MessageType"    ,to_name = "action"        ,rule_name = "action"   ,tll = 1},
    {from_name = "File"           ,to_name = "file"          ,rule_name = "file"     ,tll = 1},
    {from_name = "Host"           ,to_name = "host"          ,rule_name = "host"     ,tll = 1},
    {from_name = "Path"           ,to_name = "accPath"       },
    {from_name = "ClientApp"      ,to_name = "prog"          },
    {from_name = "ClientDomain"   ,to_name = "dom"           },
    {from_name = "ClientResponse" ,to_name = "resp"          ,tll = 1},
    {from_name = "ServerChanllege",to_name = "cha"           ,tll = 1},
    {from_name = "CliVersion"     ,to_name = "cliVer"        ,rule_name = "version_client",tll = 1},
    {from_name = "SerVersion"     ,to_name = "srvVer"        ,rule_name = "version_server",tll = 1},

    
  }
}
yalua_register_proto(mapping)