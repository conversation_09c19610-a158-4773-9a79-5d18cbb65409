-- diameter.lua
local mapping = {
  from_proto_name = "diameter",
  to_proto_name = "diameter",
  rule_proto_name = "diameter",
  common_flag = true,
  field = {
    {from_name = "origin_realm"          ,to_name = "oriRealm"      },
    {from_name = "session_id"            ,to_name = "sesID"         },
    {from_name = "origin_host"           ,to_name = "originHost"    },
    {from_name = "AuthRequestType"       ,to_name = "authReqType"   },
    {from_name = "dest_host"             ,to_name = "dstHost"       },
    {from_name = "AccountingSessionId"   ,to_name = "accSesID"      },
    {from_name = "AccountingRecordNumber",to_name = "accRecNum"     },
    {from_name = "CallingStationId"      ,to_name = "calliStaID"    },
    {from_name = "CalledStationId"       ,to_name = "calleStaID"    },
    {from_name = "FramedIPAddress"       ,to_name = "fraIPAddr"     },
    {from_name = "NASPortType"           ,to_name = "NASProtType"   },
    {from_name = "NASIdentifier"         ,to_name = "NASID"         },
    {from_name = "NASPortId"             ,to_name = "NASPortID"     },
    {from_name = "NASPort"               ,to_name = "NASPort"       },
    {from_name = "NASIPAddress"          ,to_name = "NASIPAddr"     },
    {from_name = "user_name"             ,to_name = "login"         },
    {from_name = "attVal"                ,to_name = "attVal"        },
    {from_name = "attType"               ,to_name = "attType"       },
    {from_name = "app_id"                ,to_name = "appID"         },
    {from_name = "command"               ,to_name = "cmdCode"       },

    
  }
}
yalua_register_proto(mapping)