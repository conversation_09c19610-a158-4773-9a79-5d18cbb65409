-- smb.lua
local mapping = {
  from_proto_name = "smb",
  to_proto_name = "smb",
  rule_proto_name = "smb",
  common_flag = true,
  field = {
    {from_name = "deviceType"          ,to_name = "deviceType"          },
    {from_name = "hdrFlag2"            ,to_name = "hdrFlag2"            },
    {from_name = "hdrFlags"            ,to_name = "hdrFlags"            },
    {from_name = "hdrCommand"          ,to_name = "hdrCommand"          },
    {from_name = "hdrPID"              ,to_name = "hdrPID"              },
    {from_name = ""                    ,to_name = "share"               },
    {from_name = "user"                ,to_name = "user"                ,rule_name = "user"      ,tll = 1},
    {from_name = "fileNameCnt"         ,to_name = "fileNameCnt"         ,rule_name = "file_count"    ,tll = 1},
    {from_name = ""                    ,to_name = "srvDomCnt"           },
    {from_name = ""                    ,to_name = "fileExtAtt"          },
    {from_name = "fileAtt"             ,to_name = "fileAtt"             },
    {from_name = "fileSize"            ,to_name = "fileSize"            ,rule_name = "filesize"  ,tll = 1},
    {from_name = "fileName"            ,to_name = "fileName"            ,rule_name = "file"        ,tll = 1},
    {from_name = "authType"            ,to_name = "authType"            ,rule_name = "auth_type" ,tll = 1},
    {from_name = "ClientSB_Hostname"   ,to_name = "cliHost"             ,rule_name = "host_client"    ,tll = 1},
    {from_name = "ServerSB_Version"    ,to_name = "ver"                 ,rule_name = "version"   ,tll = 1},
    {from_name = "ServerNativeOS"      ,to_name = "natOS"               ,rule_name = "os"        ,tll = 1},
    {from_name = "ServerName"          ,to_name = "hostName"            ,rule_name = "host.s"    ,tll = 1},
    {from_name = "ServerDomainName"    ,to_name = "srvDom"              ,rule_name = "domain"    ,tll = 1},
    {from_name = "path"                ,to_name = "path"                ,rule_name = "path"      ,tll = 1},
    {from_name = "dir"                 ,to_name = "dir"                 ,rule_name = "dir"       ,tll = 1},
    {from_name = "accRghts"            ,to_name = "accRghts"            ,rule_name = "accRghts"  ,tll = 1},
    {from_name = "infoFileNm"          ,to_name = "infoFileNm"          ,rule_name = "infoFileNm",tll = 1},
    {from_name = "streamInfo"          ,to_name = "streamInfo"          ,rule_name = "streamInfo",tll = 1},
    {from_name = "usrID"               ,to_name = "usrID"               },
  }
}
yalua_register_proto(mapping)
