local mapping_link = {
  from_proto_name = "link",
  to_proto_name = "trans",
  rule_proto_name = "trans",
  rule_name_flag = false,
  field = {
    {from_name = "upLinkTransPayHex"      ,to_name = "trans_payload_hex_source"                ,tll = 1},
    {from_name = "downLinkTransPayHex"    ,to_name = "trans_payload_hex_destination"           ,tll = 1},
    {from_name = "upLinkPayLenSet"        ,to_name = "trans_payload_length_seq_source"         ,rule_name = "payload_length_seq_source"     ,tll = 1},
    {from_name = "downLinkPayLenSet"      ,to_name = "trans_payload_length_seq_destination"    ,rule_name = "payload_length_seq_destination"     ,tll = 1},
    {from_name = "upLinkBigPktLen"        ,to_name = "trans_packet_length_source_max"                 ,tll = 1},
    {from_name = "downLinkBigPktLen"      ,to_name = "trans_packet_length_destination_max"            ,tll = 1},
    {from_name = "upLinkSmaPktLen"        ,to_name = "trans_packet_length_source_min"                 ,tll = 1},
    {from_name = "downLinkSmaPktLen"      ,to_name = "trans_packet_length_destination_min"            ,tll = 1},
    {from_name = "upLinkFreqPktLen"       ,to_name = "trans_packet_length_source_high_frequency"      ,tll = 1},
    {from_name = "downLinkFreqPktLen"     ,to_name = "trans_packet_length_destination_high_frequency" ,tll = 1},
    {from_name = "upLinkBigPktInt"        ,to_name = "trans_packet_interval_source_max"               ,tll = 1},
    {from_name = "downLinkBigPktInt"      ,to_name = "trans_packet_interval_destination_max"          ,tll = 1},
    {from_name = "upLinkSmaPktInt"        ,to_name = "trans_packet_interval_source_min"               ,tll = 1},
    {from_name = "downLinkSmaPktInt"      ,to_name = "trans_packet_interval_destination_min"          ,tll = 1},
    {from_name = "firstFlag"              ,to_name = "tcp_first_flag"               ,tll = 1},
    {from_name = "upLinkSynSeqNum"        ,to_name = "tcp_syn_seq_source"           ,tll = 1},
    {from_name = "downLinkSynSeqNum"      ,to_name = "tcp_syn_seq_destination"      ,tll = 1},
    {from_name = "upLinkSynTcpWins"       ,to_name = "tcp_window_size_source"       ,tll = 1},
    {from_name = "downLinkSynTcpWins"     ,to_name = "tcp_window_size_destination"  ,tll = 1},
    {from_name = "upLinkTcpOpts"          ,to_name = "tcp_options_source"           ,tll = 1},
    {from_name = "downLinkTcpOpts"        ,to_name = "tcp_options_destination"      ,tll = 1},
    {from_name = "upLinkFlags"            ,to_name = "tcp_flags_source"             ,tll = 1},
    {from_name = "downLinkFlags"          ,to_name = "tcp_flags_destination"        ,tll = 1},
    {from_name = "tcpFlagsFinCnt"         ,to_name = "tcp_flags_fin_count"          ,tll = 1},
    {from_name = "tcpFlagsSynCnt"         ,to_name = "tcp_flags_syn_count"          ,tll = 1},
    {from_name = "tcpFlagsRstCnt"         ,to_name = "tcp_flags_rst_count"          ,tll = 1},
    {from_name = "tcpFlagsPshCnt"         ,to_name = "tcp_flags_psh_count"          ,tll = 1},
    {from_name = "tcpFlagsAckCnt"         ,to_name = "tcp_flags_ack_count"          ,tll = 1},
    {from_name = "tcpFlagsUrgCnt"         ,to_name = "tcp_flags_urg_count"          ,tll = 1},
    {from_name = "tcpFlagsEceCnt"         ,to_name = "tcp_flags_ece_count"          ,tll = 1},
    {from_name = "tcpFlagsCwrCnt"         ,to_name = "tcp_flags_cwr_count"          ,tll = 1},
    {from_name = "tcpFlagsNSCnt"          ,to_name = "tcp_flags_ns_count"           ,tll = 1},
    {from_name = "tcpFlagsSynAckCnt"      ,to_name = "tcp_flags_synack_count"       ,tll = 1},
    {from_name = "tcpEstablish"           ,to_name = "tcp_established"              ,tll = 1},
    {from_name = "tcpFinished"            ,to_name = "tcp_finished"                 ,tll = 1},



  }

}

yalua_register_proto(mapping_link)
