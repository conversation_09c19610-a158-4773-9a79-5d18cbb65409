-- dcerpc.lua
local mapping = {
  from_proto_name = "dcerpc",
  to_proto_name = "dcerpc",
  rule_proto_name = "dcerpc",
  common_flag = true,
  field = {
    {from_name = ""       ,to_name = "dcerpc_version"           ,to_name = "version",tll= 1},
    {from_name = ""       ,to_name = "dcerpc_packet_types"      ,to_name = "packet_types",tll= 1},
    {from_name = ""       ,to_name = "dcerpc_auth_type"         ,to_name = "auth_type",tll= 1},
    {from_name = ""       ,to_name = "decrpc_second_address"    ,to_name = "second_address",tll= 1},
    {from_name = ""       ,to_name = "decrpc_auth_level"        ,to_name = "auth_level",tll= 1},
    {from_name = ""       ,to_name = "decrpc_object"            ,to_name = "object",tll= 1},
    {from_name = ""       ,to_name = "decrpc_interface"         ,to_name = "interface",tll= 1},
    {from_name = ""       ,to_name = "decrpc_operation_number"  ,to_name = "operation_number",tll= 1},
    {from_name = ""       ,to_name = "decrpc_endpoint"          ,to_name = "endpoint",tll= 1},
  }
}
yalua_register_proto(mapping)




