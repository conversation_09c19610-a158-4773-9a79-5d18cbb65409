local M = {}
-- this is common and link alias
M.mapping_common = {
  rule_proto_name = "common",
  field = {
    {from_name = "tags_site"                ,rule_name = "tags_site"               },
    {from_name = "tags_unit"                ,rule_name = "tags_unit"               },
    {from_name = "tags_task"                ,rule_name = "tags_task"               },
    {from_name = "tags_rule"                ,rule_name = "tags_rule"               },
    {from_name = "tags_anomaly"             ,rule_name = "tags_anomaly"            },
    {from_name = "tags_threat"              ,rule_name = "tags_threat"             },
    {from_name = "tags_user"                ,rule_name = "tags_user"               },
    {from_name = "tags_attack"              ,rule_name = "tags_attack"             },
    {from_name = "time_capture"             ,rule_name = "time_capture"            },
    {from_name = "tags_isp"                 ,rule_name = "tags_isp"                },
    {from_name = "mobile_imsi"              ,rule_name = "mobile_imsi"             },
    {from_name = "mobile_imei"              ,rule_name = "mobile_imei"             },
    {from_name = "mobile_msisdn"            ,rule_name = "mobile_msisdn"           },
    {from_name = "mobile_interface"         ,rule_name = "mobile_interface"        },
    {from_name = "mobile_srcip"             ,rule_name = "mobile_srcip"            },
    {from_name = "mobile_srcport"           ,rule_name = "mobile_srcport"          },
    {from_name = "mobile_destip"            ,rule_name = "mobile_destip"           },
    {from_name = "mobile_destport"          ,rule_name = "mobile_destport"         },
    {from_name = "mobile_apn"               ,rule_name = "mobile_apn"              },
    {from_name = "mobile_ecgi"              ,rule_name = "mobile_ecgi"             },
    {from_name = "mobile_tai"               ,rule_name = "mobile_tai"              },
    {from_name = "mobile_teid"              ,rule_name = "mobile_teid"             },
    {from_name = "mobile_srcisp"            ,rule_name = "mobile_srcisp"           },
    {from_name = "mobile_destisp"           ,rule_name = "mobile_destisp"          },
    {from_name = "mobile_local_province"    ,rule_name = "mobile_local_province"   },
    {from_name = "mobile_local_city"        ,rule_name = "mobile_local_city"       },
    {from_name = "mobile_owner_province"    ,rule_name = "mobile_owner_province"   },
    {from_name = "mobile_owner_city"        ,rule_name = "mobile_owner_city"       },
    {from_name = "mobile_roaming_type"      ,rule_name = "mobile_roaming_type"     },
    {from_name = "mobile_rat"               ,rule_name = "mobile_rat"              }
  }
}

M.mapping_link = {
  rule_proto_name = "trans",
  field = {
    {from_name = "upLinkTransPayHex"      ,rule_name = "trans_payload_hex_source"                       },
    {from_name = "downLinkTransPayHex"    ,rule_name = "trans_payload_hex_destination"                  },
    {from_name = "upLinkPayLenSet"        ,rule_name = "trans_payload_length_seq_source"                },
    {from_name = "downLinkPayLenSet"      ,rule_name = "trans_payload_length_seq_destination"           },
    {from_name = "upLinkBigPktLen"        ,rule_name = "trans_packet_length_source_max"                 },
    {from_name = "downLinkBigPktLen"      ,rule_name = "trans_packet_length_destination_max"            },
    {from_name = "upLinkSmaPktLen"        ,rule_name = "trans_packet_length_source_min"                 },
    {from_name = "downLinkSmaPktLen"      ,rule_name = "trans_packet_length_destination_min"            },
    {from_name = "upLinkFreqPktLen"       ,rule_name = "trans_packet_length_source_high_frequency"      },
    {from_name = "downLinkFreqPktLen"     ,rule_name = "trans_packet_length_destination_high_frequency" },
    {from_name = "upLinkBigPktInt"        ,rule_name = "trans_packet_interval_source_max"               },
    {from_name = "downLinkBigPktInt"      ,rule_name = "trans_packet_interval_destination_max"          },
    {from_name = "upLinkSmaPktInt"        ,rule_name = "trans_packet_interval_source_min"               },
    {from_name = "downLinkSmaPktInt"      ,rule_name = "trans_packet_interval_destination_min"          },
    {from_name = "firstFlag"              ,rule_name = "tcp_first_flag"                                 },
    {from_name = "upLinkSynSeqNum"        ,rule_name = "tcp_syn_seq_source"                             },
    {from_name = "downLinkSynSeqNum"      ,rule_name = "tcp_syn_seq_destination"                        },
    {from_name = "upLinkSynTcpWins"       ,rule_name = "tcp_window_size_source"                         },
    {from_name = "downLinkSynTcpWins"     ,rule_name = "tcp_window_size_destination"                    },
    {from_name = "upLinkTcpOpts"          ,rule_name = "tcp_options_source"                             },
    {from_name = "downLinkTcpOpts"        ,rule_name = "tcp_options_destination"                        },
    {from_name = "upLinkFlags"            ,rule_name = "tcp_flags_source"                               },
    {from_name = "downLinkFlags"          ,rule_name = "tcp_flags_destination"                          },
    {from_name = "tcpFlagsFinCnt"         ,rule_name = "tcp_flags_fin_count"                            },
    {from_name = "tcpFlagsSynCnt"         ,rule_name = "tcp_flags_syn_count"                            },
    {from_name = "tcpFlagsRstCnt"         ,rule_name = "tcp_flags_rst_count"                            },
    {from_name = "tcpFlagsPshCnt"         ,rule_name = "tcp_flags_psh_count"                            },
    {from_name = "tcpFlagsAckCnt"         ,rule_name = "tcp_flags_ack_count"                            },
    {from_name = "tcpFlagsUrgCnt"         ,rule_name = "tcp_flags_urg_count"                            },
    {from_name = "tcpFlagsEceCnt"         ,rule_name = "tcp_flags_ece_count"                            },
    {from_name = "tcpFlagsCwrCnt"         ,rule_name = "tcp_flags_cwr_count"                            },
    {from_name = "tcpFlagsNSCnt"          ,rule_name = "tcp_flags_ns_count"                             },
    {from_name = "tcpFlagsSynAckCnt"      ,rule_name = "tcp_flags_synack_count"                         },
    {from_name = "tcpEstablish"           ,rule_name = "tcp_established"                                },
    {from_name = "tcpFinished"            ,rule_name = "tcp_finished"                                   }
  }

}

function M.print_sdt()
  print("------------ hello ----------------------- custom")
end

return M
