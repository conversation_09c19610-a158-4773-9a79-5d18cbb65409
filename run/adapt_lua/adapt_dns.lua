-- dns.lua
local mapping = {
  from_proto_name = "dns",
  to_proto_name = "dns",
  rule_proto_name = "dns",
  common_flag = true,
  field = {
    {from_name = "DNSTEX"           ,to_name = "dns_txt"              ,rule_name = "txt"                ,tll = 1},
    {from_name = "DNSSPF"           ,to_name = "dns_spf"              ,rule_name = "spf"                ,tll = 1},
    {from_name = "nameSrvCountry"   ,to_name = "dns_ns_ip_country"    ,tll = 1},
    {from_name = "nameSrvAsn"       ,to_name = "dns_ns_ip_asn"        ,tll = 1},
    {from_name = "nameSrvIPv6Cnt"   ,to_name = "dns_ns_ipv6_count"    ,tll = 1},
    {from_name = "nameSrvIPv6"      ,to_name = "dns_ns_ipv6"          ,tll = 1},
    {from_name = "nameSrvIPCnt"     ,to_name = "dns_ns_ipv4_count"    ,tll = 1},
    {from_name = "nameSrvIp"        ,to_name = "dns_ns_ipv4"          ,rule_name = "ns_ipv4"         ,tll = 1},
    {from_name = "nameSrvHostCnt"   ,to_name = "dns_ns_host_count"    ,tll = 1},
    {from_name = "nameSrvHost"      ,to_name = "dns_ns_host"          ,rule_name = "ns_host"       ,tll = 1},
    {from_name = "mailSrvCountry"   ,to_name = "dns_mx_ip_country"    ,tll = 1},
    {from_name = "mailSrvAsn"       ,to_name = "dns_mx_ip_asn"        ,tll = 1},
    {from_name = "mailSrvIPv6Cnt"   ,to_name = "dns_mx_ipv6_count"    ,tll = 1},
    {from_name = "mailSrvIPv6"      ,to_name = "dns_mx_ipv6"          ,tll = 1},
    {from_name = "mailSrvIPCnt"     ,to_name = "dns_mx_ipv4_count"    ,rule_name = "mx_ipv4_count"      ,tll = 1},
    {from_name = "mailSrvIp"        ,to_name = "dns_mx_ipv4"          ,rule_name = "mx_ipv4"            ,tll = 1},
    {from_name = "mailSrvHostcnt"   ,to_name = "dns_mx_host_count"    ,rule_name = "mx_host_count"      ,tll = 1},
    {from_name = "mailSrvHost"      ,to_name = "dns_mx_host"          ,rule_name = "mx_host"            ,tll = 1},
    {from_name = "aipCountry"       ,to_name = "dns_aip_country"      ,tll = 1},
    {from_name = "aipAsn"           ,to_name = "dns_aip_asn"          ,tll = 1},
    {from_name = "ansIPv6Cnt"       ,to_name = "dns_aaaa_ipv6_count"  ,tll = 1},
    {from_name = "ansIPv6"          ,to_name = "dns_aaaa_ipv6"        ,rule_name = "aaaa_ipv6"          ,tll = 1},
    {from_name = "AipCnt"           ,to_name = "dns_a_ipv4_count"     ,tll = 1},
    {from_name = "Aip"              ,to_name = "dns_a_ipv4"           ,rule_name = "a_ipv4"             ,tll = 1},
    {from_name = "ansCnameCnt"      ,to_name = "dns_cname_count"      ,tll = 1},
    {from_name = "ansCname"         ,to_name = "dns_cname"            ,rule_name = "cname"              ,tll = 1},
    {from_name = "additional_RRs"   ,to_name = "dns_additional_rrs"           },
    {from_name = "authorization_RRs",to_name = "dns_authority_rrs"            },
    {from_name = "answer_RRs"       ,to_name = "dns_answer_rrs"       ,tll = 1},
    {from_name = "ansTypes"         ,to_name = "dns_answer_type"      ,rule_name = "answer_type"        ,tll = 1},
    {from_name = "An_name00"        ,to_name = "dns_answer_name"      ,rule_name = "answer_name"       ,tll = 1},

    
  }
}
yalua_register_proto(mapping)
