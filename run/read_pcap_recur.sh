#!/bin/bash

#This script is specific to dbqp

#PCAP文件根目录
PCAP_DIR=${1:-"/root/pcap"}

#协议分布信息日志
FLOW_LOG_DIR=${2:-"/tmp/tbls/flow_log"}

#CMDLINE='./yaDpi --vdev="pcap_dir,dir=${PCAP_DIR}/${task}" -l 0-1 -- -r 1 -p 2,3,4,5,6,7 -l 8'
CMDLINE='./yaDpi --vdev="pcap_recur,dir=${PCAP_DIR}/${task},T=N,JUMP=ing" -l 0-1 -- -r 1 -p 2,3,4,5,6,7 -l 8 >./.info'


function delete_file
{
	DIR="$1"
	for son in `ls $DIR`
	do
		if [ -d "${DIR}/${son}" ]; then
			delete_file  "${DIR}/${son}"
		elif [ -f "${DIR}/${son}" ]; then
			grep "$son" .info 
			if [ $?  == 0 ]; then
				printf "delete %s\n" "${DIR}/${son}"
				rm -f "${DIR}/${son}"
			fi
		fi
	done
}

if [ ! -d $PCAP_DIR ]; then
	printf "%s is not dir" $PCAP_DIR
	exit 1
fi


while :
do

	#TASKS=`ls $PCAP_DIR | grep -v '_[fb]$' | grep '^\[[[:digit:]]\]' `
	#if [ -z "$H_TASKS" ]; then
	#	TASKS=`ls $PCAP_DIR | grep -v '_[fb]$' `
	#fi

	#if [ -z "$TASKS" ]; then
	#	sleep 10
	#	continue
	#fi

	TASKS=`ls $PCAP_DIR`

	for task in $TASKS
	do
		eval $CMDLINE

        if [ $? == 250 ]; then
			delete_file "${PCAP_DIR}/${task}"
			#mv "${PCAP_DIR}/${task}" "${PCAP_DIR}/${task}_f"
			#printf "%-20s processed\n" $task

			#[flow log info]
			#if [ ! -d "${FLOW_LOG_DIR}" ]; then
			#	mkdir -p "${FLOW_LOG_DIR}"
			#fi
			#mv /tmp/record_per_day.txt "${FLOW_LOG_DIR}/${task}_log"

		else

			#mv "${PCAP_DIR}/${task}" "${PCAP_DIR}/${task}_b"
			printf "%-20s error\n" $task
		fi

		sleep 10
		#break
	done

	sleep 30 
done

