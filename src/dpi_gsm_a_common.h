#ifndef _DPI_GSM_A_COMMON_H_
#define _DPI_GSM_A_COMMON_H_

/* flags for the packet-gsm_a_common routines */
#define GSM_A_PDU_TYPE_BSSMAP       0  /* BSSAP_PDU_TYPE_BSSMAP i.e. 0 - until split complete at least! */
#define GSM_A_PDU_TYPE_DTAP         1  /* BSSAP_PDU_TYPE_DTAP i.e. 1   - until split complete at least! */
#define GSM_A_PDU_TYPE_RP           2
#define GSM_A_PDU_TYPE_RR           3
#define GSM_A_PDU_TYPE_COMMON       4
#define GSM_A_PDU_TYPE_GM           5
#define GSM_A_PDU_TYPE_BSSLAP       6
#define GSM_A_PDU_TYPE_SACCH        7
#define GSM_PDU_TYPE_BSSMAP_LE      8
#define NAS_PDU_TYPE_COMMON         9
#define NAS_PDU_TYPE_EMM            10
#define NAS_PDU_TYPE_ESM            11
#define SGSAP_PDU_TYPE              12
#define BSSGP_PDU_TYPE              13
#define GMR1_IE_COMMON              14
#define GMR1_IE_RR                  15

#define P2P_DIR_UNKNOWN -1
#define P2P_DIR_SENT    0
#define P2P_DIR_RECV    1

#define P2P_DIR_UL  0
#define P2P_DIR_DL  1

struct s1ap_info {
    uint32_t link_dir;
    uint32_t p2p_dir;
    
    uint32_t procedure_code;
    const char *procedure_code_str;    
    const char *nasMsgEmmType;
    uint32_t protocol_ie_id;

    uint32_t mme_ue_id;
    uint32_t enb_ue_id;

    uint32_t emi_nas_emm_type_of_id;
    uint32_t protocol_extension_id;
    
    char emi_imei[32];
    //49 emi_imsi
    char emi_mmeGroupID[32];
    uint8_t emi_mmeCode;
    char emi_m_tmsi[32];
    //53 old_p_tmsi
    //54 oldLAI
    uint8_t esm_pdnType;
    //56 id_RRC_Establishment_Cause
    char uEaggregateMaximumBitRateDL[32];
    char uEaggregateMaximumBitRateUL[32];
    //59 e_RAB_ID
    uint8_t e_qCI;
    char transportLayerAddrIPv4[32];
    char GTP_TEID[32];
    //63 attachResult
    char esm_apn[32];
    char esm_pdn_ipv4[32];
    char esm_pdn_ipv6[32];
    //67 esm_llc_sapi
    //68 esm_pri_dns_address
    //69 esm_sec_dns_address
    //70 tmsi
    //71 t3412
    //72 t3402
    //73 t3423
    //74 smsType
    char RPOriginatingAddress[32];
    char RPDestinationAddress[32];
    char TPDestinationAddress[32];
    char smsText[1024];
};

typedef enum
{
    /* Mobility Management Information Elements [3] 10.5.3 */
    DE_AUTH_PARAM_RAND,             /* Authentication Parameter RAND */
    DE_AUTH_PARAM_AUTN,             /* Authentication Parameter AUTN (UMTS and EPS authentication challenge) */
    DE_AUTH_RESP_PARAM,             /* Authentication Response Parameter */
    DE_AUTH_RESP_PARAM_EXT,         /* Authentication Response Parameter (extension) (UMTS authentication challenge only) */
    DE_AUTH_FAIL_PARAM,             /* Authentication Failure Parameter (UMTS and EPS authentication challenge) */
    DE_CM_SRVC_TYPE,                /* CM Service Type */
    DE_ID_TYPE,                     /* Identity Type */
    DE_LOC_UPD_TYPE,                /* Location Updating Type */
    DE_NETWORK_NAME,                /* Network Name */
    DE_REJ_CAUSE,                   /* Reject Cause */
    DE_FOP,                         /* Follow-on Proceed */
    DE_TIME_ZONE,                   /* Time Zone */
    DE_TIME_ZONE_TIME,              /* Time Zone and Time */
    DE_CTS_PERM,                    /* CTS Permission */
    DE_LSA_ID,                      /* LSA Identifier */
    DE_DAY_SAVING_TIME,             /* Daylight Saving Time */
    DE_EMERGENCY_NUM_LIST,          /* Emergency Number List */
    DE_ADD_UPD_PARAMS,              /* Additional update parameters */
    DE_MM_TIMER,                    /* MM Timer */
    /* Call Control Information Elements 10.5.4 */
    DE_AUX_STATES,                  /* Auxiliary States */
    DE_BEARER_CAP,                  /* Bearer Capability */
    DE_CC_CAP,                      /* Call Control Capabilities */
    DE_CALL_STATE,                  /* Call State */
    DE_CLD_PARTY_BCD_NUM,           /* Called Party BCD Number */
    DE_CLD_PARTY_SUB_ADDR,          /* Called Party Subaddress */
    DE_CLG_PARTY_BCD_NUM,           /* Calling Party BCD Number */
    DE_CLG_PARTY_SUB_ADDR,          /* Calling Party Subaddress */
    DE_CAUSE,                       /* Cause */
    DE_CLIR_SUP,                    /* CLIR Suppression */
    DE_CLIR_INV,                    /* CLIR Invocation */
    DE_CONGESTION,                  /* Congestion Level */
    DE_CONN_NUM,                    /* Connected Number */
    DE_CONN_SUB_ADDR,               /* Connected Subaddress */
    DE_FACILITY,                    /* Facility */
    DE_HLC,                         /* High Layer Compatibility */
    DE_KEYPAD_FACILITY,             /* Keypad Facility */
    DE_LLC,                         /* Low Layer Compatibility */
    DE_MORE_DATA,                   /* More Data */
    DE_NOT_IND,                     /* Notification Indicator */
    DE_PROG_IND,                    /* Progress Indicator */
    DE_RECALL_TYPE,                 /* Recall type $(CCBS)$ */
    DE_RED_PARTY_BCD_NUM,           /* Redirecting Party BCD Number */
    DE_RED_PARTY_SUB_ADDR,          /* Redirecting Party Subaddress */
    DE_REPEAT_IND,                  /* Repeat Indicator */
    DE_REV_CALL_SETUP_DIR,          /* Reverse Call Setup Direction */
    DE_SETUP_CONTAINER,             /* SETUP Container $(CCBS)$ */
    DE_SIGNAL,                      /* Signal */
    DE_SS_VER_IND,                  /* SS Version Indicator */
    DE_USER_USER,                   /* User-user */
    DE_ALERT_PATTERN,               /* Alerting Pattern $(NIA)$ */
    DE_ALLOWED_ACTIONS,             /* Allowed Actions $(CCBS)$ */
    DE_SI,                          /* Stream Identifier */
    DE_NET_CC_CAP,                  /* Network Call Control Capabilities */
    DE_CAUSE_NO_CLI,                /* Cause of No CLI */
    DE_SUP_CODEC_LIST,              /* Supported Codec List */
    DE_SERV_CAT,                    /* Service Category */
    DE_REDIAL,                      /* ********* Redial */
    DE_NET_INIT_SERV_UPG,           /* ********* Network-initiated Service Upgrade ind */
    /* Short Message Service Information Elements [5] 8.1.4 */
    DE_CP_USER_DATA,                /* CP-User Data */
    DE_CP_CAUSE,                    /* CP-Cause */
    /* Tests procedures information elements 3GPP TS 44.014 6.4.0 and 3GPP TS 34.109 6.4.0 */
    DE_TP_SUB_CHANNEL,                  /* Close TCH Loop Cmd Sub-channel */
    DE_TP_ACK,                          /* Open Loop Cmd Ack */
    DE_TP_LOOP_TYPE,                    /* Close Multi-slot Loop Cmd Loop type*/
    DE_TP_LOOP_ACK,                     /* Close Multi-slot Loop Ack Result */
    DE_TP_TESTED_DEVICE,                /* Test Interface Tested device */
    DE_TP_PDU_DESCRIPTION,              /* GPRS Test Mode Cmd PDU description */
    DE_TP_MODE_FLAG,                    /* GPRS Test Mode Cmd Mode flag */
    DE_TP_EGPRS_MODE_FLAG,              /* EGPRS Start Radio Block Loopback Cmd Mode flag */
    DE_TP_MS_POSITIONING_TECHNOLOGY,    /* MS Positioning Technology */
    DE_TP_UE_TEST_LOOP_MODE,            /* Close UE Test Loop Mode */
    DE_TP_UE_POSITIONING_TECHNOLOGY,    /* UE Positioning Technology */
    DE_TP_RLC_SDU_COUNTER_VALUE,        /* RLC SDU Counter Value */
    DE_TP_EPC_UE_TEST_LOOP_MODE,        /* UE Test Loop Mode */
    DE_TP_EPC_UE_TL_A_LB_SETUP,         /* UE Test Loop Mode A LB Setup */
    DE_TP_EPC_UE_TL_B_LB_SETUP,         /* UE Test Loop Mode B LB Setup */
    DE_TP_EPC_UE_TL_C_SETUP,            /* UE Test Loop Mode C Setup */
    DE_TP_EPC_UE_POSITIONING_TECHNOLOGY,/* UE Positioning Technology */
    DE_TP_EPC_MBMS_PACKET_COUNTER_VALUE,/* MBMS Packet Counter Value */
    DE_TP_EPC_ELLIPSOID_POINT_WITH_ALT, /* ellipsoidPointWithAltitude */
    DE_TP_EPC_HORIZONTAL_VELOCITY,      /* horizontalVelocity */
    DE_TP_EPC_GNSS_TOD_MSEC,            /* gnss-TOD-msec */
    /* Group Call Control Service Information Elements ETSI TS 100 948 V8.1.0 (GSM 04.68 version 8.1.0 Release 1999) */
    DE_GCC_CALL_REF,                    /* Call Reference */
    DE_GCC_CALL_STATE,                  /* Call state */
    DE_GCC_CAUSE,                       /* Cause */
    DE_GCC_ORIG_IND,                    /* Originator indication */
    DE_GCC_STATE_ATTR,                  /* State attributes */
    /* Broadcast Call Control Information Elements ETSI TS 144 069 V10.0.0 (3GPP TS 44.069 version 10.0.0 Release 10) */
    DE_BCC_CALL_REF,                    /* Call Reference */
    DE_BCC_CALL_STATE,                  /* Call state */
    DE_BCC_CAUSE,                       /* Cause */
    DE_BCC_ORIG_IND,                    /* Originator indication */
    DE_BCC_STATE_ATTR,                  /* State attributes */
    DE_BCC_COMPR_OTDI,                  /* Compressed otdi */
    DE_NONE                             /* NONE */
}
dtap_elem_idx_t;

typedef enum
{
    /* 9.9.3    EPS Mobility Management (EMM) information elements */
    DE_EMM_ADD_UPD_RES,         /* 9.9.3.0A Additional update result */
    DE_EMM_ADD_UPD_TYPE,        /* 9.9.3.0B Additional update type */
    DE_EMM_AUTH_FAIL_PAR,       /* 9.9.3.1  Authentication failure parameter (dissected in packet-gsm_a_dtap.c)*/
    DE_EMM_AUTN,                /* 9.9.3.2  Authentication parameter AUTN */
    DE_EMM_AUTH_PAR_RAND,       /* 9.9.3.3  Authentication parameter RAND */
    DE_EMM_AUTH_RESP_PAR,       /* 9.9.3.4  Authentication response parameter */
    DE_EMM_CSFB_RESP,           /* 9.9.3.5  CSFB response */
    DE_EMM_DAYL_SAV_T,          /* 9.9.3.6  Daylight saving time */
    DE_EMM_DET_TYPE,            /* 9.9.3.7  Detach type */
    DE_EMM_DRX_PAR,             /* 9.9.3.8  DRX parameter (dissected in packet-gsm_a_gm.c)*/
    DE_EMM_CAUSE,               /* 9.9.3.9  EMM cause */
    DE_EMM_ATT_RES,             /* 9.9.3.10 EPS attach result (Coded inline */
    DE_EMM_ATT_TYPE,            /* 9.9.3.11 EPS attach type (Coded Inline)*/
    DE_EMM_EPS_MID,             /* 9.9.3.12 EPS mobile identity */
    DE_EMM_EPS_NET_FEATURE_SUP, /* 9.9.3.12A EPS network feature support */
    DE_EMM_EPS_UPD_RES,         /* 9.9.3.13 EPS update result ( Coded inline)*/
    DE_EMM_EPS_UPD_TYPE,        /* 9.9.3.14 EPS update type */
    DE_EMM_ESM_MSG_CONT,        /* 9.9.3.15 ESM message conta */
    DE_EMM_GPRS_TIMER,          /* 9.9.3.16 GPRS timer ,See subclause 10.5.7.3 in 3GPP TS 24.008 [6]. */
    DE_EMM_GPRS_TIMER_2,        /* 9.9.3.16A GPRS timer 2, See subclause 10.5.7.4 in 3GPP TS 24.008. */
    DE_EMM_GPRS_TIMER_3,        /* 9.9.3.16B GPRS timer 3, See subclause 10.5.7.4a in 3GPP TS 24.008. */
    DE_EMM_ID_TYPE_2,           /* 9.9.3.17 Identity type 2 ,See subclause ******** in 3GPP TS 24.008 [6]. */
    DE_EMM_IMEISV_REQ,          /* 9.9.3.18 IMEISV request ,See subclause ********0 in 3GPP TS 24.008 [6]. */
    DE_EMM_KSI_AND_SEQ_NO,      /* 9.9.3.19 KSI and sequence number */
    DE_EMM_MS_NET_CAP,          /* 9.9.3.20 MS network capability ,See subclause ********* in 3GPP TS 24.008 [6]. */
    DE_EMM_MS_NET_FEAT_SUP,     /* 9.9.3.20A MS network feature support, See subclause 10.5.1.15 in 3GPP TS 24.008. */
    DE_EMM_NAS_KEY_SET_ID,      /* 9.9.3.21 NAS key set identifier (coded inline)*/
    DE_EMM_NAS_MSG_CONT,        /* 9.9.3.22 NAS message container */
    DE_EMM_NAS_SEC_ALGS,        /* 9.9.3.23 NAS security algorithms */
    DE_EMM_NET_NAME,            /* 9.9.3.24 Network name, See subclause 10.5.3.5a in 3GPP TS 24.008 [6]. */
    DE_EMM_NONCE,               /* 9.9.3.25 Nonce */
    DE_EMM_PAGING_ID,           /* 9.9.3.25A Paging identity */
    DE_EMM_P_TMSI_SIGN,         /* 9.9.3.26 P-TMSI signature, See subclause ******** in 3GPP TS 24.008 [6]. */
    DE_EMM_EXT_CAUSE,           /* 9.9.3.26A Extended EMM cause */
    DE_EMM_SERV_TYPE,           /* 9.9.3.27 Service type */
    DE_EMM_SHORT_MAC,           /* 9.9.3.28 Short MAC */
    DE_EMM_TZ,                  /* 9.9.3.29 Time zone, See subclause 10.5.3.8 in 3GPP TS 24.008 [6]. */
    DE_EMM_TZ_AND_T,            /* 9.9.3.30 Time zone and time, See subclause 10.5.3.9 in 3GPP TS 24.008 [6]. */
    DE_EMM_TMSI_STAT,           /* 9.9.3.31 TMSI status, See subclause ******** in 3GPP TS 24.008 [6]. */
    DE_EMM_TRAC_AREA_ID,        /* 9.9.3.32 Tracking area identity */
    DE_EMM_TRAC_AREA_ID_LST,    /* 9.9.3.33 Tracking area identity list */
    DE_EMM_UE_NET_CAP,          /* 9.9.3.34 UE network capability */
    DE_EMM_UE_RA_CAP_INF_UPD_NEED,  /* 9.9.3.35 UE radio capability information update needed */
    DE_EMM_UE_SEC_CAP,          /* 9.9.3.36 UE security capability */
    DE_EMM_EMERG_NUM_LST,       /* 9.9.3.37 Emergency Number List */
    DE_EMM_CLI,                 /* 9.9.3.38 CLI */
    DE_EMM_SS_CODE,             /* 9.9.3.39 SS Code */
    DE_EMM_LCS_IND,             /* 9.9.3.40 LCS indicator */
    DE_EMM_LCS_CLIENT_ID,       /* 9.9.3.41 LCS client identity */
    DE_EMM_GEN_MSG_CONT_TYPE,   /* 9.9.3.42 Generic message container type */
    DE_EMM_GEN_MSG_CONT,        /* 9.9.3.43 Generic message container */
    DE_EMM_VOICE_DMN_PREF,      /* 9.9.3.44 Voice domain preference and UE's usage setting */
    DE_EMM_GUTI_TYPE,           /* 9.9.3.45 GUTI type */
    DE_EMM_EXT_DRX_PARAMS,      /* 9.9.3.46 Extended DRX parameters */
    DE_EMM_DATA_SERV_TYPE,      /* 9.9.3.47 Data service type */
    DE_EMM_NONE                 /* NONE */

} nas_emm_elem_idx_t;

/* 9.9.4 EPS Session Management (ESM) information elements */
typedef enum
{
    DE_ESM_APN,                     /* 9.9.4.1 Access point name */
    DE_ESM_APN_AGR_MAX_BR,            /* 9.9.4.2 APN aggregate maximum bit rate */
    DE_ESM_CONNECTIVITY_TYPE,        /* 9.9.4.2A Connectivity type */
    DE_ESM_EPS_QOS,                 /* 9.9.4.3 EPS quality of service */
    DE_ESM_CAUSE,                    /* ******* ESM cause */
    DE_ESM_INF_TRF_FLG,             /* ******* ESM information transfer flag */
    DE_ESM_LNKED_EPS_B_ID,            /* ******* Linked EPS bearer identity  */
    DE_ESM_LLC_SAPI,                /* ******* LLC service access point identifier */
    DE_ESM_NOTIF_IND,                /* *******a Notification indicator */
    DE_ESM_P_FLW_ID,                /* ******* Packet flow identifier  */
    DE_ESM_PDN_ADDR,                /* ******* PDN address */
    DE_ESM_PDN_TYPE,                /* ******** PDN type */
    DE_ESM_PROT_CONF_OPT,            /* ******** Protocol configuration options */
    DE_ESM_QOS,                     /* ******** Quality of service */
    DE_ESM_RA_PRI,                    /* ******** Radio priority    */
    DE_ESM_RE_ATTEMPT_IND,            /* ********a Re-attempt indicator */
    DE_ESM_REQ_TYPE,                /* ******** Request type */
    DE_ESM_TRAF_FLOW_AGR_DESC,        /* ******** Traffic flow aggregate description */
    DE_ESM_TRAF_FLOW_TEMPL,         /* ******** Traffic flow template */
    DE_ESM_TID,                     /* ******** Transaction identifier */
    DE_ESM_WLAN_OFFLOAD_ACCEPT,     /* ******** WLAN offload acceptability */
    DE_ESM_NBIFOM_CONT,             /* ******** NBIFOM container */
    DE_ESM_REMOTE_UE_CONTEXT_LIST,    /* ******** Remote UE context list */
    DE_ESM_PKMF_ADDRESS,            /* ******** PKMF address */
    DE_ESM_HDR_COMPR_CONFIG,        /* ******** Header compression configuration */
    DE_ESM_CTRL_PLANE_ONLY_IND,     /* ******** Control plane only indication */
    DE_ESM_USER_DATA_CONT,            /* ******** User data container */
    DE_ESM_REL_ASSIST_IND,            /* ******** Release assistance indication */
    DE_ESM_EXT_PCO,                 /* ******** Extended protocol configuration options */
    DE_ESM_HDR_COMPR_CONFIG_STATUS, /* ******** Header compression configuration status */
    DE_ESM_SERV_PLMN_RATE_CTRL,     /* ******** Serving PLMN rate control */
    DE_ESM_NONE                     /* NONE */
}
nas_esm_elem_idx_t;

typedef enum
{
    /* Short Message Service Information Elements [5] 8.2 */
    DE_RP_MESSAGE_REF,                /* RP-Message Reference */
    DE_RP_ORIG_ADDR,                /* RP-Originator Address */
    DE_RP_DEST_ADDR,                /* RP-Destination Address */
    DE_RP_USER_DATA,                /* RP-User Data */
    DE_RP_CAUSE,                    /* RP-Cause */
    DE_RP_NONE                            /* NONE */
}
rp_elem_idx_t;

typedef enum
{
    /* GPRS Mobility Management Information Elements [3] 10.5.5 */
    DE_ADD_UPD_TYPE,                /* [11] ******** Additional Update Type */
    DE_ATTACH_RES,                  /* [7] ******** Attach Result*/
    DE_ATTACH_TYPE,                 /* [7] ******** Attach Type */
    DE_CIPH_ALG,                    /* [7] ******** Ciphering Algorithm */
    DE_INTEG_ALG,                   /* [11] ********a Integrity Algorithm */
    DE_TMSI_STAT,                   /* [7] ******** TMSI Status */
    DE_DETACH_TYPE,                 /* [7] ******** Detach Type */
    DE_DRX_PARAM,                   /* [7] ******** DRX Parameter */
    DE_FORCE_TO_STAND,              /* [7] ******** Force to Standby */
    DE_FORCE_TO_STAND_H,            /* [7] ******** Force to Standby - Info is in the high nibble */
    DE_P_TMSI_SIG,                  /* [7] ******** P-TMSI Signature */
    DE_P_TMSI_SIG_2,                /* [7] ********a P-TMSI Signature 2 */
    DE_ID_TYPE_2,                   /* [7] ******** Identity Type 2 */
    DE_IMEISV_REQ,                  /* [7] ********0 IMEISV Request */
    DE_REC_N_PDU_NUM_LIST,          /* [7] ********1 Receive N-PDU Numbers List */
    DE_MS_NET_CAP,                  /* [7] ********* MS Network Capability */
    DE_MS_RAD_ACC_CAP,              /* [7] *********a MS Radio Access Capability */
    DE_GMM_CAUSE,                   /* [7] ********4 GMM Cause */
    DE_RAI,                         /* [7] ********5 Routing Area Identification */
    DE_RAI_2,                       /* [7] ********5a Routing Area Identification 2 */
    DE_UPD_RES,                     /* [7] ********7 Update Result */
    DE_UPD_TYPE,                    /* [7] ********8 Update Type */
    DE_AC_REF_NUM,                  /* [7] ********9 A&C Reference Number */
    DE_AC_REF_NUM_H,                /* A&C Reference Number - Info is in the high nibble */
    DE_SRVC_TYPE,                   /* [7] ********0 Service Type */
    DE_CELL_NOT,                    /* [7] ********1 Cell Notification */
    DE_PS_LCS_CAP,                  /* [7] ********2 PS LCS Capability */
    DE_NET_FEAT_SUP,                /* [7] ********3 Network Feature Support */
    DE_ADD_NET_FEAT_SUP,            /* [11] ********3a Additional network feature support */
    DE_RAT_INFO_CONTAINER,          /* [7] ********4 Inter RAT information container */
    DE_REQ_MS_INFO,                 /* [7] ********5 Requested MS information */
    DE_UE_NETWORK_CAP,              /* [7] ********6 UE network capability */
    DE_EUTRAN_IRAT_INFO_CONTAINER,  /* [7] ********7 E-UTRAN inter RAT information container */
    DE_VOICE_DOMAIN_PREF,           /* [7] ********8 Voice domain preference and UE's usage setting */
    DE_PTMSI_TYPE,                  /* [10] ********9 P-TMSI type */
    DE_LAI_2,                       /* [10] ********0 Location Area Identification 2 */
    DE_NET_RES_ID_CONT,             /* [11] ********1 Network resource identifier container */
    DE_EXT_DRX_PARAMS,              /* [11] ********2 Extended DRX parameters */
    DE_MAC,                         /* [11] ********3 Message Authentication Code */
    DE_UP_INTEG_IND,                /* [11] ********4 User Plane integrity indicator */
    /* Session Management Information Elements [3] 10.5.6 */
    DE_ACC_POINT_NAME,              /* Access Point Name */
    DE_NET_SAPI,                    /* Network Service Access Point Identifier */
    DE_PRO_CONF_OPT,                /* Protocol Configuration Options */
    DE_PD_PRO_ADDR,                 /* Packet Data Protocol Address */
    DE_QOS,                         /* Quality Of Service */
    DE_RE_ATTEMPT_IND,              /* Re-attempt indicator */
    DE_SM_CAUSE,                    /* SM Cause */
    DE_SM_CAUSE_2,                  /* SM Cause 2 */
    DE_LINKED_TI,                   /* Linked TI */
    DE_LLC_SAPI,                    /* LLC Service Access Point Identifier */
    DE_TEAR_DOWN_IND,               /* Tear Down Indicator */
    DE_PACKET_FLOW_ID,              /* Packet Flow Identifier */
    DE_TRAFFIC_FLOW_TEMPLATE,       /* Traffic Flow Template */
    DE_TMGI,                        /* Temporary Mobile Group Identity (TMGI) */
    DE_MBMS_BEARER_CAP,             /* MBMS bearer capabilities */
    DE_MBMS_PROT_CONF_OPT,          /* MBMS protocol configuration options */
    DE_ENH_NSAPI,                   /* Enhanced network service access point identifier */
    DE_REQ_TYPE,                    /* Request type */
    DE_SM_NOTIF_IND,                /* Notification indicator */
    DE_SM_CONNECTIVITY_TYPE,        /* Connectivity type */
    DE_SM_WLAN_OFFLOAD_ACCEPT,      /* WLAN offload acceptability */
    DE_NBIFOM_CONT,                 /* NBIFOM container */
    /* GPRS Common Information Elements [8] 10.5.7 */
    DE_PDP_CONTEXT_STAT,            /* [8] 10.5.7.1     PDP Context Status */
    DE_RAD_PRIO,                    /* [8] 10.5.7.2     Radio Priority */
    DE_GPRS_TIMER,                  /* [8] 10.5.7.3     GPRS Timer */
    DE_GPRS_TIMER_2,                /* [8] 10.5.7.4     GPRS Timer 2 */
    DE_GPRS_TIMER_3,                /* [10] 10.5.7.4a   GPRS Timer 3 */
    DE_RAD_PRIO_2,                  /* [8] 10.5.7.5     Radio Priority 2 */
    DE_MBMS_CTX_STATUS,             /* [8] 10.5.7.6     MBMS context status */
    DE_UPLINK_DATA_STATUS,          /* [8] 10.5.7.7     Uplink data status */
    DE_DEVICE_PROPERTIES,           /* [10] 10.5.7.8    Device properties */
    DE_GM_NONE                          /* NONE */
}
gm_elem_idx_t;

uint16_t de_cld_party_bcd_num(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *result, int max_len);

uint16_t elem_lv(struct dpi_pkt_st *pkt, int pdu_type, int idx, uint32_t offset, uint32_t len, struct s1ap_info *info);
uint16_t elem_v(struct dpi_pkt_st *pkt, int pdu_type, int idx, uint32_t offset, uint32_t len, struct s1ap_info *info);
int dissect_dtap(struct dpi_pkt_st *pkt, void *data);
int dissect_rp(struct dpi_pkt_st *pkt, struct s1ap_info *info);

#endif
