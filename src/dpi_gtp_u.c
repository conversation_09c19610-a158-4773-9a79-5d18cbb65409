/****************************************************************************************
 * 文 件 名 : dpi_gtp_u.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *设计: chenzq   2021/11/11
 *编码: chenzq   2021/11/11 
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2018 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <glib.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "dpi_gtp_u.h"


#define GTP_SET_IP_FLOW_TUPLE(tuple, protocol, version, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                                   \
                tuple.proto = protocol;                                                         \
                tuple.ip_version = version;                                                     \
                memcpy(tuple.ip_src, srcip, version == 4 ? 4 : 16);                             \
                memcpy(tuple.ip_dst, dstip, version == 4 ? 4 : 16);                             \
                tuple.port_src = srcport;                                                       \
                tuple.port_dst = dstport;                                                       \
                tuple.sctp_id  = _sctp_id;                                                      \
            }

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];

enum gtp_u_index_em{
    EM_GTP_U_TYPE,
    EM_GTP_U_OUTERIPSRC,
    EM_GTP_U_OUTERIPDST,
    EM_GTP_U_OUTERIPPROTO,
    EM_GTP_U_OUTERSRCPORT,
    EM_GTP_U_OUTERDSTPORT,
    EM_GTP_U_INNERIPSRC,
    EM_GTP_U_INNERIPDST,
    EM_GTP_U_INNERIPPROTO,
    EM_GTP_U_INNERSRCPORT,
    EM_GTP_U_INNERDSTPORT,
    EM_GTP_U_MAX
};


static dpi_field_table  gtp_u_field_array[] = {
    DPI_FIELD_D(EM_GTP_U_TYPE,                   YA_FT_STRING,          "ProtoType"),
    DPI_FIELD_D(EM_GTP_U_OUTERIPSRC,             YA_FT_STRING,          "OuterIPSrc"),
    DPI_FIELD_D(EM_GTP_U_OUTERIPDST,             YA_FT_STRING,          "OuterIPDst"),
    DPI_FIELD_D(EM_GTP_U_OUTERIPPROTO,           YA_FT_UINT8,           "OuterIPProto"),
    DPI_FIELD_D(EM_GTP_U_OUTERSRCPORT,           YA_FT_UINT16,          "OuterSrcPort"),
    DPI_FIELD_D(EM_GTP_U_OUTERDSTPORT,           YA_FT_UINT16,          "OuterDstPort"),
    DPI_FIELD_D(EM_GTP_U_INNERIPSRC,             YA_FT_STRING,          "InnerIPSrc"),
    DPI_FIELD_D(EM_GTP_U_INNERIPDST,             YA_FT_STRING,          "InnerIPDst"),
    DPI_FIELD_D(EM_GTP_U_INNERIPPROTO,           YA_FT_UINT8,           "InnerIPProto"),
    DPI_FIELD_D(EM_GTP_U_INNERSRCPORT,           YA_FT_UINT16,          "InnerSrcPort"),
    DPI_FIELD_D(EM_GTP_U_INNERDSTPORT,           YA_FT_UINT16,          "InnerDstPort"),
};


typedef struct dpi_gtp_uhd {
    // struct five_tuple outer;
    uint8_t ip_version;
    struct five_tuple inner;
    uint8_t direction;

    uint32_t teid;
}GTPUInfo;



static int gtp_u_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, GTPUInfo *info, int *idx, int i)
{
    char __str[64] = {0};
    //int local_idx=*idx;
    switch(i){
    case EM_GTP_U_TYPE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 0);
        break;
    case EM_GTP_U_OUTERIPSRC:
        write_one_ip_reconds(log_ptr->record, idx, flow->ip_version, flow->tuple.inner.ip_src);
        break;
    case EM_GTP_U_OUTERIPDST:
        write_one_ip_reconds(log_ptr->record, idx, flow->ip_version, flow->tuple.inner.ip_dst);
        break;
    case EM_GTP_U_OUTERIPPROTO:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gtp_u_field_array[i].type, NULL, flow->tuple.inner.proto);
        break;
    case EM_GTP_U_OUTERSRCPORT:
        if(flow->tuple.inner.port_src == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gtp_u_field_array[i].type, NULL, ntohs(flow->tuple.inner.port_src));
        }
        break;
    case EM_GTP_U_OUTERDSTPORT:
        if(flow->tuple.inner.port_dst == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gtp_u_field_array[i].type, NULL, ntohs(flow->tuple.inner.port_dst));
        }
        break;
    case EM_GTP_U_INNERIPSRC:
        write_one_ip_reconds(log_ptr->record, idx, info->ip_version, info->inner.ip_src);
        break;
    case EM_GTP_U_INNERIPDST:
        write_one_ip_reconds(log_ptr->record, idx, info->ip_version, info->inner.ip_dst);
        break;
    case EM_GTP_U_INNERIPPROTO:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gtp_u_field_array[i].type, NULL, info->inner.proto);
        break;
    case EM_GTP_U_INNERSRCPORT:
        if(info->inner.port_src == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gtp_u_field_array[i].type, NULL, ntohs(info->inner.port_src));
        }
        break;
    case EM_GTP_U_INNERDSTPORT:
        if(info->inner.port_dst == 0){
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }else{
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, gtp_u_field_array[i].type, NULL, ntohs(info->inner.port_dst));
        }
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    return 0;

    UNUSED(direction);
    UNUSED(__str);
}


static int write_gtp_u_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    if (g_config.protocol_switch[PROTOCOL_GTP_U] == 0)
        return 0;

    GTPUInfo *info=(GTPUInfo *)field_info;
    if(!info){
        return 0;
    }
    
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;


    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, 0, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "gtp_u");

    for(i=0; i<EM_GTP_U_MAX;i++){
        gtp_u_field_element(log_ptr,flow, direction, info, &idx, i);
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_GTP_U;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->flow        = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static
int dissect_gtp_u(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(g_config.protocol_switch[PROTOCOL_GTP_U] == 0 || payload_len < 12)
        return -1;

    uint32_t offset = 0;
    uint32_t inner_l4_offset = 0;
    char src_ip[16];
    char dst_ip[16];
    // const uint8_t *src_ip = NULL;
    // const uint8_t *dst_ip = NULL;
    uint16_t sport,dport;
    struct pkt_info  pkt_data;
    GTPUInfo  info;
    memset(&pkt_data, 0, sizeof(struct pkt_info));
    memset(&info, 0, sizeof(GTPUInfo));


    uint8_t flags = payload[offset];
    uint8_t message_type = payload[offset+1];

    if (((flags & 0xE0) >> 5) != 1 || message_type != 0xFF ) {
        return -1;
    }

    /* TEID */
    info.teid = ntohl(get_uint32_t(payload, offset + 4));
    flow->tunnel_id = info.teid;

    /*gtp_u data*/
    offset += 8;
    if (flags & 0x04) offset += 1; /* next_ext_header is present */
    if (flags & 0x02) offset += 4; /* sequence_number is present (it also includes next_ext_header and pdu_number) */
    if (flags & 0x01) offset += 1; /* pdu_number is present */

    const uint8_t *chunck_data = payload;
    uint16_t chunck_total_len = payload_len; 
    const uint8_t *inner_l3 = &chunck_data[offset];
    chunck_total_len = chunck_total_len - offset;
    uint16_t raw = chunck_total_len;

    pkt_data.ipversion = *((const uint8_t*)inner_l3) >> 4;
    info.ip_version = pkt_data.ipversion;

    if (pkt_data.ipversion != 4 && pkt_data.ipversion != 6) {
        return -1;
    }


    if (info.ip_version == 4 && chunck_total_len >= 20) {
        append_hardlink_proto_info(ETH_P_IP);
        const struct dpi_iphdr *inner_iph = (const struct dpi_iphdr *)inner_l3;
        inner_l4_offset = inner_iph->ihl * 4;
        pkt_data.iph4 = inner_iph;
        pkt_data.proto = inner_iph->protocol;
        memcpy(src_ip, (const char *)inner_iph->saddr, 4);
        memcpy(dst_ip, (const char *)inner_iph->daddr, 4);
    }

    if (info.ip_version == 6 && chunck_total_len >= 40) {
        append_hardlink_proto_info(ETH_P_IPV6);
        const struct dpi_ipv6hdr *iph6 = (const struct dpi_ipv6hdr *)inner_l3;
        inner_l4_offset = 40;
        pkt_data.iph6=iph6;
        pkt_data.proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        memcpy(src_ip, (const char *)iph6->ip6_src, 16);
        memcpy(dst_ip, (const char *)iph6->ip6_dst, 16);
    }

    chunck_total_len = chunck_total_len - inner_l4_offset;

    const uint8_t *inner_l4  = ((const uint8_t *)inner_l3 + inner_l4_offset);

    if (pkt_data.proto == IPPROTO_TCP && chunck_total_len >= 20) {
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)inner_l4;
        sport = ntohs(tcph->source);
        dport = ntohs(tcph->dest);
        pkt_data.tcph=tcph;
    } else if(pkt_data.proto == IPPROTO_UDP && chunck_total_len >= 8) {
        const struct dpi_udphdr *udph = (const struct dpi_udphdr *)inner_l4;
        sport = ntohs(udph->source);
        dport = ntohs(udph->dest);
        pkt_data.udph=udph;
    } else {
        sport = dport = 0;
    }

    info.direction = ntohs(sport) < ntohs(dport) ? FLOW_DIR_DST2SRC : FLOW_DIR_SRC2DST;

    

    GTP_SET_IP_FLOW_TUPLE(info.inner,  pkt_data.proto, pkt_data.ipversion, src_ip, dst_ip, sport, dport, 0);

    struct five_tuple   *outer_tuple;
    outer_tuple = direction == FLOW_DIR_SRC2DST ? &flow->tuple.inner : &flow->tuple_reverse.inner;

    SdtAclMatchedRuleInfo acl = {0};
    dpi_dissect_inherit_out_layer(flow, &pkt_data, &acl);
    if(pkt_data.proto){
        append_hardlink_proto_info(pkt_data.proto);
    }
    dpi_packet_processing_ip_layer(&flow_thread_info[flow->thread_id],
                                    flow->timestamp,
                                    outer_tuple,
                                    &pkt_data,
                                    (const struct dpi_iphdr*)inner_l3,
                                    raw, raw, &acl);
    return 0;

    UNUSED(seq);
    UNUSED(flag);
}


static 
void identify_gtp_u(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if(g_config.protocol_switch[PROTOCOL_GTP_U] == 0 || payload_len < 12)
        return;

    uint16_t offset = 0;
    uint8_t flags = 0;
    uint8_t msg_type  = 0;

    flags = get_uint8_t(payload, offset);
    msg_type = get_uint8_t(payload, offset + 1);
    if (((flags & 0xE0) >> 5) == 1 && msg_type == 0xFF) {


        flow->real_protocol_id = PROTOCOL_GTP_U;
    }

    return ;
}

static void init_gtp_u_dissector(void)
{
    dpi_register_proto_schema(gtp_u_field_array, EM_GTP_U_MAX, "gtp_u");

    port_add_proto_head(IPPROTO_UDP, 2152, PROTOCOL_GTP_U);

    udp_detection_array[PROTOCOL_GTP_U].proto = PROTOCOL_GTP_U;
    udp_detection_array[PROTOCOL_GTP_U].identify_func = identify_gtp_u;
    udp_detection_array[PROTOCOL_GTP_U].dissect_func = dissect_gtp_u;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_GTP_U].excluded_protocol_bitmask, PROTOCOL_GTP_U);
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_GTP_U].excluded_protocol_bitmask, PROTOCOL_GTP_U);



    map_fields_info_register(gtp_u_field_array,PROTOCOL_GTP_U, EM_GTP_U_MAX,"gtp_u");

    return;
}


static __attribute((constructor)) void     before_init_gtp_u(void){
    register_tbl_array(TBL_LOG_GTP_U, 0, "gtp_u", init_gtp_u_dissector);
}

