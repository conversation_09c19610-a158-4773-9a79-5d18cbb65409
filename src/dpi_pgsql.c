/****************************************************************************************
 * 文 件 名 : dpi_pgsql.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: zhangsx             2021/07/18
编码: zhangsx             2021/07/18
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <iconv.h>
#include <stdbool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"

#ifndef PGSQL_PORT
#define PGSQL_PORT 5432
#endif

extern struct rte_mempool *tbl_log_mempool;
extern dpi_field_table  dbbasic_field_array[];

static __thread bool first_packet = true;

enum {
    PGSQL_START,
    PGSQL_REQUEST,
    PGSQL_RESPONSE,
};

enum {
    PGSQL_AUTH_TYPE_SUCCESS,
    PGSQL_AUTH_TYPE_KERBEROS4,
    PGSQL_AUTH_TYPE_KERBEROS5,
    PGSQL_AUTH_TYPE_PLAINTEXT,
    PGSQL_AUTH_TYPE_CRYPT,
    PGSQL_AUTH_TYPE_MD5,
    PGSQL_AUTH_TYPE_SCM,
    PGSQL_AUTH_TYPE_GSSAPI,
    PGSQL_AUTH_TYPE_GSSAPI_SSPI_CONTINUE,
    PGSQL_AUTH_TYPE_SSPI,
    PGSQL_AUTH_TYPE_SASL ,
    PGSQL_AUTH_TYPE_SASL_CONTINUE,
    PGSQL_AUTH_TYPE_SASL_COMPLETE,
};

typedef enum {
    PGSQL_AUTH_STATE_NONE,               /*  No authentication seen or used */
    PGSQL_AUTH_SASL_REQUESTED,           /* Server sends SASL auth request with supported SASL mechanisms*/
    PGSQL_AUTH_SASL_CONTINUE,            /* Server and/or client send further SASL challange-response messages */
    PGSQL_AUTH_GSSAPI_SSPI_DATA,         /* GSSAPI/SSPI in use */
} pgsql_auth_state_t;

struct pgsql_session {
    bool ssl_requested;
    pgsql_auth_state_t auth_state; /* Current authentication state */
    uint32_t session_state;
    uint16_t port;
    uint8_t  last_ctype;
    uint8_t  ip_version;
    union {
        struct in_addr ip;
        struct in6_addr ip6;
    };

    char username[64];
    char password[64];
    char dbname[64];
    char appname[64];
    char sql[1024];
};

static const struct int_to_string fe_messages[] = {
    { 'p', "Authentication message" },
    { 'Q', "Simple query" },
    { 'P', "Parse" },
    { 'B', "Bind" },
    { 'E', "Execute" },
    { 'D', "Describe" },
    { 'C', "Close" },
    { 'H', "Flush" },
    { 'S', "Sync" },
    { 'F', "Function call" },
    { 'd', "Copy data" },
    { 'c', "Copy completion" },
    { 'f', "Copy failure" },
    { 'X', "Termination" },
    { 0, NULL }
};

static const struct int_to_string be_messages[] = {
    { 'R', "Authentication request" },
    { 'K', "Backend key data" },
    { 'S', "Parameter status" },
    { '1', "Parse completion" },
    { '2', "Bind completion" },
    { '3', "Close completion" },
    { 'C', "Command completion" },
    { 't', "Parameter description" },
    { 'T', "Row description" },
    { 'D', "Data row" },
    { 'I', "Empty query" },
    { 'n', "No data" },
    { 'E', "Error" },
    { 'N', "Notice" },
    { 's', "Portal suspended" },
    { 'Z', "Ready for query" },
    { 'A', "Notification" },
    { 'V', "Function call response" },
    { 'G', "CopyIn response" },
    { 'H', "CopyOut response" },
    { 'd', "Copy data" },
    { 'c', "Copy completion" },
    { 'v', "Negotiate protocol version" },
    { 0, NULL }
};

enum pgsql_index_em {
    EM_PGSQL_USER,
    EM_PGSQL_DB,
    EM_PGSQL_APP,
    EM_DB_USER,
    EM_DB_PASSWD,
    EM_DB_IP,
    EM_DB_PORT,
    EM_DB_NAME,
    EM_DB_SQL,
    EM_PGSQL_MAX,
};

static dpi_field_table  pgsql_field_array[] = {
    DPI_FIELD_D(EM_PGSQL_USER,                   EM_F_TYPE_STRING,                 "PostgresqlUsername"),
    DPI_FIELD_D(EM_PGSQL_DB,                     EM_F_TYPE_STRING,                 "PostgresqlDatabase"),
    DPI_FIELD_D(EM_PGSQL_APP,                    EM_F_TYPE_STRING,                 "PostgresqlApplication"),
    DPI_FIELD_D(EM_DB_USER,                      EM_F_TYPE_STRING,                 "DatabaseUsername"),
    DPI_FIELD_D(EM_DB_PASSWD,                    EM_F_TYPE_STRING,                 "DataqbasePassword"),
    DPI_FIELD_D(EM_DB_IP,                        EM_F_TYPE_STRING,                 "DatabaseIpAddress"),
    DPI_FIELD_D(EM_DB_PORT,                      EM_F_TYPE_UINT16,                 "DatabasePort"),
    DPI_FIELD_D(EM_DB_NAME,                      EM_F_TYPE_STRING,                 "DatabaseName"),
    DPI_FIELD_D(EM_DB_SQL,                       EM_F_TYPE_STRING,                 "DatabaseSql"),
};

static dpi_field_table  pgsql_field_array_sdt[] = {
    DPI_FIELD_D(EM_PGSQL_USER,                   YA_FT_STRING,                 "PostgresqlUsername"),
    DPI_FIELD_D(EM_PGSQL_DB,                     YA_FT_STRING,                 "PostgresqlDatabase"),
    DPI_FIELD_D(EM_PGSQL_APP,                    YA_FT_STRING,                 "PostgresqlApplication"),
    DPI_FIELD_D(EM_DB_USER,                      YA_FT_STRING,                 "DatabaseUsername"),
    DPI_FIELD_D(EM_DB_PASSWD,                    YA_FT_STRING,                 "DataqbasePassword"),
    DPI_FIELD_D(EM_DB_IP,                        YA_FT_STRING,                 "DatabaseIpAddress"),
    DPI_FIELD_D(EM_DB_PORT,                      YA_FT_UINT16,                "DatabasePort"),
    DPI_FIELD_D(EM_DB_NAME,                      YA_FT_STRING,                 "DatabaseName"),
    DPI_FIELD_D(EM_DB_SQL,                       YA_FT_STRING,                 "DatabaseSql"),
};



static uint32_t
pgsql_length(const uint8_t *payload, int offset)
{
    uint32_t n = 0;
    uint8_t type;
    uint32_t length;

    /* The length is either the four bytes after the type, or, if the
       type is 0, the first four bytes. */
    type = get_uint8_t(payload, offset);
    if (type != '\0')
        n = 1;
    length = get_uint32_ntohl(payload, offset+n);
    return length+n;
}

static void
write_pgsql_log(struct flow_info *flow, int direction)
{
    int idx = 0;
    int i,index,j;
    struct tbl_log *log_ptr;

    const char * strval = NULL;
    char ip_str[46] = {0};
    uint64_t int_val = 0;

    struct pgsql_session   *conn_data;
    conn_data = (struct pgsql_session *)flow->app_session;
    if (!conn_data) 
        return;

    if (conn_data->session_state != PGSQL_RESPONSE)
        return;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "postgresql");

    for (i = 0; i < EM_PGSQL_MAX; ++i) {
        switch (pgsql_field_array[i].index)
        {
        case EM_PGSQL_USER:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                              pgsql_field_array[i].type, (uint8_t *)conn_data->username,
                              RTE_MIN(strlen(conn_data->username), sizeof(conn_data->username)));
            break;
        case EM_PGSQL_APP:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                              pgsql_field_array[i].type, (uint8_t *)conn_data->appname,
                              RTE_MIN(strlen(conn_data->appname), sizeof(conn_data->appname)));
            break;
        case EM_PGSQL_DB:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                              pgsql_field_array[i].type, (uint8_t *)conn_data->dbname,
                              RTE_MIN(strlen(conn_data->dbname), sizeof(conn_data->dbname)));
            break;

        case EM_DB_IP:
            if (conn_data->ip_version == 4) {
                get_ip4string(ip_str, 46, conn_data->ip.s_addr);
            } else {
                get_ip6string(ip_str, 46, (uint8_t *)&conn_data->ip6);
            }
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                              pgsql_field_array[i].type, (uint8_t *)ip_str, 46);
            break;

        case EM_DB_PORT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, conn_data->port);
            break;

        case EM_DB_NAME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                    pgsql_field_array[i].type, (uint8_t *)conn_data->dbname,
                    RTE_MIN(strlen(conn_data->dbname), sizeof(conn_data->dbname)));
            break;

        case EM_DB_SQL:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                    pgsql_field_array[i].type, (uint8_t *)conn_data->sql,
                    RTE_MIN(strlen(conn_data->sql), sizeof(conn_data->sql)));
            break;

        case EM_DB_USER:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                    pgsql_field_array[i].type, (uint8_t *)conn_data->username,
                    RTE_MIN(strlen(conn_data->username), sizeof(conn_data->username)));
            break;

        case EM_DB_PASSWD:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,
                    pgsql_field_array[i].type, (uint8_t *)conn_data->password,
                    RTE_MIN(strlen(conn_data->password), sizeof(conn_data->password)));
            break;
        default:
            break;
        }
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_PGSQL;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
}

static void write_dbbasic_log(struct flow_info *flow, int direction)
{
    char __str[64] = {0};
    int idx = 0,i;
    struct tbl_log *log_ptr;
    
    struct pgsql_session   *conn_data;
    conn_data = (struct pgsql_session *)flow->app_session;
    if (!conn_data) 
        return;

    if (conn_data->session_state != PGSQL_RESPONSE)
        return;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,PROTOCOL_DBBASIC);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "dbbasic");

    for(i=0;i<EM_DBBASIC_MAX;i++){
        switch(dbbasic_field_array[i].index){
        case EM_DBBASIC_DBTYPE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "postgresql", 10);
            break;
        case EM_DBBASIC_USERNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, conn_data->username, RTE_MIN(strlen(conn_data->username), sizeof(conn_data->username)));    
            break;
        case EM_DBBASIC_PASSWORD:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, conn_data->password, RTE_MIN(strlen(conn_data->password), sizeof(conn_data->password)));
            break;
        case EM_DBBASIC_DBNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, conn_data->dbname, RTE_MIN(strlen(conn_data->dbname), sizeof(conn_data->dbname)));
            break;
        case EM_DBBASIC_DBSQL:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, conn_data->sql, RTE_MIN(strlen(conn_data->sql), sizeof(conn_data->sql)));
            break;
        case EM_DBBASIC_DBIP:
            if (conn_data->ip_version == 4) {
                get_ip4string(__str, sizeof(__str), conn_data->ip.s_addr);
            } else {
                get_ip6string(__str, sizeof(__str), (uint8_t *)&conn_data->ip6);
            }
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_DBBASIC_DBPORT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, conn_data->port);
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    log_ptr->proto_id = PROTOCOL_DBBASIC;
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_DBBASIC;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return;
}


static int
dissect_pgsql_fe_msg(struct flow_info *flow, struct pgsql_session * conn_data, uint8_t type,
                     uint32_t offset, const uint8_t *payload, const uint32_t payload_len)
{
    if (!conn_data || !flow || !payload) 
        return 0;

    uint8_t c;
    uint32_t data_offset = offset, data_length = payload_len;
    int i, siz;
    char *s;

    if (conn_data->session_state != PGSQL_REQUEST)
        conn_data->session_state = PGSQL_REQUEST;

    switch (type) {
    /* Password, SASL or GSSAPI Response, depending on context */
    case 'p':
        if (conn_data->auth_state == PGSQL_AUTH_STATE_NONE) {
            data_offset += snprintf(conn_data->password, sizeof(conn_data->password), "%s", payload + data_offset);
            data_offset += 1;
        }
        break;

    /* Simple query */
    case 'Q':
        data_offset += snprintf(conn_data->sql, sizeof(conn_data->sql), "%s", payload + data_offset);
        data_offset += 1;
        break;

    /* Parse */
    case 'P':
        data_offset += strlen((char *)payload + data_offset) + 1;
        data_offset += snprintf(conn_data->sql, sizeof(conn_data->sql), "%s", payload + data_offset);
        data_offset += 1;
        break;

    /* Bind */
    case 'B':
        
        break;

    /* Execute */
    case 'E':
        
        break;

    /* Describe, Close */
    case 'D':
    case 'C':
        
        break;

    /* Messages without a type identifier */
    case '\0':
        i = get_uint32_ntohl(payload, data_offset);
        data_offset += 4;
        data_length -= data_offset;
        switch (i) {
        /* Startup message */
        case 196608:
            while ((signed)data_length > 0) {
                siz = strlen((char *)payload) + 1;
                data_length -= siz;
                if ((signed)data_length <= 0)
                    break;

                if (memcmp("user", payload + data_offset, 5) == 0) {
                    data_offset += snprintf(conn_data->username, sizeof(conn_data->username), "%s", payload + data_offset+5);
                    data_offset += 6;
                }

                if (memcmp("database", payload + data_offset, 9) == 0) {
                    data_offset += snprintf(conn_data->dbname, sizeof(conn_data->dbname), "%s", payload + data_offset + 9);
                    data_offset += 10;
                }

                if (memcmp("application_name", payload + data_offset, 17) == 0) {
                    data_offset += snprintf(conn_data->appname, sizeof(conn_data->appname), "%s", payload + data_offset + 17);
                    data_offset += 18;
                }

                data_length -= data_offset;
                if (data_length == 1 && get_uint8_t(payload, data_offset) == 0)
                    break;
            }
            break;

        /* SSL request */
        case 80877103:
            /* Next reply will be a single byte. */
            conn_data->ssl_requested = true;
            break;

        /* Cancellation request */
        case 80877102:
            break;
        }
        break;

    /* Copy data */
    case 'd':
        break;

    /* Copy failure */
    case 'f':
        break;

    /* Function call */
    case 'F':
        break;
    }

    return 0;
}

static int
dissect_pgsql_be_msg(struct flow_info *flow, struct pgsql_session * conn_data, uint8_t type,
                     uint32_t offset, const uint8_t *payload, const uint32_t payload_len)
{
    uint32_t auth_type;
    if (!conn_data) 
        return 0;
    switch (type) {
        case 'R':
        auth_type = get_uint32_t(payload, offset);
        switch (auth_type) {
            case PGSQL_AUTH_TYPE_CRYPT:
            case PGSQL_AUTH_TYPE_MD5:
                conn_data->auth_state = PGSQL_AUTH_STATE_NONE;
                break;
            case PGSQL_AUTH_TYPE_GSSAPI_SSPI_CONTINUE:
                conn_data->auth_state = PGSQL_AUTH_GSSAPI_SSPI_DATA;
                break;
            case PGSQL_AUTH_TYPE_SASL:
                conn_data->auth_state = PGSQL_AUTH_SASL_REQUESTED;
                break;
            case PGSQL_AUTH_TYPE_SASL_CONTINUE:
            case PGSQL_AUTH_TYPE_SASL_COMPLETE:
                conn_data->auth_state = PGSQL_AUTH_SASL_CONTINUE;
                break;
        }
        break;
  
        default:
        break;
    }

    if (conn_data->session_state == PGSQL_REQUEST)
        conn_data->session_state = PGSQL_RESPONSE;

    conn_data->ip_version = flow->ip_version;
    memcpy(&conn_data->ip6, flow->tuple.inner.ip_dst, 16);
    conn_data->port = ntohs(flow->tuple.inner.port_dst);

    return PKT_OK;
}

static int
dissect_pgsql_msg(struct flow_info *flow, int direction,
                      uint32_t seq, const uint8_t *payload, 
                      const uint32_t payload_len, uint8_t flag)
{
    struct pgsql_session * conn_data;

    int n;
    uint8_t type;
    const char *typestr;
    uint32_t length;
    bool fe;

    fe = (direction == FLOW_DIR_SRC2DST);

    conn_data = (struct pgsql_session *)flow->app_session;
    if (!conn_data) 
        return 0;

    n = 0;
    type = get_uint8_t(payload, 0);
    if (type != '\0')
        n += 1;
    length = get_uint32_ntohl(payload, n);
    n += 4;

    /* This is like specifying VALS(messages) for hf_type, which we can't do
       directly because of messages without type bytes, and because the type
       interpretation depends on fe. */
    if (fe) {
        /* There are a few frontend messages that have no leading type byte.
           We identify them by the fact that the first byte of their length
           must be zero, and that the next four bytes are a unique tag. */
        if (type == '\0') {
            uint32_t tag = get_uint32_ntohl(payload, 4);

            if (length == 16 && tag == 80877102)
                typestr = "Cancel request";
            else if (length == 8 && tag == 80877103)
                typestr = "SSL request";
            else if (tag == 196608)
                typestr = "Startup message";
            else
                typestr = "Unknown";
        } else if (type == 'p') {
            switch (conn_data->auth_state) {
                case PGSQL_AUTH_SASL_REQUESTED:
                    typestr = "SASLInitialResponse message";
                    break;
                case PGSQL_AUTH_SASL_CONTINUE:
                    typestr = "SASLResponse message";
                    break;
                case PGSQL_AUTH_GSSAPI_SSPI_DATA:
                    typestr = "GSSResponse message";
                    break;
                default:
                    typestr = "Password message";
                    break;
            }
        } else
            typestr = val_to_string(type, fe_messages);
    }
    else {
        typestr = val_to_string(type, be_messages);
    }

    /* This is a terrible hack. It makes the "Info" column reflect
        the contents of every message in a TCP packet. Could it be
        done any better? */

    first_packet = false;

    if (fe)
        dissect_pgsql_fe_msg(flow, conn_data, type, n, payload, payload_len);
    else
        dissect_pgsql_be_msg(flow, conn_data, type, n, payload, payload_len);

    return PKT_OK;
}


static void
identify_pgsql(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_PGSQL] == 0)
        return;

    if (NULL == flow || NULL == payload)
        return;

    uint16_t   s_port = 0, d_port = 0;
    s_port = ntohs(flow->tuple.inner.port_src);
    d_port = ntohs(flow->tuple.inner.port_dst);
    
    if (get_uint8_t(payload, 0) == 'S')
        return;

    if (get_uint8_t(payload, 0) == 0 && get_uint32_ntohl(payload, 0) == payload_len) {
        if (get_uint32_ntohl(payload, 4) == 196608)
            flow->real_protocol_id = PROTOCOL_PGSQL;
    }

    if (s_port == PGSQL_PORT || d_port == PGSQL_PORT )
        flow->real_protocol_id = PROTOCOL_PGSQL;

    if (flow->real_protocol_id == PROTOCOL_PGSQL)
        first_packet = true;

}

static int
dissect_pgsql(struct flow_info *flow, int direction,
                  uint32_t seq, const uint8_t *payload, 
                  const uint32_t payload_len, uint8_t flag)
{
    if (NULL == flow || NULL == payload)
        return PKT_DROP;

    uint32_t offset = 0;

    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct pgsql_session));
        memset(flow->app_session, 0, sizeof(struct pgsql_session));
    }

    while (payload_len > offset)
    {
        uint32_t pg_len = pgsql_length(payload, offset);
        if(0==pg_len){
            break;
        }
        
        dissect_pgsql_msg(flow, direction, seq, payload + offset, pg_len, flag);
        offset += pg_len;
    }
    //write_pgsql_log(flow, direction);
    write_dbbasic_log(flow, direction);

    return PKT_OK;
}


static void
init_pgsql_dissector(void)
{
    dpi_register_proto_schema(pgsql_field_array, EM_PGSQL_MAX, "postgresql");
    port_add_proto_head(IPPROTO_TCP, PGSQL_PORT, PROTOCOL_PGSQL);
    tcp_detection_array[PROTOCOL_PGSQL].proto = PROTOCOL_PGSQL;
    tcp_detection_array[PROTOCOL_PGSQL].identify_func = identify_pgsql;
    tcp_detection_array[PROTOCOL_PGSQL].dissect_func = dissect_pgsql;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_PGSQL].excluded_protocol_bitmask, PROTOCOL_PGSQL);


    
    map_fields_info_register(pgsql_field_array_sdt, PROTOCOL_PGSQL, EM_PGSQL_MAX, "pgsql");

    return;
}

static __attribute((constructor)) void
before_init_pgsql(void)
{
    register_tbl_array(TBL_LOG_PGSQL, 0, "postgresql", init_pgsql_dissector);
}

static void init_dbbasic_dissector(void)
{
    dpi_register_proto_schema(dbbasic_field_array,EM_DBBASIC_MAX,"dbbasic");
    map_fields_info_register(dbbasic_field_array,PROTOCOL_DBBASIC, EM_DBBASIC_MAX,"dbbasic");
    return;
}
static __attribute((constructor)) void    before_init_dbbasic(void){
    register_tbl_array(TBL_LOG_DBBASIC, 0, "dbbasic", init_dbbasic_dissector);
}
