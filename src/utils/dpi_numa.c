
#include <stdint.h>
#include <stdio.h>

#include <numa.h>
#include <numaif.h>

// #include "dpi_log.h"

#define MAX_CPU_CORES     128
#define MAX_CPU_PER_NODE  64
#define MAX_NUMA_NODE     2
struct dpi_numa_t
{
  enum {
    LCORE_FLAG_UNUSABLE = -1, /* unsable */
    LCORE_FLAG_UNUSED = 0,    /* sable */
    LCORE_FLAG_USED = 1,     /* sable */
  } numa_node, flag;
}DpiNuma;

struct dpi_numa_t numa_lcore[MAX_CPU_CORES];
uint8_t g_numa_physical_node[MAX_NUMA_NODE][MAX_CPU_PER_NODE];

static int is_physical_node(uint8_t core_id)
{
    FILE *fp = fopen("/proc/cpuinfo", "r");
    if (!fp) {
        perror("fopen");
        printf("error\n");
        return 0;  // 无法打开文件，返回0
    }

    char line[1024];
    int physical_id = -1;
    int processor = -1;
    int curr_core_id = -1;
    // printf("core id ===============> %d\n", core_id);
    while (fgets(line, sizeof(line), fp)) {
        if (strncmp(line, "processor", 8) == 0) {
            sscanf(line, "processor : %d", &processor);
        } else if (strncmp(line, "physical id", 11) == 0) {
            sscanf(line, "physical id : %d", &physical_id);
        } else if (strncmp(line, "core id", 7) == 0) {
            sscanf(line, "core id : %d", &curr_core_id);
        }

        // 调试输出

        // 检查条件
        if (processor == core_id && physical_id != -1 && curr_core_id != -1 && physical_id < MAX_NUMA_NODE && g_numa_physical_node[physical_id][curr_core_id] == 0) {
            // printf("processor: %d, physical_id: %d, curr_core_id: %d\n", processor, physical_id, curr_core_id);
            g_numa_physical_node[physical_id][curr_core_id] = 1;
            fclose(fp);
            return 1;
        }
    }

    fclose(fp);
    return 0;
}

void dpi_numa_init()
{
  // 默认初始化为 不可用状态
  for (int i = 0; i < MAX_CPU_CORES; i++) {
    numa_lcore[i].flag      = LCORE_FLAG_UNUSABLE;
    numa_lcore[i].numa_node = LCORE_FLAG_UNUSABLE;
  }

  if (numa_available() == -1) {
    printf("此系统不支持 NUMA.");
    exit(1);
  }

  int max_node = numa_max_node();
  printf("NUMA node: %d\n", max_node);

  int max_cpu = numa_num_configured_cpus();
  printf("Configured CPUs: %d\n", max_cpu);

  for (int node = 0; node <= max_node; node++) {
    struct bitmask *cpumask = numa_allocate_cpumask();

    if (numa_node_to_cpus(node, cpumask) == 0) {
      printf("NUMA Node %d Cpus: ", node);
      for (int i = 0; i < (int)cpumask->size; i++) {
        if (numa_bitmask_isbitset(cpumask, i) && is_physical_node(i) == 1) {
          printf("%d ", i);
          numa_lcore[i].numa_node = node;               // 标记 NUMA id
          numa_lcore[i].flag      = LCORE_FLAG_UNUSED;  // 标记 未占用状态
        }
      }
      printf("\n");

    } else {
      printf("在 NUMA %d 上没有获取到 CPUs 信息\n", node);
      exit(1);
    }

    numa_free_cpumask(cpumask);
  }
  printf("end\n");
}


uint8_t dpi_numa_get_suitable_core(int numa_node)
{
  if (numa_node < 0 || numa_node >= MAX_NUMA_NODE) {
    return (uint8_t)-1;
  }

  for (int i = 0; i < MAX_CPU_PER_NODE; i++) {
    if (numa_lcore[i].numa_node == numa_node && numa_lcore[i].flag == LCORE_FLAG_UNUSED) {
      return i;
    }
  }

  // 如果没有在指定的 numa 上找到可以分配的核心， 返回第一个没有被占用的核心
  for (int i = 0; i < MAX_CPU_PER_NODE; i++) {
    if (numa_lcore[i].flag == LCORE_FLAG_UNUSED) {
      return i;
    }
  }

  printf("没有足够的cpu, 需要合理分配 cpu 核心\n");
  return (uint8_t)-1;
}


void dpi_numa_set_used_core(uint8_t core_id)
{
  if (core_id >= MAX_CPU_PER_NODE) {
    return;
  }
  numa_lcore[core_id].flag = LCORE_FLAG_USED;
}
