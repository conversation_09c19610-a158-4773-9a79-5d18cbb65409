#ifndef DPI_VNC_H
#define DPI_VNC_H

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <rte_memcpy.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "dpi_vnc.h"

#define  TCP_PORT_VNC 5901
#define  VNC_VERSION_LEN    7
#define  VNC_VERSION_PREFIX "RFB "
#define  false 0
#define  true  1


int is_client_or_server_version(const uint8_t* payload, const uint32_t payload_len);
uint8_t check_security_types(uint8_t* types, const uint8_t* payload, const uint32_t payload_len);
uint8_t get_server_message_type(const uint8_t* payload, uint32_t offset);
uint8_t get_client_message_type(const uint8_t* payload);
void write_vnc_pixel_information_log(struct tbl_log* log_ptr, int* idx, const uint8_t* payload, const uint32_t payload_len);
//void write_tail_info_log(struct tbl_log* log_ptr, int idx);
void write_tail_info_log(struct flow_info* flow, struct tbl_log* log_ptr, int idx);

void vnc_client_set_pixel_format(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void vnc_client_set_encodings(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void vnc_client_framebuffer_update_request(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void vnc_client_key_event(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void vnc_client_pointer_event(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void vnc_client_cut_text(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void vnc_client_to_server(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);

void vnc_server_framebuffer_update(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset);
void vnc_server_set_colormap_entries(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset);
void vnc_server_ring_bell(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset);
void vnc_server_cut_text(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset);
void vnc_server_to_client(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len);
void handle_client_or_server_message(struct flow_info* flow, const uint8_t* payload, const uint32_t payload_len, int direction);
void write_common_session_info(struct tbl_log* log_ptr, struct flow_info* flow, const uint8_t* payload, const uint32_t payload_len, int direction, int* idx);
void write_client_pointer_log(precord_t *record, const uint8_t* payload, int* idx);


#endif
