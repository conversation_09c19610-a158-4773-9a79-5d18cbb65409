#include "dpi_flow.h"

#include <rte_mempool.h>
#include <rte_atomic.h>
// #include "dpi_detect.h"
#include "dpi_log.h"
#include "jhash.h"

struct rte_mempool *flow_mempool;
extern struct global_config g_config;

#ifdef DPDK_MEMPOOL

static struct rte_hash_parameters hash_params = {
    .key_len = sizeof(struct flow_key), /* 13 */
    .hash_func = rte_jhash,
    .hash_func_init_val = 0,
    //.socket_id = 0,
};

static void init_flow_obj(struct rte_mempool *mp, __attribute__((unused)) void *arg,
        void *obj, unsigned i)
{
    UNUSED(i);
    memset(obj, 0, mp->elt_size);
}

void dpi_flow_init()
{
  char name_buff[COMMON_FILE_NAME] = {0};
  snprintf(name_buff, COMMON_FILE_NAME, "flow_mempool_%d", g_config.socketid);
  flow_mempool =
      rte_mempool_create(name_buff, g_config.max_flow_num, sizeof(struct flow_info), RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
                         NULL, NULL, init_flow_obj, NULL, g_config.socketid, 0);

  if (flow_mempool == NULL) {
    DPI_LOG(DPI_LOG_ERROR, "Cannot init flow mem pool");
    exit(-1);
  }
#ifdef ENABLE_ARKIME
void dpi_arkime_node_mempool_init(void);
  // 初始化Arkime节点内存池
  dpi_arkime_node_mempool_init();
#endif

}

void * dpi_flow_create()
{
  void *data;
  if (rte_mempool_get(flow_mempool, (void **)&data) < 0) {
    log_warn("not enough memory: flow_mempool");
    return NULL;
  }

  return data;
}

struct flow_info * dpi_flow_clone(struct flow_info *info)
{
  if (info == NULL) return NULL;
  rte_atomic16_inc(&info->ref);
  return info;
}

void dpi_flow_free(struct flow_info *info, dpi_free_callback cb)
{
    //2024.11.25 MDJ info is NULL
    if(NULL == info)
    {
        return;
    }
  if (rte_atomic16_dec_and_test(&info->ref)) {

    if (cb) cb(info);
    memset(info, 0, sizeof(struct flow_info));
    rte_mempool_put(flow_mempool, (void *)info);
    info = NULL;
  }
}

DpiHash * dpi_flow_hash_new(int thread_id)
{
  char hash_name[64]    = {0};
  hash_params.entries   = g_config.max_hash_node_per_thread;
  hash_params.socket_id = g_config.socketid;
  snprintf(hash_name, sizeof(hash_name), "hash_%d_%d", g_config.socketid, thread_id);
  hash_params.name = hash_name;
  return rte_hash_create(&hash_params);
}

int dpi_flow_hash_lookup(const DpiHash *hash, const void *key, void **data)
{
  return rte_hash_lookup_data(hash, key, data);
}

int dpi_flow_hash_insert(const DpiHash *hash, const void *key, void *data)
{
    int rc = rte_hash_add_key_data(hash, key, data);
    if(rc)
    {
        return -1;
    }
    return 0;
}

int dpi_flow_hash_remove(const DpiHash *hash, const void *key)
{
  return rte_hash_del_key(hash, key);
}

int dpi_flow_hash_size(const DpiHash *hash)
{
  return rte_hash_count(hash);
}

#else
static void init_flow_obj(struct rte_mempool *mp, __attribute__((unused)) void *arg,
        void *obj, unsigned i)
{
    UNUSED(i);
    memset(obj, 0, mp->elt_size);
}

void dpi_flow_init()
{
  // 初始化Arkime节点内存池
  arkime_node_mempool_init();
}

void * dpi_flow_create()
{
  void * info;
  info = malloc(sizeof(struct flow_info));
  if (!info) {
    log_error("malloc not enough memory");
    return NULL;
  }
  memset(info, 0, sizeof(struct flow_info));

  return info;
}

void dpi_flow_free(struct flow_info *info)
{
  free(info);
  info = NULL;
}

static gboolean flow_key_equal (gconstpointer v1, gconstpointer v2)
{
  return !memcmp(v1, v2, sizeof(struct flow_key));
}

static guint flow_key_hash (gconstpointer v)
{
  return (guint) jhash(v, sizeof(struct flow_key), JHASH_INITVAL);
}

DpiHash * dpi_flow_hash_new(int thread_id)
{

  return g_hash_table_new_full(flow_key_hash, flow_key_equal, NULL, NULL);
}

int dpi_flow_hash_lookup(const DpiHash *hash, const void *key, void **data)
{
  *data = g_hash_table_lookup(hash, key);
}

int dpi_flow_hash_insert(const DpiHash *hash, const void *key, void *data)
{
    //Malloc 能成功的前提, 总能成功插入
    g_hash_table_insert(hash, key, data);
    return 0;
}

int dpi_flow_hash_remove(const DpiHash *hash, const void *key)
{
  return g_hash_table_remove(hash, key);
}

int dpi_flow_hash_size(const DpiHash *hash)
{
  return g_hash_table_size(hash);
}
#endif

