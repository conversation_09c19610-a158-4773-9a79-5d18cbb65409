/*
 * ndpi_typedefs.h
 *
 * Copyright (C) 2011-16 - ntop.org
 *
 * This file is part of nDPI, an open source deep packet inspection
 * library based on the OpenDPI and PACE technology by ipoque GmbH
 *
 * nDPI is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * nDPI is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with nDPI.  If not, see <http://www.gnu.org/licenses/>.
 *
 */

#ifndef __DPI_TYPEDEFS_H__
#define __DPI_TYPEDEFS_H__
#include <sys/types.h>
#include <stdint.h>

#define __OpenBSD__

#ifdef __OpenBSD__
#include <endian.h>
//#define __BYTE_ORDER BYTE_ORDER
#if BYTE_ORDER == LITTLE_ENDIAN
#define __LITTLE_ENDIAN__
#else
#define __BIG_ENDIAN__
#endif/* BYTE_ORDER */
#endif/* __OPENBSD__ */

/* Definitions of OSI NLPIDs <Network Layer Protocol IDs> X.263*/
#define NLPID_NULL                    0x00
#define NLPID_IPI_T_70                0x01    /* T.70, when an IPI */
#define NLPID_SPI_X_29                0x01    /* X.29, when an SPI */
#define NLPID_X_633                   0x03    /* X.633 */
#define NLPID_DMS                     0x03    /* Maintenace messages: AT&T TR41459, Nortel NIS A211-1, Telcordia SR-4994, ... */
#define NLPID_Q_931                   0x08    /* Q.931, Q.932, X.36, ISO 11572, ISO 11582 */
#define NLPID_Q_933                   0x08    /* Q.933, on Frame Relay */
#define NLPID_Q_2931                  0x09    /* Q.2931 */
#define NLPID_Q_2119                  0x0c    /* Q.2119 */
#define NLPID_SNAP                    0x80
#define NLPID_ISO8473_CLNP            0x81    /* X.233 */
#define NLPID_ISO9542_ESIS            0x82
#define NLPID_ISO10589_ISIS           0x83
#define NLPID_ISO10747_IDRP           0x85
#define NLPID_ISO9542X25_ESIS         0x8a
#define NLPID_ISO10030                0x8c
#define NLPID_ISO11577                0x8d    /* X.273 */
#define NLPID_IP6                     0x8e
#define NLPID_COMPRESSED              0xb0    /* "Data compression protocol" */
#define NLPID_TRILL                   0xc0
#define NLPID_SNDCF                   0xc1    /* "SubNetwork Dependent Convergence Function */
#define NLPID_IEEE_8021AQ             0xc1    /* IEEE 802.1aq (draft-ietf-isis-ieee-aq-05.txt); defined in context of ISIS "supported protocols" TLV */
#define NLPID_IP                      0xcc
#define NLPID_PPP                     0xcf

/* NDPI_VISIT */
typedef enum
{
    ndpi_preorder,
    ndpi_postorder,
    ndpi_endorder,
    ndpi_leaf
} ndpi_VISIT;

/* NDPI_NODE */
typedef struct node_t
{
    char *key;
    struct node_t *left, *right;
} ndpi_node;


/* ++++++++++++++++++++++++ Cisco headers +++++++++++++++++++++ */

#define PACK_ON
#define PACK_OFF  __attribute__((packed))

PACK_ON
struct dpi_chdlc
{
    uint8_t addr;          /* 0x0F (Unicast) - 0x8F (Broadcast) */
    uint8_t ctrl;          /* always 0x00                       */
    uint16_t proto_code;   /* protocol type (e.g. 0x0800 IP)    */
} PACK_OFF;

/* SLARP - Serial Line ARP http://tinyurl.com/qa54e95 */
PACK_ON
struct dpi_slarp
{
    /* address requests (0x00)
    address replies  (0x01)
    keep-alive       (0x02)
    */
    uint32_t slarp_type;
    uint32_t addr_1;
    uint32_t addr_2;
} PACK_OFF;

/* Cisco Discovery Protocol http://tinyurl.com/qa6yw9l */
PACK_ON
struct dpi_cdp
{
    uint8_t version;
    uint8_t ttl;
    uint16_t checksum;
    uint16_t type;
    uint16_t length;
} PACK_OFF;

/* +++++++++++++++ Ethernet header (IEEE 802.3) +++++++++++++++ */

PACK_ON
struct dpi_ethhdr
{
    u_char h_dest[6];       /* destination eth addr */
    u_char h_source[6];     /* source ether addr    */
    uint16_t h_proto;      /* data length (<= 1500) or type ID proto (>=1536) */
} PACK_OFF;

/* +++++++++++++++++++ LLC header (IEEE 802.2) ++++++++++++++++ */

PACK_ON
struct dpi_snap_extension
{
    uint16_t   oui;
    uint8_t    oui2;
    uint16_t   proto_ID;
} PACK_OFF;

PACK_ON
struct dpi_llc_header_snap
{
    uint8_t    dsap;
    uint8_t    ssap;
    uint8_t    ctrl;
    struct dpi_snap_extension snap;
} PACK_OFF;

/* ++++++++++ RADIO TAP header (for IEEE 802.11) +++++++++++++ */
PACK_ON
struct dpi_radiotap_header
{
    uint8_t  version;         /* set to 0 */
    uint8_t  pad;
    uint16_t len;
    uint32_t present;
    uint64_t MAC_timestamp;
    uint8_t flags;
} PACK_OFF;

/* ++++++++++++ Wireless header (IEEE 802.11) ++++++++++++++++ */
PACK_ON
struct dpi_wifi_header
{
    uint16_t fc;
    uint16_t duration;
    u_char rcvr[6];
    u_char trsm[6];
    u_char dest[6];
    uint16_t seq_ctrl;
    /* uint64_t ccmp - for data encryption only - check fc.flag */
} PACK_OFF;

/* +++++++++++++++++++++++ MPLS header +++++++++++++++++++++++ */

PACK_ON
struct dpi_mpls_header
{
    uint32_t label:20, exp:3, s:1, ttl:8;
} PACK_OFF;

/* ++++++++++++++++++++++++ IP header ++++++++++++++++++++++++ */

PACK_ON
struct dpi_iphdr {
#if defined(__LITTLE_ENDIAN__)
    uint8_t ihl:4, version:4;
#elif defined(__BIG_ENDIAN__)
    uint8_t version:4, ihl:4;
#else
    # error "Byte order must be defined"
#endif
    uint8_t tos;
    uint16_t tot_len;
    uint16_t id;
    uint16_t frag_off;
    uint8_t ttl;
    uint8_t protocol;
    uint16_t check;
    uint8_t saddr[4];
    uint8_t daddr[4];
    uint8_t option_type;
} PACK_OFF;

/* +++++++++++++++++++++++ IPv6 header +++++++++++++++++++++++ */
/* rfc3542 */

struct dpi_in6_addr
{
    union
    {
        uint8_t   u6_addr8[16];
        uint16_t  u6_addr16[8];
        uint32_t  u6_addr32[4];
    } u6_addr;  /* 128-bit IP6 address */
};

PACK_ON
struct dpi_ipv6hdr
{
    union
    {
        struct ndpi_ip6_hdrctl
        {
            uint32_t ip6_un1_flow;
            uint16_t ip6_un1_plen;
            uint8_t ip6_un1_nxt;
            uint8_t ip6_un1_hlim;
        } ip6_un1;
        uint8_t ip6_un2_vfc;
    } ip6_ctlun;
    uint8_t   ip6_src[16];
    uint8_t   ip6_dst[16];

//    struct dpi_in6_addr ip6_src;
//    struct dpi_in6_addr ip6_dst;
} PACK_OFF;


/* +++++++++++++++++++++++ TCP header +++++++++++++++++++++++ */

PACK_ON
struct dpi_tcphdr
{
    uint16_t source;
    uint16_t dest;
    uint32_t seq;
    uint32_t ack_seq;
#if defined(__LITTLE_ENDIAN__)
    uint16_t res1:4, doff:4, fin:1, syn:1, rst:1, psh:1, ack:1, urg:1, ece:1, cwr:1;
#elif defined(__BIG_ENDIAN__)
    uint16_t doff:4, res1:4, cwr:1, ece:1, urg:1, ack:1, psh:1, rst:1, syn:1, fin:1;
#else
    # error "Byte order must be defined"
#endif
    uint16_t window;
    uint16_t check;
    uint16_t urg_ptr;
} PACK_OFF;

/* +++++++++++++++++++++++ UDP header +++++++++++++++++++++++ */

PACK_ON
struct dpi_udphdr
{
    uint16_t source;
    uint16_t dest;
    uint16_t len;
    uint16_t check;
} PACK_OFF;


PACK_ON
struct dpi_sctphdr
{
    uint16_t source;
    uint16_t dest;
    uint32_t v_tag;
    uint32_t checksum;
} PACK_OFF;

PACK_ON
struct dpi_dns_packet_header {
    uint16_t tr_id;
    uint16_t flags;
    uint16_t num_queries;           // DQCOUNT 问题条目数
    uint16_t num_answers;           // ANCOUNT 应答条目数
    uint16_t authority_rrs;         // NSCOUNT nameserver 数量
    uint16_t additional_rrs;
} PACK_OFF;

typedef union
{
    uint32_t ipv4;
    uint8_t ipv4_uint8_t[4];
#ifdef NDPI_DETECTION_SUPPORT_IPV6
    struct dpi_in6_addr ipv6;
#endif
} dpi_ip_addr_t;

/* +++++++++++++++++++++++ ICMP header +++++++++++++++++++++++ */

PACK_ON
struct dpi_icmphdr {
    uint8_t type;/* message type */
    uint8_t code;/* type sub-code */
    uint16_t checksum;
    union {
        struct {
            uint16_t id;
            uint16_t sequence;
        } echo; /* echo datagram */

        uint32_t gateway; /* gateway address */
        struct {
            uint16_t _unused;
            uint16_t mtu;
        } frag;/* path mtu discovery */
    } un;
} PACK_OFF;

typedef enum {
  HTTP_METHOD_UNKNOWN = 0,
  HTTP_METHOD_OPTIONS,
  HTTP_METHOD_GET,
  HTTP_METHOD_HEAD,
  HTTP_METHOD_POST,
  HTTP_METHOD_PUT,
  HTTP_METHOD_DELETE,
  HTTP_METHOD_TRACE,
  HTTP_METHOD_CONNECT
} ndpi_http_method;

/************************AH header  ****************************/
PACK_ON
struct dpi_ahhd {
    uint8_t next_hd;
    uint8_t length;
    uint32_t ah_spi;
    uint32_t ah_seq;
    uint8_t *ah_icv;
} PACK_OFF;

/* ++++++++++++++++++++++ ESP header +++++++++++++++++++++++++ */
PACK_ON
struct dpi_esphd {
    uint32_t spi;
    uint32_t seq;
    uint32_t payloadlength;
    uint32_t paddinglength;
    uint32_t authentlength;
} PACK_OFF;


struct dpi_int_one_line_struct {
    const uint8_t *ptr;
    uint16_t len;
};

#endif/* __NDPI_TYPEDEFS_H__ */

