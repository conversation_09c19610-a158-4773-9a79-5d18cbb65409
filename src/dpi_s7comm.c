/****************************************************************************************
 * 文 件 名 : dpi_s7comm.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 设计:
 编码:        chenzq    2021/7/21
 修改:
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2018 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/

#include <arpa/inet.h>
#include <rte_mbuf.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"

// 报文最小长度
#define     S7COMM_MIN_TELEGRAM_LENGTH      10
#define     COTP_MIN_LEN                    4
#define     TPKT_LEN                        4

// 协议id
#define     S7COMM_PORT_ID                  0x32


#define TCP_PORT_S7COMM         102


/**************************************************************************
 * PDU types
 */
#define S7COMM_ROSCTR_JOB                   0x01
#define S7COMM_ROSCTR_ACK                   0x02
#define S7COMM_ROSCTR_ACK_DATA              0x03
#define S7COMM_ROSCTR_USERDATA              0x07

static const struct int_to_string rosctr_names[] = {
    { S7COMM_ROSCTR_JOB,                    "Job" },        /* Request: job with acknowledgement */
    { S7COMM_ROSCTR_ACK,                    "Ack" },        /* acknowledgement without additional field */
    { S7COMM_ROSCTR_ACK_DATA,               "Ack_Data" },   /* Response: acknowledgement with additional field */
    { S7COMM_ROSCTR_USERDATA,               "Userdata" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Error classes in header
 */
#define S7COMM_ERRCLS_NONE                  0x00
#define S7COMM_ERRCLS_APPREL                0x81
#define S7COMM_ERRCLS_OBJDEF                0x82
#define S7COMM_ERRCLS_RESOURCE              0x83
#define S7COMM_ERRCLS_SERVICE               0x84
#define S7COMM_ERRCLS_SUPPLIES              0x85
#define S7COMM_ERRCLS_ACCESS                0x87

static const struct int_to_string errcls_names[] = {
    { S7COMM_ERRCLS_NONE,                   "No error" },
    { S7COMM_ERRCLS_APPREL,                 "Application relationship" },
    { S7COMM_ERRCLS_OBJDEF,                 "Object definition" },
    { S7COMM_ERRCLS_RESOURCE,               "No resources available" },
    { S7COMM_ERRCLS_SERVICE,                "Error on service processing" },
    { S7COMM_ERRCLS_SUPPLIES,               "Error on supplies" },
    { S7COMM_ERRCLS_ACCESS,                 "Access error" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Names of types in userdata parameter part
 */

#define S7COMM_UD_TYPE_NCPUSH               0x3
#define S7COMM_UD_TYPE_NCREQ                0x7
#define S7COMM_UD_TYPE_NCRES                0xb
#define S7COMM_UD_TYPE_PUSH                 0x0
#define S7COMM_UD_TYPE_REQ                  0x4
#define S7COMM_UD_TYPE_RES                  0x8

static const struct int_to_string userdata_type_names[] = {
    { S7COMM_UD_TYPE_PUSH,                  "Push" },               /* this type occurs when 2 telegrams follow after another from the same partner, or initiated from PLC */
    { S7COMM_UD_TYPE_REQ,                   "Request" },
    { S7COMM_UD_TYPE_RES,                   "Response" },
    { S7COMM_UD_TYPE_NCPUSH,                "NC Push" },            /* used only by Sinumerik NC */
    { S7COMM_UD_TYPE_NCREQ,                 "NC Request" },         /* used only by Sinumerik NC */
    { S7COMM_UD_TYPE_NCRES,                 "NC Response" },        /* used only by Sinumerik NC */
    { 0,                                    NULL }
};


/**************************************************************************
 * Names of Function groups in userdata parameter part
 */
#define S7COMM_UD_FUNCGROUP_MODETRANS       0x0
#define S7COMM_UD_FUNCGROUP_PROG            0x1
#define S7COMM_UD_FUNCGROUP_CYCLIC          0x2
#define S7COMM_UD_FUNCGROUP_BLOCK           0x3
#define S7COMM_UD_FUNCGROUP_CPU             0x4
#define S7COMM_UD_FUNCGROUP_SEC             0x5                     /* Security functions e.g. plc password */
#define S7COMM_UD_FUNCGROUP_PBC             0x6                     /* PBC = Programmable Block Communication (PBK in german) */
#define S7COMM_UD_FUNCGROUP_TIME            0x7
#define S7COMM_UD_FUNCGROUP_NCPRG           0xf

static const struct int_to_string userdata_functiongroup_names[] = {
    { S7COMM_UD_FUNCGROUP_MODETRANS,        "Mode-transition" },
    { S7COMM_UD_FUNCGROUP_PROG,             "Programmer commands" },
    { S7COMM_UD_FUNCGROUP_CYCLIC,           "Cyclic data" },        /* to read data from plc without a request */
    { S7COMM_UD_FUNCGROUP_BLOCK,            "Block functions" },
    { S7COMM_UD_FUNCGROUP_CPU,              "CPU functions" },
    { S7COMM_UD_FUNCGROUP_SEC,              "Security" },
    { S7COMM_UD_FUNCGROUP_PBC,              "PBC BSEND/BRECV" },
    { S7COMM_UD_FUNCGROUP_TIME,             "Time functions" },
    { S7COMM_UD_FUNCGROUP_NCPRG,            "NC programming" },
    { 0,                                    NULL }
};


/**************************************************************************
 * Names of userdata subfunctions in group 1 (Programmer commands)
 */
#define S7COMM_UD_SUBF_PROG_REQDIAGDATA1    0x01
#define S7COMM_UD_SUBF_PROG_VARTAB1         0x02
#define S7COMM_UD_SUBF_PROG_ERASE           0x0c
#define S7COMM_UD_SUBF_PROG_READDIAGDATA    0x0e
#define S7COMM_UD_SUBF_PROG_REMOVEDIAGDATA  0x0f
#define S7COMM_UD_SUBF_PROG_FORCE           0x10
#define S7COMM_UD_SUBF_PROG_REQDIAGDATA2    0x13

static const struct int_to_string userdata_prog_subfunc_names[] = {
    { S7COMM_UD_SUBF_PROG_REQDIAGDATA1,     "Request diag data (Type 1)" },     /* Start online block view */
    { S7COMM_UD_SUBF_PROG_VARTAB1,          "VarTab" },                         /* Variable table */
    { S7COMM_UD_SUBF_PROG_READDIAGDATA,     "Read diag data" },                 /* online block view */
    { S7COMM_UD_SUBF_PROG_REMOVEDIAGDATA,   "Remove diag data" },               /* Stop online block view */
    { S7COMM_UD_SUBF_PROG_ERASE,            "Erase" },
    { S7COMM_UD_SUBF_PROG_FORCE,            "Forces" },
    { S7COMM_UD_SUBF_PROG_REQDIAGDATA2,     "Request diag data (Type 2)" },     /* Start online block view */
    { 0,                                    NULL }
};

/**************************************************************************
 * Names of userdata subfunctions in group 2 (cyclic data)
 */
#define S7COMM_UD_SUBF_CYCLIC_MEM           0x01
#define S7COMM_UD_SUBF_CYCLIC_UNSUBSCRIBE   0x04
#define S7COMM_UD_SUBF_CYCLIC_MEM2          0x05

static const struct int_to_string userdata_cyclic_subfunc_names[] = {
    { S7COMM_UD_SUBF_CYCLIC_MEM,            "Memory" },                         /* read data from memory (DB/M/etc.) */
    { S7COMM_UD_SUBF_CYCLIC_UNSUBSCRIBE,    "Unsubscribe" },                    /* Unsubscribe (disable) cyclic data */
    { S7COMM_UD_SUBF_CYCLIC_MEM2,           "Memory2" },                        /* same as 0x01, but only S7-400 */
    { 0,                                    NULL }
};

/**************************************************************************
 * Names of userdata subfunctions in group 3 (Block functions)
 */
#define S7COMM_UD_SUBF_BLOCK_LIST           0x01
#define S7COMM_UD_SUBF_BLOCK_LISTTYPE       0x02
#define S7COMM_UD_SUBF_BLOCK_BLOCKINFO      0x03

static const struct int_to_string userdata_block_subfunc_names[] = {
    { S7COMM_UD_SUBF_BLOCK_LIST,            "List blocks" },
    { S7COMM_UD_SUBF_BLOCK_LISTTYPE,        "List blocks of type" },
    { S7COMM_UD_SUBF_BLOCK_BLOCKINFO,       "Get block info" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Names of userdata subfunctions in group 4 (CPU functions)
 */
#define S7COMM_UD_SUBF_CPU_SCAN_IND         0x09
#define S7COMM_UD_SUBF_CPU_READSZL          0x01
#define S7COMM_UD_SUBF_CPU_MSGS             0x02
#define S7COMM_UD_SUBF_CPU_DIAGMSG          0x03
#define S7COMM_UD_SUBF_CPU_ALARM8_IND       0x05
#define S7COMM_UD_SUBF_CPU_NOTIFY_IND       0x06
#define S7COMM_UD_SUBF_CPU_ALARM8LOCK       0x07
#define S7COMM_UD_SUBF_CPU_ALARM8UNLOCK     0x08
#define S7COMM_UD_SUBF_CPU_ALARMACK         0x0b
#define S7COMM_UD_SUBF_CPU_ALARMACK_IND     0x0c
#define S7COMM_UD_SUBF_CPU_ALARM8LOCK_IND   0x0d
#define S7COMM_UD_SUBF_CPU_ALARM8UNLOCK_IND 0x0e
#define S7COMM_UD_SUBF_CPU_ALARMSQ_IND      0x11
#define S7COMM_UD_SUBF_CPU_ALARMS_IND       0x12
#define S7COMM_UD_SUBF_CPU_ALARMQUERY       0x13
#define S7COMM_UD_SUBF_CPU_NOTIFY8_IND      0x16

static const struct int_to_string userdata_cpu_subfunc_names[] = {
    { S7COMM_UD_SUBF_CPU_READSZL,           "Read SZL" },
    { S7COMM_UD_SUBF_CPU_MSGS,              "Message service" },                /* Header constant is also different here */
    { S7COMM_UD_SUBF_CPU_DIAGMSG,           "Diagnostic message" },             /* Diagnostic message from PLC */
    { S7COMM_UD_SUBF_CPU_ALARM8_IND,        "ALARM_8 indication" },             /* PLC is indicating an ALARM message, using ALARM_8 SFBs */
    { S7COMM_UD_SUBF_CPU_NOTIFY_IND,        "NOTIFY indication" },              /* PLC is indicating a NOTIFY message, using NOTIFY SFBs */
    { S7COMM_UD_SUBF_CPU_ALARM8LOCK,        "ALARM_8 lock" },                   /* Lock an ALARM message from HMI/SCADA */
    { S7COMM_UD_SUBF_CPU_ALARM8UNLOCK,      "ALARM_8 unlock" },                 /* Unlock an ALARM message from HMI/SCADA */
    { S7COMM_UD_SUBF_CPU_SCAN_IND,          "SCAN indication" },                /* PLC is indicating a SCAN message */
    { S7COMM_UD_SUBF_CPU_ALARMS_IND,        "ALARM_S indication" },             /* PLC is indicating an ALARM message, using ALARM_S/ALARM_D SFCs */
    { S7COMM_UD_SUBF_CPU_ALARMSQ_IND,       "ALARM_SQ indication" },            /* PLC is indicating an ALARM message, using ALARM_SQ/ALARM_DQ SFCs */
    { S7COMM_UD_SUBF_CPU_ALARMQUERY,        "ALARM query" },                    /* HMI/SCADA query of ALARMs */
    { S7COMM_UD_SUBF_CPU_ALARMACK,          "ALARM ack" },                      /* Alarm was acknowledged in HMI/SCADA */
    { S7COMM_UD_SUBF_CPU_ALARMACK_IND,      "ALARM ack indication" },           /* Alarm acknowledge indication from CPU to HMI */
    { S7COMM_UD_SUBF_CPU_ALARM8LOCK_IND,    "ALARM lock indication" },          /* Alarm lock indication from CPU to HMI */
    { S7COMM_UD_SUBF_CPU_ALARM8UNLOCK_IND,  "ALARM unlock indication" },        /* Alarm unlock indication from CPU to HMI */
    { S7COMM_UD_SUBF_CPU_NOTIFY8_IND,       "NOTIFY_8 indication" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Names of userdata subfunctions in group 5 (Security?)
 */
#define S7COMM_UD_SUBF_SEC_PASSWD           0x01

static const struct int_to_string userdata_sec_subfunc_names[] = {
    { S7COMM_UD_SUBF_SEC_PASSWD,            "PLC password" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Names of userdata subfunctions in group 7 (Time functions)
 */
#define S7COMM_UD_SUBF_TIME_READ            0x01
#define S7COMM_UD_SUBF_TIME_SET             0x02
#define S7COMM_UD_SUBF_TIME_READF           0x03
#define S7COMM_UD_SUBF_TIME_SET2            0x04

static const struct int_to_string userdata_time_subfunc_names[] = {
    { S7COMM_UD_SUBF_TIME_READ,             "Read clock" },
    { S7COMM_UD_SUBF_TIME_SET,              "Set clock" },
    { S7COMM_UD_SUBF_TIME_READF,            "Read clock (following)" },
    { S7COMM_UD_SUBF_TIME_SET2,             "Set clock" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Subfunctions only used in Sinumerik NC file download
 */
#define S7COMM_NCPRG_FUNCREQUESTDOWNLOAD    1
#define S7COMM_NCPRG_FUNCDOWNLOADBLOCK      2
#define S7COMM_NCPRG_FUNCCONTDOWNLOAD       3
#define S7COMM_NCPRG_FUNCDOWNLOADENDED      4
#define S7COMM_NCPRG_FUNCSTARTUPLOAD        6
#define S7COMM_NCPRG_FUNCUPLOAD             7
#define S7COMM_NCPRG_FUNCCONTUPLOAD         8

static const struct int_to_string userdata_ncprg_subfunc_names[] = {
    { S7COMM_NCPRG_FUNCREQUESTDOWNLOAD,     "Request download" },
    { S7COMM_NCPRG_FUNCDOWNLOADBLOCK,       "Download block" },
    { S7COMM_NCPRG_FUNCCONTDOWNLOAD,        "Continue download" },
    { S7COMM_NCPRG_FUNCDOWNLOADENDED,       "Download ended" },
    { S7COMM_NCPRG_FUNCSTARTUPLOAD,         "Start upload" },
    { S7COMM_NCPRG_FUNCUPLOAD,              "Upload" },
    { S7COMM_NCPRG_FUNCCONTUPLOAD,          "Continue upload" },
    { 0,                                    NULL }
};


static const struct int_to_string modetrans_param_subfunc_names[] = {
    { 0,                                    "STOP" },
    { 1,                                    "Warm Restart" },
    { 2,                                    "RUN" },
    { 3,                                    "Hot Restart" },
    { 4,                                    "HOLD" },
    { 6,                                    "Cold Restart" },
    { 9,                                    "RUN_R (H-System redundant)" },
    { 11,                                   "LINK-UP" },
    { 12,                                   "UPDATE" },
    { 0,                                    NULL }
};

/**************************************************************************
 * Transport sizes in item data
 */
    /* types of 1 byte length */
#define S7COMM_TRANSPORT_SIZE_BIT           1
#define S7COMM_TRANSPORT_SIZE_BYTE          2
#define S7COMM_TRANSPORT_SIZE_CHAR          3
    /* types of 2 bytes length */
#define S7COMM_TRANSPORT_SIZE_WORD          4
#define S7COMM_TRANSPORT_SIZE_INT           5
    /* types of 4 bytes length */
#define S7COMM_TRANSPORT_SIZE_DWORD         6
#define S7COMM_TRANSPORT_SIZE_DINT          7
#define S7COMM_TRANSPORT_SIZE_REAL          8
    /* Special types */
#define S7COMM_TRANSPORT_SIZE_DATE          9
#define S7COMM_TRANSPORT_SIZE_TOD           10
#define S7COMM_TRANSPORT_SIZE_TIME          11
#define S7COMM_TRANSPORT_SIZE_S5TIME        12
#define S7COMM_TRANSPORT_SIZE_DT            15
    /* Timer or counter */
#define S7COMM_TRANSPORT_SIZE_COUNTER       28
#define S7COMM_TRANSPORT_SIZE_TIMER         29
#define S7COMM_TRANSPORT_SIZE_IEC_COUNTER   30
#define S7COMM_TRANSPORT_SIZE_IEC_TIMER     31
#define S7COMM_TRANSPORT_SIZE_HS_COUNTER    32
static const struct int_to_string item_transportsizenames[] = {
    { S7COMM_TRANSPORT_SIZE_BIT,            "BIT" },
    { S7COMM_TRANSPORT_SIZE_BYTE,           "BYTE" },
    { S7COMM_TRANSPORT_SIZE_CHAR,           "CHAR" },
    { S7COMM_TRANSPORT_SIZE_WORD,           "WORD" },
    { S7COMM_TRANSPORT_SIZE_INT,            "INT" },
    { S7COMM_TRANSPORT_SIZE_DWORD,          "DWORD" },
    { S7COMM_TRANSPORT_SIZE_DINT,           "DINT" },
    { S7COMM_TRANSPORT_SIZE_REAL,           "REAL" },
    { S7COMM_TRANSPORT_SIZE_TOD,            "TOD" },
    { S7COMM_TRANSPORT_SIZE_TIME,           "TIME" },
    { S7COMM_TRANSPORT_SIZE_S5TIME,         "S5TIME" },
    { S7COMM_TRANSPORT_SIZE_DT,             "DATE_AND_TIME" },
    { S7COMM_TRANSPORT_SIZE_COUNTER,        "COUNTER" },
    { S7COMM_TRANSPORT_SIZE_TIMER,          "TIMER" },
    { S7COMM_TRANSPORT_SIZE_IEC_COUNTER,    "IEC TIMER" },
    { S7COMM_TRANSPORT_SIZE_IEC_TIMER,      "IEC COUNTER" },
    { S7COMM_TRANSPORT_SIZE_HS_COUNTER,     "HS COUNTER" },
    { 0,                                    NULL }
};



/**************************************************************************
 * Function codes in parameter part
 */
#define S7COMM_SERV_CPU                     0x00
#define S7COMM_SERV_SETUPCOMM               0xF0
#define S7COMM_SERV_READVAR                 0x04
#define S7COMM_SERV_WRITEVAR                0x05

#define S7COMM_FUNCREQUESTDOWNLOAD          0x1A
#define S7COMM_FUNCDOWNLOADBLOCK            0x1B
#define S7COMM_FUNCDOWNLOADENDED            0x1C
#define S7COMM_FUNCSTARTUPLOAD              0x1D
#define S7COMM_FUNCUPLOAD                   0x1E
#define S7COMM_FUNCENDUPLOAD                0x1F
#define S7COMM_FUNCPISERVICE                0x28
#define S7COMM_FUNC_PLC_STOP                0x29

static const struct int_to_string param_functionnames[] = {
    { S7COMM_SERV_CPU,                      "CPU services" },
    { S7COMM_SERV_SETUPCOMM,                "Setup communication" },
    { S7COMM_SERV_READVAR,                  "Read Var" },
    { S7COMM_SERV_WRITEVAR,                 "Write Var" },
    /* Block management services */
    { S7COMM_FUNCREQUESTDOWNLOAD,           "Request download" },
    { S7COMM_FUNCDOWNLOADBLOCK,             "Download block" },
    { S7COMM_FUNCDOWNLOADENDED,             "Download ended" },
    { S7COMM_FUNCSTARTUPLOAD,               "Start upload" },
    { S7COMM_FUNCUPLOAD,                    "Upload" },
    { S7COMM_FUNCENDUPLOAD,                 "End upload" },
    { S7COMM_FUNCPISERVICE,                 "PI-Service" },
    { S7COMM_FUNC_PLC_STOP,                 "PLC Stop" },
    { 0,                                    NULL }
};


/**************************************************************************
 * Area names
 */
#define S7COMM_AREA_SYSINFO                 0x03        /* System info of 200 family */
#define S7COMM_AREA_SYSFLAGS                0x05        /* System flags of 200 family */
#define S7COMM_AREA_ANAIN                   0x06        /* analog inputs of 200 family */
#define S7COMM_AREA_ANAOUT                  0x07        /* analog outputs of 200 family */
#define S7COMM_AREA_P                       0x80        /* direct peripheral access */
#define S7COMM_AREA_INPUTS                  0x81
#define S7COMM_AREA_OUTPUTS                 0x82
#define S7COMM_AREA_FLAGS                   0x83
#define S7COMM_AREA_DB                      0x84        /* data blocks */
#define S7COMM_AREA_DI                      0x85        /* instance data blocks */
#define S7COMM_AREA_LOCAL                   0x86        /* local data (should not be accessible over network) */
#define S7COMM_AREA_V                       0x87        /* previous (Vorgaenger) local data (should not be accessible over network)  */
#define S7COMM_AREA_COUNTER                 28          /* S7 counters */
#define S7COMM_AREA_TIMER                   29          /* S7 timers */
#define S7COMM_AREA_COUNTER200              30          /* IEC counters (200 family) */
#define S7COMM_AREA_TIMER200                31          /* IEC timers (200 family) */

static const struct int_to_string item_areanames[] = {
    { S7COMM_AREA_SYSINFO,                  "System info of 200 family" },
    { S7COMM_AREA_SYSFLAGS,                 "System flags of 200 family" },
    { S7COMM_AREA_ANAIN,                    "Analog inputs of 200 family" },
    { S7COMM_AREA_ANAOUT,                   "Analog outputs of 200 family" },
    { S7COMM_AREA_P,                        "Direct peripheral access (P)" },
    { S7COMM_AREA_INPUTS,                   "Inputs (I)" },
    { S7COMM_AREA_OUTPUTS,                  "Outputs (Q)" },
    { S7COMM_AREA_FLAGS,                    "Flags (M)" },
    { S7COMM_AREA_DB,                       "Data blocks (DB)" },
    { S7COMM_AREA_DI,                       "Instance data blocks (DI)" },
    { S7COMM_AREA_LOCAL,                    "Local data (L)" },
    { S7COMM_AREA_V,                        "Unknown yet (V)" },
    { S7COMM_AREA_COUNTER,                  "S7 counters (C)" },
    { S7COMM_AREA_TIMER,                    "S7 timers (T)" },
    { S7COMM_AREA_COUNTER200,               "IEC counters (200 family)" },
    { S7COMM_AREA_TIMER200,                 "IEC timers (200 family)" },
    { 0,                                    NULL }
};


enum  s7comm_index_em{
    EM_S7COMM_PROTOID,
    EM_S7COMM_ROSCTR,
    EM_S7COMM_PDUREFER,
    EM_S7COMM_PARALEN,
    EM_S7COMM_DATALEN,
    EM_S7COMM_PARAMETER,
    EM_S7COMM_ITEMCNT,
    EM_S7COMM_DATAINFO,
    EM_S7COMM_CLASS,
    EM_S7COMM_CODE,
    EM_S7COMM_GROUP,
    EM_S7COMM_MODE,
    EM_S7COMM_SUB,
    EM_S7COMM_TYPE,

    EM_S7COMM_DATA,

    EM_S7COMM_MAX
};


static dpi_field_table s7comm_field_array[] = {
    DPI_FIELD_D(EM_S7COMM_PROTOID,              YA_FT_UINT8,                "ProtoIdentifier"),
    DPI_FIELD_D(EM_S7COMM_ROSCTR,               YA_FT_STRING,               "Rosctr"),
    DPI_FIELD_D(EM_S7COMM_PDUREFER,             YA_FT_UINT16,               "PDUReference"),
    DPI_FIELD_D(EM_S7COMM_PARALEN,              YA_FT_UINT16,               "ParameterLength"),
    DPI_FIELD_D(EM_S7COMM_DATALEN,              YA_FT_UINT16,               "DataLength"),
    DPI_FIELD_D(EM_S7COMM_PARAMETER,            YA_FT_STRING,               "Parameter"),
    DPI_FIELD_D(EM_S7COMM_ITEMCNT,              YA_FT_UINT8,                "DataEntries"),
    DPI_FIELD_D(EM_S7COMM_DATAINFO,             YA_FT_STRING,               "DataEntry"),
    DPI_FIELD_D(EM_S7COMM_CLASS,                YA_FT_UINT8,                "Class"),
    DPI_FIELD_D(EM_S7COMM_CODE,                 YA_FT_UINT8,                "Code"),
    DPI_FIELD_D(EM_S7COMM_GROUP,                YA_FT_UINT8,                "Group"),
    DPI_FIELD_D(EM_S7COMM_MODE,                 YA_FT_UINT8,                "Mode"),
    DPI_FIELD_D(EM_S7COMM_SUB,                  YA_FT_UINT8,                "Sub"),
    DPI_FIELD_D(EM_S7COMM_TYPE,                 YA_FT_UINT8,                "Type"),
    DPI_FIELD_D(EM_S7COMM_DATA,                 YA_FT_STRING,               "Data"),
};

typedef struct _S7commInfo
{
    uint8_t     protoid;
    uint8_t     rosctr;
    const char *rosctrstr;
    uint16_t    pdurefer;
    uint16_t    paralen;

    const uint8_t *data;
    uint16_t    datalen;
    uint8_t     class;
    const char *classstr;
    uint8_t     code_flag;
    uint8_t     code;
    char        codestr[8];
    uint8_t     mode;
    const char *modestr;
    uint8_t     group;              // function group
    const char *groupstr;
    uint8_t     sub;                // subfunction
    const char *substr;
    uint16_t    itemcnt;            // item counts
    uint8_t     type;               // functiontype
    const char *typestr;
    char        parameters[256];

    char  datainfo[128];

}S7commInfo;


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;


static
int write_s7comm_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{

    S7commInfo *info=(S7commInfo *)field_info;
    int idx = 0, i = 0;
    struct tbl_log *log_ptr;

    char str[16];
    char parameters[256] = {0};
    const char * codestr = info->codestr;


    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return -1;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "s7comm");

    for(i=0; i<EM_S7COMM_MAX;i++){
        if (i == EM_S7COMM_PARAMETER) {
            struct {
                const char *name;
                const char *value;
            } arrs[] = {
                {"class",   info->classstr},
                {"code",    codestr},
                {"group",   info->groupstr},
                {"mode",    info->modestr},
                {"sub",     info->substr},
                {"type",    info->typestr},
                {0,         NULL}
            };

            uint8_t fistflag = 0;
            for (int j = 0; arrs[j].name != NULL; ++j) {
                if (arrs[j].value == NULL) {
                    continue;
                }
                snprintf(parameters + strlen(parameters), sizeof(parameters), "%s%s=%s",
                fistflag == 0 ? "" : ",",
                arrs[j].name, arrs[j].value);
                fistflag++;
            }
        }
        switch(i){
        case EM_S7COMM_PROTOID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->protoid);
            break;
        case EM_S7COMM_ROSCTR:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->rosctrstr, strlen(info->rosctrstr));
            break;
        case EM_S7COMM_PDUREFER:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT16, NULL, info->pdurefer);
            break;
        case EM_S7COMM_PARALEN:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT16, NULL, info->paralen);
            break;
        case EM_S7COMM_DATALEN:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT16, NULL, info->datalen);
            break;
        case EM_S7COMM_PARAMETER:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)parameters, strlen(parameters));
            break;
        case EM_S7COMM_ITEMCNT:
            if(info->itemcnt>0)
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->itemcnt);
            else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_S7COMM_DATAINFO:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->datainfo, strlen(info->datainfo));
            break;
        case EM_S7COMM_CLASS:
            if (info->classstr != NULL) {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->class);
            } else {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;
        case EM_S7COMM_CODE:
            if (1==info->code_flag) {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->code);
            } else {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;
        case EM_S7COMM_GROUP:
            if (info->groupstr != NULL) {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,EM_F_TYPE_UINT8, NULL, info->group);
            } else {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }

            break;
        case EM_S7COMM_MODE:
            if (info->modestr != NULL) {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->mode);
            } else {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }

            break;
        case EM_S7COMM_SUB:
            if (info->substr != NULL) {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->sub);
            } else {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;
        case EM_S7COMM_TYPE:
            if (info->typestr != NULL) {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->type);
            } else {
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;
        case EM_S7COMM_DATA:
            if(info->data && info->datalen>0)
                write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->data,( uint32_t)info->datalen);
            else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }


    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_S7COMM;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}




static uint32_t
get_uint24(const uint8_t *payload, uint32_t offset) {
    uint8_t var1 = get_uint8_t(payload, offset);
    uint8_t var2 = get_uint8_t(payload, offset + 1);
    uint8_t var3 = get_uint8_t(payload, offset + 2);
    return (uint32_t)((var1 << 16) | (var2 << 8) | var3);
}

static void
parse_data(const uint8_t *payload,const uint32_t payload_len, uint32_t offset, uint8_t parse_type, S7commInfo *info)
{
    uint8_t     transize;
    const char *transizestr;
    uint16_t    datalen;
    uint16_t    dbnum;
    uint8_t     area;
    const char *areastr;
    uint32_t    address;
    char        output[256];
    uint8_t     repeatition_factor;

    switch (parse_type) {
      case 1:
        // variable function
        offset += 1;
        // length
        offset += 1;


        // syntax id
        offset += 1;
        // transport size
        transize = get_uint8_t(payload, offset);
        offset += 1;
        // data length
        datalen = get_uint16_t(payload, offset);
        offset += 2;
        if (datalen > 0) {
            // db number
            dbnum = get_uint16_ntohs(payload, offset);
            offset += 2;
            // area
            area = get_uint8_t(payload, offset);
            offset += 1;

            address = get_uint24(payload, offset);

            areastr = val_to_string(area, item_areanames);
            transizestr = val_to_string(transize, item_transportsizenames);
            snprintf(info->datainfo, sizeof(info->datainfo),
                "%s %d.DBX %d.%d %s %ud",
                areastr == NULL ? "" : areastr,
                dbnum,
                address/8, address%8,
                (transizestr == NULL) ? "" : transizestr,
                datalen);
        }
        break;
      case 3:
        area = get_uint8_t(payload, offset);
        offset += 1;
        repeatition_factor = get_uint8_t(payload, offset);
        offset += 1;
        dbnum = get_uint16_ntohs(payload, offset);
        offset += 2;
        address = get_uint16_ntohs(payload, offset);
        offset += 2;

        areastr = val_to_string(area, item_areanames);
        snprintf(info->datainfo, sizeof(info->datainfo), "%s %d.%d %ud",
            areastr == NULL ? "" : areastr,
            address,
            dbnum,
            repeatition_factor);
        break;
      default:
        break;
    }
}

static uint32_t
s7comm_ud_prog_getdata(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len,
  uint32_t offset, S7commInfo *info)
{
    uint16_t data_len;
    uint16_t data_type;
    uint16_t itemcnt;
    // return code
    offset += 1;

    // transpoort size
    offset += 1;

    data_len = get_uint16_ntohs(payload, offset);
    offset += 2;

    if (data_len > 0) {
        data_type = get_uint16_ntohs(payload, offset);
        offset += 2;

        data_len = get_uint16_ntohs(payload, offset);
        offset += 2;

        // vartab info
        offset += data_type;

        // item count
        itemcnt = get_uint16_ntohs(payload, offset);
        offset += 2;

        info->itemcnt = itemcnt;

        if (info->mode == 0x04) {
            parse_data(payload, payload_len, offset, 3, info);
        } else if (info->mode == 0x00 || info->mode == 0x08) {
            parse_data(payload, payload_len, offset, 2, info);
        }
    }


    return 0;
}

static uint32_t
s7comm_ud_cyclic_getdata(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len,
  uint32_t offset, S7commInfo *info){
    uint16_t itemcnt;
    switch (info->sub) {
    case S7COMM_UD_SUBF_CYCLIC_MEM:
        // return code
        offset += 1;
        // transport size
        offset += 1;
        //data length
        offset += 2;
        //item count
        itemcnt = get_uint16_ntohs(payload, offset);
        offset += 2;
        switch (info->mode)
        {
        case S7COMM_UD_TYPE_REQ:
            // interval timebase
            offset += 1;
            // interval time
            offset += 1;
            parse_data(payload, payload_len, offset, 1, info);
            break;
        case S7COMM_UD_TYPE_PUSH:
        case S7COMM_UD_TYPE_RES:
            parse_data(payload, payload_len, offset, 2, info);
            break;
        default:
            break;
        }
        break;
    case S7COMM_UD_SUBF_CYCLIC_UNSUBSCRIBE:
        parse_data(payload, payload_len, offset, 2, info);
        break;


    default:
        break;
    }

    return 0;
}


static uint32_t
s7comm_ud_cpu_getdata(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len,
  uint32_t offset, S7commInfo *info)
{
    uint8_t     transportsize;
    const char *transportsizestr;
    uint16_t    datalen;
    uint16_t    id;
    uint16_t    index;
    // return code
    offset += 1;

    transportsize = get_uint8_t(payload, offset);
    offset += 1;

    datalen = get_uint16_ntohs(payload, offset);
    offset += 2;


    if (datalen > 0) {
        switch (info->sub)
        {
        case 1:
            id = get_uint16_ntohs(payload, offset);
            index = get_uint16_ntohs(payload, offset + 2);
            transportsizestr = val_to_string(transportsize, item_transportsizenames);
            snprintf(info->datainfo, sizeof(info->datainfo), "%s id=0x%02x index=0x%02x",
                (transportsizestr == NULL) ? "" : transportsizestr, id, index);
            break;
        default:
            break;
        }
    }

    return 0;
}




static int
s7comm_decode_req_resp(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len,
  uint32_t offset, S7commInfo *info)
{
    info->type = get_uint8_t(payload, offset);
    info->typestr = val_to_string(info->type, param_functionnames);
    offset += 1;

    switch (info->type) {
    case S7COMM_SERV_READVAR:
        info->itemcnt = get_uint8_t(payload, offset);
        offset += 1;
        switch (info->rosctr) {
        case S7COMM_ROSCTR_JOB:
            parse_data(payload, payload_len, offset, 1, info);
            break;
        case S7COMM_ROSCTR_ACK_DATA:
            parse_data(payload, payload_len, offset, 2, info);
            break;
        default:
            break;
        }

        break;
    case S7COMM_SERV_WRITEVAR:
        info->itemcnt = get_uint8_t(payload, offset);
        offset += 1;
        switch (info->rosctr) {
        case S7COMM_ROSCTR_JOB:
            parse_data(payload, payload_len, offset, 1, info);
            break;
        case S7COMM_ROSCTR_ACK_DATA:
            break;
        default:
            break;
        }
        break;
    default:
        break;
    }


    return 0;
}


static int
s7comm_decode_ud(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len,
 uint32_t offset, S7commInfo *info)
{
    uint32_t offset_temp = offset;
    uint8_t type, subfunc;

    /* Try do decode some functions...
     * Some functions may use data that doesn't fit one telegram
     */
    /* 3 bytes constant head */
    offset_temp += 3;
    /* 1 byte length of following parameter (8 or 12 bytes) */
    offset_temp += 1;
    /* 1 byte indicating request/response again, but useful in Push telegrams*/
    offset_temp += 1;
    /* High nibble (following/request/response) */
    type = (get_uint8_t(payload, offset_temp) & 0xf0) >> 4;
    info->mode = type;
    info->group = (get_uint8_t(payload, offset_temp) & 0x0f);
    info->modestr = val_to_string(type, userdata_type_names);
    info->groupstr =  val_to_string(info->group, userdata_functiongroup_names);
    /* Low nibble function group  */
    offset_temp += 1;
    /* 1 Byte subfunction  */
    subfunc = get_uint8_t(payload, offset_temp);
    info->sub = subfunc;

    switch (info->group){
        case S7COMM_UD_FUNCGROUP_PROG:
            info->substr =  val_to_string(subfunc, userdata_prog_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_CYCLIC:
            info->substr =  val_to_string(subfunc, userdata_cyclic_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_BLOCK:
            info->substr =  val_to_string(subfunc, userdata_block_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_CPU:
            info->substr =  val_to_string(subfunc, userdata_cpu_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_SEC:
            info->substr =  val_to_string(subfunc, userdata_sec_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_TIME:
            info->substr =  val_to_string(subfunc, userdata_time_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_MODETRANS:
            info->substr =  val_to_string(subfunc, modetrans_param_subfunc_names);
            break;
        case S7COMM_UD_FUNCGROUP_NCPRG:
            info->substr =  val_to_string(subfunc, userdata_ncprg_subfunc_names);
            break;
        default:
            break;
    }
    offset_temp += 1;

    // data block
    offset += info->paralen;

     /* The first 4 bytes of the data part of a userdata telegram are the same for all types.
     * This is also the minumum length of the data part.
     */
    if (info->datalen >= 4) {
        uint8_t     ret_val;
        uint8_t     tsize;
        uint8_t     datalen;

        /* Call function to decode the rest of the data part
         * decode only when there is a data part length greater 4 bytes
         */
        if (info->datalen > 4) {
            switch (info->group) {
              case S7COMM_UD_FUNCGROUP_PROG:
                offset = s7comm_ud_prog_getdata(flow, payload, payload_len, offset, info);
                break;
              case S7COMM_UD_FUNCGROUP_CYCLIC:
                offset = s7comm_ud_cyclic_getdata(flow, payload, payload_len, offset, info);
                break;
              case S7COMM_UD_FUNCGROUP_BLOCK:
              {
                  if(offset+4>payload_len){
                      break;
                  }
                  tsize=get_uint8_t(payload, offset+1);
                  datalen=get_uint16_ntohs(payload, offset+2);
                  //if (type == S7COMM_UD_TYPE_RES && tsize != 0) {
                  //      info->itemcnt=datalen/4;
                  //}
              }
              case S7COMM_UD_FUNCGROUP_CPU:
                offset = s7comm_ud_cpu_getdata(flow, payload, payload_len, offset, info);
                break;
              default:
                    break;
            }
        }
    }
    return 0;
}


static int
dissect_s7comm(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if (g_config.protocol_switch[PROTOCOL_S7COMM] == 0) {
        return 0;
    }

    uint16_t offset = 0;
    uint16_t pkt_len;
    uint8_t     cotp_len;

    // tpkt
    offset += 1; // version
    offset += 1; // reserved

    pkt_len = get_uint16_ntohs(payload, offset);
    offset += 2;

    // cotp
    cotp_len = get_uint8_t(payload, offset);
    offset += 1;
    offset += cotp_len;
    if (offset > payload_len) {
        return 0;
    }


    const uint8_t * s7payload = payload + offset;
    const uint32_t  s7payload_len = payload_len - offset;
    uint16_t        s7offset = 0;
    const char     *tmproc;
    uint8_t         hlength;

    S7commInfo info;
    memset(&info, 0, sizeof(S7commInfo));


    info.protoid = get_uint8_t(s7payload, s7offset);
    s7offset += 1;
    info.rosctr = get_uint8_t(s7payload, s7offset);
    s7offset += 1;
    info.rosctrstr = val_to_string(info.rosctr, rosctr_names);
    /* Header 10 Bytes, when type 2 or 3 (response) -> 12 Bytes */
    if (info.rosctr == 2 || info.rosctr == 3) hlength = 12;

    // reserved
    s7offset += 2;

    info.pdurefer = get_uint16_ntohs(s7payload, s7offset);
    s7offset += 2;

    // parament length
    info.paralen = get_uint16_ntohs(s7payload, s7offset);
    s7offset += 2;

    // data length
    info.datalen = get_uint16_ntohs(s7payload, s7offset);
    s7offset += 2;


    /* when type is 2 or 3 there are 2 bytes with errorclass and errorcode */
    if (hlength == 12) {
        info.class = get_uint8_t(s7payload, s7offset);
        s7offset += 1;
        info.classstr = val_to_string(info.class, errcls_names);

        info.code_flag=1;
        info.code = get_uint8_t(s7payload, s7offset);
        s7offset += 1;
    }
    if(info.datalen>0)
        info.data=&s7payload[hlength+info.paralen];
    switch (info.rosctr) {
    case S7COMM_ROSCTR_JOB:
    case S7COMM_ROSCTR_ACK_DATA:
        s7comm_decode_req_resp(flow, s7payload, s7payload_len, s7offset, &info);
        write_s7comm_log(flow, direction, &info, NULL);
        break;
    case S7COMM_ROSCTR_USERDATA:
        s7comm_decode_ud(flow, s7payload, s7payload_len, s7offset, &info);
        write_s7comm_log(flow, direction, &info, NULL);
        break;
    default:
        break;
    }

    return 0;
}


static void
identify_s7comm(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_S7COMM] == 0) {
        return;
    }

    uint16_t offset = 0;
    // tpkt 协议 固定 4 字节
    if (!(get_uint8_t(payload, offset) == 3 && get_uint8_t(payload, offset + 1) == 0)) {
        return;
    }
    offset += 1;
    offset += 1;

    uint16_t pkt_len = get_uint16_ntohs(payload, offset);   // tpkt + cotp + s7comm
    uint16_t min_len = TPKT_LEN + COTP_MIN_LEN + S7COMM_MIN_TELEGRAM_LENGTH;
    if (pkt_len < min_len) {
        return;
    }
    offset += 2;
    // tpkt 协议

    // cotp 协议
    uint8_t cotp_len = get_uint8_t(payload, offset);
    offset += 1;

    offset += cotp_len;
    if (payload_len - S7COMM_MIN_TELEGRAM_LENGTH < offset) {
        return;
    }
    // cotp 协议

    if (get_uint8_t(payload, offset) != S7COMM_PORT_ID) {
        return;
    }

    if (get_uint8_t(payload, offset + 1) < 0x01 || get_uint8_t(payload, offset + 1) > 0x07) {
        return;
    }

    /** dtls 协议识别成功 **/
    flow->real_protocol_id = PROTOCOL_S7COMM;
    return ;
}

static void init_s7comm_dissector(void)
{
    dpi_register_proto_schema(s7comm_field_array,EM_S7COMM_MAX,"s7comm");
    port_add_proto_head(IPPROTO_TCP, TCP_PORT_S7COMM, PROTOCOL_S7COMM);

    tcp_detection_array[PROTOCOL_S7COMM].proto = PROTOCOL_S7COMM;
    tcp_detection_array[PROTOCOL_S7COMM].identify_func = identify_s7comm;
    tcp_detection_array[PROTOCOL_S7COMM].dissect_func = dissect_s7comm;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_S7COMM].excluded_protocol_bitmask, PROTOCOL_S7COMM);


    map_fields_info_register(s7comm_field_array, PROTOCOL_S7COMM, EM_S7COMM_MAX,"s7comm");


    return;
}


static __attribute((constructor)) void    before_init_s7comm(void){
    register_tbl_array(TBL_LOG_S7COMM, 0, "s7comm", init_s7comm_dissector);
}
