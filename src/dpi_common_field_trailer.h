    DPI_FIELD_D(EM_COMMON_TAGTYPE,                   EM_F_TYPE_STRING,               "TAGTYPE"),
    DPI_FIELD_D(EM_COMMON_OPERATOR_TYPE,             EM_F_TYPE_STRING,               "OPERATOR_TYPE"),
    DPI_FIELD_D(EM_COMMON_HW_NCODE,                  EM_F_TYPE_STRING,               "HW_NCODE"),
    DPI_FIELD_D(EM_COMMON_HW_ACCOUNT,                EM_F_TYPE_STRING,               "HW_ACCOUNT"),
    DPI_FIELD_D(EM_COMMON_HW_ESN,                    EM_F_TYPE_STRING,               "HW_ESN"),
    DPI_FIELD_D(EM_COMMON_HW_MEID,                   EM_F_TYPE_STRING,               "HW_MEID"),
    DPI_FIELD_D(EM_COMMON_HW_LAC,                    EM_F_TYPE_STRING,               "HW_LAC"),
    DPI_FIELD_D(EM_COMMON_HW_SAC,                    EM_F_TYPE_STRING,               "HW_SAC"),
    DPI_FIELD_D(EM_COMMON_HW_RAC,                    EM_F_TYPE_STRING,               "HW_RAC"),
    DPI_FIELD_D(EM_COMMON_HW_CALL_ID,                EM_F_TYPE_STRING,               "HW_CI"),
    DPI_FIELD_D(EM_COMMON_HW_ECGI,                   EM_F_TYPE_STRING,               "HW_ECGI"),
    DPI_FIELD_D(EM_COMMON_HW_BSID,                   EM_F_TYPE_STRING,               "HW_BSID"),
    DPI_FIELD_D(EM_COMMON_HW_GRE_KEY,                EM_F_TYPE_STRING,               "HW_GRE_KEY"),
    DPI_FIELD_D(EM_COMMON_HW_TAI,                    EM_F_TYPE_STRING,               "HW_TAI"),
    DPI_FIELD_D(EM_COMMON_HW_EGI_MNC,                EM_F_TYPE_STRING,               "HW_ECGI_MNC"),
    DPI_FIELD_D(EM_COMMON_HW_APN,                    EM_F_TYPE_STRING,               "HW_APN"),
    DPI_FIELD_D(EM_COMMON_RTL_TEID,                  EM_F_TYPE_STRING,               "RTL_TEID"),           /* HW_TEID */
    DPI_FIELD_D(EM_COMMON_RTL_OUTTER_SRC,            EM_F_TYPE_STRING,               "RTL_OUTTER_SRC"),     /* HW_GTP_SIP */
    DPI_FIELD_D(EM_COMMON_RTL_OUTTER_DST,            EM_F_TYPE_STRING,               "RTL_OUTTER_DST"),     /* HW_GTP_DIP */
    DPI_FIELD_D(EM_COMMON_RTL_MSISDN,                EM_F_TYPE_STRING,               "RTL_MSISDN"),         /* HW_MSISDN */
    DPI_FIELD_D(EM_COMMON_RTL_IMEI,                  EM_F_TYPE_STRING,               "RTL_IMEI"),           /* HW_IMEI */
    DPI_FIELD_D(EM_COMMON_RTL_IMSI,                  EM_F_TYPE_STRING,               "RTL_IMSI"),           /* HW_IMSI/MSID */
    DPI_FIELD_D(EM_COMMON_RTL_TAC,                   EM_F_TYPE_STRING,               "RTL_TAC"),            /* HW_TAC */
    DPI_FIELD_D(EM_COMMON_RTL_PLMN_ID,               EM_F_TYPE_STRING,               "RTL_PLMN_ID"),
    DPI_FIELD_D(EM_COMMON_RTL_ULI,                   EM_F_TYPE_STRING,               "RTL_ULI"),
    DPI_FIELD_D(EM_COMMON_RTL_ENODE_ID,              EM_F_TYPE_STRING,               "RTL_ENODE_ID"),
    DPI_FIELD_D(EM_COMMON_RTL_CELL_ID,               EM_F_TYPE_STRING,               "RTL_CELL_ID"),
    DPI_FIELD_D(EM_COMMON_RTL_BS,                    EM_F_TYPE_STRING,               "RTL_BS"),
    DPI_FIELD_D(EM_COMMON_DEVNO,                     EM_F_TYPE_STRING,                "DevNo"),          //接口号
    DPI_FIELD_D(EM_COMMON_LINENO,                    EM_F_TYPE_STRING,                "LineNo"),         //线路号
    DPI_FIELD_D(EM_COMMON_LINKLAYERTYPE,             EM_F_TYPE_STRING,               "LinkLayerType"),  //链路层类型
    DPI_FIELD_D(EM_COMMON_ISIPV6,                    EM_F_TYPE_UINT8,                "isIPv6"),
    DPI_FIELD_D(EM_COMMON_ISMPLS,                    EM_F_TYPE_UINT8,                "isMPLS"),
    DPI_FIELD_D(EM_COMMON_NLABEL,                    EM_F_TYPE_STRING,                "nLabel"),
    DPI_FIELD_D(EM_COMMON_INNERLABEL,                EM_F_TYPE_STRING,                "innerLabel"),     //通道号
    DPI_FIELD_D(EM_COMMON_OTHERLABEL,                EM_F_TYPE_STRING,                "otherLabel"),
    DPI_FIELD_D(EM_COMMON_RESV1,                     EM_F_TYPE_STRING,                "resv1"),
    DPI_FIELD_D(EM_COMMON_RESV2,                     EM_F_TYPE_STRING,                "resv2"),
    DPI_FIELD_D(EM_COMMON_RESV3,                     EM_F_TYPE_STRING,                "resv3"),
    DPI_FIELD_D(EM_COMMON_RESV4,                     EM_F_TYPE_STRING,                "resv4"),
    DPI_FIELD_D(EM_COMMON_RESV5,                     EM_F_TYPE_STRING,                "resv5"),
    DPI_FIELD_D(EM_COMMON_RESV6,                     EM_F_TYPE_STRING,                "resv6"),
    DPI_FIELD_D(EM_COMMON_RESV7,                     EM_F_TYPE_STRING,                "resv7"),
