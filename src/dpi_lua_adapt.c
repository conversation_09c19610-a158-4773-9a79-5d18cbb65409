#include "dpi_lua_adapt.h"

#include <dirent.h>

#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_utils.h"
extern struct global_config g_config;
__thread padapt_engine_t* engine;
static pschema_db_t* g_adapt_db = NULL;

padapt_controller_t *g_padapt_controller;
padapt_controller_t *g_padapt_tll_controller;
uint8_t              g_adapt_init_flag = 0;  // 是否遍历标记

/*输出适配后的字段表*/
void dpi_pschema_dump_adapt_proto_schemas(const char* pschema_output_dir) {
  // if (g_adapt_db == NULL) {
  //   return;
  // }
  // for (pschema_t* schema = pschema_get_first(g_adapt_db); schema != NULL; schema = pschema_get_next(g_adapt_db, schema)) {
  //   _dpi_pschema_dump_proto_schema_of(schema, pschema_output_dir);
  // }
}

bool dpi_padapt_init(pschema_db_t *pschema_db)
{
  if (NULL == pschema_db) {
    return false;
  }
  g_padapt_controller = padapt_controller_create(pschema_db);
  return true;
}

bool dpi_padapt_tll_init(pschema_db_t *pschema_db)
{
  if (NULL == pschema_db) {
    return false;
  }
  g_padapt_tll_controller = padapt_controller_create(pschema_db);
  return true;
}

static bool ends_with(const char *str, const char *suffix) {
    // 计算字符串和子串的长度
    size_t str_len = strlen(str);
    size_t suffix_len = strlen(suffix);

    // 如果子串长度大于字符串长度，直接返回 false
    if (suffix_len > str_len) {
        return false;
    }

    // 使用 strstr() 函数查找子串在字符串中的位置
    const char *result = strstr(str + (str_len - suffix_len), suffix);

    // 如果结果为 NULL，则表示子串不在字符串中，返回 false；否则返回 true
    return result != NULL;
}

static void _padapt_load_script(const char * file_path)
{
  int   ret = 0;
  char *filename;
  const char * err_msg = NULL;

  if (file_path == NULL) return;

  filename = strrchr(file_path, '/');
  if (!filename) return;
  filename += 1;

  // 确保文件是 .lua 结尾，并且以 adapt_ 开头
  if (ends_with(filename, ".lua") && strncmp(filename, "adapt_", 6) == 0) {
    ret = padapt_controller_load_adapt_script(g_padapt_controller, file_path);
    if (ret != 0) {
      err_msg = precord_misc_get_last_error();
      log_debug("load lua adapt file %s failed, error: %s", file_path, err_msg);
    } else {
      log_debug("adapt proto file = %s", file_path);
      init_reflect_protoname_for_lua(file_path);
    }
  }
}

static void _padapt_tll_load_script(const char * file_path)
{
  int   ret = 0;
  char *filename;
  const char * err_msg = NULL;

  if (file_path == NULL) return;

  filename = strrchr(file_path, '/');
  if (!filename) return;
  filename += 1;

  // 确保文件是 .lua 结尾，并且以 adapt_ 开头
  if (ends_with(filename, ".lua") && strncmp(filename, "adapt_", 6) == 0) {
    ret = padapt_controller_load_adapt_script(g_padapt_tll_controller, file_path);
    if (ret != 0) {
      err_msg = precord_misc_get_last_error();
      printf("load lua adapt tll file %s failed, error: %s\n", file_path, err_msg);
    } else {
      printf("adapt tll proto file = %s\n", file_path);
    }
  }
}

static void _list_files_recursive(const char *path) {
    DIR *dir;
    struct dirent *entry;
    struct stat statbuf;

    // 打开目录
    if ((dir = opendir(path)) == NULL) {
        perror("opendir");
        return;
    }

    // 读取目录中的每个项
    while ((entry = readdir(dir)) != NULL) {
        char filepath[1024];
        snprintf(filepath, sizeof(filepath), "%s/%s", path, entry->d_name);

        // 获取文件信息
        if (lstat(filepath, &statbuf) == -1) {
            perror("lstat");
            continue;
        }

        // 如果是目录，则递归遍历该目录
        if (S_ISDIR(statbuf.st_mode)) {
            // 忽略 . 和 .. 目录
            if (strcmp(entry->d_name, ".") != 0 && strcmp(entry->d_name, "..") != 0) {
                _list_files_recursive(filepath);
            }
        } else {
            _padapt_load_script(filepath);
            // 如果是文件，则打印文件路径
            printf("load lua adapt file %s\n", filepath);
        }
    }

    closedir(dir);
}

bool dpi_padapt_load_script(const char * dir)
{
  char init_file[256]  = {0};
  char proto_dir[256] = {0};

  if (dir == NULL) return false;
  snprintf(init_file, sizeof(init_file), "%s/init.lua", dir);
  if (!access(init_file, F_OK)) {
    log_info("adapt init lua = %s", init_file);
    padapt_controller_load_init_script(g_padapt_controller, init_file);
  }

  dpi_utils_traverse_dir(dir, _padapt_load_script);
  return true;
}

bool dpi_padapt_tll_load_script(const char * dir)
{
  char init_file[256]  = {0};
  char proto_dir[256] = {0};

  if (dir == NULL) return false;
  snprintf(init_file, sizeof(init_file), "%s/init_tll.lua", dir);
  if (!access(init_file, F_OK)) {
    log_info("adapt init lua = %s", init_file);
    padapt_controller_load_init_script(g_padapt_tll_controller, init_file);
  }

  dpi_utils_traverse_dir(dir, _padapt_tll_load_script);
  return true;
}

padapt_engine_t *dpi_padapt_engine_create()
{
    return padapt_engine_create(g_padapt_controller);
}

int dpi_padapt_engine_set_clibs_patch(padapt_engine_t*engine ,char* path)
{
    return padapt_controller_set_clibs_patch(g_padapt_controller,path);
}

padapt_engine_t *dpi_padapt_tll_engine_create()
{
    return padapt_engine_create(g_padapt_tll_controller);
}

void dpi_padapt_engine_destroy(padapt_engine_t * engine)
{
    if (engine == NULL) return;

    padapt_engine_destroy(engine);
}

int dpi_padapt_record_adapt(padapt_engine_t *engine, precord_t **to_record, precord_t * from_record)
{
    int ret = 0;
    const char * err_msg = NULL;
    ret = padapt_engine_record_check_and_adapt(engine, to_record, from_record);
    if (ret != 0) {
        err_msg = precord_misc_get_last_error();
        printf("record adapt failed, error: %s\n", err_msg);
    }

    return ret;
}

void dpi_padapt_print_all_schema()
{
    pschema_db_t * db = padapt_controller_get_adapted_schema_db(g_padapt_controller);
    for (pschema_t* schema = pschema_get_first(db); schema != NULL; schema = pschema_get_next(db, schema)) {
        printf(" ======> lua adapt schema name: %s\n", pschema_get_proto_name(schema));
        for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema); fdesc != NULL; fdesc = pschema_fdesc_get_next(schema, fdesc)) {
            printf("field name: %s\n", pfdesc_get_name(fdesc));
        }
    }
}
