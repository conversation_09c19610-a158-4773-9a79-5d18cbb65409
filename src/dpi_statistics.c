#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <sys/types.h>
#include <ifaddrs.h>
#include <linux/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>

#include "dpi_dpdk_wrapper.h"

#include <yaSdxWatch/dpi_statistics.h>
#include <yaSdxWatch/yaSdxWatch.h>
#include "cJSON.h"

#include "dpi_detect.h"
#include "dpi_utils.h"
#include "dpi_sdt_match.h"

extern struct rte_hash *g_sdt_hash_db;


/*
<状态参数 名称="输入信号速率Gbps">100</状态参数>

<状态参数 名称="连接完整率">98%</状态参数>
<状态参数 名称="连接双向率">20%</状态参数>
<状态参数 名称="组报缓存空间占用率">XXX</状态参数>

// 输入输出流量统计
<状态参数 名称="输入流量累计值">XXX</状态参数>
<状态参数 名称="输入流量瞬时速率">XXX</状态参数>
<状态参数 名称="输出流量累计值">XXX</状态参数>
<状态参数 名称="输出流量瞬时速率">XXX</状态参数>

<状态参数 名称="输入通联信息数量">XXX</状态参数>
<状态参数 名称="邮件报文数量">XXX</状态参数>
<状态参数 名称="HTTP报文数量">XXX</状态参数>
<状态参数 名称="口令字报文数量">XXX</状态参数>
<状态参数 名称="VOIP报文数量">XXX</状态参数>
<状态参数 名称="文件还原数量">XXX</状态参数>
<状态参数 名称="元数据数量">XXX</状态参数>


// 规则统计
<状态参数 名称="生效规则数量">XXX</状态参数>
<状态参数 名称="掩码五元组数量">XXX</状态参数>
<状态参数 名称="精确IP数量">XXX</状态参数>
<状态参数 名称="C网段IP数量">XXX</状态参数>
<状态参数 名称="B网段IP数量">XXX</状态参数>
<状态参数 名称="浮动关键字数量">XXX</状态参数>
<状态参数 名称="固定关键字数量">XXX</状态参数>
<状态参数 名称="正则表达式数量">XXX</状态参数>
<状态参数 名称="协议特征数量">XXX</状态参数>
<状态参数 名称="最近规则更新时间">XXX</状态参数>


<状态参数 名称="流量存盘瞬时速率">XXX</状态参数>
<状态参数 名称="输入端口状态">XXX</状态参数>
<状态参数 名称="输出端口状态">XXX</状态参数>

<状态参数 名称="KAFKA连接状态">XXX</状态参数>
<状态参数 名称="文件系统连接状态">XXX</状态参数>

*/


extern struct rte_mempool *tcp_reassemble_mempool;

extern struct traffic_stats stat_dpdk[DEV_MAX_NUM][TRAFFIC_NUM];
extern struct global_config g_config;

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];

extern const char *protocol_name_array[PROTOCOL_MAX];

extern struct rule_statistic_elem   g_rule_statistic;

static int dpi_get_link_status(int port_id)
{
    struct rte_eth_link link;

    rte_eth_link_get_nowait(port_id, &link);

    int link_status=link.link_status;

    return link_status;
}


/**
 * 计算网卡利用率 百分比
 * @param pps
 *   实时速率，单位 bit/s
 * @param link_speed
 *   网卡最高速率，参考dpdk ETH_SPEED_NUM_, 单位 Mbit/s
 */
static float dpi_get_nic_used_percent(uint64_t pps, uint32_t link_speed)
{
    if (link_speed == RTE_ETH_SPEED_NUM_NONE) {
        return 0;
    }

    return  (float)pps / (link_speed * 1000 * 1000) * 100;
}

#if 0
int dpi_get_port_num(void)
{
#ifdef _DPI_DPDK_17
    int port_numbers=rte_eth_dev_count();
#else
    int port_numbers=rte_eth_dev_count_avail();
#endif


    if(READ_FROM_PCAP == g_config.data_source && port_numbers>0){
        port_numbers-=1;
    }

    return port_numbers;
}



static int dpi_get_port_mac(int port_id, char *mac, int mac_len)
{
#ifdef _DPI_DPDK_17
    int port_numbers=rte_eth_dev_count();
#else
    int port_numbers=rte_eth_dev_count_avail();
#endif

    if(port_id>=port_numbers || port_id<0){
        return -1;
    }

    if(!mac || mac_len<18){
        return -1;
    }

    struct ether_addr addr;
    rte_eth_macaddr_get(port_id, &addr);
    snprintf(mac, mac_len,"%02x:%02x:%02x:%02x:%02x:%02x\n",
                                         addr.addr_bytes[0],
                                         addr.addr_bytes[1],
                                         addr.addr_bytes[2],
                                         addr.addr_bytes[3],
                                         addr.addr_bytes[4],
                                         addr.addr_bytes[5]);

    return 0;
}

int dpi_port_statistics( int port_id, struct port_stat *stat)
{
    if(!stat){
        return -1;
    }

    stat->port_id  = port_id;

    stat->rx_total = stat_dpdk[port_id][0].ibytes;
    stat->rx_bps         = (stat_dpdk[port_id][0].ibytes -
                                 stat_dpdk[port_id][1].ibytes)*8;
    //stat->rx_pps         = stat_dpdk[port_id][0].ipkts -
    //                            stat_dpdk[port_id][1].ipkts;

    stat->tx_total = stat_dpdk[port_id][0].obytes;
    stat->tx_bps         = (stat_dpdk[port_id][0].obytes -
                                 stat_dpdk[port_id][1].obytes)*8;
    //stat->opps         = stat_dpdk[port_id][0].opkts - stat_dpdk[port_id][1].opkts;


    stat->port_link_status  = dpi_get_link_status(port_id);

    dpi_get_port_mac(port_id, stat->mac, sizeof(stat->mac));

    return 0;
}


#endif


/* 输入通联信息数量 */
static uint64_t dpi_get_rx_datace(void)
{
    uint8_t thread_id;
    uint64_t rx_datace_bytes = 0;
    uint64_t rx_datace_pkts  = 0;

    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        rx_datace_pkts  += flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_TLL];
        rx_datace_bytes += flow_thread_info[thread_id].stats.flow_stats_total_bytes[PROTOCOL_TLL];
    }

    return rx_datace_bytes;
}


enum PortType
{
  PORT_RX,
  PORT_TX
};
#define PORT_INFO_MAX_LEN  256

static
int dpi_device_port_status(struct status_info *info, enum PortType type)
{
  uint8_t port_num = 0;
  uint8_t port = 0;
  uint8_t * port_list;
  char port_str[2] = { 0 };
  char out_buff[32];
  uint64_t val;
  cJSON *json_1;
  cJSON *json_2;
  cJSON *json_3;
  uint8_t port_stat = 0;
  char *total;
  char *bps;
  char *link_status;
  char *nic_used;
  char *json_str;

  json_1 = cJSON_CreateObject();
  json_2 = cJSON_CreateObject();
  json_3 = cJSON_CreateObject();
  int i = 0;
  switch (type)
  {
  case PORT_RX:
    port_num = g_config.sdx_config.sdx_rx_port_num;
    port_list = g_config.sdx_config.sdx_rx_port_list;
    total = info->rx_total;
    bps = info->rx_bps;
    link_status = info->rx_link_status;
    nic_used = info->nic_used_percent;
    // port =g_config.sdx_config.sdx_rx_port_list[i];
    break;
  case PORT_TX:
    port_num = g_config.sdx_config.sdx_tx_port_num;
    port_list = g_config.sdx_config.sdx_tx_port_list;
    total = info->tx_total;
    bps = info->tx_bps;
    link_status = info->tx_link_status;
    nic_used = NULL;     // 仅统计收包网口的利用率
    break;
  default:
    goto end;
    break;
  }


  for (i = 0; i < port_num; ++i) {
      struct rte_eth_link link;
      port =port_list[i];

      snprintf(port_str, sizeof(port_str), "%d", port_list[i]);
      rte_eth_link_get_nowait(port, &link);

      val = stat_dpdk[port][0].ibytes;
      cJSON_AddStringToObject(json_1, port_str, dpi_utils_show_bytes(val, out_buff, sizeof(out_buff)));

      // ibytes 累加过程是非原子性的,有可能导致出现前者比后者小,差为负数情况
      val = (stat_dpdk[port][0].ibytes - stat_dpdk[port][1].ibytes);    // 注意求值顺序，这一步的val需要参与计算下一步的网口利用率
      if ((int64_t)val < 0)
        val = 0;

      cJSON_AddStringToObject(json_2, port_str, dpi_utils_show_bps(val*8, out_buff, sizeof(out_buff)));

      if (nic_used) {
        snprintf(out_buff, sizeof(out_buff), "%.2f%%", dpi_get_nic_used_percent(val*8, link.link_speed));
        cJSON_AddStringToObject(json_3, port_str, out_buff);
      }

      port_stat |= (link.link_status << i);
    }

    if (json_1->child != NULL) {
        json_str = cJSON_PrintUnformatted(json_1);
        snprintf(total, PORT_INFO_MAX_LEN, "%s", json_str);
        free(json_str);
    }
    if (json_2->child != NULL) {
        json_str = cJSON_PrintUnformatted(json_2);
        snprintf(bps, PORT_INFO_MAX_LEN, "%s", json_str);
        free(json_str);
    }

    if (json_3->child != NULL && nic_used != NULL) {
        json_str = cJSON_PrintUnformatted(json_3);
        snprintf(nic_used, PORT_INFO_MAX_LEN, "%s", json_str);
        free(json_str);
    }

    if ((port_stat + 1) != pow(2, port_num)) {
      snprintf(link_status, PORT_INFO_MAX_LEN, "%s","部分端口异常:");
      for (i = 0; i < port_num; ++i) {
        snprintf(link_status + strlen(link_status), PORT_INFO_MAX_LEN,
                 "端口%d: %s,", i, (port_stat & (1 < i)) ? "UP" : "DOWN");
      }
    } else {
      snprintf(link_status, PORT_INFO_MAX_LEN, "%s", "端口全部正常");
    }

  end:
    if (json_1)
        cJSON_Delete(json_1);
    if (json_2)
        cJSON_Delete(json_2);
    if (json_3)
        cJSON_Delete(json_3);
    return 0;
}


static
int dpi_device_nic_status(struct status_info *info)
{
    struct ifaddrs *addrs, *tmp;
    int family = 0;
    getifaddrs(&addrs);
    tmp = addrs;
    int skfd = 0;
    struct ifreq ifr;
    char *json_str;

    cJSON * json = cJSON_CreateObject();
    for (tmp = addrs; tmp != NULL; tmp = tmp->ifa_next) {
        if (tmp->ifa_addr == NULL)
            continue;

        family = tmp->ifa_addr->sa_family;
        // if (family != AF_INET6 && family != AF_INET)
        //   continue;
        if (family != AF_PACKET)
            continue;

        if (tmp->ifa_flags & IFF_LOOPBACK)
            continue;

        skfd = socket(family, SOCK_DGRAM, 0);
        if (skfd < 0)
            continue;

        memset(&ifr, 0, sizeof(ifr));
        strcpy(ifr.ifr_name, tmp->ifa_name);

        if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0)  {
            close(skfd);
            continue;
        }

        // if (family == AF_INET6)
        if (ifr.ifr_flags & IFF_RUNNING) {
            cJSON_AddStringToObject(json, tmp->ifa_name, "UP");
        } else {
            cJSON_AddStringToObject(json, tmp->ifa_name, "DOWN");
        }

        close(skfd);
    }

    json_str = cJSON_PrintUnformatted(json);
    snprintf(info->nic_conn_status, sizeof(info->nic_conn_status),
           "%s", json_str);

    free(json_str);
    freeifaddrs(addrs);
    cJSON_Delete(json);
    return 0;
}

int dpi_device_nfs_stats(struct status_info *info)
{
  const char *nfs_path = "/mnt/nfs";
  char cmd[256];
  snprintf(cmd, sizeof(cmd), "mount | grep %s > /dev/null", nfs_path);
  int ret = system(cmd);
  if (ret == -1) {
    perror("system");
    return -1;
  } else if (WIFEXITED(ret)) {
    int status = WEXITSTATUS(ret);
    if (status == 0) {
      snprintf(info->ntfs_conn_status, sizeof(info->ntfs_conn_status), "%s", "UP");
    } else {
      snprintf(info->ntfs_conn_status, sizeof(info->ntfs_conn_status), "%s", "DOWN");
    }
  } else {
    fprintf(stderr, "system did not exit normally\n");
    return -1;
  }
  return 0;
}


int dpi_device_status_info(struct status_info *info)
{
    if(!info){
        return -1;
    }
    int i=0, j = 0;
    // info->signal_bps = 0;
    snprintf(info->signal_bps, sizeof(info->signal_bps), "%s", "10Gbps");
    info->conn_full  = 50;
    info->conn_duplex= 50;

    info->rx_datace  = dpi_get_rx_datace();

    dpi_device_port_status(info, PORT_RX);
    dpi_device_port_status(info, PORT_TX);

    dpi_device_nic_status(info);

    snprintf(info->kafka_conn_status, sizeof(info->kafka_conn_status), "%s", "Down");
    dpi_device_nfs_stats(info);

    // dpi_device_status_debug(info);
    return 0;
}




/*============================ private statistics ==========================================*/

static float dpi_get_inuse_mempool_per(struct rte_mempool *mp)
{
    if(!mp){
        return 0;
    }

    uint32_t in_use_num = rte_mempool_in_use_count(mp);
    uint32_t free_num   = rte_mempool_avail_count(mp);

    return (float)(in_use_num/(free_num+in_use_num));
}



static int dpi_get_mail_stat(void)
{
    uint8_t id;
    uint64_t mail_pkts=0;
    uint64_t mail_bytes=0;
    for (id = 0; id < g_config.dissector_thread_num; id++) {
        mail_pkts  += flow_thread_info[id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_SMTP]  +
                      flow_thread_info[id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_ESMTP] +
                      flow_thread_info[id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_IMAP]  +
                      flow_thread_info[id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_POP];

        mail_bytes += flow_thread_info[id].stats.flow_stats_total_bytes[PROTOCOL_MAIL_SMTP]  +
                      flow_thread_info[id].stats.flow_stats_total_bytes[PROTOCOL_MAIL_ESMTP] +
                      flow_thread_info[id].stats.flow_stats_total_bytes[PROTOCOL_MAIL_IMAP]  +
                      flow_thread_info[id].stats.flow_stats_total_bytes[PROTOCOL_MAIL_POP];
    }

    return 0;
}



static int dpi_get_http_stat(void)
{
    int id;
    uint64_t http_pkts=0;
    uint64_t http_bytes=0;
    for (id = 0; id < (int)g_config.dissector_thread_num; id++) {
        http_pkts  += flow_thread_info[id].stats.flow_stats_total_pkts[PROTOCOL_HTTP];
        http_bytes += flow_thread_info[id].stats.flow_stats_total_bytes[PROTOCOL_HTTP];
    }
    printf("http bytes:%lu, pkt_nums:%lu\n",http_bytes, http_pkts);
    return 0;
}


static int dpi_get_voip_stat(void)
{
    int id;
    uint64_t sip_pkts=0;
    uint64_t sip_bytes=0;
    for (id = 0; id < (int)g_config.dissector_thread_num; id++) {
        sip_pkts  += flow_thread_info[id].stats.flow_stats_total_pkts[PROTOCOL_SIP];
        sip_bytes += flow_thread_info[id].stats.flow_stats_total_bytes[PROTOCOL_SIP];
    }
    return 0;
}


static int
dpi_get_content_stat(struct dpi_content_stat *stat)
{

    // if(!stat){
    //     printf("stat is NULL!\n");
    //     return -1;
    // }
    // snprintf(stat->device.case_name, STATISTICS_DESC_SIZE, "%s", g_config.sdx_config.sdx_stat_case_name);
    // stat->device.node_type=PROGRAM_CONTENT;
    // float    ressemble_mempool_left_percent;

    // int thread_id;

    // for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
    //     stat->mail_packets_count += flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_SMTP]  +
    //                                 flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_ESMTP] +
    //                                 flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_IMAP]  +
    //                                 flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_MAIL_POP];
    //     stat->http_packets_count += flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_HTTP];
    //     stat->voip_packets_count += flow_thread_info[thread_id].stats.flow_stats_total_pkts[PROTOCOL_SIP];
    // }

    // stat->ressemble_mempool_left_percent = dpi_get_inuse_mempool_per(tcp_reassemble_mempool);

    return 0;
}


static int
dpi_get_meta_stat(struct dpi_meta_stat *stat)
{

    if(!stat){
        printf("stat is NULL!\n");
        return -1;
    }
    snprintf(stat->device.case_name, STATISTICS_DESC_SIZE, "%s", g_config.sdx_config.sdx_stat_case_name);
    stat->device.node_type=PROGRAM_META;
    stat->protocol_number = PROTOCOL_MAX;

    return 0;
}


static int
dpi_get_sdt_stat(struct dpi_sdt_stat *stat)
{
    if(!stat){
        printf("stat is NULL!\n");
        return -1;
    }

    uint32_t            packet_on_rule_hit = 0;
    SdtRuleStatistics  *rules_stat=NULL;
    rules_stat=sdt_get_statistics();

    snprintf(stat->device.ip_address, STATISTICS_DESC_SIZE, "%s", g_config.sdx_config.sdx_stat_web_addr);
    snprintf(stat->device.case_name, STATISTICS_DESC_SIZE, "%s", g_config.sdx_config.sdx_stat_case_name);
    stat->device.node_type=PROGRAM_SDT;

    // 统计各线程的匹配命中信息
    for (int i=0; i<(int)(g_config.dissector_thread_num + g_config.app_match_thread_num); i++)
    {
        #if 0
        // 20230906, 按协议统计命中次数 功能取消
        stat->proto_num = PROTOCOL_MAX;
        for (int j=0; j<PROTOCOL_MAX; j++)
        {
            stat->proto_statis_list[j].proto_name = protocol_name_array[j];
            stat->proto_statis_list[j].hit_packet_num += sdx_match_process_status[i].proto_hit_cnt[j];
        }
        #endif
        packet_on_rule_hit += sdx_match_process_status[i].packet_on_rule_hit;
    }

    //以下规则合在一起, 相当于 组合规则数
    int KeyArry[] = {
        RULE_TYPE_ip_p,
        RULE_TYPE_ip_b,
        RULE_TYPE_ip_c,
        RULE_TYPE_fivetupe,
        RULE_TYPE_linename,
        RULE_TYPE_linklayer,
        RULE_TYPE_payloadlen,
        RULE_TYPE_ttl,
        RULE_TYPE_tcp_flag,
        RULE_TYPE_fixed_match,
        RULE_TYPE_float_match,
        RULE_TYPE_regex_match,
        RULE_TYPE_metafield_match,
        RULE_TYPE_link_features,
    };
    int ruleSum = 0;
    for(size_t i = 0; i < sizeof(KeyArry)/sizeof(KeyArry[0]); i++)
    {
        ruleSum += rules_stat->stats_array[i];
    }

    stat->accurate_ip_number        = rules_stat->stats_array[RULE_TYPE_ip_p];
    stat->b_network_segment_number  = rules_stat->stats_array[RULE_TYPE_ip_b];
    stat->c_network_segment_number  = rules_stat->stats_array[RULE_TYPE_ip_c];
    stat->mask_five_tuple_number    = rules_stat->stats_array[RULE_TYPE_fivetupe];
    stat->line_name_number          = rules_stat->stats_array[RULE_TYPE_linename];
    stat->link_layer_number         = rules_stat->stats_array[RULE_TYPE_linklayer];
    stat->pkt_len_number            = rules_stat->stats_array[RULE_TYPE_payloadlen];
    stat->ttl_name_number           = rules_stat->stats_array[RULE_TYPE_ttl];
    stat->tcpflag_rule_number       = rules_stat->stats_array[RULE_TYPE_tcp_flag];
    stat->fix_keyword_number        = rules_stat->stats_array[RULE_TYPE_fixed_match];
    stat->flexible_keyword_number   = rules_stat->stats_array[RULE_TYPE_float_match];
    stat->regex_rules_number        = rules_stat->stats_array[RULE_TYPE_regex_match];
    stat->metdata_name_number       = rules_stat->stats_array[RULE_TYPE_metafield_match];
    stat->protocol_feature_number   = rules_stat->stats_array[RULE_TYPE_link_features];
    stat->last_update_time          = rules_stat->stats_array[RULE_TYPE_last_update];//规则更新时间
    stat->effect_rules_number       = rules_stat->stats_array[RULE_TYPE_effective]; //规则有效个数
    stat->filter_pkt_num            = packet_on_rule_hit;   //规则命中数
    stat->rule_combine_num          = ruleSum;              //组合规则数
    return 0;
}



int dpi_common_get_statistics(void *stat)
{
    int ret=0;

    switch(g_config.sdx_config.sdx_stat_program_mode){
    case PROGRAM_META:
        ret = dpi_get_meta_stat((struct dpi_meta_stat *)stat);
        break;
    case PROGRAM_CONTENT:
        ret = dpi_get_content_stat((struct dpi_content_stat *)stat);
        break;
    case PROGRAM_SDT:
        ret = dpi_get_sdt_stat((struct dpi_sdt_stat *)stat);
        break;
    default:
        ret=-1;
        break;
    }

    return ret;
}


static int out_rule_stats(const struct rule_stat_t *s_rule, void *user)
{
    printf("===uid:%s,gid:%s,rule_id:%u, ghits:%lu, gpkts:%lu\n",
                            s_rule->taskID, s_rule->groupID, s_rule->rule_id,
                            s_rule->total_rule_hits,s_rule->total_rule_match_pkts);

    return 0;
}


static void
statistics_keep_before(const struct stu_rule_statistics *current, struct stu_rule_statistics *before)
{
    before->rule_match_hits  = current->rule_match_hits;
    before->rule_match_pkts  = current->rule_match_pkts;
    before->rule_match_bytes = current->rule_match_bytes;
    before->rule_match_flows = current->rule_match_flows;

    if (0 == before->rule_first_match_time)
    {
       before->rule_first_match_time = current->rule_first_match_time;
    }

    before->rule_last_match_time = current->rule_last_match_time;
}


int dpi_get_per_rule_statistic(int (*out_rule_stats)(const struct rule_stat_t *, void *), void *user)
{
    sdt_out_status *next_hop;
    char           *rule_key;
    uint32_t       iter = 0;
    int            retval=0;
    struct rule_stat_t *cur_stat;

    if(!g_sdt_hash_db || !out_rule_stats){
        return -1;
    }

    cur_stat = (struct rule_stat_t*)malloc(sizeof(struct rule_stat_t));

    if (!cur_stat) {
        return -1;
    }

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag || !next_hop->match_result){
            continue;
        }

        memset(cur_stat, 0, sizeof(*cur_stat));
        strncpy(cur_stat->unitID, next_hop->match_result->unitID, sizeof(cur_stat->unitID));
        strncpy(cur_stat->taskID, next_hop->match_result->taskID, sizeof(cur_stat->taskID));
        strncpy(cur_stat->groupID,next_hop->match_result->groupID,sizeof(cur_stat->groupID));

        cur_stat->rule_id               = next_hop->match_result->ruleID;
        cur_stat->total_rule_hits       = next_hop->statistics_current.rule_match_hits;
        cur_stat->total_rule_match_pkts = next_hop->statistics_current.rule_match_pkts;

        cur_stat->inc_rule_hits       = next_hop->statistics_current.rule_match_hits - next_hop->statistics_before.rule_match_hits;
        cur_stat->inc_rule_match_pkts = next_hop->statistics_current.rule_match_pkts - next_hop->statistics_before.rule_match_pkts;

        // web端统计只需要增量, 有新的命中才上报
        if (0 < cur_stat->inc_rule_hits) {
            out_rule_stats(cur_stat, user);
        }

        statistics_keep_before(&next_hop->statistics_current, &next_hop->statistics_before);
    }

    free(cur_stat);
    return 0;
}


static int dpi_get_protocol_nums(void)
{

    return PROTOCOL_MAX;

}

static const char *dpi_get_protocol_name(int proto_id)
{
    if(proto_id>=PROTOCOL_MAX || proto_id<PROTOCOL_UNKNOWN){
        return NULL;
    }
    return protocol_name_array[proto_id];
}


int dpi_module_test(const char *cmd)
{

    return 0;
}


int dpi_module_unit_test(const char *cmd, char *msg, int len);
int dpi_module_unit_test(const char *cmd, char *msg, int len)
{
    if(!cmd){
        printf("cmd is null, need a id\n");
        return 0;
    }
    int command=atoi(cmd);

    int idx=0;
    switch(command){
    case 1:  // 节点设备信息
        {
            struct dpi_sdt_stat test_stat;
            memset(&test_stat, 0, sizeof(struct dpi_sdt_stat));
            dpi_common_get_statistics((void *)&test_stat);
            idx += snprintf(msg + idx, len - idx,"================= yaDpi 基本信息及规则统计 =================\n");
            idx += snprintf(msg + idx, len - idx,"http监听IP端口:%s\n", test_stat.device.ip_address);
            idx += snprintf(msg + idx, len - idx,"节点角色:%d\n", test_stat.device.node_type);
            idx += snprintf(msg + idx, len - idx,"实例名:%s\n", test_stat.device.case_name);
            idx += snprintf(msg + idx, len - idx,"sdt规则信息：\n");
            idx += snprintf(msg + idx, len - idx,"\t精确IP数量：%u\n",test_stat.accurate_ip_number);
            idx += snprintf(msg + idx, len - idx,"\tB网段IP数量：%u\n",test_stat.b_network_segment_number);
            idx += snprintf(msg + idx, len - idx,"\tC网段IP数量：%u\n",test_stat.c_network_segment_number);
            idx += snprintf(msg + idx, len - idx,"\t掩码五元组数量：%u\n",test_stat.mask_five_tuple_number);
            idx += snprintf(msg + idx, len - idx,"\t线路名称规则：%u\n",test_stat.line_name_number);
            idx += snprintf(msg + idx, len - idx,"\t链路层要素：%u\n",test_stat.link_layer_number);
            idx += snprintf(msg + idx, len - idx,"\t包长匹配：%u\n",test_stat.pkt_len_number);
            idx += snprintf(msg + idx, len - idx,"\tttl 匹配：%u\n",test_stat.ttl_name_number);
            idx += snprintf(msg + idx, len - idx,"\ttcp flag：%u\n",test_stat.tcpflag_rule_number);
            idx += snprintf(msg + idx, len - idx,"\t固定关键字数量：%u\n",test_stat.fix_keyword_number);
            idx += snprintf(msg + idx, len - idx,"\t浮动关键字数量：%u\n",test_stat.flexible_keyword_number);
            idx += snprintf(msg + idx, len - idx,"\t正则表达式数量：%u\n",test_stat.regex_rules_number);
            idx += snprintf(msg + idx, len - idx,"\t元数据字段：%u\n",test_stat.metdata_name_number);
            idx += snprintf(msg + idx, len - idx,"\t协议特征数量：%u\n",test_stat.protocol_feature_number);
            idx += snprintf(msg + idx, len - idx,"\t筛选命中包数:%lu\n", test_stat.filter_pkt_num);
            idx += snprintf(msg + idx, len - idx,"\t复合规则数量:%u\n", test_stat.rule_combine_num);
            idx += snprintf(msg + idx, len - idx,"\n");
        }
        break;
    case 2: // 公共信息统计
        {
            struct status_info test_info;
            memset(&test_info,0,sizeof(struct status_info));
            dpi_device_status_info(&test_info);
            idx += snprintf(msg + idx, len - idx,"\n");
            idx += snprintf(msg + idx, len - idx,"输入信号速率:%s\n",      test_info.signal_bps);
            idx += snprintf(msg + idx, len - idx,"连接完整率:%f\n",test_info.conn_full);
            idx += snprintf(msg + idx, len - idx,"连接双向率:%f\n",test_info.conn_duplex);
            idx += snprintf(msg + idx, len - idx,"输入通联信息数量 :%lu\n",test_info.rx_datace);
            idx += snprintf(msg + idx, len - idx,"nsf文件系统连接状态:%s\n",test_info.ntfs_conn_status);
            idx += snprintf(msg + idx, len - idx,"输入流量总字节数:%s\n",test_info.rx_total);
            idx += snprintf(msg + idx, len - idx,"输入流量瞬时速率:%s\n",test_info.rx_bps);
            idx += snprintf(msg + idx, len - idx,"输出流量总字节数:%s\n",test_info.tx_total);
            idx += snprintf(msg + idx, len - idx,"输出流量瞬时速率:%s\n",test_info.tx_bps);
            idx += snprintf(msg + idx, len - idx,"输入端口连接状态:%s\n",test_info.rx_link_status);
            idx += snprintf(msg + idx, len - idx,"输出端口连接状态:%s\n",test_info.tx_link_status);
            idx += snprintf(msg + idx, len - idx,"\n");
        }
        break;
    case 3:
        dpi_get_per_rule_statistic(out_rule_stats, NULL);
        snprintf(msg, len,"get command is %d\n",command);
        break;
    default:
        idx += snprintf(msg + idx, len - idx,"[ERROR]not support test cmd %s !!!\n",cmd);
        break;
    }


    return 0;
}



