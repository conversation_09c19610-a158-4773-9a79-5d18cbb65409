#ifndef _FLOW_FLOOD_POWER_BY_CHUNLI_
#define _FLOW_FLOOD_POWER_BY_CHUNLI_

#ifdef __cplusplus
extern "C"
{
#endif /* __cplusplus */

struct flood_pkt_t
{
    unsigned char          *pkt_ptr;
    int                     pkt_len;
    int                     datalink;
};

struct flood_context_t*
flow_flood_init(const char *filename);

struct flood_pkt_t*
flow_flood_next(struct flood_context_t *ctx);

void
flow_flood_free(struct flood_context_t *ctx);

int read_pcap(const char *dirname, int cb_pkt(unsigned char *p, int l, void *user), void *user);

#ifdef __cplusplus
} /* extern "C" */
#endif /* __cplusplus */

#endif
