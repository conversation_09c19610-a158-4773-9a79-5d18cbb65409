/****************************************************************************************
 * 文 件 名 : dpi_gtp_control.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh              2018/08/27
编码: liugh            2018/08/27
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/



#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#define GTP_C_PORT_0    3386      //GTP V0
#define GTP_C_PORT_1    2123      //GTP V1


#define  GTP_VERSION_1   1
#define  GTP_VERSION_2   2

#define GTPV2_IE_RESERVED                 0
#define GTPV2_IE_IMSI                     1
#define GTPV2_IE_CAUSE                    2
#define GTPV2_REC_REST_CNT                3
/*Start SRVCC Messages*/
#define GTPV2_IE_STN_SR                  51
#define GTPV2_IE_SRC_TGT_TRANS_CON       52
#define GTPV2_IE_TGT_SRC_TRANS_CON       53
#define GTPV2_IE_MM_CON_EUTRAN_SRVCC     54
#define GTPV2_IE_MM_CON_UTRAN_SRVCC      55
#define GTPV2_IE_SRVCC_CAUSE             56
#define GTPV2_IE_TGT_RNC_ID              57
#define GTPV2_IE_TGT_GLOGAL_CELL_ID      58
#define GTPV2_IE_TEID_C                  59
#define GTPV2_IE_SV_FLAGS                60
#define GTPV2_IE_SAI                     61
#define GTPV2_IE_MM_CTX_FOR_CS_TO_PS_SRVCC 62
/* 61 - 70 for future sv interface use*/
/*End SRVCC Messages*/
#define GTPV2_APN                        71
#define GTPV2_AMBR                       72
#define GTPV2_EBI                        73
#define GTPV2_IP_ADDRESS                 74
#define GTPV2_MEI                        75
#define GTPV2_IE_MSISDN                  76
#define GTPV2_INDICATION                 77
#define GTPV2_PCO                        78
#define GTPV2_PAA                        79
#define GTPV2_BEARER_QOS                 80
#define GTPV2_IE_FLOW_QOS                81
#define GTPV2_IE_RAT_TYPE                82
#define GTPV2_IE_SERV_NET                83
#define GTPV2_IE_BEARER_TFT              84
#define GTPV2_IE_TAD                     85
#define GTPV2_IE_ULI                     86
#define GTPV2_IE_F_TEID                  87
#define GTPV2_IE_TMSI                    88
#define GTPV2_IE_GLOBAL_CNID             89
#define GTPV2_IE_S103PDF                 90
#define GTPV2_IE_S1UDF                   91
#define GTPV2_IE_DEL_VAL                 92
#define GTPV2_IE_BEARER_CTX              93
#define GTPV2_IE_CHAR_ID                 94
#define GTPV2_IE_CHAR_CHAR               95
#define GTPV2_IE_TRA_INFO                96
#define GTPV2_BEARER_FLAG                97
/* define GTPV2_IE_PAGING_CAUSE          98 (void) */
#define GTPV2_IE_PDN_TYPE                99
#define GTPV2_IE_PTI                    100
#define GTPV2_IE_DRX_PARAM              101
#define GTPV2_IE_UE_NET_CAPABILITY      102
#define GTPV2_IE_MM_CONTEXT_GSM_T       103
#define GTPV2_IE_MM_CONTEXT_UTMS_CQ     104
#define GTPV2_IE_MM_CONTEXT_GSM_CQ      105
#define GTPV2_IE_MM_CONTEXT_UTMS_Q      106
#define GTPV2_IE_MM_CONTEXT_EPS_QQ      107
#define GTPV2_IE_MM_CONTEXT_UTMS_QQ     108
#define GTPV2_IE_PDN_CONNECTION         109
#define GTPV2_IE_PDN_NUMBERS            110
#define GTPV2_IE_P_TMSI                 111
#define GTPV2_IE_P_TMSI_SIG             112
#define GTPV2_IE_HOP_COUNTER            113
#define GTPV2_IE_UE_TIME_ZONE           114
#define GTPV2_IE_TRACE_REFERENCE        115
#define GTPV2_IE_COMPLETE_REQUEST_MSG   116
#define GTPV2_IE_GUTI                   117
#define GTPV2_IE_F_CONTAINER            118
#define GTPV2_IE_F_CAUSE                119
#define GTPV2_IE_SEL_PLMN_ID            120
#define GTPV2_IE_TARGET_ID              121
/* GTPV2_IE_NSAPI                       122 */
#define GTPV2_IE_PKT_FLOW_ID            123
#define GTPV2_IE_RAB_CONTEXT            124
#define GTPV2_IE_S_RNC_PDCP_CTX_INFO    125
#define GTPV2_IE_UDP_S_PORT_NR          126
#define GTPV2_IE_APN_RESTRICTION        127
#define GTPV2_IE_SEL_MODE               128
#define GTPV2_IE_SOURCE_IDENT           129
#define GTPV2_IE_BEARER_CONTROL_MODE    130
#define GTPV2_IE_CNG_REP_ACT            131
#define GTPV2_IE_FQ_CSID                132
#define GTPV2_IE_CHANNEL_NEEDED         133
#define GTPV2_IE_EMLPP_PRI              134
#define GTPV2_IE_NODE_TYPE              135
#define GTPV2_IE_FQDN                   136
#define GTPV2_IE_TI                     137
#define GTPV2_IE_MBMS_SESSION_DURATION  138
#define GTPV2_IE_MBMS_SERVICE_AREA      139
#define GTPV2_IE_MBMS_SESSION_ID        140
#define GTPV2_IE_MBMS_FLOW_ID           141
#define GTPV2_IE_MBMS_IP_MC_DIST        142
#define GTPV2_IE_MBMS_DIST_ACK          143
#define GTPV2_IE_RFSP_INDEX             144
#define GTPV2_IE_UCI                    145
#define GTPV2_IE_CSG_INFO_REP_ACTION    146
#define GTPV2_IE_CSG_ID                 147
#define GTPV2_IE_CMI                    148
#define GTPV2_IE_SERVICE_INDICATOR      149
#define GTPV2_IE_DETACH_TYPE            150
#define GTPV2_IE_LDN                    151
#define GTPV2_IE_NODE_FEATURES          152
#define GTPV2_IE_MBMS_TIME_TO_DATA_XFER 153
#define GTPV2_IE_THROTTLING             154
#define GTPV2_IE_ARP                    155
#define GTPV2_IE_EPC_TIMER              156
#define GTPV2_IE_SIG_PRIO_IND           157
#define GTPV2_IE_TMGI                   158
#define GTPV2_IE_ADD_MM_CONT_FOR_SRVCC  159
#define GTPV2_IE_ADD_FLAGS_FOR_SRVCC    160
#define GTPV2_IE_MMBR                   161
#define GTPV2_IE_MDT_CONFIG             162
#define GTPV2_IE_APCO                   163
#define GTPV2_IE_ABS_MBMS_DATA_TF_TIME  164
#define GTPV2_IE_HENB_INFO_REPORT       165
#define GTPV2_IE_IP4CP                  166
#define GTPV2_IE_CHANGE_TO_REPORT_FLAGS 167
#define GTPV2_IE_ACTION_INDICATION      168
#define GTPV2_IE_TWAN_IDENTIFIER        169
#define GTPV2_IE_ULI_TIMESTAMP          170
#define GTPV2_IE_MBMS_FLAGS             171
#define GTPV2_IE_RAN_NAS_CAUSE          172
#define GTPV2_IE_CN_OP_SEL_ENT          173
#define GTPV2_IE_TRUST_WLAN_MODE_IND    174
#define GTPV2_IE_NODE_NUMBER            175
#define GTPV2_IE_NODE_IDENTIFIER        176
#define GTPV2_IE_PRES_REP_AREA_ACT      177
#define GTPV2_IE_PRES_REP_AREA_INF      178
#define GTPV2_IE_TWAN_ID_TS             179
#define GTPV2_IE_OVERLOAD_CONTROL_INF   180
#define GTPV2_IE_LOAD_CONTROL_INF       181
#define GTPV2_IE_METRIC                 182
#define GTPV2_IE_SEQ_NO                 183
#define GTPV2_IE_APN_AND_REL_CAP        184
#define GTPV2_IE_WLAN_OFFLOADABILITY_IND 185
#define GTPV2_IE_PAGING_AND_SERVICE_INF 186
#define GTPV2_IE_INTEGER_NUMBER         187
#define GTPV2_IE_MILLISECOND_TS         188
/*
189    Monitoring Event Information
190    ECGI List
191    Remote UE Context
192    Remote User ID
193    Remote UE IP information
*/
#define GTPV2_IE_CIOT_OPT_SUPPORT_IND   194
/*
195    SCEF PDN Connection
*/
#define GTPV2_IE_HEADER_COMP_CONF           196
#define GTPV2_IE_EXTENDED_PCO               197
#define GTPV2_IE_SERV_PLMN_RATE_CONTROL     198
#define GTPV2_IE_COUNTER                    199

/* 200    Mapped UE Usage Type */
#define GTPV2_IE_SECONDARY_RAT_USAGE_DATA_REPORT     201
#define GTPV2_IE_UP_FUNC_SEL_INDI_FLG       202
/*
203 to 253    Spare. For future use.
254    Special IE type for IE Type Extension
255    Private Extension
256 to 65535    Spare. For future use.

*/
/* 169 to 254 reserved for future use */
#define GTPV2_IE_PRIVATE_EXT            255


#define GTPv2_ULI_CGI_MASK          0x01
#define GTPv2_ULI_SAI_MASK          0x02
#define GTPv2_ULI_RAI_MASK          0x04
#define GTPv2_ULI_TAI_MASK          0x08
#define GTPv2_ULI_ECGI_MASK         0x10
#define GTPv2_ULI_LAI_MASK          0x20



#define MR_S1_U_eNodeB_GTP_U         0    
#define MR_S1_U_SGW_GTP_U            1
#define MR_S12_RNC_GTP_U            2
#define MR_S12_SGW_GTP_U            3
#define MR_S5_S8_SGW_GTP_U            4   
#define MR_S5_S8_PGW_GTP_U            5
#define MR_S5_S8_SGW_GTP_C            6
#define MR_S5_S8_PGW_GTP_C            7
#define MR_S11_MME_GTP_C            10         
#define MR_S11_S4_SGW_GTP_C            11
#define MR_S10_MME_GTP_C            12
#define MR_S3_MME_GTP_C                13
#define MR_S3_SGSN_GTP_C            14
#define MR_S4_SGSN_GTP_U            15
#define MR_S4_SGW_GTP_U                16
#define MR_S4_SGSN_GTP_C            17

#define MR_FIELD_VALUE_LEN          128
#define MR_FIELD_LITTLE_LEN         64


#define MR_F_TEID_FIELD(key)                                            \
    gtp_info->ie_array[key##_TEIDGREKEY].data=&payload[offset];       \
    gtp_info->ie_array[key##_TEIDGREKEY].length=4;                    \
    offset+=4;                                                          \
    if(1==ipv4_flag){    /* IPV4 */                                     \
        gtp_info->ie_array[key##_IPV4].data=&payload[offset];         \
        gtp_info->ie_array[key##_IPV4].length=4;                      \
        offset+=4;                                                      \
    }                                                                   \
    if(1==ipv6_flag){     /* IPV6 */                                    \
        gtp_info->ie_array[key##_IPV6].data=&payload[offset];         \
        gtp_info->ie_array[key##_IPV6].length=16;                     \
    }                                                                   \
    break


typedef enum _em_gtp_index{
    EM_TEID_DATA_I,
    EM_TEID_DATA_II,
    EM_TEID_CONTROL_PLANE,
    EM_NSAPI,
    EM_CHARGINGID,
    EM_MSISDN,
    EM_IMEI,
    EM_IMSI,
    EM_MCC,
    EM_MNC,
    EM_LAC,
    EM_RAC,
    EM_CELL_LAC,
    EM_CELL_CI,
    EM_SAC,
    EM_APN,

    EM_GSN_ADDRESS_IPV4,
    EM_RNC_ADDRESS_IPV4,
    EM_CG_ADDRESS_IPV4,
    EM_END_USER_ADDRESS_IPV4,
    EM_END_USER_ADDRESS_IPV6,
    EM_PDN_ADDRESS_PREFIX_IPV4,
    EM_PDN_ADDRESS_PREFIX_IPV6,
    EM_TRACKING_AREA_CODE,
    EM_ECGI_ECI,
    EM_LAI,
    EM_RAI,
    EM_SAI,
    EM_CGI,
    EM_ENODEBID,
    EM_CELL_ID,
    EM_RAT,
    EM_PDN_TYPE,
    EM_CAUSE,
    EM_CHARGING_ID,
    EM_UE_TIME_ZONE,
    EM_IPCP_PRI_DNS_ADDRESS,
    EM_IPCP_SEC_DNS_ADDRESS,
    EM_DNS_SERVER_IPV4_ADDRESS,

    EM_S1_U_eNodeB_GTP_U_TeidGREKey,        
    EM_S1_U_SGW_GTP_U_TeidGREKey,            
    EM_S12_RNC_GTP_U_TeidGREKey,              
    EM_S12_SGW_GTP_U_TeidGREKey,
    EM_S5_S8_SGW_GTP_U_TeidGREKey,                 
    EM_S5_S8_PGW_GTP_U_TeidGREKey,
    EM_S5_S8_SGW_GTP_C_TeidGREKey,
    EM_S5_S8_PGW_GTP_C_TeidGREKey,
    EM_S11_MME_GTP_C_TeidGREKey,                     
    EM_S11_S4_SGW_GTP_C_TeidGREKey,
    EM_S10_MME_GTP_C_TeidGREKey,
    EM_S3_MME_GTP_C_TeidGREKey,
    EM_S3_SGSN_GTP_C_TeidGREKey,
    EM_S4_SGSN_GTP_U_TeidGREKey,
    EM_S4_SGW_GTP_U_TeidGREKey,
    EM_S4_SGSN_GTP_C_TeidGREKey,

    EM_S1_U_eNodeB_GTP_U_IPV4,
    EM_S1_U_SGW_GTP_U_IPV4,
    EM_S12_RNC_GTP_U_IPV4,
    EM_S12_SGW_GTP_U_IPV4,
    EM_S5_S8_SGW_GTP_U_IPV4,
    EM_S5_S8_PGW_GTP_U_IPV4,
    EM_S5_S8_SGW_GTP_C_IPV4,
    EM_S5_S8_PGW_GTP_C_IPV4,
    EM_S11_MME_GTP_C_IPV4,
    EM_S11_S4_SGW_GTP_C_IPV4,
    EM_S10_MME_GTP_C_IPV4,
    EM_S3_MME_GTP_C_IPV4,
    EM_S3_SGSN_GTP_C_IPV4,
    EM_S4_SGSN_GTP_U_IPV4,
    EM_S4_SGW_GTP_U_IPV4,
    EM_S4_SGSN_GTP_C_IPV4,

    EM_S1_U_eNodeB_GTP_U_IPV6,
    EM_S1_U_SGW_GTP_U_IPV6,
    EM_S12_RNC_GTP_U_IPV6,
    EM_S12_SGW_GTP_U_IPV6,
    EM_S5_S8_SGW_GTP_U_IPV6,
    EM_S5_S8_PGW_GTP_U_IPV6,
    EM_S5_S8_SGW_GTP_C_IPV6,
    EM_S5_S8_PGW_GTP_C_IPV6,
    EM_S11_MME_GTP_C_IPV6,
    EM_S11_S4_SGW_GTP_C_IPV6,
    EM_S10_MME_GTP_C_IPV6,
    EM_S3_MME_GTP_C_IPV6,
    EM_S3_SGSN_GTP_C_IPV6,
    EM_S4_SGSN_GTP_U_IPV6,
    EM_S4_SGW_GTP_U_IPV6,
    EM_S4_SGSN_GTP_C_IPV6,


    EM_FIELD_MAX
    
}em_gtp_index;

enum gtp_control_index_em{
    
    EM_GTP_CONTROL_VERSION,
    EM_GTP_CONTROL_MESSAGETYPE,
    EM_GTP_CONTROL_SEQUENCENUMBER,
    EM_GTP_CONTROL_TEID,
    EM_GTP_CONTROL_APN,
    EM_GTP_CONTROL_IMSI,
    EM_GTP_CONTROL_MSISDN,
    EM_GTP_CONTROL_IMEI,
    EM_GTP_CONTROL_CGI_MCC,
    EM_GTP_CONTROL_CGI_MNC,
    EM_GTP_CONTROL_CGI_LAC,
    EM_GTP_CONTROL_CGI_CI,
    EM_GTP_CONTROL_TAI_MCC,
    EM_GTP_CONTROL_TAI_MNC,
    EM_GTP_CONTROL_TAI_LAC,
    EM_GTP_CONTROL_SAI_MCC,
    EM_GTP_CONTROL_SAI_MNC,
    EM_GTP_CONTROL_SAI_LAC,
    EM_GTP_CONTROL_LAI_MCC,
    EM_GTP_CONTROL_LAI_MNC,
    EM_GTP_CONTROL_LAI_LAC,
    EM_GTP_CONTROL_ECGI_MCC,
    EM_GTP_CONTROL_ECGI_MNC,
    EM_GTP_CONTROL_ECGI_CI,
    EM_GTP_CONTROL_STARTTIME,
    EM_GTP_CONTROL_ENDTIME,
    EM_GTP_CONTROL_SESSION_DURATION,
    EM_GTP_CONTROL_CAUSE,
    EM_GTP_CONTROL_MCC,
    EM_GTP_CONTROL_MNC,
    EM_GTP_CONTROL_LAI,
    EM_GTP_CONTROL_CGI,
    EM_GTP_CONTROL_GSNADDRESS,
    EM_GTP_CONTROL_TEIDDATAI,
    EM_GTP_CONTROL_TEIDDATAII,
    EM_GTP_CONTROL_TEIDCONTROLPLANE,
    EM_GTP_CONTROL_NSAPI,
    EM_GTP_CONTROL_CHARGINGID,
    EM_GTP_CONTROL_LAC,
    EM_GTP_CONTROL_RAC,
    EM_GTP_CONTROL_CELLLAC,
    EM_GTP_CONTROL_CELLCI,
    EM_GTP_CONTROL_SAC,
    EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV4,
    EM_GTP_CONTROL_GGSN_CONTROL_ADDRESSIPV6,
    EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV4,
    EM_GTP_CONTROL_GGSN_USER_ADDRESSIPV6,
    EM_GTP_CONTROL_GSNADDRESSIPV4,
    EM_GTP_CONTROL_GSNADDRESSIPV6,
    EM_GTP_CONTROL_RNCADDRESSIPV4,
    EM_GTP_CONTROL_CGADDRESSIPV4,
    EM_GTP_CONTROL_ENDUSERADDRESSIPV4,
    EM_GTP_CONTROL_ENDUSERADDRESSIPV6,
    EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV4,
    EM_GTP_CONTROL_PDNADDRESSANDPREFIXIPV6,
    EM_GTP_CONTROL_TRACKINGAREACODE,
    EM_GTP_CONTROL_ECGI_ECI,
    EM_GTP_CONTROL_RAI,
    EM_GTP_CONTROL_SAI,
    EM_GTP_CONTROL_ENODEBID,
    EM_GTP_CONTROL_CELLID,
    EM_GTP_CONTROL_RAT,
    EM_GTP_CONTROL_PDNTYPE,
    EM_GTP_CONTROL_CHARGING_ID,
    EM_GTP_CONTROL_UETIMEZONE,
    EM_GTP_CONTROL_IPCP_PRI_DNS_ADDRESS,
    EM_GTP_CONTROL_IPCP_SEC_DNS_ADDRESS,
    EM_GTP_CONTROL_DNSSERVERIPV4ADDRESS,
    EM_GTP_CONTROL_S1_U_ENODEB_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S1_U_SGW_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S12_RNC_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S12_SGW_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S5_S8_SGW_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S5_S8_PGW_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S5_S8_SGW_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S5_S8_PGW_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S11_MME_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S11_S4_SGW_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S10_MME_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S3_MME_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S3_SGSN_GTP_C_TEIDGREKEY,
    EM_GTP_CONTROL_S4_SGSN_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S4_SGW_GTP_U_TEIDGREKEY,
    EM_GTP_CONTROL_S4_SGSN_GTP_C_TEIDGREKEY,

    /*注意，所有新增字段应该在此之前添加，否则可能会有未知错误*/
    EM_GTP_CONTROL_MAX,

    EM_GTP_CONTROL_S1_U_ENODEB_GTP_U_IPV4,
    EM_GTP_CONTROL_S1_U_SGW_GTP_U_IPV4,
    EM_GTP_CONTROL_S12_RNC_GTP_U_IPV4,
    EM_GTP_CONTROL_S12_SGW_GTP_U_IPV4,
    EM_GTP_CONTROL_S5_S8_SGW_GTP_U_IPV4,
    EM_GTP_CONTROL_S5_S8_PGW_GTP_U_IPV4,
    EM_GTP_CONTROL_S5_S8_SGW_GTP_C_IPV4,
    EM_GTP_CONTROL_S5_S8_PGW_GTP_C_IPV4,
    EM_GTP_CONTROL_S11_MME_GTP_C_IPV4,
    EM_GTP_CONTROL_S11_S4_SGW_GTP_C_IPV4,
    EM_GTP_CONTROL_S10_MME_GTP_C_IPV4,
    EM_GTP_CONTROL_S3_MME_GTP_C_IPV4,
    EM_GTP_CONTROL_S3_SGSN_GTP_C_IPV4,
    EM_GTP_CONTROL_S4_SGSN_GTP_U_IPV4,
    EM_GTP_CONTROL_S4_SGW_GTP_U_IPV4,
    EM_GTP_CONTROL_S4_SGSN_GTP_C_IPV4,

    EM_GTP_CONTROL_IP4_MAX,

    EM_GTP_CONTROL_S1_U_ENODEB_GTP_U_IPV6,
    EM_GTP_CONTROL_S1_U_SGW_GTP_U_IPV6,
    EM_GTP_CONTROL_S12_RNC_GTP_U_IPV6,
    EM_GTP_CONTROL_S12_SGW_GTP_U_IPV6,
    EM_GTP_CONTROL_S5_S8_SGW_GTP_U_IPV6,
    EM_GTP_CONTROL_S5_S8_PGW_GTP_U_IPV6,
    EM_GTP_CONTROL_S5_S8_SGW_GTP_C_IPV6,
    EM_GTP_CONTROL_S5_S8_PGW_GTP_C_IPV6,
    EM_GTP_CONTROL_S11_MME_GTP_C_IPV6,
    EM_GTP_CONTROL_S11_S4_SGW_GTP_C_IPV6,
    EM_GTP_CONTROL_S10_MME_GTP_C_IPV6,
    EM_GTP_CONTROL_S3_MME_GTP_C_IPV6,
    EM_GTP_CONTROL_S3_SGSN_GTP_C_IPV6,
    EM_GTP_CONTROL_S4_SGSN_GTP_U_IPV6,
    EM_GTP_CONTROL_S4_SGW_GTP_U_IPV6,
    EM_GTP_CONTROL_S4_SGSN_GTP_C_IPV6,

    EM_GTP_CONTROL_IP6_MAX,
};

/*********************************** gtpv1 gtp message type **********************************/
/* definitions of GTP messages */
#define GTP_MSG_UNKNOWN             0x00
#define GTP_MSG_ECHO_REQ            0x01
#define GTP_MSG_ECHO_RESP           0x02
#define GTP_MSG_VER_NOT_SUPP        0x03
#define GTP_MSG_NODE_ALIVE_REQ      0x04
#define GTP_MSG_NODE_ALIVE_RESP     0x05
#define GTP_MSG_REDIR_REQ           0x06
#define GTP_MSG_REDIR_RESP          0x07
/*
 * 8-15 For future use. Shall not be sent. If received,
 * shall be treated as an Unknown message.
 */
#define GTP_MSG_CREATE_PDP_REQ      0x10
#define GTP_MSG_CREATE_PDP_RESP     0x11
#define GTP_MSG_UPDATE_PDP_REQ      0x12
#define GTP_MSG_UPDATE_PDP_RESP     0x13
#define GTP_MSG_DELETE_PDP_REQ      0x14
#define GTP_MSG_DELETE_PDP_RESP     0x15
#define GTP_MSG_INIT_PDP_CONTEXT_ACT_REQ   0x16    /* 2G */
#define GTP_MSG_INIT_PDP_CONTEXT_ACT_RESP  0x17    /* 2G */
/*
 * 24-25 For future use. Shall not be sent. If received,
 * shall be treated as an Unknown message.
 */
#define GTP_MSG_DELETE_AA_PDP_REQ   0x18    /* 2G */
#define GTP_MSG_DELETE_AA_PDP_RESP  0x19    /* 2G */
#define GTP_MSG_ERR_IND             0x1A
#define GTP_MSG_PDU_NOTIFY_REQ      0x1B
#define GTP_MSG_PDU_NOTIFY_RESP     0x1C
#define GTP_MSG_PDU_NOTIFY_REJ_REQ  0x1D
#define GTP_MSG_PDU_NOTIFY_REJ_RESP 0x1E
#define GTP_MSG_SUPP_EXT_HDR        0x1F
#define GTP_MSG_SEND_ROUT_INFO_REQ  0x20
#define GTP_MSG_SEND_ROUT_INFO_RESP 0x21
#define GTP_MSG_FAIL_REP_REQ        0x22
#define GTP_MSG_FAIL_REP_RESP       0x23
#define GTP_MSG_MS_PRESENT_REQ      0x24
#define GTP_MSG_MS_PRESENT_RESP     0x25
/*
 * 38-47 For future use. Shall not be sent. If received,
 * shall be treated as an Unknown message.
 */
#define GTP_MSG_IDENT_REQ           0x30
#define GTP_MSG_IDENT_RESP          0x31
#define GTP_MSG_SGSN_CNTXT_REQ      0x32
#define GTP_MSG_SGSN_CNTXT_RESP     0x33
#define GTP_MSG_SGSN_CNTXT_ACK      0x34
#define GTP_MSG_FORW_RELOC_REQ      0x35
#define GTP_MSG_FORW_RELOC_RESP     0x36
#define GTP_MSG_FORW_RELOC_COMP     0x37
#define GTP_MSG_RELOC_CANCEL_REQ    0x38
#define GTP_MSG_RELOC_CANCEL_RESP   0x39
#define GTP_MSG_FORW_SRNS_CNTXT     0x3A
#define GTP_MSG_FORW_RELOC_ACK      0x3B
#define GTP_MSG_FORW_SRNS_CNTXT_ACK 0x3C
/*
 * 61-69 For future use. Shall not be sent. If received,
 * shall be treated as an Unknown message.
 */
#define GTP_MSG_RAN_INFO_RELAY      70
/*
 * 71-95 For future use. Shall not be sent. If received,
 * shall be treated as an Unknown message.
 */
#define GTP_MBMS_NOTIFY_REQ         96
#define GTP_MBMS_NOTIFY_RES         97
#define GTP_MBMS_NOTIFY_REJ_REQ     98
#define GTP_MBMS_NOTIFY_REJ_RES     99
#define GTP_CREATE_MBMS_CNTXT_REQ   100
#define GTP_CREATE_MBMS_CNTXT_RES   101
#define GTP_UPD_MBMS_CNTXT_REQ      102
#define GTP_UPD_MBMS_CNTXT_RES      103
#define GTP_DEL_MBMS_CNTXT_REQ      104
#define GTP_DEL_MBMS_CNTXT_RES      105
/*
 * 106 - 111 For future use. Shall not be sent. If received,
 * shall be treated as an Unknown message.
 */
#define GTP_MBMS_REG_REQ            112
#define GTP_MBMS_REG_RES            113
#define GTP_MBMS_DE_REG_REQ         114
#define GTP_MBMS_DE_REG_RES         115
#define GTP_MBMS_SES_START_REQ      116
#define GTP_MBMS_SES_START_RES      117
#define GTP_MBMS_SES_STOP_REQ       118
#define GTP_MBMS_SES_STOP_RES       119
#define GTP_MBMS_SES_UPD_REQ        120
#define GTP_MBMS_SES_UPD_RES        121
/* 122-127  For future use. Shall not be sent.
 * If received, shall be treated as an Unknown message.
 */
#define GTP_MS_INFO_CNG_NOT_REQ     128
#define GTP_MS_INFO_CNG_NOT_RES     129
/* 130-239  For future use. Shall not be sent.
 * If received, shall be treated as an Unknown message.
 */
#define GTP_MSG_DATA_TRANSF_REQ     0xF0
#define GTP_MSG_DATA_TRANSF_RESP    0xF1
/* 242-253  For future use. Shall not be sent.
 * If received, shall be treated as an Unknown message.
 */
#define GTP_MSG_END_MARKER          0xFE /* 254 */
#define GTP_MSG_TPDU                0xFF
/*********************************** gtpv1 gtp message type **********************************/
/* definitions of fields in extension header */
#define GTP_EXT_CAUSE                 0x01
#define GTP_EXT_IMSI                  0x02
#define GTP_EXT_RAI                   0x03
#define GTP_EXT_TLLI                  0x04
#define GTP_EXT_PTMSI                 0x05
#define GTP_EXT_QOS_GPRS              0x06
#define GTP_EXT_REORDER               0x08
#define GTP_EXT_AUTH_TRI              0x09
#define GTP_EXT_MAP_CAUSE             0x0B
#define GTP_EXT_PTMSI_SIG             0x0C
#define GTP_EXT_MS_VALID              0x0D
#define GTP_EXT_RECOVER               0x0E
#define GTP_EXT_SEL_MODE              0x0F

#define GTP_EXT_16                    0x10
#define GTP_EXT_FLOW_LABEL            0x10
#define GTP_EXT_TEID                  0x10    /* 0xFF10 3G */

#define GTP_EXT_17                    0x11
#define GTP_EXT_FLOW_SIG              0x11
#define GTP_EXT_TEID_CP               0x11    /* 0xFF11 3G */

#define GTP_EXT_18                    0x12
#define GTP_EXT_FLOW_II               0x12
#define GTP_EXT_TEID_II               0x12    /* 0xFF12 3G */

#define GTP_EXT_19                    0x13    /* 19 TV Teardown Ind 7.7.16 */
#define GTP_EXT_MS_REASON             0x13    /* same as 0x1D GTPv1_EXT_MS_REASON */
#define GTP_EXT_TEAR_IND              0x13    /* 0xFF13 3G */

#define GTP_EXT_NSAPI                 0x14    /* 3G */
#define GTP_EXT_RANAP_CAUSE           0x15    /* 3G */
#define GTP_EXT_RAB_CNTXT             0x16    /* 3G */
#define GTP_EXT_RP_SMS                0x17    /* 3G */
#define GTP_EXT_RP                    0x18    /* 3G */
#define GTP_EXT_PKT_FLOW_ID           0x19    /* 3G */
#define GTP_EXT_CHRG_CHAR             0x1A    /* 3G */
#define GTP_EXT_TRACE_REF             0x1B    /* 3G */
#define GTP_EXT_TRACE_TYPE            0x1C    /* 3G */
#define GTPv1_EXT_MS_REASON           0x1D    /* 3G 29 TV MS Not Reachable Reason 7.7.25A */
/* 117-126 Reserved for the GPRS charging protocol (see GTP' in 3GPP TS 32.295 [33]) */
#define GTP_EXT_TR_COMM               0x7E    /* charging */
#define GTP_EXT_CHRG_ID               0x7F    /* 127 TV Charging ID 7.7.26 */
#define GTP_EXT_USER_ADDR             0x80
#define GTP_EXT_MM_CNTXT              0x81
#define GTP_EXT_PDP_CNTXT             0x82
#define GTP_EXT_APN                   0x83
#define GTP_EXT_PROTO_CONF            0x84
#define GTP_EXT_GSN_ADDR              0x85
#define GTP_EXT_MSISDN                0x86
#define GTP_EXT_QOS_UMTS              0x87    /* 3G */
#define GTP_EXT_AUTH_QUI              0x88    /* 3G */
#define GTP_EXT_TFT                   0x89    /* 3G */
#define GTP_EXT_TARGET_ID             0x8A    /* 3G */
#define GTP_EXT_UTRAN_CONT            0x8B    /* 3G */
#define GTP_EXT_RAB_SETUP             0x8C    /* 3G */
#define GTP_EXT_HDR_LIST              0x8D    /* 3G */
#define GTP_EXT_TRIGGER_ID            0x8E    /* 3G   142 7.7.41 */
#define GTP_EXT_OMC_ID                0x8F    /* 3G   143 TLV OMC Identity 7.7.42 */
#define GTP_EXT_RAN_TR_CONT           0x90    /* 3G   144 TLV RAN Transparent Container 7.7.43 */
#define GTP_EXT_PDP_CONT_PRIO         0x91    /* 3G   145 TLV PDP Context Prioritization 7.7.45 */
#define GTP_EXT_ADD_RAB_SETUP_INF     0x92    /* 3G   146 TLV Additional RAB Setup Information 7.7.45A */
#define GTP_EXT_SSGN_NO               0x93    /* 3G   147 TLV SGSN Number 7.7.47 */
#define GTP_EXT_COMMON_FLGS           0x94    /* 3G   148 TLV Common Flags 7.7.48 */
#define GTP_EXT_APN_RES               0x95    /* 3G   149 */
#define GTP_EXT_RA_PRIO_LCS           0x96    /* 3G   150 TLV Radio Priority LCS 7.7.25B */
#define GTP_EXT_RAT_TYPE              0x97    /* 3G   151 TLV RAT Type 7.7.50 */
#define GTP_EXT_USR_LOC_INF           0x98    /* 3G   152 TLV User Location Information 7.7.51 */
#define GTP_EXT_MS_TIME_ZONE          0x99    /* 3G   153 TLV MS Time Zone 7.7.52 */
#define GTP_EXT_IMEISV                0x9A    /* 3G   154 TLV IMEI(SV) 7.7.53 */
#define GTP_EXT_CAMEL_CHG_INF_CON     0x9B    /* 3G   155 TLV CAMEL Charging Information Container 7.7.54 */
#define GTP_EXT_MBMS_UE_CTX           0x9C    /* 3G   156 TLV MBMS UE Context 7.7.55 */
#define GTP_EXT_TMGI                  0x9D    /* 3G   157 TLV Temporary Mobile Group Identity (TMGI) 7.7.56 */
#define GTP_EXT_RIM_RA                0x9E    /* 3G   158 TLV RIM Routing Address 7.7.57 */
#define GTP_EXT_MBMS_PROT_CONF_OPT    0x9F    /* 3G   159 TLV MBMS Protocol Configuration Options 7.7.58 */
#define GTP_EXT_MBMS_SA               0xA0    /* 3G   160 TLV MBMS Service Area 7.7.60 */
#define GTP_EXT_SRC_RNC_PDP_CTX_INF   0xA1    /* 3G   161 TLV Source RNC PDCP context info 7.7.61 */
#define GTP_EXT_ADD_TRS_INF           0xA2    /* 3G   162 TLV Additional Trace Info 7.7.62 */
#define GTP_EXT_HOP_COUNT             0xA3    /* 3G   163 TLV Hop Counter 7.7.63 */
#define GTP_EXT_SEL_PLMN_ID           0xA4    /* 3G   164 TLV Selected PLMN ID 7.7.64 */
#define GTP_EXT_MBMS_SES_ID           0xA5    /* 3G   165 TLV MBMS Session Identifier 7.7.65 */
#define GTP_EXT_MBMS_2G_3G_IND        0xA6    /* 3G   166 TLV MBMS 2G/3G Indicator 7.7.66 */
#define GTP_EXT_ENH_NSAPI             0xA7    /* 3G   167 TLV Enhanced NSAPI 7.7.67 */
#define GTP_EXT_MBMS_SES_DUR          0xA8    /* 3G   168 TLV MBMS Session Duration 7.7.59 */
#define GTP_EXT_ADD_MBMS_TRS_INF      0xA9    /* 3G   169 TLV Additional MBMS Trace Info 7.7.68 */
#define GTP_EXT_MBMS_SES_ID_REP_NO    0xAA    /* 3G   170 TLV MBMS Session Identity Repetition Number 7.7.69 */
#define GTP_EXT_MBMS_TIME_TO_DATA_TR  0xAB    /* 3G   171 TLV MBMS Time To Data Transfer 7.7.70 */
#define GTP_EXT_PS_HO_REQ_CTX         0xAC    /* 3G   172 TLV PS Handover Request Context 7.7.71 */
#define GTP_EXT_BSS_CONT              0xAD    /* 3G   173 TLV BSS Container 7.7.72 */
#define GTP_EXT_CELL_ID               0xAE    /* 3G   174 TLV Cell Identification 7.7.73 */
#define GTP_EXT_PDU_NO                0xAF    /* 3G   175 TLV PDU Numbers                               7.7.74 */
#define GTP_EXT_BSSGP_CAUSE           0xB0    /* 3G   176 TLV BSSGP Cause                               7.7.75 */
#define GTP_EXT_REQ_MBMS_BEARER_CAP   0xB1    /* 3G   177 TLV Required MBMS bearer capabilities         7.7.76 */
#define GTP_EXT_RIM_ROUTING_ADDR_DISC 0xB2    /* 3G   178 TLV RIM Routing Address Discriminator         7.7.77 */
#define GTP_EXT_LIST_OF_SETUP_PFCS    0xB3    /* 3G   179 TLV List of set-up PFCs                       7.7.78 */
#define GTP_EXT_PS_HANDOVER_XIP_PAR   0xB4    /* 3G   180 TLV PS Handover XID Parameters                7.7.79 */
#define GTP_EXT_MS_INF_CHG_REP_ACT    0xB5    /* 3G   181 TLV MS Info Change Reporting Action           7.7.80 */
#define GTP_EXT_DIRECT_TUNNEL_FLGS    0xB6    /* 3G   182 TLV Direct Tunnel Flags                       7.7.81 */
#define GTP_EXT_CORRELATION_ID        0xB7    /* 3G   183 TLV Correlation-ID                            7.7.82 */
#define GTP_EXT_BEARER_CONTROL_MODE   0xB8    /* 3G   184 TLV Bearer Control Mode                       7.7.83 */
#define GTP_EXT_MBMS_FLOW_ID          0xB9    /* 3G   185 TLV MBMS Flow Identifier                      7.7.84 */
#define GTP_EXT_MBMS_IP_MCAST_DIST    0xBA    /* 3G   186 TLV MBMS IP Multicast Distribution            7.7.85 */
#define GTP_EXT_MBMS_DIST_ACK         0xBB    /* 3G   187 TLV MBMS Distribution Acknowledgement         7.7.86 */
#define GTP_EXT_RELIABLE_IRAT_HO_INF  0xBC    /* 3G   188 TLV Reliable INTER RAT HANDOVER INFO          7.7.87 */
#define GTP_EXT_RFSP_INDEX            0xBD    /* 3G   189 TLV RFSP Index                                7.7.88 */
#define GTP_EXT_FQDN                  0xBE    /* 3G   190 TLV Fully Qualified Domain Name (FQDN)        7.7.90 */
#define GTP_EXT_EVO_ALLO_RETE_P1      0xBF    /* 3G   191 TLV Evolved Allocation/Retention Priority I   7.7.91 */
#define GTP_EXT_EVO_ALLO_RETE_P2      0xC0    /* 3G   192 TLV Evolved Allocation/Retention Priority II  7.7.92 */
#define GTP_EXT_EXTENDED_COMMON_FLGS  0xC1    /* 3G   193 TLV Extended Common Flags                     7.7.93 */
#define GTP_EXT_UCI                   0xC2    /* 3G   194 TLV User CSG Information (UCI)                7.7.94 */
#define GTP_EXT_CSG_INF_REP_ACT       0xC3    /* 3G   195 TLV CSG Information Reporting Action          7.7.95 */
#define GTP_EXT_CSG_ID                0xC4    /* 3G   196 TLV CSG ID                                    7.7.96 */
#define GTP_EXT_CMI                   0xC5    /* 3G   197 TLV CSG Membership Indication (CMI)           7.7.97 */
#define GTP_EXT_AMBR                  0xC6    /* 3G   198 TLV Aggregate Maximum Bit Rate (AMBR)         7.7.98 */
#define GTP_EXT_UE_NETWORK_CAP        0xC7    /* 3G   199 TLV UE Network Capability                     7.7.99 */
#define GTP_EXT_UE_AMBR               0xC8    /* 3G   200 TLV UE-AMBR                                   7.7.100 */
#define GTP_EXT_APN_AMBR_WITH_NSAPI   0xC9    /* 3G   201 TLV APN-AMBR with NSAPI                       7.7.101 */
#define GTP_EXT_GGSN_BACK_OFF_TIME    0xCA    /* 3G   202 TLV GGSN Back-Off Time                        7.7.102 */
#define GTP_EXT_SIG_PRI_IND           0xCB    /* 3G   203 TLV Signalling Priority Indication            7.7.103 */
#define GTP_EXT_SIG_PRI_IND_W_NSAPI   0xCC    /* 3G   204 TLV Signalling Priority Indication with NSAPI 7.7.104 */
#define GTP_EXT_HIGHER_BR_16MB_FLG    0xCD    /* 3G   205 TLV Higher bitrates than 16 Mbps flag         7.7.105 */
#define GTP_EXT_MAX_MBR_APN_AMBR      0xCE    /* 3G   206 TLV Max MBR/APN-AMBR                          7.7.106 */
#define GTP_EXT_ADD_MM_CTX_SRVCC      0xCF    /* 3G   207 TLV Additional MM context for SRVCC           7.7.107 */
#define GTP_EXT_ADD_FLGS_SRVCC        0xD0    /* 3G   208 TLV Additional flags for SRVCC                7.7.108 */
#define GTP_EXT_STN_SR                0xD1    /* 3G   209 TLV STN-SR                                    7.7.109 */
#define GTP_EXT_C_MSISDN              0xD2    /* 3G   210 TLV C-MSISDN                                  7.7.110 */
#define GTP_EXT_EXT_RANAP_CAUSE       0xD3    /* 3G   211 TLV Extended RANAP Cause                      7.7.111 */
#define GTP_EXT_ENODEB_ID             0xD4    /* 3G   212 TLV eNodeB ID                                 7.7.112 */
#define GTP_EXT_SEL_MODE_W_NSAPI      0xD5    /* 3G   213 TLV Selection Mode with NSAPI                 7.7.113 */
#define GTP_EXT_ULI_TIMESTAMP         0xD6    /* 3G   214 TLV ULI Timestamp                             7.7.114 */
#define GTP_EXT_LHN_ID_W_SAPI         0xD7    /* 3G   215 TLV Local Home Network ID (LHN-ID) with NSAPI 7.7.115 */
#define GTP_EXT_CN_OP_SEL_ENTITY      0xD8    /* 3G   216 TLV CN Operator Selection Entity              7.7.116 */
#define GTP_EXT_UE_USAGE_TYPE         0xD9    /* 3G   217 TLV UE Usage Type                             7.7.117 */
#define GTP_EXT_EXT_COMMON_FLGS_II    0xDA    /* 3G   218 TLV Extended Common Flags II                  7.7.118 */
#define GTP_EXT_NODE_IDENTIFIER       0xDB    /* 3G   219 TLV Node Identifier                           7.7.119 */
#define GTP_EXT_CIOT_OPT_SUP_IND      0xDC    /* 3G   220 TLV CIoT Optimizations Support Indication     7.7.120 */
#define GTP_EXT_SCEF_PDN_CONNECTION   0xDD    /* 3G   221 TLV SCEF PDN Connection                       7.7.121 */

/*  222-238 TLV Spare. For future use.     */

/* 239-250  Reserved for the GPRS charging protocol (see GTP' in 3GPP TS 32.295 [33])*/

#define GTP_EXT_C1                    0xC1
#define GTP_EXT_C2                    0xC2
#define GTP_EXT_REL_PACK              0xF9    /* charging */
#define GTP_EXT_CAN_PACK              0xFA    /* charging */
#define GTP_EXT_CHRG_ADDR             0xFB    /* 3G   251     TLV     Charging Gateway Address        7.7.44 */
/* 252-254  Reserved for the GPRS charging protocol (see GTP' in 3GPP TS 32.295 [33])*/
#define GTP_EXT_DATA_REQ              0xFC    /* charging */
#define GTP_EXT_DATA_RESP             0xFD    /* charging */
#define GTP_EXT_NODE_ADDR             0xFE    /* charging */
#define GTP_EXT_PRIV_EXT              0xFF




#define GTP_PT_MASK         0x10
#define GTP_SPARE1_MASK     0x0E
#define GTP_SPARE2_MASK     0x08
#define GTP_E_MASK          0x04
#define GTP_S_MASK          0x02
#define GTP_SNN_MASK        0x01
#define GTP_PN_MASK         0x01




typedef struct _gtp_hdr {
  uint8_t flags;  /* GTP header flags */
  uint8_t message; /* Message type */
  uint16_t length; /* Length of header */
  int64_t teid; /* Tunnel End-point ID */
} gtp_hdr_t;

typedef  struct _ie_type{
    uint8_t     flag;
    uint32_t    length;
    const uint8_t   *data;
}ie_type_t;


typedef struct _gtp_info{
    uint8_t        dev_flag;
    uint8_t        uli_flag;
    uint8_t        version;
    uint8_t        message_type; /* Message type */
    uint16_t       length; /* Length of header */
    uint32_t       teid;
    char           cgi_mnc[4];
    //uint16_t teid_len;
    uint16_t       seq_num;
    char           gsnaddress[64];
    const uint8_t  *seq;
    uint16_t       seq_len;
    uint8_t        spare;
    ie_type_t      ie_array[EM_GTP_CONTROL_IP6_MAX];
    
    uint8_t     msisdn[MR_FIELD_VALUE_LEN];
    uint8_t     imsi[MR_FIELD_VALUE_LEN];
    uint8_t     imei[MR_FIELD_VALUE_LEN];

    uint8_t  nsapi;
    uint16_t mcc;
    uint16_t mnc;
    uint16_t lac;
    uint16_t rac;
    uint16_t cell_lac;
    uint16_t cell_ci;
    uint16_t sac;

    char controlip4[32];
    char controlip6[64];
    char userip4[32];
    char userip6[64];

    uint16_t tac;
    char starttime[128];
    char endtime[128];
    char session_duration[256];
    char cause[256];
    uint32_t  enobe_id;
    uint8_t   cell_id;
    uint8_t   rat_type;
    uint8_t   pdn_type;
    uint32_t  ecgi;
    uint8_t   UETimeZone[MR_FIELD_LITTLE_LEN];
    uint8_t   lai[MR_FIELD_LITTLE_LEN];
    uint8_t   rai[MR_FIELD_LITTLE_LEN];
    uint8_t   sai[MR_FIELD_LITTLE_LEN];
    uint8_t   cgi[MR_FIELD_LITTLE_LEN];
    uint8_t   apn[MR_FIELD_LITTLE_LEN];
    uint32_t chrg_id;
}gtp_info_t;




typedef struct dgt_set_t
{
   const unsigned char out[16];
}dgt_set_t;

void payload_to_ipv6(const uint8_t *payload, uint32_t offset, char *ipv6);
void dissect_mcc_mnc(const uint8_t *payload, uint32_t offset, const uint16_t length,gtp_info_t *gtp_info);

void dissect_gtpv2_imsi(const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info);
void dissect_gtpv2_mei( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info);
void dissect_gtpv2_msisdn( const uint8_t *payload, const uint16_t payload_len,uint32_t offset,uint16_t length,gtp_info_t *gtp_info);


int write_gtp_control_log(struct flow_info *flow, int direction,  gtp_info_t *gtp_info);
int dissect_gtpv2_control(struct flow_info *flow, int direction, uint32_t seq, 
                                const uint8_t *payload, const uint32_t payload_len, 
                                uint8_t flag);
void init_gtp_control_field(void);

int dpi_gtp_skip_pdu_head(const uint8_t *payload, const uint16_t payload_len, uint16_t *_offset);

//void init_gtp_control_dissector(void);


