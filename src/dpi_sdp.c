/****************************************************************************************
 * 文 件 名 : dpi_sdp.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/18
编码: wangy            2018/07/18
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <glib.h>
#include <string.h>
#include <rte_mbuf.h>

#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_sdp.h"

extern struct rte_mempool *tbl_log_mempool;



enum  sdp_index_em{
    EM_SDP_OWNER,
    EM_SDP_ESSION_NAME,
    EM_SDP_TIME,
    EM_SDP_CONNECTION_INFO_00,
    EM_SDP_CONNECTION_INFO_01,
    EM_SDP_MEDIA_00,
    EM_SDP_MEDIA_ATTR_00_00,
    EM_SDP_MEDIA_ATTR_00_01,
    EM_SDP_MEDIA_ATTR_00_02,
    EM_SDP_MEDIA_ATTR_00_03,
    EM_SDP_MEDIA_ATTR_00_04,
    EM_SDP_MEDIA_ATTR_00_05,
    EM_SDP_MEDIA_ATTR_00_06,
    EM_SDP_MEDIA_ATTR_00_07,
    EM_SDP_MEDIA_ATTR_00_08,
    EM_SDP_MEDIA_ATTR_00_09,
    EM_SDP_MEDIA_ATTR_00_10,
    EM_SDP_MEDIA_ATTR_00_11,
    EM_SDP_MEDIA_ATTR_00_12,
    EM_SDP_MEDIA_ATTR_00_13,
    EM_SDP_MEDIA_ATTR_00_14,
    EM_SDP_MEDIA_ATTR_00_15,
    EM_SDP_MEDIA_01,
    EM_SDP_MEDIA_ATTR_01_00,
    EM_SDP_MEDIA_ATTR_01_01,
    EM_SDP_MEDIA_ATTR_01_02,
    EM_SDP_MEDIA_ATTR_01_03,
    EM_SDP_MEDIA_ATTR_01_04,
    EM_SDP_MEDIA_ATTR_01_05,
    EM_SDP_MEDIA_ATTR_01_06,
    EM_SDP_MEDIA_ATTR_01_07,
    EM_SDP_MEDIA_ATTR_01_08,
    EM_SDP_MEDIA_ATTR_01_09,
    EM_SDP_MEDIA_ATTR_01_10,
    EM_SDP_MEDIA_ATTR_01_11,
    EM_SDP_MEDIA_ATTR_01_12,
    EM_SDP_MEDIA_ATTR_01_13,
    EM_SDP_MEDIA_ATTR_01_14,
    EM_SDP_MEDIA_ATTR_01_15,

    EM_SDP_MAX
};


static dpi_field_table  sdp_field_array[] = {
    DPI_FIELD_D(EM_SDP_OWNER,                               EM_F_TYPE_NULL,                 "Owner"),
    DPI_FIELD_D(EM_SDP_ESSION_NAME,                          EM_F_TYPE_NULL,                 "session_name"),
    DPI_FIELD_D(EM_SDP_TIME,                              EM_F_TYPE_NULL,                 "time"),
    DPI_FIELD_D(EM_SDP_CONNECTION_INFO_00,                 EM_F_TYPE_NULL,                 "connection_info_00"),
    DPI_FIELD_D(EM_SDP_CONNECTION_INFO_01,                 EM_F_TYPE_NULL,                 "connection_info_01"),
    DPI_FIELD_D(EM_SDP_MEDIA_00,                          EM_F_TYPE_NULL,                 "media_00"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_00,                 EM_F_TYPE_NULL,                 "media_attr_00_00"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_01,                 EM_F_TYPE_NULL,                 "media_attr_00_01"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_02,                 EM_F_TYPE_NULL,                 "media_attr_00_02"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_03,                 EM_F_TYPE_NULL,                 "media_attr_00_03"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_04,                 EM_F_TYPE_NULL,                 "media_attr_00_04"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_05,                 EM_F_TYPE_NULL,                 "media_attr_00_05"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_06,                 EM_F_TYPE_NULL,                 "media_attr_00_06"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_07,                 EM_F_TYPE_NULL,                 "media_attr_00_07"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_08,                 EM_F_TYPE_NULL,                 "media_attr_00_08"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_09,                 EM_F_TYPE_NULL,                 "media_attr_00_09"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_10,                 EM_F_TYPE_NULL,                 "media_attr_00_10"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_11,                 EM_F_TYPE_NULL,                 "media_attr_00_11"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_12,                 EM_F_TYPE_NULL,                 "media_attr_00_12"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_13,                 EM_F_TYPE_NULL,                 "media_attr_00_13"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_14,                 EM_F_TYPE_NULL,                 "media_attr_00_14"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_00_15,                 EM_F_TYPE_NULL,                 "media_attr_00_15"),
    DPI_FIELD_D(EM_SDP_MEDIA_01,                          EM_F_TYPE_NULL,                 "media_01"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_00,                 EM_F_TYPE_NULL,                 "media_attr_01_00"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_01,                 EM_F_TYPE_NULL,                 "media_attr_01_01"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_02,                 EM_F_TYPE_NULL,                 "media_attr_01_02"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_03,                 EM_F_TYPE_NULL,                 "media_attr_01_03"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_04,                 EM_F_TYPE_NULL,                 "media_attr_01_04"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_05,                 EM_F_TYPE_NULL,                 "media_attr_01_05"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_06,                 EM_F_TYPE_NULL,                 "media_attr_01_06"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_07,                 EM_F_TYPE_NULL,                 "media_attr_01_07"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_08,                 EM_F_TYPE_NULL,                 "media_attr_01_08"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_09,                 EM_F_TYPE_NULL,                 "media_attr_01_09"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_10,                 EM_F_TYPE_NULL,                 "media_attr_01_10"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_11,                 EM_F_TYPE_NULL,                 "media_attr_01_11"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_12,                 EM_F_TYPE_NULL,                 "media_attr_01_12"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_13,                 EM_F_TYPE_NULL,                 "media_attr_01_13"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_14,                 EM_F_TYPE_NULL,                 "media_attr_01_14"),
    DPI_FIELD_D(EM_SDP_MEDIA_ATTR_01_15,                 EM_F_TYPE_NULL,                 "media_attr_01_15"),

};







static const value_string rtp_payload_type_vals[] =
{
/*  0 */    { PT_PCMU,          "ITU-T G.711 PCMU" },
/*  1 */    { PT_1016,          "USA Federal Standard FS-1016" },
/*  2 */    { PT_G721,          "ITU-T G.721" },
/*  3 */    { PT_GSM,           "GSM 06.10" },
/*  4 */    { PT_G723,          "ITU-T G.723" },
/*  5 */    { PT_DVI4_8000,     "DVI4 8000 samples/s" },
/*  6 */    { PT_DVI4_16000,    "DVI4 16000 samples/s" },
/*  7 */    { PT_LPC,           "Experimental linear predictive encoding from Xerox PARC" },
/*  8 */    { PT_PCMA,          "ITU-T G.711 PCMA" },
/*  9 */    { PT_G722,          "ITU-T G.722" },
/* 10 */    { PT_L16_STEREO,    "16-bit uncompressed audio, stereo" },
/* 11 */    { PT_L16_MONO,      "16-bit uncompressed audio, monaural" },
/* 12 */    { PT_QCELP,         "Qualcomm Code Excited Linear Predictive coding" },
/* 13 */    { PT_CN,            "Comfort noise" },
/* 14 */    { PT_MPA,           "MPEG-I/II Audio"},
/* 15 */    { PT_G728,          "ITU-T G.728" },
/* 16 */    { PT_DVI4_11025,    "DVI4 11025 samples/s" },
/* 17 */    { PT_DVI4_22050,    "DVI4 22050 samples/s" },
/* 18 */    { PT_G729,          "ITU-T G.729" },
/* 19 */    { PT_CN_OLD,        "Comfort noise (old)" },
/* 20 */    { 20,               "Unassigned" },
/* 21 */    { 21,               "Unassigned" },
/* 22 */    { 22,               "Unassigned" },
/* 23 */    { 23,               "Unassigned" },
/* 24 */    { 24,               "Unassigned" },
/* 25 */    { PT_CELB,          "Sun CellB video encoding" },
/* 26 */    { PT_JPEG,          "JPEG-compressed video" },
/* 27 */    { 27,               "Unassigned" },
/* 28 */    { PT_NV,            "'nv' program" },
/* 29 */    { 29,               "Unassigned" },
/* 30 */    { 30,               "Unassigned" },
/* 31 */    { PT_H261,          "ITU-T H.261" },
/* 32 */    { PT_MPV,           "MPEG-I/II Video"},
/* 33 */    { PT_MP2T,          "MPEG-II transport streams"},
/* 34 */    { PT_H263,          "ITU-T H.263" },
/* 35-71     Unassigned  */
/* 35 */    { 35,               "Unassigned" },
/* 36 */    { 36,               "Unassigned" },
/* 37 */    { 37,               "Unassigned" },
/* 38 */    { 38,               "Unassigned" },
/* 39 */    { 39,               "Unassigned" },
/* 40 */    { 40,               "Unassigned" },
/* 41 */    { 41,               "Unassigned" },
/* 42 */    { 42,               "Unassigned" },
/* 43 */    { 43,               "Unassigned" },
/* 44 */    { 44,               "Unassigned" },
/* 45 */    { 45,               "Unassigned" },
/* 46 */    { 46,               "Unassigned" },
/* 47 */    { 47,               "Unassigned" },
/* 48 */    { 48,               "Unassigned" },
/* 49 */    { 49,               "Unassigned" },
/* 50 */    { 50,               "Unassigned" },
/* 51 */    { 51,               "Unassigned" },
/* 52 */    { 52,               "Unassigned" },
/* 53 */    { 53,               "Unassigned" },
/* 54 */    { 54,               "Unassigned" },
/* 55 */    { 55,               "Unassigned" },
/* 56 */    { 56,               "Unassigned" },
/* 57 */    { 57,               "Unassigned" },
/* 58 */    { 58,               "Unassigned" },
/* 59 */    { 59,               "Unassigned" },
/* 60 */    { 60,               "Unassigned" },
/* 61 */    { 61,               "Unassigned" },
/* 62 */    { 62,               "Unassigned" },
/* 63 */    { 63,               "Unassigned" },
/* 64 */    { 64,               "Unassigned" },
/* 65 */    { 65,               "Unassigned" },
/* 66 */    { 66,               "Unassigned" },
/* 67 */    { 67,               "Unassigned" },
/* 68 */    { 68,               "Unassigned" },
/* 69 */    { 69,               "Unassigned" },
/* 70 */    { 70,               "Unassigned" },
/* 71 */    { 71,               "Unassigned" },
/* 72-76     Reserved for RTCP conflict avoidance                                  [RFC3551] */
/* 72 */    { 72,               "Reserved for RTCP conflict avoidance" },
/* 73 */    { 73,               "Reserved for RTCP conflict avoidance" },
/* 74 */    { 74,               "Reserved for RTCP conflict avoidance" },
/* 75 */    { 75,               "Reserved for RTCP conflict avoidance" },
/* 76 */    { 76,               "Reserved for RTCP conflict avoidance" },
/* 77-95     Unassigned      ? */
/* 77 */    { 77,               "Unassigned" },
/* 78 */    { 78,               "Unassigned" },
/* 79 */    { 79,               "Unassigned" },
/* 80 */    { 80,               "Unassigned" },
/* 81 */    { 81,               "Unassigned" },
/* 82 */    { 82,               "Unassigned" },
/* 83 */    { 83,               "Unassigned" },
/* 84 */    { 84,               "Unassigned" },
/* 85 */    { 85,               "Unassigned" },
/* 86 */    { 86,               "Unassigned" },
/* 87 */    { 87,               "Unassigned" },
/* 88 */    { 88,               "Unassigned" },
/* 89 */    { 89,               "Unassigned" },
/* 90 */    { 90,               "Unassigned" },
/* 91 */    { 91,               "Unassigned" },
/* 92 */    { 92,               "Unassigned" },
/* 93 */    { 93,               "Unassigned" },
/* 94 */    { 94,               "Unassigned" },
/* 95 */    { 95,               "Unassigned" },
        /* Added to support addtional RTP payload types
         * See epan/rtp_pt.h */
        { PT_UNDF_96,   "DynamicRTP-Type-96" },
        { PT_UNDF_97,   "DynamicRTP-Type-97" },
        { PT_UNDF_98,   "DynamicRTP-Type-98" },
        { PT_UNDF_99,   "DynamicRTP-Type-99" },
        { PT_UNDF_100,  "DynamicRTP-Type-100" },
        { PT_UNDF_101,  "DynamicRTP-Type-101" },
        { PT_UNDF_102,  "DynamicRTP-Type-102" },
        { PT_UNDF_103,  "DynamicRTP-Type-103" },
        { PT_UNDF_104,  "DynamicRTP-Type-104" },
        { PT_UNDF_105,  "DynamicRTP-Type-105" },
        { PT_UNDF_106,  "DynamicRTP-Type-106" },
        { PT_UNDF_107,  "DynamicRTP-Type-107" },
        { PT_UNDF_108,  "DynamicRTP-Type-108" },
        { PT_UNDF_109,  "DynamicRTP-Type-109" },
        { PT_UNDF_110,  "DynamicRTP-Type-110" },
        { PT_UNDF_111,  "DynamicRTP-Type-111" },
        { PT_UNDF_112,  "DynamicRTP-Type-112" },
        { PT_UNDF_113,  "DynamicRTP-Type-113" },
        { PT_UNDF_114,  "DynamicRTP-Type-114" },
        { PT_UNDF_115,  "DynamicRTP-Type-115" },
        { PT_UNDF_116,  "DynamicRTP-Type-116" },
        { PT_UNDF_117,  "DynamicRTP-Type-117" },
        { PT_UNDF_118,  "DynamicRTP-Type-118" },
        { PT_UNDF_119,  "DynamicRTP-Type-119" },
        { PT_UNDF_120,  "DynamicRTP-Type-120" },
        { PT_UNDF_121,  "DynamicRTP-Type-121" },
        { PT_UNDF_122,  "DynamicRTP-Type-122" },
        { PT_UNDF_123,  "DynamicRTP-Type-123" },
        { PT_UNDF_124,  "DynamicRTP-Type-124" },
        { PT_UNDF_125,  "DynamicRTP-Type-125" },
        { PT_UNDF_126,  "DynamicRTP-Type-126" },
        { PT_UNDF_127,  "DynamicRTP-Type-127" },

        { 0,        NULL },
};

static void _copy_one_item(char *result, int max_len, const uint8_t *value, uint16_t len)
{
    int copy_len;
    copy_len = len;
    if (copy_len > max_len - 1)
        copy_len = max_len - 1;

    strncpy(result, (const char *)value, copy_len);
    result[copy_len] = 0;

    return;
}

static void _add_one_item_to_hash(GHashTable *table, const char *header, const uint8_t *value, uint16_t len)
{
    char *_header;
    struct header_value *_value;
    _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
    if (!_value)
        return;
    _header = strdup(header);
    if (!_header) {
        DPI_LOG(DPI_LOG_WARNING, "malloc failed");
        dpi_free(_value);
        return;
    }
    _value->need_free = 0;
    _value->len = len;
    _value->ptr = value;
    g_hash_table_insert(table, _header, _value);    

    return;
}

static void dissect_sdp_owner(const uint8_t *line, int line_len, GHashTable *table)
{
    int black_num = 0;
    int black_index;
    int index = 0;
    struct header_value *_value;

    while (index < line_len) {
        black_index = find_blank_space(line + index, line_len - index);
        if (black_index <= 0) {
            break;
        }
        _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
        if (!_value)
            return;        
        _value->need_free = 0;
        _value->ptr = line + index;
        _value->len = black_index;

        switch (black_num) {
            case 0:
                g_hash_table_insert(table, strdup(SDP_O_USERNAME), _value);
                break;
            case 1:
                g_hash_table_insert(table, strdup(SDP_O_SESSIONID), _value);
                break;
            case 2:
                g_hash_table_insert(table, strdup(SDP_O_VERSION), _value);
                break;
            case 3:
                g_hash_table_insert(table, strdup(SDP_O_NETWORK_TYPE), _value);
                break;
            case 4:
                g_hash_table_insert(table, strdup(SDP_O_ADDRESS_TYPE), _value);

                if (line_len > index + black_index + 1) {
                    _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
                    if (!_value)
                        return;                    
                    _value->need_free = 0;
                    _value->ptr = line + index + black_index + 1;
                    _value->len = line_len - index - black_index - 1;
                    g_hash_table_insert(table, strdup(SDP_O_ADDRESS), _value);
                }
                break;
            default:
                break;

        }
        black_num++;
        index += black_index + 1;
    }
    
    return;
}

static void dissect_sdp_connnection_info(const uint8_t *line, int line_len, GHashTable *table)
{
    int black_num = 0;
    int black_index;
    int index = 0;
    struct header_value *_value;

    while (index < line_len) {
        black_index = find_blank_space(line + index, line_len - index);
        if (black_index <= 0) {
            break;
        }
        _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
        if (!_value)
            return;
        _value->need_free = 0;
        _value->ptr = line + index;
        _value->len = black_index;

        switch (black_num) {
            case 0:
                g_hash_table_insert(table, strdup(SDP_C_NETWORK_TYPE), _value);
                break;
            case 1:
                g_hash_table_insert(table, strdup(SDP_C_ADDRESS_TYPE), _value);

                if (line_len > index + black_index + 1) {
                    _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
                    if (!_value)
                        return;
                    _value->need_free = 0;
                    _value->ptr = line + index + black_index + 1;
                    _value->len = line_len - index - black_index - 1;
                    g_hash_table_insert(table, strdup(SDP_C_ADDRESS), _value);
                }
                break;
            default:
                break;

        }
        black_num++;
        index += black_index + 1;
    }
    
    return;
}

static void dissect_sdp_time(const uint8_t *line, int line_len, GHashTable *table)
{
    int black_index;
    struct header_value *_value;

    black_index = find_blank_space(line, line_len);
    if (black_index <= 0) {
        return;
    }
    _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
    if (!_value)
        return;
    _value->need_free = 0;    
    _value->ptr = line;
    _value->len = black_index;

    g_hash_table_insert(table, strdup(SDP_T_TIME_START), _value);

    if (line_len > black_index + 1) {
        _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
        if (!_value)
            return;
        _value->need_free = 0;
        _value->ptr = line + black_index + 1;
        _value->len = line_len - black_index - 1;
        g_hash_table_insert(table, strdup(SDP_T_TIME_END), _value);
    }

    return;    
}

static void find_media_payload(const uint8_t *start, int len, char *result, uint16_t *len_ptr)
{
    int black_index;
    int offset = 0;
    char num_str[32];
    int copy_len;
    int num;
    int _len = 0;
    
    while (offset < len) {
        black_index = find_blank_space(start + offset, len - offset);
        if (black_index <= 0) {
            break;
        }
        copy_len = black_index >= 32 ? 31 : black_index;
        strncpy(num_str, (const char *)start + offset, copy_len);
        num_str[copy_len] = 0;

        num = atoi(num_str);
        if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
            _len += snprintf(result + _len, MEDIA_PAYLOAD_LEN_MAX - _len, "%s;", rtp_payload_type_vals[num].strptr);
        }
        offset += black_index + 1;
    }

    if (offset < len) {
        copy_len = len - offset >= 32 ? 31 : len - offset;
        strncpy(num_str, (const char *)start + offset, copy_len);
        num_str[copy_len] = 0;

        num = atoi(num_str);
        if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
            _len += snprintf(result + _len, MEDIA_PAYLOAD_LEN_MAX - _len, "%s;", rtp_payload_type_vals[num].strptr);
        }
    }
    *len_ptr = _len;
    
}

static void dissect_sdp_media(const uint8_t *line, int line_len, GHashTable *table)
{
    int black_num = 0;
    int black_index;
    int index = 0;
    struct header_value *_value;
    struct header_tmp_value *_tmp_value;

    while (index < line_len) {
        black_index = find_blank_space(line + index, line_len - index);
        if (black_index <= 0) {
            break;
        }
        _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
        if (!_value)
            return;        
        _value->need_free = 0;
        _value->ptr = line + index;
        _value->len = black_index;

        switch (black_num) {
            case 0:
                g_hash_table_insert(table, strdup(SDP_M_TYPE), _value);
                break;
            case 1:
                g_hash_table_insert(table, strdup(SDP_M_PORT), _value);
                break;
            case 2:
                g_hash_table_insert(table, strdup(SDP_M_PROTO), _value);
                if (line_len > index + black_index + 1) {
                    _tmp_value = (struct header_tmp_value *)dpi_malloc(sizeof(struct header_tmp_value));
                    if (!_tmp_value)
                        return;

                    _tmp_value->need_free = 1;
                    _tmp_value->ptr = dpi_malloc(MEDIA_PAYLOAD_LEN_MAX);
                    if (!_tmp_value->ptr) {
                        dpi_free(_tmp_value);
                        return;
                    }
                    find_media_payload(line + index + black_index + 1, line_len - index - black_index - 1, (char *)_tmp_value->ptr, &_tmp_value->len);

                    /*
                    _value->need_free = 0;
                    _value->ptr = line + index + black_index + 1;
                    _value->len = line_len - index - black_index - 1;
                    */
                    
                    g_hash_table_insert(table, strdup(SDP_M_PAYLOADS), _tmp_value);
                }
                
                return; //return
            default:
                break;
        }
        black_num++;
        index += black_index + 1;
    }
    
    return;
}

int dissect_sdp(const uint8_t *payload, const uint32_t payload_len, GHashTable *table)
{
    uint32_t offset = 0;
    const uint8_t *line;
    int line_len;
    char type;
    char delim;
    
    line = payload;
    while (offset < payload_len) {
        line_len = find_packet_line_end(line, payload_len - offset);
    
        /*
        * Line must contain at least e.g. "v=".
        */    
        if (line_len < 2)
            break;

        type = *line;
        delim = *(line + 1);
        if (delim != '=')
            goto next_line;

        switch (type) {
            case 'v':            
                _add_one_item_to_hash(table, SDP_V_VERSION, line + 2, line_len - 2);
                break;
            case 'o':
                dissect_sdp_owner(line + 2, line_len - 2, table);
                break;
            case 's':                
                _add_one_item_to_hash(table, SDP_S_NAME, line + 2, line_len - 2);
                break;
            case 'i':
                _add_one_item_to_hash(table, SDP_I_INFO, line + 2, line_len - 2);
                break;
            case 'u':
                _add_one_item_to_hash(table, SDP_U_URI, line + 2, line_len - 2);
                break;
            case 'e':
                _add_one_item_to_hash(table, SDP_E_EMAIL, line + 2, line_len - 2);
                break;
            case 'p':
                _add_one_item_to_hash(table, SDP_P_PHONE, line + 2, line_len - 2);
                break;
            case 'c':
                dissect_sdp_connnection_info(line + 2, line_len - 2, table);
                break;
            case 'b':
                _add_one_item_to_hash(table, SDP_B_BANDWIDTHS, line + 2, line_len - 2);
                break;
            case 't':
                dissect_sdp_time(line + 2, line_len - 2, table);
                break;
            case 'r':
                _add_one_item_to_hash(table, SDP_R_REPEATTIME, line + 2, line_len - 2);
                break;
            case 'm':
                dissect_sdp_media(line + 2, line_len - 2, table);
                break;

            //now only one attributes is writing
            case 'a':
                _add_one_item_to_hash(table, SDP_A_ATTRIBUTES, line + 2, line_len - 2);
                return 0;
            
            default:
                break;
        }
next_line:    
        offset += line_len + 2;
        line = &payload[offset];
    }

    return 0;
}


static void dissect_sdp_time2(const uint8_t *line, int line_len, struct sdp_info *info)
{
    int black_index;

    black_index = find_blank_space(line, line_len);
    if (black_index <= 0) {
        return;
    }
    
    _copy_one_item(info->t_time_start, sizeof(info->t_time_start), line, black_index);

    if (line_len > black_index + 1) {
        _copy_one_item(info->t_time_end, sizeof(info->t_time_end), line + black_index + 1, line_len - black_index - 1);
    }

    return;    
}

static void dissect_sdp_connnection_info2(const uint8_t *line, int line_len, struct sdp_info *info)
{
    int black_num = 0;
    int black_index;
    int index = 0;

    while (index < line_len) {
        black_index = find_blank_space(line + index, line_len - index);
        if (black_index <= 0) {
            break;
        }

        switch (black_num) {
            case 0:
                _copy_one_item(info->c_network_type, sizeof(info->c_network_type), line + index, black_index);
                break;
            case 1:
                _copy_one_item(info->c_address_type, sizeof(info->c_address_type), line + index, black_index);

                if (line_len > index + black_index + 1) {
                    _copy_one_item(info->c_address, sizeof(info->c_address), line + index + black_index + 1, line_len - index - black_index - 1);
                }
                break;
            default:
                break;

        }
        black_num++;
        index += black_index + 1;
    }
    return;
}


static void dissect_sdp_owner2(const uint8_t *line, int line_len, struct sdp_info *info)
{
    int black_num = 0;
    int black_index;
    int index = 0;

    while (index < line_len) {
        black_index = find_blank_space(line + index, line_len - index);
        if (black_index <= 0) {
            break;
        }
        switch (black_num) {
            case 0:
                _copy_one_item(info->o_username, sizeof(info->o_username), line + index, black_index);
                break;
            case 1:                
                _copy_one_item(info->o_sessionid, sizeof(info->o_sessionid), line + index, black_index);
                break;
            case 2:
                _copy_one_item(info->o_version, sizeof(info->o_version), line + index, black_index);
                break;
            case 3:
                _copy_one_item(info->o_network_type, sizeof(info->o_network_type), line + index, black_index);
                break;
            case 4:
                _copy_one_item(info->o_address_type, sizeof(info->o_address_type), line + index, black_index);

                if (line_len > index + black_index + 1) {
                    _copy_one_item(info->o_address, sizeof(info->o_address), line + index + black_index + 1, line_len - index - black_index - 1);
                }
                break;
            default:
                break;

        }
        black_num++;
        index += black_index + 1;
    }
    
    return;
}

static void find_media_payload2(const uint8_t *start, int len, char *result, int max_len)
{
    int black_index;
    int offset = 0;
    char num_str[32];
    int copy_len;
    int num;
    int _len = 0;
    
    while (offset < len) {
        black_index = find_blank_space(start + offset, len - offset);
        if (black_index <= 0) {
            break;
        }
        copy_len = black_index >= 32 ? 31 : black_index;
        strncpy(num_str, (const char *)start + offset, copy_len);
        num_str[copy_len] = 0;

        num = atoi(num_str);
        if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
            _len += snprintf(result + _len, max_len - _len, "%s;", rtp_payload_type_vals[num].strptr);
        }
        offset += black_index + 1;
    }

    if (offset < len) {
        copy_len = len - offset >= 32 ? 31 : len - offset;
        strncpy(num_str, (const char *)start + offset, copy_len);
        num_str[copy_len] = 0;

        num = atoi(num_str);
        if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
            _len += snprintf(result + _len, max_len - _len, "%s;", rtp_payload_type_vals[num].strptr);
        }
    }
    
}

static void dissect_sdp_media2(const uint8_t *line, int line_len, struct sdp_m_info *info)
{
    int black_num = 0;
    int black_index;
    int index = 0;

    while (index < line_len) {
        black_index = find_blank_space(line + index, line_len - index);
        if (black_index <= 0) {
            break;
        }

        switch (black_num) {
            case 0:
                _copy_one_item(info->m_type, sizeof(info->m_type), line + index, black_index);
                break;
            case 1:
                _copy_one_item(info->m_port, sizeof(info->m_port), line + index, black_index);
                break;
            case 2:
                _copy_one_item(info->m_proto, sizeof(info->m_proto), line + index, black_index);
                if (line_len > index + black_index + 1) {
                    find_media_payload2(line + index + black_index + 1, line_len - index - black_index - 1, info->m_payloads, sizeof(info->m_payloads));
                }
                
                return; //return
            default:
                break;
        }
        black_num++;
        index += black_index + 1;
    }
    
    return;
}

static void write_sdp_l5_log(struct flow_info *flow, int direction, struct sdp_info *info)
{
//    char __str[64] = {0};
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;
    
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "sdp_l5");

    for(i=0;i<EM_SDP_CONNECTION_INFO_01+1;i++){
        switch(sdp_field_array[i].index){
        case EM_SDP_OWNER:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->o_owner, strlen(info->o_owner));
            break;
        case EM_SDP_ESSION_NAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->s_name, strlen(info->s_name));
            break;
        case EM_SDP_TIME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->t_time, strlen(info->t_time));
            break;
        case EM_SDP_CONNECTION_INFO_00:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->c_info, strlen(info->c_info));
            break;            
        default:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        }
    }
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[0].m_media, strlen(info->m_info[0].m_media));
    for (i = 0; i < SDP_M_ATTR_MAX_NUM; i++) {
        //if (info->m_info[0].a_attributes[i][0] == 0)
        //    break;
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[0].a_attributes[i], strlen(info->m_info[0].a_attributes[i]));
    }
    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[1].m_media, strlen(info->m_info[1].m_media));
    for (i = 0; i < SDP_M_ATTR_MAX_NUM; i++) {
        //if (info->m_info[1].a_attributes[i][0] == 0)
        //    break;
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[1].a_attributes[i], strlen(info->m_info[1].a_attributes[i]));
    }    

    #if 0
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->o_owner, strlen(info->o_owner));
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->s_name, strlen(info->s_name));
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->t_time, strlen(info->t_time));
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->c_info, strlen(info->c_info));
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[0].m_media, strlen(info->m_info[0].m_media));
    for (i = 0; i < SDP_M_ATTR_MAX_NUM; i++) {
        //if (info->m_info[0].a_attributes[i][0] == 0)
        //    break;
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[0].a_attributes[i], strlen(info->m_info[0].a_attributes[i]));
    }
    
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[1].m_media, strlen(info->m_info[1].m_media));
    for (i = 0; i < SDP_M_ATTR_MAX_NUM; i++) {
        //if (info->m_info[1].a_attributes[i][0] == 0)
        //    break;
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->m_info[1].a_attributes[i], strlen(info->m_info[1].a_attributes[i]));
    }    
    #endif

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_SDP_L5;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return;
}

int dissect_sdp_2(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, struct sdp_info *info)
{
    if(!g_config.protocol_switch[PROTOCOL_SDP_L5])
        return 0;

    uint8_t f_in_media = 0;
    uint32_t offset = 0;    
    const uint8_t *line;
    int line_len;
    char type;
    char delim;

    int attr_index = 0;
    int media_index = -1;
    
    line = payload;
    while (offset < payload_len) {
        if (media_index >= SDP_MEDIA_MAX_NUM - 1)
            break;
        line_len = find_packet_line_end(line, payload_len - offset);
    
        /*
        * Line must contain at least e.g. "v=".
        */    
        if (line_len < 2)
            break;

        type = *line;
        delim = *(line + 1);
        if (delim != '=')
            goto next_line;

        switch (type) {
            case 'v':
                _copy_one_item(info->v_version, sizeof(info->v_version), line + 2, line_len - 2);
                break;
            case 'o':
                _copy_one_item(info->o_owner, sizeof(info->o_owner), line + 2, line_len - 2);
                dissect_sdp_owner2(line + 2, line_len - 2, info);
                break;
            case 's':                
                _copy_one_item(info->s_name, sizeof(info->s_name), line + 2, line_len - 2);
                break;
            case 'i':
                if (f_in_media)
                    _copy_one_item(info->m_info[media_index].m_title, sizeof(info->m_info[media_index].m_title), line + 2, line_len - 2);
                else
                    _copy_one_item(info->i_info, sizeof(info->i_info), line + 2, line_len - 2);
                break;
            case 'u':
                _copy_one_item(info->u_uri, sizeof(info->u_uri), line + 2, line_len - 2);
                break;
            case 'e':
                _copy_one_item(info->e_email, sizeof(info->e_email), line + 2, line_len - 2);
                break;
            case 'p':
                _copy_one_item(info->p_phone, sizeof(info->p_phone), line + 2, line_len - 2);
                break;
            case 'c':            
                _copy_one_item(info->c_info, sizeof(info->c_info), line + 2, line_len - 2);
                dissect_sdp_connnection_info2(line + 2, line_len - 2, info);
                break;
            case 'b':
                _copy_one_item(info->b_bandwidths, sizeof(info->b_bandwidths), line + 2, line_len - 2);
                break;
            case 't':                
                _copy_one_item(info->t_time, sizeof(info->t_time), line + 2, line_len - 2);
                dissect_sdp_time2(line + 2, line_len - 2, info);
                break;
            case 'r':
                _copy_one_item(info->r_repeattime, sizeof(info->r_repeattime), line + 2, line_len - 2);
                break;
            case 'm':
                f_in_media = 1;
                media_index++;
                attr_index = 0;
                _copy_one_item(info->m_info[media_index].m_media, sizeof(info->m_info[media_index].m_media), line + 2, line_len - 2);
                dissect_sdp_media2(line + 2, line_len - 2, &info->m_info[media_index]);
                break;

            //now only one attributes is writing
            case 'a':
                if (f_in_media) {
                    if (attr_index >= SDP_M_ATTR_MAX_NUM)
                        break;
                    _copy_one_item(info->m_info[media_index].a_attributes[attr_index], sizeof(info->m_info[media_index].a_attributes[attr_index]), line + 2, line_len - 2);
                    attr_index++;
                }
                else
                    _copy_one_item(info->session_attribute, sizeof(info->session_attribute), line + 2, line_len - 2);
                break;            
            default:
                break;
        }
next_line:    
        offset += line_len + 2;
        line = &payload[offset];
    }
    write_sdp_l5_log(flow, direction, info);

    return 0;
}


static void init_sdp_l5_dissector(void)
{
    dpi_register_proto_schema(sdp_field_array,EM_SDP_MAX,"sdp_l5");
}


static __attribute((constructor)) void    before_init_sdp_l5(void){
    register_tbl_array(TBL_LOG_SDP_L5, 0, "sdp_l5", init_sdp_l5_dissector);
}


