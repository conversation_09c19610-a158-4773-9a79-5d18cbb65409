/****************************************************************************************
 * 文 件 名 : dpi_sdt_match.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh             2021/09/17
编码: liugh             2021/09/17
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef _DPI_SDT_MATCH_H_
#define _DPI_SDT_MATCH_H_

#include <netinet/in.h>
#include <arpa/inet.h>

#include <rte_mempool.h>
#include <libsdt/libsdt_interface.h>

#if 0

enum _ip_index_e{
    ENUM_IP_VERSION,
    ENUM_IP_LEN,
    ENUM_IP_STREAM0,
    ENUM_IP_STREAM1,

    ENUM_IP_MAX
};

enum _udp_index_e{
    ENUM_UDP_PAYLOAD,
    ENUM_UDP_PAYLOAD_LEN,
    ENUM_UDP_STREAM0,
    ENUM_UDP_STREAM1,

    ENUM_UDP_MAX
};

enum _tcp_index_e{
    ENUM_TCP_PAYLOAD,
    ENUM_TCP_PAYLOAD_LEN,
    ENUM_TCP_FLAGS,
    ENUM_TCP_WINDOWSIZE,
    ENUM_TCP_STREAM0,
    ENUM_TCP_STREAM1,

    ENUM_TCP_MAX
};
#endif

enum _list_node{
    EM_LIST_NODE = 0,
    EM_LIST_SNODE,
    EM_LIST_TNODE
};

#define MATCH_ENGINE_MAX_NUM (MAX_FLOW_THREAD_NUM + TBL_RING_MAX_NUM)

/** 匹配状态统计信息 */
typedef struct SdxMatchProcessStatus_
{
    uint32_t    packet_on_rule_hit;             // 总命中数
    uint32_t    proto_hit_cnt[PROTOCOL_MAX];    // 按协议统计命中数量

} SdxMatchProcessStatus;

/** 匹配线程数据 */
typedef struct SdxMatchThreadCtx_
{
    int         ring_id;                        // 从0开始
    int         engine_id;                      // dissector_thread_num + ring_id
    uint8_t     core_id;                        // 线程绑定核心
    SdtEngine   *app_engine;                    // 匹配引擎

} SdxMatchThreadCtx;

typedef int (*fun_write_proto_log)(struct flow_info *flow, int direction, const void *field_info, SdtMatchResult *match_result, ProtoRecord *pRec);
typedef int (*fun_write_trans_log)(struct flow_info *flow, const void *field_info, ProtoRecord *pRec, void *tbl);

extern int sdt_match_thfunc_signal;
extern struct rte_ring *app_match_ring[APP_PROTOCOL_RING_MAX_NUM];
extern SdxMatchProcessStatus  sdx_match_process_status[MATCH_ENGINE_MAX_NUM];

void sdx_match_status_add(int engine_id, int proto_id, int value);
void sdx_match_status_clean();

int  sdt_match_start(void);
void sdt_match_stop(void);

int   init_app_match_ring(void);

int
dpi_sdt_engine_match_TCPIP(struct flow_info    *flow,
                           const struct pkt_info  *pkt,
                           int direction,
                           ProtoRecord * pRec,
                           int (*fun_write_proto_log)(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result),
                           void *field_info);


int
dpi_sdt_engine_proto_match_rules(struct flow_info    *flow,
                                 const struct pkt_info  *pkt,
                                 SdxMatchThreadCtx      *match_ctx,
                                 ProtoRecord * pRec,
                                 uint8_t (*fun_write_proto_log)(void *log),
                                 void *log,
                                 uint8_t *enqueue_flag);

int
dpi_sdt_flow_link_match(struct flow_info *flow);


int
dpi_sdt_flow_timeout(struct flow_info *flow);

int
dpi_sdt_engine_match_TCPIP_with_more_acl(struct flow_info    *flow,
                                  const struct pkt_info  *pkt,
                                  ProtoRecord * pRec,
                                  fun_write_proto_log write_log,
                                  const void *field_info);

int dpi_multi_mode_trans_match(const struct pkt_info *pkt, ProtoRecord *pRec, fun_write_trans_log write_log);


void sdt_statistic_packet_match_result(SdtMatchResult *result, struct flow_info *flow, struct pkt_info *pkt);
void sdt_statistic_session_match_result(SdtMatchResult *result, struct flow_info *flow, struct pkt_info *pkt);
void sdt_statistic_datalink_match_result(SdtMatchResult *result, sdt_out_status *out_status, uint32_t thread_id, struct pkt_info *pkt);

#ifdef __cplusplus
extern "C" {
#endif

int write_ip_log (struct flow_info *flow, void *pkt, ProtoRecord *prec, void *log_ptr);
int write_tcp_log(struct flow_info *flow, void *pkt, ProtoRecord *prec, void *log_ptr);
int write_udp_log(struct flow_info *flow, void *pkt, ProtoRecord *prec, void *log_ptr);
//int write_sctp_log(struct flow_info *flow, struct pkt_info *pkt, ProtoRecord *prec, struct tbl_log *log_ptr);

#ifdef __cplusplus
}
#endif


#endif

