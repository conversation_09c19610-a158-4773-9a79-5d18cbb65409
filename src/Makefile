ifeq ($(RTE_SDK),)
$(error "Please define RTE_SDK environment variable")
endif

# Default target, can be overridden by command line or environment
RTE_TARGET ?= x86_64-native-linuxapp-gcc

include $(RTE_SDK)/mk/rte.vars.mk

# binary name
APP = ../../run/yaDpiSdt_$(shell git describe --abbrev=0 --tags)
#TRAILER = libtrailer.a
PC_PATH=/usr/local/lib64/pkgconfig

# project version
PROJECT_VERSION = 0.1.30

# all source are stored in SRCS-y
SRCS-y := charsets.c          dpi_ipip.c            dpi_smtp_2.c              \
    dpi_arp.c                 dpi_jl_trailer.c      dpi_snmp_2.c              \
    dpi_ber_ori.c             dpi_l2tp.c            dpi_socket.c              \
    dpi_bgp.c                 dpi_log.c             dpi_socks.c               \
    dpi_bits.c                dpi_mac_pheader.c     dpi_ssh.c                 \
    dpi_b_tree.c              dpi_main.c            dpi_ssl.c                 \
    dpi_capture.c             dpi_mobile_log.c      dpi_statistics.c          \
    dpi_cdp.c                 dpi_modbus.c          dpi_syslog.c              \
    dpi_common.c              dpi_mysql.c           dpi_tbl_log.c             \
    dpi_conversation.c        dpi_notify.c          dpi_tcp_reassemble.c      \
    dpi_cwmp.c                dpi_ocsp.c            dpi_tds.c                 \
    dpi_detect.c              dpi_per.c             dpi_teamviewer.c          \
    dpi_dns.c                 dpi_pgsql.c           dpi_telnet.c              \
    dpi_err_pcap_dump.c       dpi_plugin_example.c  dpi_tftp.c                \
    dpi_fl_trailer.c          dpi_pop_2.c           dpi_thread_timer.c        \
    dpi_forward.c             dpi_ppp.c             dpi_tns.c                 \
    dpi_forward_collect.c     dpi_proto_ids.c       dpi_trailer.c             \
                              dpi_rdp.c             dpi_tunnel.c              \
    dpi_ftp.c                 dpi_rt_trailer.c      dpi_utils.c               \
    dpi_gre.c                 dpi_s7comm.c          dpi_vnc.c                 \
    dpi_gtp_control.c         dpi_sctp.c            dpi_vrrp.c                \
    dpi_gtp_u.c               dpi_sdp.c             dpi_vtysh.c               \
    dpi_gtpv2_control.c       dpi_sdt_ipp.c         dpi_write_trailer.c       \
    dpi_http.c                dpi_sdt_ip_udp_tcp.c  dpi_x509.c                \
    dpi_hw_default_trailer.c  dpi_sdt_link.c        ip2region.c               \
    dpi_hw_yn_trailer.c       dpi_sdt_match.c       post.c                    \
    dpi_hz_trailer.c          dpi_sdx_common.c      sdt_action_out.c          \
    dpi_icmp.c                dpi_share_header.c    sdtapp_interface.c        \
    dpi_imap_2.c              dpi_smb.c             dpi_high_app_protos.c     \
    dpi_http_high_proto.c

#SRCS-y := *.c
#CFLAGS += -fsanitize=address
CFLAGS += -std=c11 -g -D_GNU_SOURCE -pthread -I$(SRCDIR)/. -I$(SRCDIR)/../include/ -I/usr/include/glib-2.0/ -I/usr/lib64/glib-2.0/include
CFLAGS += $(WERROR_FLAGS)   -Wno-unused-parameter -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable -Wno-cast-qual
CFLAGS += $(DPI_DPDK_CFLAGS)
CFLAGS += $(LDFLAGS)

# add option to protect stack from smashing.
#CFLAGS += -fstack-protector-all
#CFLAGS += -fsanitize=address -fno-omit-frame-pointer -static-libasan

# define PROJECT_VERSION_STR macro for dpi_main.o
dpi_main.o: GIT_TAG       := $(shell git describe --abbrev=0 --tags)
dpi_main.o: GIT_COMMITID  := $(shell git log -1 --pretty=format:%h)
dpi_main.o: GIT_BRANCH    := $(shell git rev-parse --abbrev-ref HEAD)
dpi_main.o: GIT_DATA      := $(shell date +%Y_%m_%d)
dpi_main.o: BUILD_STR     := $(GIT_TAG)_$(GIT_COMMITID)_$(GIT_BRANCH)_$(GIT_DATA)
dpi_main.o: CFLAGS += -DPROJECT_VERSION_STR=\"$(BUILD_STR)\"

LDLIBS += -L$(SRCDIR)/../lib -L$(SRCDIR)/PostParse/zlib
LDLIBS += -lpthread  -liniparser -lglib-2.0 -lssl -lcrypto -lmaxminddb -lz -ldl -lyasdt -lsdt-acl -lstdc++ -lpcre -lmongoose
LDLIBS += -ljsoncpp -lcjson -lhs -lsdx_rdkafka_producer_consumer -lrdkafka -lmicroxml
LDLIBS += -lyaSdxWatch -lcurl -ltcp_rsm
LDLIBS += $(shell export PKG_CONFIG_PATH=$(PC_PATH); pkg-config --static --libs libyv_sub)
#LDLIBS += -lasan

include $(RTE_SDK)/mk/rte.extapp.mk
#include $(SRCDIR)/libmodule.mk
