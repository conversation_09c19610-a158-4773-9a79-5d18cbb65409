#include <rte_mbuf.h>
#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_common.h"
#include "dpi_proto_ids.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

enum teamviewer_index_em{
	EM_TV_LENGTH,
	EM_TV_TYPE,
	EM_TV_MAX,
};

static dpi_field_table teamviewer_field_array[] = {
	DPI_FIELD_D(EM_TV_LENGTH,	YA_FT_INT32,		"length"),
	DPI_FIELD_D(EM_TV_TYPE, 	YA_FT_STRING,		"type"),
};

static int dissect_teamviewer_tcp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
	if(payload_len < 5)
		return PKT_DROP;

	if(payload[0] != 0x11 && payload[0] != 0x17)
		return PKT_DROP;
	
	int idx = 0;
	struct tbl_log *log_ptr;

	if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
		DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
		return PKT_DROP;
	}
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "teamviewer");

	write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload_len);
	if(payload[0] == 0x17)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "key exchange", 12);
	else
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "encrypted data", 14);


	log_ptr->log_type = TBL_LOG_TEAMVIEWER;
	log_ptr->log_len  = idx;

	if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
		rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
	return PKT_OK;
}

static void identify_teamviewer_tcp(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len)
{
	if(g_config.protocol_switch[PROTOCOL_TEAMVIEWER] == 0 || payload_len < 24)
		return;

	if(ntohs(flow->tuple.inner.port_dst) != 5938 && ntohs(flow->tuple.inner.port_src) != 5938)
		return;

	if(payload[0] == 0x11 && get_uint16_t(payload, 4) + 24 == payload_len)
		flow->real_protocol_id = PROTOCOL_TEAMVIEWER;

	if(payload[0] == 0x17 && get_uint16_t(payload, 3) + 5 == payload_len)
		flow->real_protocol_id = PROTOCOL_TEAMVIEWER;

	return;
}

static void init_teamviewer_dissector(void)
{
    map_fields_info_register(teamviewer_field_array, PROTOCOL_TEAMVIEWER, EM_TV_MAX, "teamviewer");
	dpi_register_proto_schema(teamviewer_field_array, EM_TV_MAX, "teamviewer");
	port_add_proto_head(IPPROTO_TCP, 5938, PROTOCOL_TEAMVIEWER);
	port_add_proto_head(IPPROTO_UDP, 5938, PROTOCOL_TEAMVIEWER);

	udp_detection_array[PROTOCOL_TEAMVIEWER].proto = PROTOCOL_TEAMVIEWER;
	udp_detection_array[PROTOCOL_TEAMVIEWER].identify_func = identify_teamviewer_tcp;
	udp_detection_array[PROTOCOL_TEAMVIEWER].dissect_func  = dissect_teamviewer_tcp;
	
	tcp_detection_array[PROTOCOL_TEAMVIEWER].proto = PROTOCOL_TEAMVIEWER;
	tcp_detection_array[PROTOCOL_TEAMVIEWER].identify_func = identify_teamviewer_tcp;
	tcp_detection_array[PROTOCOL_TEAMVIEWER].dissect_func  = dissect_teamviewer_tcp;

	DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TEAMVIEWER].excluded_protocol_bitmask, PROTOCOL_TEAMVIEWER);
}

static __attribute((constructor)) void before_init_teamviewer(void){
	register_tbl_array(TBL_LOG_TEAMVIEWER, 0, "teamviewer", init_teamviewer_dissector);
}
