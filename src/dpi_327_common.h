#ifndef _DPI_327_COMMON_H_
#define _DPI_327_COMMON_H_

#include <yaProtoRecord/precord.h>

enum _p327_header_index_e{
    ENUM_P327_RULESYSID,
    ENUM_P327_PORTGROUPID,
    ENUM_P327_LINKDIRECTION,
    ENUM_P327_OPRATOR,
    ENUM_P327_DATATYPE,
    ENUM_P327_HASH,
    ENUM_P327_216RULEID,
    ENUM_P327_RULEID,
    ENUM_P327_FROMPORT,
    ENUM_P327_FROMDEV,

    ENUM_P327_MAX,
};

struct p327_header
{
    uint32_t P327_ruleSysID;
    uint8_t P327_portGroupID;
    uint8_t P327_linkDirection;
    uint8_t P327_oprator;
    uint8_t P327_dataType;
    uint16_t P327_hash;
    uint8_t P327_216ruleID;
    uint8_t P327_fromDev;
    uint16_t P327_ruleID;
    uint16_t P327_fromPort;
};


void dpi_327_register_common(pschema_t* schema);

int dissect_mac_to_p327_header(struct p327_header *p327_header, const uint8_t *payload, int payload_len);

int write_tbl_log_p327_header(precord_t* log_content, int *idx, int log_len_max, struct p327_header *p327_header);
#endif
