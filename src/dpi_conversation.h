#ifndef _DPI_CONVERSATION_H_
#define _DPI_CONVERSATION_H_

#include "dpi_common.h"
#include "dpi_detect.h"
#include <yaProtoRecord/precord_schema.h>
#include <yaProtoRecord/precord_layer.h>

#include <stdint.h>
#include <stddef.h>
#include <arpa/inet.h>
#include <glib.h>


#define NO_ADDR_B 0x01
#define NO_PORT_B 0x02

#define CONVERSATION_TIMEOUT 30
#define CONVERSATION_FTPCONTROL_TIMEOUT 10


#define COMMON_BIG_SIZE          100*1024

struct flow_info;

struct conversation_tuple
{
    uint8_t proto;
    uint16_t port_src;
    uint16_t port_dst;
    union {
        uint32_t ip4;
        uint8_t ip6[16];
    } ip_src;
    union {
        uint32_t ip4;
        uint8_t ip6[16];
    } ip_dst;
} PACK_OFF;

struct conversation_value
{
    uint16_t protocol;
    uint32_t createtime;
    void *conv_session;  // add by liugh
};


struct ftp_port_filename
{
  uint16_t dataPort;                     // 文件数据传输接口
  char     storpath[COMMON_FILE_PATH];   /* ftp传输文件名 */
  char     trans_cmd[COMMON_FILE_PATH];  // 文件传输发起的命令
};

#define MAX_FTP_PORT_FILENAME_NUM 50
struct ftp_session
{

      //ftp-control
    char username[COMMON_NAME_PASSWD_LEN];
    char password[COMMON_NAME_PASSWD_LEN];

    char filetype[COMMON_SOME_TYPE];
    char storpath[COMMON_FILE_PATH]; /* ftp传输文件名 */
    char filepath[COMMON_FILE_PATH]; /* 落盘文件名 */
    char login_status[COMMON_STATUS_LEN];
    char software[64];
    char hostName[64];
    //文件传输流相关
    union {
        uint32_t ip4;
        uint8_t  ip6[16];
    } control_ip_server;
    union {
        uint32_t ip4;
        uint8_t  ip6[16];
    } ftp_data_ip;
    uint8_t control_ipVer;
    uint8_t ftp_data_ipVer;

    uint16_t dataPort;  //文件数据传输接口
    struct   conversation_tuple control_conv_tuple;  //ftp_control 自身的tpule


   //文件还原相关
    int      offset;                       //文件传输起始偏移位置
    char     con_type[8];                  //传输文件类型
    uint32_t total_len;
    uint32_t real_len;


    /*请求命令，请求参数，响应代码，响应参数，流中每个都要保存，用逗号拼接*/
    char reqCmd[8192];//请求命令
    char reqArg[8192];//请求参数
    char resCode[8192];//响应代码
    char resArg[8192];//响应参数
    char trans_cmd[1024];
    uint8_t mode;  //FTP主被动模式，主动模式填0，被动模式填1
    uint16_t file_num;  //还原文件数
    precord_t * record;
    uint64_t creatime;
    FILE* data_fp;

    uint64_t stream_file_id;         //JZ用于关联组报中的实体文件,从flow中获取
    struct ftp_port_filename proto_filename[MAX_FTP_PORT_FILENAME_NUM];//一条流暂时只支持10个文件

    int filesize;
    char file_flag;

    uint16_t port_src;
    uint16_t port_dst;
    uint32_t ftp_ip;
    uint16_t ftp_port;
    struct flow_info *flow;
};


/* conversation for sip rtp add by liugh*/
struct sip_session
{
    char     filepath[COMMON_FILE_PATH];
    uint16_t port_src;
    uint16_t port_dst;
    uint32_t ip_src;
    uint32_t ip_dst;
    uint8_t  sip_flag;   //当解析sip报文收集齐rtp信息则不写sip数据
    
};


/* conversation for tftp add by liugh*/
struct tftp_session
{
    char     opt_str[COMMON_SOME_TYPE];
    char     transmode[COMMON_SOME_TYPE];
    char     filename[COMMON_FILE_NAME];
    char     filepath[COMMON_FILE_PATH];
    char     filetype[COMMON_SOME_TYPE];
    uint16_t blocksize;
    int      timeout;
    int      filesize;
    uint64_t now_time_usec;

    uint8_t  convert;
    char     conv_c;         /* convert support data */
    int      last_block;
    uint8_t  lost;

    uint16_t port_src;
    uint16_t port_dst;
};



#define RDP_MAX_FLOW_PER_CLIENT 100
struct rdp_conv
{
    int     count;
    int     num;    
    uint8_t state[RDP_MAX_FLOW_PER_CLIENT]; 
};

struct conversation_value * find_conversation(struct conversation_tuple *tuple, uint8_t options);
struct conversation_value *find_or_create_conversation(struct conversation_tuple *tuple, uint8_t options, uint16_t protocol, void *session);
void clean_conversation_hash(void);
void timeout_conversation_hash(void);
void init_conversation(void);

//文件io线程
enum conv_status{
  CONV_DO_INSERT = 0x01,
  CONV_DO_UPDATE,
};
typedef struct thread_conversation_info {
    uint16_t real_protocol_id;  //通过协议id调取相应的超时函数
    uint64_t last_update_time;
    enum conv_status status;
    int      conv_thread_num;
    gpointer key;
    uint8_t *value;
} thread_conv_info;

int  conversation_thread_func(void *arg);
void init_flow_conv(void);
//构造好conv_info，送入队列
void dpi_conv_insert_key_value(struct flow_info *flow, char *key,int key_len, uint8_t *value,int value_len,enum conv_status status);
#endif
