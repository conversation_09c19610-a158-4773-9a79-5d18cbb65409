#include "dpi_trailer.h"

//static char* RT_NSP[] = {"CTCC", "CMCC", "CUCC", "unknow"};
//static char* HP_NSP[] = {"unknow", "CMCC", "CUCC", "CTCC"}; 


void get_trailer_type(const char* arg, Trailer_Type *trailer_type, Net_Type *net_type, size_t *trailer_len)
{
	if(arg == NULL)
		return;

	if(!strcmp(arg, "NONE")){
		*trailer_type = INVALID_TYPE;
		*net_type     = INVALID_NET;
	}
	else if(!strcmp(arg, "F:HW:SH:MAC")){
		*trailer_type = HW_SH_MAC;
		*net_type     = FIXED_NET;
	}
	else if(!strcmp(arg, "F:HW:SH:VLAN")){
		*trailer_type = HW_SH_VLAN;
		*net_type     = FIXED_NET;
	}
	else if(!strcmp(arg, "F:HY:FS")){
		*trailer_type = HY_FS;
		*net_type     = FIXED_NET;
	}
	else if(!strcmp(arg, "F:PH")){
		*trailer_type = PH_DEFAULT;
		*net_type     = FIXED_NET;
	}
    else if(!strcmp(arg, "F:RT6402")){
		*trailer_type = RT_6402;
		*net_type     = FIXED_NET;
	}
    else if(!strcmp(arg, "F:RT9800")){
		*trailer_type = RT_9800;
		*net_type     = FIXED_NET;
	}
    else if(!strcmp(arg, "470")){
		*trailer_type = LINE_470;
		*net_type     = FIXED_NET;
    }
	else if(!strcmp(arg, "M:RT6402")){
		*trailer_type = RT_6402;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct rt_trailer);
	}
	else if(!strcmp(arg, "M:RT9800")){
		*trailer_type = RT_9800;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct rt_trailer);
	}
    else if(!strcmp(arg, "M:RTSINO")){
		*trailer_type = RT_SINO;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct rt_trailer);
	}
	else if(!strcmp(arg, "M:JL")){
		*trailer_type = JL_DEFAULT;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct jl_trailer);
	}
	else if(!strcmp(arg, "M:FL")){
		*trailer_type = FL_DEFAULT;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct fl_trailer);
	}
	else if(!strcmp(arg, "M:HZ:SOFT")){
		*trailer_type = HZ_SOFT;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct hz_trailer);
	}
	else if(!strcmp(arg, "M:HW:DB")){
		*trailer_type = HW_DB;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct hw_default_trailer);
	}
	else if(!strcmp(arg, "M:HW:YN")){
		*trailer_type = HW_YN;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct hw_yn_trailer);
	}
	else if(!strcmp(arg, "M:HW:DEFAULT")){
		*trailer_type = HW_DEFAULT;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct hw_default_trailer);
	}
	else if(!strcmp(arg, "M:JSD")){
		*trailer_type = BJ_JSD;
		*net_type     = MOBILE_NET;
		*trailer_len  = sizeof(struct hw_default_trailer);
	}
	else{
		*trailer_type = INVALID_TYPE;
		*net_type     = INVALID_NET;
	}

	return;
}

void get_eth_info(const uint8_t *payload, uint32_t payload_len, struct data_link_layer_t *data_link_layer, Trailer_Type type)
{
	switch(type){
		case HW_SH_MAC:
		case PH_DEFAULT:
			data_link_layer->nsp  = (payload[4] >> 5) & 0x03;
			break;
		case RT_6402:
		case RT_9800:
			data_link_layer->nsp  = (payload[6] >> 6) & 0x03;
			break;
		case HW_YN:
			data_link_layer->base = payload[7];
			break;
		case HW_SH_VLAN:
			if(payload_len > 16 && get_uint16_t(payload, 12) == 0x8100){
				data_link_layer->vlan_id[0] = get_uint16_t(payload, 14) & 0x0fff;
			}
			break;
		case HY_FS:
			data_link_layer->inter_id = get_uint16_ntohs(payload, 10);
			break;
		case LINE_470:
			data_link_layer->line_id = get_uint64_t(payload, 6);
			break;
		default:
			break;
	}
	return;
}


int parse_trailer(void** pTrailer, const uint8_t *payload, uint16_t payload_len, Trailer_Type type, size_t len)
{
	if (!payload || !payload_len || INVALID_TYPE == type)
		return -1; 


	if (NULL == *pTrailer){
		*pTrailer = malloc(len);
		if(NULL == *pTrailer)
			return -1; 
	}
	bzero(*pTrailer, len);

	switch (type)
	{
		case RT_6402:
		case RT_9800:
			parse_rt_trailer(*pTrailer, payload, payload_len);
			break;
        case RT_SINO:
            parse_rt_sino_trailer(*pTrailer, payload, payload_len);
            break;
		case JL_DEFAULT:
			parse_jl_trailer(*pTrailer, payload, payload_len);
			break;
		case HW_DEFAULT:
		case HW_DB:
        case BJ_JSD:
			parse_hw_default_trailer(*pTrailer, payload, payload_len, type == BJ_JSD);
			break;
		case HW_YN:
			parse_hw_yn_trailer(*pTrailer, payload, payload_len);
			break;
		case HZ_SOFT:
			parse_hz_soft_trailer(*pTrailer, payload, payload_len);
			break;
        case FL_DEFAULT:
			parse_fl_trailer(*pTrailer, payload, payload_len);
			break;
		default:
			break;
	}
	return 0;
}

void destroy_trailer(void** pTrailer)
{
    if (pTrailer && *pTrailer)
    {   
        free(*pTrailer);
        *pTrailer = NULL;
    }   
    return;
}

