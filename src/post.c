/****************************************************************************************
 * 文 件 名 : post.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.4
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : licl      '2018-05-20
* 编    码 : licl      '2018-06-06
* 修    改 : licl      '2018-06-26

相关环境变量配置:
export YV_POST_LIB_ENV_LOG_LEVEL=1              #日志输出开关：0->关, 1->开                                                   默认:关闭Debug
export YV_POST_LIB_ENV_MIN_DATA=20              #对于乱码中，当存在大于此数值的连续可见字符，才输出                           默认:20个字符
export YV_POST_LIB_ENV_PRINT_HEX=0              #乱码的输出格式: 0-> 输出连续的可见字符，1->将乱码以16进制输出                默认:输出最长的连续可见字符
export YV_POST_LIB_ENV_ERR_DIR="DIR"            #错误POST TBL存储路径, 关闭此功能方法:export -n YV_POST_LIB_ENV_ERR_DIR       默认:不收集错误文件
export YV_POST_LIB_ENV_OPT_FREFIX="优化输出:"   #乱码优化输出的前缀,   关闭此功能方法:export -n YV_POST_LIB_ENV_OPT_FREFIX    默认:不带前缀
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <iconv.h>
#include <sys/time.h>
#include <time.h>
#include <arpa/inet.h>

#include "zlib/zlib.h"
#include "post.h"

#define OUTPUT_BUFFER_MAX_LEN  1024*1024  //程序的缓存区使用了栈上空间， 建议栈上空间设置100M  ulimit  -s 102400
#define YV_POST_MAGIC_DODE "YVyv"
#define MIN_POST_DATA_LEN 9
#define MAX_FILE_NAME_LEN 2048
/* 各种压缩格式 magic_code */
static char    g_GZIP_Magic[]           = {0x1F, 0x8B};
static char    g_Zlib_Magic1[]          = {0x78, 0x01};
static char    g_Zlib_Magic2[]          = {0x78, 0x5E};
static char    g_Zlib_Magic3[]          = {0x78, 0x9C};
static char    g_Zlib_Magic4[]          = {0x78, 0xDA};
static int     g_LogLevel               = 0;
static int     g_PrintHEX               = 0;
static int     g_MIN_PrintableData      = 20;
static char   *g_ERR_POST_Info_Stroage  = NULL;
static const char   *g_OptPrefix              = "";
static int copy_file(char*, char*);
const char *stristr (const char*, const char*);
const char *Post_basename(char const *);
int DeleteSpace(char*, int, char*, int*);
int printable(const char *pStr, int len, int num);
int strnicmp (const char*, const char*, size_t);
static int  YV_UrlDecode(const char *, int, char*, int);
int AllInline(char *pInPut, int lInPutLen, char *pOutBuffer, int lOutBufferLen);
int ZlibDecompress(Byte *zdata, uLong nzdata, Byte *data, uLong ndata);
int YV_HttpPostParse_Parse(hParsePost phParsePost, ST_PostInfo* pstPostInfo, char *OutBuffer, unsigned int OutBufferSize);
int YV_HttpPostParse_ParseRAW(ST_PostInfo* pstPostInfo, const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize);
int YV_ProcessPostFormat(const char *pRAWRecoder, unsigned int lPostInfoLength);
int charset_convert(const char *from_charset, const char *to_charset, char *in_buf, size_t in_left, char *out_buf, size_t out_left);
int isUTF8(const char *pData, int len);
int isGBK(const char *pData, int len);
int YV_PostContentIsPrint(const char* pPostData, int Datalength);
const char *GetMaxLenPrintableStr(const char *pStr, int *pLen/*in , out*/);
int PrivateProto_Is_WeChat_POST(const char *inPut, int inPutLen);
int PrivateProtoParse_WeChat_POST(const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize);
int GzipDecompress(Byte *zdata, uLong nzdata, Byte *data, uLong ndata);
int YV_BinToHex(const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize, int Flags);
int CharacterSetConvert(char* pPostData, int Datalength, char *pCharsetConvertBuffer, int lCharsetConvertBufferSize);
int ConvertConfusionCode(const char *in_buf, size_t in_len, char *out_buf, size_t buf_size);

/*****************************************************************
*Struct      :ST_HttpPostPareHandle
*Description :POST解析库的handle
*            MAX_FILE_NAME_LEN: 文件长度限制
*            PostFilename     : POST文件路径
*            pMmapBuffer      : POST文件Buffer
*            lMmapBufferLen   : POST文件长度
*****************************************************************/
typedef struct st_HttpPostPareHandle
{
    char                PostFilename[MAX_FILE_NAME_LEN];
    char               *pMmapBuffer;
    unsigned long long  lMmapBufferLen;
    long long int       lFirstOffset;
} ST_HttpPostPareHandle;


/*****************************************************************
*Function    :YV_HttpPostParse_SetLogLevel
*Description :设置debug日志开关
*Input       :数字0 代表关闭, 1代表打开
*Output      :void
*Return      :void
*Others      :none
*****************************************************************/
static void YV_HttpPostParse_SetLogLevel(int LogLevel)
{
    g_LogLevel = LogLevel;
}

/*****************************************************************
*Function    :YV_HttpPostParse_WriteLog
*Description :将日志输出到屏幕
*Input       :fmt
*Output      :void
*Return      :void
*Others      :none
*****************************************************************/
static void YV_HttpPostParse_WriteLog(const char *format, ...)
{
    switch(g_LogLevel)
    {
        case 0:
            /* 关闭日志输出 */
            break;

        case 1:
            /* 日志输出 STDERR */
            printf("%s\n",format);
            break;

        case 2:
            /* 日志输出文件中*/
            break;

        default:
            break;
    }
}

/*****************************************************************
*Function    :Post_basename
*Description :返回文件的bansename
*Input       :文件路径
*Output      :none
*Return      :新的字符串
*Others      :返回的字符串是从堆内存分配的，需要被释放
*****************************************************************/
const char *Post_basename(char const *path)
{
    if(NULL == path)
    {
        return NULL;
    }
    char *s = strrchr(path, '/');
    if (!s)
    {
        return path;
    }
    else
    {
        return s + 1;
    }
}

/*****************************************************************
*Function    :mkdirs
*Description :递归创建目录
*Input       :想要创建的目录
*Output      :
*Return      :void
*Others      :none
*****************************************************************/
static void post_mkdirs(const char *dir)
{
    char tmp[2048];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp),"%s",dir);
    len = strlen(tmp);
    if(tmp[len - 1] == '/')
        tmp[len - 1] = 0;
    for(p = tmp + 1; *p; p++)
        if(*p == '/') {
            *p = 0;
            mkdir(tmp, S_IRWXU);
            *p = '/';
        }
    mkdir(tmp, S_IRWXU);
}

/*****************************************************************
*Function    :copy_file
*Description :复制文件
*Input       :原文件路径， 目标文件路径
*Output      :
*Return      : 0 -> OK
*Others      :none
*****************************************************************/
static int copy_file(char *old_filename, char  *new_filename)
{
    FILE  *ptr_old = NULL;
    FILE  *ptr_new = NULL;
    int  aChar;
    int  lRet = 0;

    ptr_old = fopen(old_filename, "rb");
    ptr_new = fopen(new_filename, "wb");

    if(NULL == ptr_old)
    {
        lRet = -1;
        goto OUT;
    }

    if(NULL == ptr_new)
    {
        lRet = -1;
        goto OUT;
    }

    while(1)
    {
        aChar  =  fgetc(ptr_old);
        if(!feof(ptr_old))
            fputc(aChar, ptr_new);
        else
            break;
    }
    lRet= 0;

OUT:

    if(NULL != ptr_new)
    {
        fclose(ptr_new);
        ptr_new = NULL;
    }
    if(NULL != ptr_old)
    {
        fclose(ptr_old);
        ptr_old = NULL;
    }
    return  lRet;
}


/*****************************************************************
*Function    :YV_POST_ERR_Info_Recoder
*Description :收集错误的TBL 与 POST 文件
*Input       :pstHttpPostPareHandle POST解析库的handle
*Input       :pMode                 出错类型，将被附着在文件名尾
*Input       :pERRInfo              出错的详细信息
*Output      :none
*Return      :void
*Others      :none
*****************************************************************/
static void YV_POST_ERR_Info_Recoder(char *pPostFilename, char *pERRInfo)
{
    if(NULL == g_ERR_POST_Info_Stroage || NULL == pPostFilename || NULL == pERRInfo)
    {
        return;
    }

    char szBuffer1[2048];
    char szBuffer2[2048];
    char *pDot = NULL;
    const char *pPOSTFileBaseName = NULL;
    char *pFileNamePrefixWithoutPath = NULL;
    char *pFileNamePrefixWithPath = NULL;
    struct stat File1Stat;
    struct stat File2Stat;

    /* 路径是否可读 */
    if (0 != stat(g_ERR_POST_Info_Stroage, &File1Stat))
    {
       post_mkdirs(g_ERR_POST_Info_Stroage);        /* 创建目录 */
    }
    if (0 != stat(g_ERR_POST_Info_Stroage, &File1Stat))
    {
        return;                                /* 创建失败, 返回吧 */
    }

    /* 获取文件前缀 不带路径 如: helloworld.out -> helloworld */
    pPOSTFileBaseName = Post_basename(pPostFilename);
    if(NULL == pPOSTFileBaseName)
    {
        return; /* 此时可以直接返回 */
    }
    pFileNamePrefixWithoutPath = strdup(pPOSTFileBaseName);
    pDot = strrchr(pFileNamePrefixWithoutPath, '.');
    if(NULL == pDot)
    {
        goto OUT;
    }
    *pDot = 0;

    /* 获取文件前缀 带路径 如: ./abc/helloworld.out  ->  ./abc/helloworld */
    pFileNamePrefixWithPath = strdup(pPostFilename);
    pDot = strrchr(pFileNamePrefixWithPath, '.');
    if(NULL == pDot)
    {
        goto OUT;
    }
    *pDot = 0;

    /* 复制 tbl 文件 */
    snprintf(szBuffer1, sizeof(szBuffer1), "%s.tbl", pFileNamePrefixWithPath);                                /* 原文件路径   */
    snprintf(szBuffer2, sizeof(szBuffer2), "%s/%s.tbl", g_ERR_POST_Info_Stroage, pFileNamePrefixWithoutPath); /* 目标文件路径 */
    YV_HttpPostParse_WriteLog("复制tbl文件 原文件[%s], 目标文件[%s]\n", szBuffer1, szBuffer2);
    if(0 == stat(szBuffer1, &File1Stat) && 0 == stat(szBuffer2, &File2Stat))
    {
        /* 如果文件大小大于0， 且两个文件大小一样, 则跳过 */
        if(File1Stat.st_size > 0 && File2Stat.st_size > 0 && File1Stat.st_size == File2Stat.st_size)
        {
            YV_HttpPostParse_WriteLog("文件已经存在\n");
            return;
        }
    }
    copy_file(szBuffer1, szBuffer2);

    /* 复制 POST 文件 */
    snprintf(szBuffer1, sizeof(szBuffer1), "%s.POST", pFileNamePrefixWithPath);                                /* 原文件路径   */
    snprintf(szBuffer2, sizeof(szBuffer2), "%s/%s.POST", g_ERR_POST_Info_Stroage, pFileNamePrefixWithoutPath); /* 目标文件路径 */
    YV_HttpPostParse_WriteLog("复制POST文件 原文件[%s], 目标文件[%s]\n", szBuffer1, szBuffer2);
    if(0 == stat(szBuffer1, &File1Stat) && 0 == stat(szBuffer2, &File2Stat))
    {
        /* 如果文件大小大于0， 且两个文件大小一样, 则跳过 */
        if(File1Stat.st_size > 0 && File2Stat.st_size > 0 && File1Stat.st_size == File2Stat.st_size)
        {
            YV_HttpPostParse_WriteLog("文件已经存在\n");
            return;
        }
    }

    copy_file(szBuffer1, szBuffer2);

    /* 获取系统时间 */
    time_t CurrentTime = time(NULL);
    char TimeBuf[2048];
    strftime(TimeBuf, sizeof(TimeBuf), "%Y-%m-%d %H:%M:%S  ", localtime(&CurrentTime));
    YV_HttpPostParse_WriteLog("当前系统时间:%s\n", TimeBuf);

    /* 写入 出错信息  */
    snprintf(szBuffer1, sizeof(szBuffer1), "%s/%s.ERR_Info", g_ERR_POST_Info_Stroage, pFileNamePrefixWithoutPath);
    YV_HttpPostParse_WriteLog("错误信息文件[%s]\n", szBuffer1);
    int fdERRInfo = open(szBuffer1, O_APPEND|O_RDWR|O_CREAT, 0644);
    if(fdERRInfo == -1)
    {
        YV_HttpPostParse_WriteLog("创建文件出错[%s]\n", szBuffer1);
        goto OUT;
    }
    snprintf(szBuffer1, sizeof(szBuffer1), " file=%-80s", pPostFilename);
    write(fdERRInfo, TimeBuf,  strlen(TimeBuf));
    write(fdERRInfo, szBuffer1,  strlen(szBuffer1));
    write(fdERRInfo, pERRInfo, strlen(pERRInfo));
    close(fdERRInfo);
    fdERRInfo = 0;

OUT:
    /* 释放内存 */
    if(NULL != pFileNamePrefixWithPath)
    {
        free(pFileNamePrefixWithPath);
        pFileNamePrefixWithPath = NULL;
    }
    if(NULL != pFileNamePrefixWithoutPath)
    {
        free(pFileNamePrefixWithoutPath);
        pFileNamePrefixWithPath = NULL;
    }
}

/*****************************************************************
*Function    :stristr
*Description :忽略字母大小写查找字串是否存在
*Input       :str1 长串, str2 字串
*Output      :
*Return      :返回首次字串位置，NULL代表没有找到
*Others      :none
*****************************************************************/
const char *stristr (const char *str1, const char * str2)
{
    const char *cp = str1;
    const char *s1, *s2;

    if ( !*str2 )
        return str1;

    while (*cp)
    {
        s1 = cp;
        s2 = str2;

        while ( *s1 && *s2 && (!(*s1-*s2)
                    || !(*s1-*s2-32) || !(*s1-*s2+32)) )
        {
            s1++, s2++;
        }
        if (!*s2)
        {
            return(cp);
        }
        cp++;
    }
    return(NULL);
}

/*****************************************************************
*Function    :DeleteSpace
*Description :删除pInPut中的所有空格，输出到pOutBuffer
*Input       :字符首地址，字符长度
*Output      :已删除空格的字符
*Return      :返回已转换字符的个数， 负数代表失败
*Others      :none
*****************************************************************/
int DeleteSpace(char *pInPut, int lInPutLen, char *pOutBuffer, int *plOutBufferLen)
{
    int lReturn;
    int i;
    int InputPos;

    lReturn = 0;
    i = 0;

    YV_HttpPostParse_WriteLog("DelteSpace  参数[pInPut=%p][lInPutLen=%d][pOutBuffer=%p][plOutBufferLen=%p]\n", pInPut, lInPutLen, pOutBuffer, plOutBufferLen);
    if(NULL == pInPut || lInPutLen <= 0 || NULL == pOutBuffer || NULL == plOutBufferLen)
    {
        YV_HttpPostParse_WriteLog("DelteSpace  参数错误[pInPut=%p][lInPutLen=%d][pOutBuffer=%p][plOutBufferLen=%p]\n", pInPut, lInPutLen, pOutBuffer, plOutBufferLen);
        lReturn = -1;
        goto OUT;
    }

    i = 0;
    for(InputPos=0;('\0' != *pInPut)&&(InputPos < lInPutLen);InputPos++)
    {
        if(*pInPut == ' ')
        {
            pInPut++;
        }
        else
        {
            *pOutBuffer++ = *pInPut++;
            i++;
        }
    }

    *plOutBufferLen = i;
    *pOutBuffer++ = '\0';
    lReturn = 0;
OUT:
    return lReturn;
}

/*****************************************************************
*Function    :AllInline
*Description :将一片数据中的 0x00, 0x0d, 0x0a, 替换为空格
*Input       :字符首地址，数据的长度
*Output      :pOutBuffer 已转换的数据
*Return      :返回已处理后字符个数
*Others      :none
*****************************************************************/
int AllInline(char *pInPut, int lInPutLen, char *pOutBuffer, int lOutBufferLen)
{
    int i;

    i = 0;

    if(NULL == pInPut || lInPutLen <= 0 || NULL == pOutBuffer || lOutBufferLen <= 0)
    {
        YV_HttpPostParse_WriteLog("AllInline  参数错误[pInPut=%p][lInPutLen=%d][pOutBuffer=%p][lOutBufferLen=%d]\n", pInPut, lInPutLen, pOutBuffer, lOutBufferLen);
        return -1;
    }
    if(lInPutLen > lOutBufferLen)
    {
        return -1;
    }
    i = 0;
    while(lInPutLen--)
    {
        if( (*pInPut >= 0X00 && *pInPut <= 0X1F) || *pInPut == 0X7F ) /* 这是ASCII 中不显示的部分 */
        {
            pInPut++;
            *pOutBuffer++ = ' ';
            i++;
        }
        else
        {
            //YV_HttpPostParse_WriteLog("pInPut=%c ", *pInPut);
            *pOutBuffer++ = *pInPut++;
            i++;
        }
    }
    *pOutBuffer = '\0';
    return i;
}

/*****************************************************************
*Function    :strnicmp
*Description :忽略字符的大小写 比较
*Input       :两个字符首地址，比较的长度
*Output      :none
*Return      : 0代表相等，其他代表差值
*Others      :none
*****************************************************************/
#define TOUPPER(CH) \
    (((CH) >= 'a' && (CH) <= 'z') ? ((CH) - 'a' + 'A') : (CH))
int strnicmp (const char *s1, const char *s2, size_t n)
{
    const char *s2end = s2 + n;

    while (s2 < s2end && *s2 != 0 && TOUPPER (*s1) == TOUPPER (*s2))
        s1++, s2++;
    if (s2end == s2)
        return 0;
    return (int) (TOUPPER (*s1) - TOUPPER (*s2));
}


/*****************************************************************
*Function    :ZlibDecompress
*Description :解压Zlib格式
*Input       :数据首地址，数据的长度
*Output      :已解压的数据
*Return      :返回解压的长度， -1代表解压失败
*Others      :none
 *****************************************************************/
int ZlibDecompress(Byte *zdata, uLong nzdata, Byte *data, uLong ndata)
{
    int err = 0;
    z_stream d_stream = { 0 }; /* decompression stream */
    static char dummy_head[2] =
    {
        0x8 + 0x7 * 0x10,
        (((0x8 + 0x7 * 0x10) * 0x100 + 30) / 31 * 31) & 0xFF,
    };
    d_stream.zalloc = NULL;
    d_stream.zfree = NULL;
    d_stream.opaque = NULL;
    d_stream.next_in = zdata;
    d_stream.avail_in = 0;
    d_stream.next_out = data;
    if(inflateInit2(&d_stream, 47) != Z_OK)
    {
        return -1;
    }
    int ldatasize = ndata - 1; /* 解决 数据溢出问题 */
    while (d_stream.total_out < (unsigned)ldatasize && d_stream.total_in < nzdata)
    {
        d_stream.avail_in = d_stream.avail_out = 1; /* force small buffers */
        if ((err = inflate(&d_stream, Z_NO_FLUSH)) == Z_STREAM_END)
        {
            break;
        }
        if (err != Z_OK)
        {
            if (err == Z_DATA_ERROR)
            {
                d_stream.next_in = (Bytef*)dummy_head;
                d_stream.avail_in = sizeof(dummy_head);
                if ((err = inflate(&d_stream, Z_NO_FLUSH)) != Z_OK)
                {
                    inflateEnd(&d_stream);
                    return -1;
                }
            }
            else
            {
                inflateEnd(&d_stream);
                return -1;
            }        }
    }
    if (inflateEnd(&d_stream) != Z_OK)
    {
        return -1;
    }
    return d_stream.total_out;
}



/*****************************************************************
*Function    :gzdecompress
*Description :解压 G-zip 格式
*Input       :数据的首地址，数据的长度
*Output      :已解压的数据
*Return      :返回解压的长度， -1代表解压失败
*Others      :none
 *****************************************************************/
/* 解压 G-zip 格式 */
int GzipDecompress(Byte *zdata, uLong nzdata, Byte *data, uLong ndata)
{
    int err = 0;
    z_stream d_stream = { 0 }; /* decompression stream */
    static char dummy_head[2] =
    {
        0x8 + 0x7 * 0x10,
        (((0x8 + 0x7 * 0x10) * 0x100 + 30) / 31 * 31) & 0xFF,
    };
    d_stream.zalloc = NULL;
    d_stream.zfree = NULL;
    d_stream.opaque = NULL;
    d_stream.next_in = zdata;
    d_stream.avail_in = 0;
    d_stream.next_out = data;

    //只有设置为MAX_WBITS + 16才能在解压带header和trailer的文本
    if (inflateInit2(&d_stream, MAX_WBITS + 16) != Z_OK)
    {
        return -1;
    }

    ndata = ndata-1;/* 解决 数据溢出问题 */
    while (d_stream.total_out < ndata && d_stream.total_in < nzdata)
    {
        d_stream.avail_in = d_stream.avail_out = 1; /* force small buffers */
        if ((err = inflate(&d_stream, Z_NO_FLUSH)) == Z_STREAM_END)
        {
            break;
        }
        if (err != Z_OK)
        {
            if (err == Z_DATA_ERROR)
            {
                d_stream.next_in = (Bytef*)dummy_head;
                d_stream.avail_in = sizeof(dummy_head);
                if ((err = inflate(&d_stream, Z_NO_FLUSH)) != Z_OK)
                {
                    inflateEnd(&d_stream);
                    return -1;
                }
            }
            else
            {
                inflateEnd(&d_stream);
                return -1;
            }
        }
    }
    if (inflateEnd(&d_stream) != Z_OK)
    {
        return -1;
    }
    return d_stream.total_out;
}


/*****************************************************************
*Function    :YV_HttpPostParse_Init
*Description :POST解析库的初始化操作
*Input       :文件名，句柄值，日志开关
*Output      :none
*Return      :-1代表失败, 0代表成功
*Others      :none
*****************************************************************/
int YV_HttpPostParse_Init(const char *PostFileName, hParsePost *phParsePost, int LogLevel)
{
    int                    lStatus;
    int                    fdPostFile;
    off_t                  lFileLength;
    char                  *pMmapBuffer;
    char                  *pPOSTLibEnv;
    ST_HttpPostPareHandle *pstHttpPostPareHandle;

    lStatus               = 0;
    fdPostFile            = 0;
    lFileLength           = 0;
    pstHttpPostPareHandle = NULL;
    pPOSTLibEnv           = NULL;
    pMmapBuffer           = NULL;

    YV_HttpPostParse_SetLogLevel(LogLevel);
    YV_HttpPostParse_WriteLog("HELLO LOG!\n");

    YV_HttpPostParse_WriteLog("函数[%s]的初始值检测\n", __func__);
    if(NULL == PostFileName || NULL == phParsePost)
    {
        YV_HttpPostParse_WriteLog("无效的数值:PostFileName=[%p], phParsePost=[%p]\n", PostFileName, phParsePost);
        return -1;
    }

    /* 文件名的长度检测 */
    YV_HttpPostParse_WriteLog("文件名长度检测[name=%s][len=%d]\n", PostFileName, MAX_FILE_NAME_LEN);
    if(strlen(PostFileName) > MAX_FILE_NAME_LEN)
    {
        YV_HttpPostParse_WriteLog("文件名太长[%d > %d]\n", strlen(PostFileName), MAX_FILE_NAME_LEN);
        return -1;
    }

    /* 获取环境变量 */
    pPOSTLibEnv = getenv("YV_POST_LIB_ENV_LOG_LEVEL");
    if(NULL != pPOSTLibEnv)
    {
        g_LogLevel = atoi(pPOSTLibEnv);
    }
    pPOSTLibEnv = getenv("YV_POST_LIB_ENV_PRINT_HEX");
    if(NULL != pPOSTLibEnv)
    {
        g_PrintHEX =  atoi(pPOSTLibEnv);
    }
    pPOSTLibEnv = getenv("YV_POST_LIB_ENV_MIN_DATA");
    if(NULL != pPOSTLibEnv)
    {
        int MIN_PrintableData = atoi(pPOSTLibEnv);
        g_MIN_PrintableData = MIN_PrintableData > g_MIN_PrintableData ? MIN_PrintableData : g_MIN_PrintableData;
    }
    pPOSTLibEnv = getenv("YV_POST_LIB_ENV_ERR_DIR");
    if(NULL != pPOSTLibEnv)
    {
            g_ERR_POST_Info_Stroage = pPOSTLibEnv;
    }
    pPOSTLibEnv = getenv("YV_POST_LIB_ENV_OPT_FREFIX");
    if(NULL != pPOSTLibEnv)
    {
            g_OptPrefix = pPOSTLibEnv;
    }

    /* 判断文件是否存在 */
    YV_HttpPostParse_WriteLog("检测文件是否存在:[%s]\n", PostFileName);
    struct stat PostFileStat;
    lStatus = stat(PostFileName, &PostFileStat);
    if( 0 != lStatus)
    {
        YV_HttpPostParse_WriteLog("文件无法访问:[%s]\n", PostFileName);
        return -1; /* 可以直接返回 */
    }

    /* 获取文件长度 */
    YV_HttpPostParse_WriteLog("获取文件长度:[%s]\n", PostFileName);
    lFileLength = PostFileStat.st_size;
    if(lFileLength <= 0 || lFileLength > 1024*1024*100) /* POST文件太大， 太小都不合适 */
    {
        YV_HttpPostParse_WriteLog("文件长度无效:[%s],Len=[%d]\n", PostFileName, lFileLength);
        return -1; /* 可以直接返回 */
    }

    /* 打开POST文件, 记得要关闭 */
    YV_HttpPostParse_WriteLog("获取文件FD:[%s]\n", PostFileName);
    fdPostFile = open(PostFileName, O_RDONLY);
    if(-1 == fdPostFile)
    {
        YV_HttpPostParse_WriteLog("文件无法打开:[%s]\n", PostFileName);
        return -1; /* 可以直接返回 */
    }

    /* mmap内存映射，记得要释放 */
    YV_HttpPostParse_WriteLog("创建malloc映射:[len=%d]\n", lFileLength);
    pMmapBuffer = malloc(lFileLength);
    if(NULL == pMmapBuffer)
    {
        goto ERR;
    }
    lStatus = read(fdPostFile, pMmapBuffer, lFileLength);
    if(lStatus != lFileLength)
    {
        close(fdPostFile);
        goto ERR;
    }

    //pMmapBuffer= mmap(NULL, lFileLength, PROT_READ, MAP_PRIVATE, fdPostFile, 0);
    //if( ((void *) -1) == pMmapBuffer)
    //{
    //    YV_HttpPostParse_WriteLog("创建内存映射失败:[%s], Len=[%d]\n", PostFileName, lFileLength);
    //    goto ERR; /* 不可以直接返回 */
    //}

    /* 打开POST文件, 执行关闭 */
    close(fdPostFile); /* 不关心 关闭是否成功 */
    fdPostFile = 0;

    /* 为Handle申请内存， 记得释放 */
    YV_HttpPostParse_WriteLog("设定Handle属性\n");
    pstHttpPostPareHandle = malloc(sizeof(ST_HttpPostPareHandle));
    if(NULL == pstHttpPostPareHandle)
    {
        YV_HttpPostParse_WriteLog("init Malloc ERROR\n");
        goto ERR; /* 不可以直接返回 */
    }
    memset(pstHttpPostPareHandle, 0, sizeof(ST_HttpPostPareHandle));

    /* 判断POST文件名长度 */
    if(strlen(PostFileName) > (sizeof(pstHttpPostPareHandle->PostFilename)-1))
    {
        YV_HttpPostParse_WriteLog("FileNameTooLong\n");
        goto ERR;
    }
    memcpy(pstHttpPostPareHandle->PostFilename, PostFileName, strlen(PostFileName));
    pstHttpPostPareHandle->pMmapBuffer    = pMmapBuffer;
    pstHttpPostPareHandle->lMmapBufferLen = lFileLength;
    pstHttpPostPareHandle->lFirstOffset   = -1;
    *phParsePost = (hParsePost*)pstHttpPostPareHandle;

    YV_HttpPostParse_WriteLog("pMmapBuffer=%p, lMmapBufferLen=%d\n", pMmapBuffer, lFileLength);
    YV_HttpPostParse_WriteLog("INIT 完成\n");

    return 0;

ERR:
    /* 释放 mmap  */
    //if(((void *) -1) != pMmapBuffer)
    //{
    //    lStatus = munmap(pMmapBuffer, lFileLength);
    //    if(0 != lStatus)
    //    {
    //        YV_HttpPostParse_WriteLog("mmap 内存泄漏![Addr=%p],[len=%d]\n", pstHttpPostPareHandle->pMmapBuffer, pstHttpPostPareHandle->lMmapBufferLen);
    //    }
    //}
    if(NULL != pMmapBuffer)
    {
        free(pMmapBuffer);
        pMmapBuffer = NULL;
    }


    /* 关闭文件 */
    if(0 != fdPostFile)
    {
        close(fdPostFile);
        fdPostFile = 0;
    }
    /* 释放handle */
    if(NULL != pstHttpPostPareHandle)
    {
        free(pstHttpPostPareHandle);
        pstHttpPostPareHandle = 0;
    }
    return -1;
}

/*****************************************************************
*Function    :YV_HttpPostParse_Fini
*Description :POST解析库释放
*Input       :句柄值
*Output      :none
*Return      :void
*Others      :none
*****************************************************************/
void YV_HttpPostParse_Fini(hParsePost *phParsePost)
{
    //int lStatus = 0;

    YV_HttpPostParse_WriteLog("Handle 析构参数检测\n");
    if(NULL == phParsePost || NULL == *phParsePost)
    {
        /* 注意 phParsePost 是二级指针*/
        YV_HttpPostParse_WriteLog("无效的参数:[phParsePost=%p],[*phParsePost=%p]\n", phParsePost, *phParsePost);
        return; /* 无效的数据，无法往下执行 */
    }

    /* 取出实际 handle 数据 */
    ST_HttpPostPareHandle *pstHttpPostPareHandle = (ST_HttpPostPareHandle*)(*phParsePost);
    YV_HttpPostParse_WriteLog("Handle 正在释放mmap内存\n");
    YV_HttpPostParse_WriteLog("pMmapBuffer=[%p][len=%d]\n", pstHttpPostPareHandle->pMmapBuffer, pstHttpPostPareHandle->lMmapBufferLen);
    //lStatus = munmap(pstHttpPostPareHandle->pMmapBuffer, pstHttpPostPareHandle->lMmapBufferLen);
    //if(0 != lStatus)
    //{
    //    YV_HttpPostParse_WriteLog("mmap 内存泄漏![Addr=%p],[len=%d]\n", pstHttpPostPareHandle->pMmapBuffer, pstHttpPostPareHandle->lMmapBufferLen);
    //    return;
    //}
    //YV_HttpPostParse_WriteLog("mmap释放OK\n");

    if(NULL != pstHttpPostPareHandle->pMmapBuffer)
    {
        free(pstHttpPostPareHandle->pMmapBuffer);
        pstHttpPostPareHandle->pMmapBuffer = NULL;
    }


    /* 释放Handle */
    if(NULL != *phParsePost)
    {
        free(*phParsePost);
        *phParsePost = NULL;
        YV_HttpPostParse_WriteLog("Handle释放OK\n");
    }
}

/*****************************************************************
*Function    :ishex
*Description :判断是不是HEX字符
*Input       :数字
*Output      :none
*Return      :1是， 0否
*Others      :none
*****************************************************************/
static                          /* add by zhengsw : static 兼容g++ */
inline int ishex(int x)
{
    return  (x >= '0' && x <= '9')  || (x >= 'a' && x <= 'f')  || (x >= 'A' && x <= 'F');
}


/*****************************************************************
*Function    :YV_UrlDecode
*Description :URLcode编码转换, 三个字节转换为1个字节
*Input       :URLcode编码
*Output      :URLdecode编码
*Return      :转换成功字节数
*Others      :none
*****************************************************************/
static int  YV_UrlDecode(const char *src, int inputLen, char *dec, int BufferSize)
{
    char *pdec = NULL;
    const char *srcEnd = src + inputLen;
    int a = 0;
    int b = 0;
    int c = 0;
    unsigned char value = 0;

    if(NULL == src || NULL == dec || inputLen < 0 || BufferSize < 0)
    {
        return -1;
    }


    int  counter = 0; //转换成功计数器
    for (pdec = dec; src < srcEnd; pdec++)
    {
        if(pdec - dec > BufferSize)
        {
            return pdec - dec;
        }
        a = *(src + 0);
        b = *(src + 1);
        c = *(src + 2);

        if (a == '%' && ishex(b) && ishex(c))
        {
            if(b >= 'a' && b <= 'f')
            {
                value = b - 'a'+ 0x0a;
            }
            else if (b >= 'A' && b <= 'F')
            {
                value = b - 'A'+ 0x0a;
            }
            else
            {
                value = b - '0' + 0x30;
            }

            value = value << 4;

            if(c >= 'a' && c <= 'f')
            {
                value |= (0x0F & (c - 'a' + 0x0a));
            }
            else if (c >= 'A' && c <= 'F')
            {
                value |= (0x0F & (c - 'A' + 0x0a));
            }
            else
            {
                value |= (0x0F & (c - '0'+ 0x30));
            }

            *pdec = value;
            src+=3;
        }
        else
        {
            *pdec = *src;
            src++;
        }
        counter++; // 转换成功计数器累计
    }
    return  counter;
}


/*****************************************************************
*Function    :YV_ProcessURLDecode
*Description :字符串URLDecode
*Input       :数据的地址与长度
*Output      :已decode的URL
*Return      :返回decode的长度， -1代表失败
*Others      :
*****************************************************************/
static int YV_ProcessURLDecode(char *pInput, int InputLen, char *OutBuffer, int OutBufferSize)
{
    int lDecodeLen;
    char szURLDecodeBuffer1[OUTPUT_BUFFER_MAX_LEN];
    char szURLDecodeBuffer2[OUTPUT_BUFFER_MAX_LEN];
    char *pStr;
    int lDataLen;

    lDecodeLen = 0;
    lDataLen = 0;
    pStr = NULL;

    if(NULL == pInput || NULL == OutBuffer || InputLen <= 0 || OutBufferSize <= 0 )
    {
        return   -1;
    }

    YV_HttpPostParse_WriteLog("Before_URL_Decode[len=%d][%s]\n", InputLen, pInput);
    pStr = pInput;
    lDataLen = InputLen;

    /* 第1次URLdecode */
    lDecodeLen = YV_UrlDecode(pStr, lDataLen, szURLDecodeBuffer1, OUTPUT_BUFFER_MAX_LEN);
    if(lDecodeLen < 0)
    {
        return -1;
    }
    YV_HttpPostParse_WriteLog("第1次urlDecode结果:[len=%d][%s]\n", lDecodeLen, szURLDecodeBuffer1);
    pStr = szURLDecodeBuffer1;
    lDataLen = lDecodeLen;
    pStr[lDataLen] = '\0';

    /* 第2次URLdecode */
    if(lDecodeLen < InputLen) // 判断是否需要再次 URLdecode
    {
        lDecodeLen = YV_UrlDecode(pStr, lDataLen, szURLDecodeBuffer2, OUTPUT_BUFFER_MAX_LEN);
        if(lDecodeLen < 0)
        {
            return -1;
        }
        YV_HttpPostParse_WriteLog("第2次urlDecode结果:[len=%d][%s]\n",lDecodeLen, szURLDecodeBuffer2);
        pStr = szURLDecodeBuffer2;
        lDataLen = lDecodeLen;
        pStr[lDataLen] = '\0';
    }

    /* 判断 URLdecode 的合理性 */
    if((lDecodeLen < InputLen/3) && InputLen < 20)
    {
        YV_HttpPostParse_WriteLog("urlDecode失败, 无效的URLdecode[InLen=%d][AfterLen=%d]\n", InputLen, strlen(pStr));
        return -1;
    }

    /* 返回有效的字节数 */
    int lCopyLength = lDataLen < (OutBufferSize-1) ? lDataLen : (OutBufferSize-1);
    memcpy(OutBuffer, pStr, lCopyLength);
    OutBuffer[lCopyLength] = '\0';
    return lCopyLength;
}


/*****************************************************************
*Function    :YV_ProcessDeCompress
*Description :探测是否压缩并处理
*Input       :数据的地址与长度
*Output      :已解压的数据
*Return      :返回解压后数据的长度
*Others      :
*****************************************************************/
static int YV_ProcessDeCompress(char *pInput, int InputLen, char *OutBuffer, int OutBufferSize)
{
    int lStatus;
    int lCompressFlags;

    lStatus = 0;
    lCompressFlags = 0;

    if(NULL == pInput || NULL == OutBuffer || InputLen <= 2 || OutBufferSize <= 0 ) /* 必须保证InputLen大于2才有意义, 压缩头为2字节*/
    {
        return -1;
    }


    /* 2.2.1 判断是否gzip压缩 */
    lCompressFlags = 0;

    /* 2.2.3 探测是否有 GZIP_Magic 标记 */
    lStatus = memcmp(pInput, g_GZIP_Magic, 2); /* 读取比较 两个字节 */
    if(0 == lStatus)
    {
        lCompressFlags = 1;
    }
    /* 2.2.4 探测是否有 Zlib_Magic 标记 */
    if(0 == memcmp(pInput, g_Zlib_Magic1, 2) || 0 == memcmp(pInput, g_Zlib_Magic2, 2) || 0 == memcmp(pInput, g_Zlib_Magic3, 2) || 0 == memcmp(pInput, g_Zlib_Magic4, 2) )
    {
        lCompressFlags = 1;
    }

    /* 2.2.3 GZIP 解压 */
    if(1 == lCompressFlags)
    {
        lStatus = GzipDecompress((Byte *)pInput, InputLen, (Byte *)OutBuffer, OutBufferSize -1);
        if(lStatus > 0)
        {
            int lCopyRealLength = (OutBufferSize-1) < lStatus ? (OutBufferSize-1) : lStatus; /* 小心数据越界 */
            YV_HttpPostParse_WriteLog("gzdecompress_OK [解压之前=%d][解压之后=%d]\n", InputLen, lCopyRealLength);
            YV_HttpPostParse_WriteLog("After_gzdecompress[%02X][%02X][%02X][%02X][%s]\n", (unsigned char)OutBuffer[0], (unsigned char)OutBuffer[1],(unsigned char)OutBuffer[2],(unsigned char)OutBuffer[3], OutBuffer);
            return lCopyRealLength; /* 保证返回的数值小于输出缓冲区大小 */
        }

        lStatus = ZlibDecompress((Byte *)pInput, InputLen, (Byte *)OutBuffer, OutBufferSize - 1);
        if(lStatus > 0)
        {
            int lCopyRealLength = (OutBufferSize-1) < lStatus ? (OutBufferSize-1) : lStatus; /* 小心数据越界 */
            YV_HttpPostParse_WriteLog("ZlibDecompress_OK [解压之前=%d][解压之后=%d]\n", InputLen, lCopyRealLength);
            YV_HttpPostParse_WriteLog("After_ZlibDecompress[%02X][%02X][%02X][%02X][%s]\n", (unsigned char)OutBuffer[0], (unsigned char)OutBuffer[1],(unsigned char)OutBuffer[2],(unsigned char)OutBuffer[3], OutBuffer);
            return lCopyRealLength; /* 保证返回的数值小于输出缓冲区大小 */
        }
    }

    // 运行到此， 要么不是压缩数据， 要么就是解压失败  将原数据放到下一级流水线
    int lCopyRealLength = (OutBufferSize-1) < InputLen ? (OutBufferSize-1) : InputLen;
    memcpy(OutBuffer, pInput, lCopyRealLength);
    OutBuffer[lCopyRealLength] = '\0';
    return lCopyRealLength;         /* 保证返回的数值小于输出缓冲区大小 */
}


/*****************************************************************
*Function    :YV_ProcessPostFormat
*Description :POST记录检测
*Input       :POST原始记录 包含Magic_code, length, data
*Output      :none
*Return      :0代表成功， -1代表失败
*Others      :none
*****************************************************************/
int YV_ProcessPostFormat(const char *pRAWRecoder, unsigned int lPostInfoLength)
{
    int lReturn;
    int lStatus;
    int lContentLen;

    lReturn = 0;
    lStatus = 0;
    lContentLen = 0;

    if(NULL == pRAWRecoder || lPostInfoLength <= 8)/* 4字节magic, 4字节length */
    {
        lReturn = -1;
        goto OUT;
    }

    /* 1.1 magic code 检测 */
    YV_HttpPostParse_WriteLog("检测Magic[%s]...\n", YV_POST_MAGIC_DODE);
    lStatus = memcmp(pRAWRecoder, YV_POST_MAGIC_DODE, 4);
    if(0 != lStatus)
    {
        YV_HttpPostParse_WriteLog("magic code ERR[0x%02x,0x%02x,0x%02x,0x%02x]\n", (unsigned char)pRAWRecoder[0], (unsigned char)pRAWRecoder[1], (unsigned char)pRAWRecoder[2], (unsigned char)pRAWRecoder[3]);
        lReturn = -1;
        goto OUT;
    }
    YV_HttpPostParse_WriteLog("Magic...OK\n");

    /* 1.2 content length 检测 */
    YV_HttpPostParse_WriteLog("POST记录长度比较\n");
    lContentLen = *((const int*)(pRAWRecoder + 4));/* 小端存储 */
    if(lPostInfoLength <= 0)
    {
        YV_HttpPostParse_WriteLog("post len长度无效[%d]\n",lPostInfoLength);
        lReturn = -3;
        goto OUT;
     }
     else if(lContentLen > OUTPUT_BUFFER_MAX_LEN || lPostInfoLength != (unsigned)lContentLen)
     {
        YV_HttpPostParse_WriteLog("POST报文长度无效[文件记录中的len=%d][函数传递过来的Len=%d]\n", lContentLen, lPostInfoLength);
         lReturn = -2;
         goto OUT;
     }


    YV_HttpPostParse_WriteLog("POST记录长度 与 TBL 记录长度一致[len=%d]\n", lContentLen);
    lReturn = 0;

OUT:
    return lReturn;
}


/*****************************************************************
*Function    :YV_PostContentIsPrint
*Description :检测data数据是否可见
*Input       :数据的地址与长度
*Output      :none
*Return      :0代表成功， -1代表失败
*Others      :none
*****************************************************************/
int YV_PostContentIsPrint(const char* pPostData, int Datalength)
{
    int lLoop;

    lLoop   = 0;

    if(NULL == pPostData || Datalength <= 0)
    {
        YV_HttpPostParse_WriteLog("参数错误[pPostData=%p][Datalength=%d]\n", pPostData, Datalength);
        return  -1;
    }
    YV_HttpPostParse_WriteLog("Before_isprintf=[%s]\n",pPostData);

    for(lLoop = 0; lLoop < Datalength; lLoop++)
    {
        if(0 == isprint(pPostData[lLoop]))
        {
            if(0x00 == pPostData[lLoop] && lLoop != Datalength)
            {
                continue;
            }
            if(0x0a == pPostData[lLoop] && lLoop != Datalength)
            {
                continue;
            }
            if(0x0d == pPostData[lLoop] && lLoop != Datalength)
            {
                continue;
            }

            YV_HttpPostParse_WriteLog("遇到不可见字符[i=%d][TotalLen=%d][pPostData=%p][0x%02x]\n", lLoop, Datalength, pPostData, (unsigned char)pPostData[lLoop]);
            return -1; /* 遇到不可见字符*/
        }
    }
    YV_HttpPostParse_WriteLog("字符全部可见[%s]\n", pPostData);
    return 0;
}

/*****************************************************************
*Function    :isGBK
*Description :探测数据域是不是2字节的GBK数据
*Input       :数据的地址与长度
*Output      :none
*Return      :返回GBK数据的长度， -1代表不全是GBK数据
*Others      :none
*****************************************************************/
int isGBK(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {
            YV_HttpPostParse_WriteLog("GBK汉字区 [0X81]%02X[0XA0] [0X40]%02X[0XFE]\n", (unsigned char)p[0], (unsigned char)p[1]);
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {
            YV_HttpPostParse_WriteLog("GBK汉字区 [0XA1]%02X[0XA9] [0XA1]%02X[0XFE]\n", (unsigned char)p[0], (unsigned char)p[1]);
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {
            YV_HttpPostParse_WriteLog("GBK汉字区 [0XA8]%02X[0XA9] [0X40]%02X[0XA0]\n", (unsigned char)p[0], (unsigned char)p[1]);
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {
            YV_HttpPostParse_WriteLog("GBK汉字区 [0XAA]%02X[0XFE] [0X40]%02X[0XA0]\n", (unsigned char)p[0], (unsigned char)p[1]);
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {
            YV_HttpPostParse_WriteLog("GBK汉字区 [0XB0]%02X[0XF7] [0XA1]%02X[0XFE]\n", (unsigned char)p[0], (unsigned char)p[1]);
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        YV_HttpPostParse_WriteLog("NOT_GBK[%02X][%02X]\n", (unsigned char)p[0], (unsigned char)p[1]);
        return 0;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

/*****************************************************************
*Function    :isUTF8
*Description :检测数据是不是UTF8编码
*Input       :数据的长度与地址
*Output      :none
*Return      :返回UTF8数据的长度， 0代表不是UTF8编码数据
*Others      :none
*****************************************************************/
int isUTF8(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;

    if(NULL == pData || len <=0)
    {
        return 0;
    }

    while(loop > 0 )
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=2 &&  (0XC0 == (p[0] & 0XE0)) && (0X80 == (p[1] & 0XC0)))
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        else if(loop>=4 &&  (0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) )
        {
            p = p + 4;
            loop = loop - 4;
            continue;
        }
        return 0;/* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

/*****************************************************************
*Function    :charset_convert
*Description :对iconv库的再次封装
*Input       :原编码， 目标编码， 原始数据与长度， 输出数据与容量
*Output      :转码后的数据
*Return      :返回转码成功长度， 转码失败-1
*Others      :none
*****************************************************************/
int charset_convert(const char *from_charset, const char *to_charset, char *in_buf, size_t in_left, char *out_buf, size_t out_left)
{
    iconv_t icd;
    char *pin = in_buf;
    char *pout = out_buf;
    size_t out_len = out_left;
    if ((iconv_t)-1 == (icd = iconv_open(to_charset,from_charset)))
    {
        return -1;
    }
    if ((size_t)-1 == iconv(icd, &pin, &in_left, &pout, &out_left))
    {
        iconv_close(icd);
        return -1;
    }
    out_buf[out_len - out_left] = 0;
    iconv_close(icd);
    return (int)out_len - out_left;
}

/*****************************************************************
*Function    :GetMaxLenPrintableStr
*Description :获取一片数据中的最长可见字符
*Input       :数据的地址与长度
*Output      :可见数据的长度
*Return      :最长可见串的首地址
*Others      :none
*****************************************************************/
const char *GetMaxLenPrintableStr(const char *pStr, int *pLen/*in , out*/)
{
    const char *pMAXLeft = NULL;
    int          MAXPrintAbleCounter = 0;
    const char *pLeft = NULL;
    const char *p = pStr;
    int Len = *pLen;
    int isPrintAble = 0;
    int lPrintAbleCounter = 0;

    /* 贪婪模式 匹配二进制数据中最长的可见字符串(支持ASCII，UTF8) */
    while(Len > 0)
    {
        /* 0x00  不算可见字符  */
        if( *p >= 1 &&  *p <= 126 ) // ASCII 有效范围为 0 ~ 127
        {
            if(0 == isPrintAble)/* 状态跳变: 只有从不可见字符转为可见字符状态 */
            {
                isPrintAble = 1;/* 改变状态机                     */
                pLeft = p;       /* 记录可见字符地址               */
            }

            p++;
            Len--;
            lPrintAbleCounter++;
            if(lPrintAbleCounter > MAXPrintAbleCounter) /* 当前匹配的串是不是最长的串啊? */
            {
                MAXPrintAbleCounter = lPrintAbleCounter;/* 更新最长串值                  */
                pMAXLeft = pLeft;                       /* 更新最长串的首地址            */
            }
            continue;
        }
        else if(isUTF8(p, 2) > 0)
        {
            if(0 == isPrintAble)
            {
                isPrintAble = 1;
                pLeft = p;
            }

            p+=2;
            Len-=2;
            lPrintAbleCounter+=2;
            if(lPrintAbleCounter > MAXPrintAbleCounter)
            {
                MAXPrintAbleCounter = lPrintAbleCounter;
                pMAXLeft = pLeft;
            }
            continue;
        }
        else if(isUTF8(p, 3) > 0)
        {
            if(0 == isPrintAble)
            {
                isPrintAble = 1;
                pLeft = p;
            }

            p+=3;
            Len-=3;
            lPrintAbleCounter+=3;
            if(lPrintAbleCounter > MAXPrintAbleCounter)
            {
                MAXPrintAbleCounter = lPrintAbleCounter;
                pMAXLeft = pLeft;
            }
            continue;
        }
        else if(isUTF8(p, 4) > 0)
        {
            if(0 == isPrintAble)
            {
                isPrintAble = 1;
                pLeft = p;
            }

            p+=4;
            Len-=4;
            lPrintAbleCounter+=4;
            if(lPrintAbleCounter > MAXPrintAbleCounter)
            {
                MAXPrintAbleCounter = lPrintAbleCounter;
                pMAXLeft = pLeft;
            }
            continue;
        }

        /* 遇到乱码 */
        pLeft = NULL;
        isPrintAble = 0;
        lPrintAbleCounter = 0;
        p++;
        Len--;
    }
    YV_HttpPostParse_WriteLog("MAXPrintAbleCounter=%d\n", MAXPrintAbleCounter);
    *pLen = MAXPrintAbleCounter ;
    return pMAXLeft;
}

/*****************************************************************
*Function    :ConvertConfusionCode
*Description :乱码处理
*Input       :数据的首地址与长度
*Output      :转码后的数据
*Return      :可见串的长度
*Others      :none
*****************************************************************/
int ConvertConfusionCode(const char *in_buf, size_t in_len, char *out_buf, size_t buf_size)
{
    if(in_len > buf_size-1)
    {
        YV_HttpPostParse_WriteLog("%s: in_len > buf_size\n", __func__);
        return -1;
    }

    /* 乱码输出的模式 */
    if(1 == g_PrintHEX)
    {
        YV_HttpPostParse_WriteLog("优化输出_不可见字符输出为HEX\n");
        int lPrintRet = 0;
        int loop = in_len;
        const char *p = in_buf;
        char szConfusionCode[OUTPUT_BUFFER_MAX_LEN];
        int lConfusionCodeSize = OUTPUT_BUFFER_MAX_LEN;
        char *pout = szConfusionCode;

        memset(szConfusionCode, 0, OUTPUT_BUFFER_MAX_LEN);

        lPrintRet = snprintf(pout, lConfusionCodeSize, "%s", g_OptPrefix);
        pout += lPrintRet;
        lConfusionCodeSize -= lPrintRet;

        while(loop > 0)
        {
            if(isprint(*p) > 0)
            {
                *pout++ = *p++;
                lConfusionCodeSize--;
                loop--;
                continue;
            }
            else if(isUTF8(p, 3) > 0)
            {
                *pout++ = *p++;
                *pout++ = *p++;
                *pout++ = *p++;
                lConfusionCodeSize-=3;
                loop-=3;
                continue;
            }
            else if('\r'== *p || '\n' == *p)
            {
                *pout++ = ' ';
                p++;
                lConfusionCodeSize--;
                loop--;
                continue;
            }

            /* 转换混乱的数据 */
            lPrintRet = snprintf(pout, lConfusionCodeSize, " 0X%02X", (unsigned char)p[0]);
            lConfusionCodeSize -= lPrintRet;
            pout+=lPrintRet;
            loop--;
            p++;
        }
        *pout = 0;
        lPrintRet = charset_convert("UTF-8//IGNORE", "UTF-8", szConfusionCode, (size_t)(pout - szConfusionCode), out_buf, (size_t)buf_size);
        YV_HttpPostParse_WriteLog("END_OF_ConvertConfusionCode:[len=%d][%s]\n", lPrintRet, out_buf);
        return pout - szConfusionCode;
    }
    else/* 只输出最长串 */
    {
        YV_HttpPostParse_WriteLog("优化输出_只输出最长串\n");
        int len = in_len;
        int lPrintRet = 0;
        const char *pLeft = NULL;

        pLeft = GetMaxLenPrintableStr(in_buf, &len);
        lPrintRet = snprintf(out_buf, len + 1, "%s%s", g_OptPrefix, pLeft); /* +1 -> '\0' */
        YV_HttpPostParse_WriteLog("After_GetMaxLenPrintableStr:[len=%d][%s]\n", lPrintRet, out_buf);
        return len + 1; /* +1 -> '\0' */
    }
}


/*****************************************************************
*Function    :printable
*Description :检测可见字符长度是否大于期望值
*Input       :数据的首地，期望字符个数
*Output      :none
*Return      :返回期望值，-1代表达不到期望值
*Others      :none
*****************************************************************/
int printable(const char *pStr, int len, int num)
{
    int loop = len;
    const char *p = pStr;
    int lPrintAbleNum = 0;
    while(loop > 0)
    {
        /* 检测字符连续长度， 返回*/
        if(lPrintAbleNum >= num)
        {
            YV_HttpPostParse_WriteLog("连续字符达到预期个数[%d]\n", num);
            return num;
        }

        /* 0x00 不算可见字符 */
        if('\r' == *p || '\n' == *p || isprint(*p) > 0)
        {
            YV_HttpPostParse_WriteLog("[%03d]在乱码中检测到1字节的ASCII可见字符[%02X][%c]\n",lPrintAbleNum, (unsigned char)p[0], (unsigned char)p[0]);
            p++;
            loop--;
            lPrintAbleNum++;
            continue;
        }

        //if(isGBK(p, 2) > 0)
        //{
        //    //YV_HttpPostParse_WriteLog("[%03d]在乱码中检测到2字节的GBK编码[%02X][%02X]\n", lPrintAbleNum, (unsigned char)p[0], (unsigned char)p[1]);
        //    p+=2;
        //    loop-=2;
        //    lPrintAbleNum+=2;
        //    continue;
        //}
        //else if(isUTF8(p, 3) > 0)
        if(isUTF8(p, 3) > 0)
        {
            YV_HttpPostParse_WriteLog("[%03d]在乱码中检测到3字节的UTF8编码[%02X][%02X][%02X]\n", lPrintAbleNum, (unsigned char)p[0], (unsigned char)p[1], (unsigned char)p[2]);
            p+=3;
            loop-=3;
            lPrintAbleNum+=3;
            continue;
        }


        /* 运行到此 说明依然是乱码*/
        YV_HttpPostParse_WriteLog("在乱码中检测到依然是乱码_计数器清零[%02X][%c]\n", (unsigned char)p[0], (unsigned char)p[0]);
        lPrintAbleNum = 0;

        /* 即使是乱码，也要继续前行 */
        loop--;
        p++;
    }


    /* 全部遍历结束，可见字符都达不到预期个数， 返回-1*/
    YV_HttpPostParse_WriteLog("连续字符达不到预期个数[%d]\n", num);
    return -1;
}

/*****************************************************************
*Function    :CharacterSetConvert
*Description :将非ASCII编码， 探测， 转码， 输出UTF8
*Input       :数据的首地址与长度
*Output      :已转换的数据
*Return      :转换OK的字符长度, -1 代表有乱码
*Others      :none
*****************************************************************/
int CharacterSetConvert(char* pPostData, int Datalength, char *pCharsetConvertBuffer, int lCharsetConvertBufferSize)
{
    int lRet;
    lRet = 0;

    if(NULL == pPostData || Datalength <=0 || NULL == pCharsetConvertBuffer || lCharsetConvertBufferSize <=0 )
    {
        YV_HttpPostParse_WriteLog("[%s]无效的参数[%p][%d][%p][%d]\n", __func__, pPostData, Datalength, pCharsetConvertBuffer, lCharsetConvertBufferSize);
        return -1;
    }

    /* 是不是UTF-8编码 ?  */
    YV_HttpPostParse_WriteLog("Before_isUTF8 len=%d, %s\n", Datalength, pPostData);
    if(isUTF8(pPostData, Datalength) > 0)
    {
        /* 转为UTF-8 编码 */
        YV_HttpPostParse_WriteLog("探测结果为UTF8字符编码\n");

        lRet = charset_convert("UTF-8//IGNORE", "UTF-8", pPostData, Datalength, pCharsetConvertBuffer, lCharsetConvertBufferSize);
        /* 如果转码失败， 当乱码处理 */
        if(lRet < 0)
        {
            YV_HttpPostParse_WriteLog("UTF-8//IGNORE转UTF8编码失败_当乱码处理\n");
            return -1;
        }
        else
        {
            return lRet;
        }
    }
    YV_HttpPostParse_WriteLog("探测结果不是UTF8字符编码\n");

    /* 是不是GBK编码 ? */
    YV_HttpPostParse_WriteLog("Before_isGBK [len=%d][%s]\n", Datalength, pPostData);
    if(isGBK(pPostData, Datalength) > 0)
    {
        /* 转为UTF-8 编码 */
        YV_HttpPostParse_WriteLog("探测结果为GBK字符编码\n");
        lRet = charset_convert("GBK//IGNORE", "UTF-8", pPostData, Datalength, pCharsetConvertBuffer, lCharsetConvertBufferSize);
        /* 如果转码失败， 当乱码处理 */
        if(lRet < 0)
        {
            YV_HttpPostParse_WriteLog("GBK转UTF8编码失败_当乱码处理\n");
            return -1;
        }
        else
        {
            return lRet;
        }
    }
    YV_HttpPostParse_WriteLog("探测结果不是GBK字符编码\n");

    return -1;
}

/*****************************************************************
*Function    :YV_BinToHex
*Description :将二进制数据转换为16进制
*Output      :16进制字符
*Return      :返回字符串的实际长度
*Others      :Flags[0 代表没有）0X前缀][1 代表有）0X前缀]
*****************************************************************/
int YV_BinToHex(const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize, int Flags)
{
    char * pOrigin = OutBuffer;
    int i = 0;
    for(i = 0; i < inPutLen; i++)
    {
        if(0 == Flags)
        {
            snprintf(OutBuffer, OutBufferSize, "%02X", (unsigned char)inPut[i]);
            OutBuffer+=2;
        }
        else if( 1 == Flags)
        {
            snprintf(OutBuffer, OutBufferSize, "0X%02X", (unsigned char)inPut[i]);
            OutBuffer+=4;
        }
    }
        return OutBuffer - pOrigin;
}


/*****************************************************************
*Function    :PrivateProtoParse_WeChat_POST
*Description :微信客户端 POST 内容 解析
*Input       :POST 原始数据与长度，输出缓冲区的大小
*Output      :解析后的明文
*Return      :返回字符串的实际长度
*Others      :none
*****************************************************************/
int PrivateProtoParse_WeChat_POST(const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
#define   BINMAXLEN             256

    const char *pData           = inPut;
    int lCount                  = 0;
    int lStatusMachine          = 0;
    char szBuffer[4096];
    int  lHexLen;
    unsigned int lWeChartTagLen = 0;
    char *pOutBuffer = OutBuffer;
    const char *pStr = NULL;

    enum  /* 定义状态机的状态 */
    {
        ST_NORMAL             =  0X0000,
        ST_ValueBin2Hex,
        ST_SHOW_LENGTH,
        ST_SHOW_NULL,
    };

    pStr = "PrivateProto=WeChatPost";
    if(OutBufferSize < strlen(pStr))//memcpy 安全检测
    {
        return -1;
    }
    memcpy(pOutBuffer, pStr, strlen(pStr));
    pOutBuffer += strlen(pStr);

    lStatusMachine =  ST_NORMAL;
    lCount         = 0;
    while(pData - inPut < (inPutLen -4) ) // 至少要有4个字节长， 用于获取长度
    {
        switch (lStatusMachine)
        {
            case ST_NORMAL:
                {
                    memcpy(&lWeChartTagLen, pData, 4);
                    lWeChartTagLen = ntohl(lWeChartTagLen);                        /* 得到 Tag 的长度                        */
                    pData  += 4;                                                   /* 偏移到4个字节的 Length, 指向Tag        */

                    const char *pSplit = ((++lCount)%2) == 0 ? "=" : "&";
                    if((OutBufferSize - (pOutBuffer - OutBuffer)) < 1) // 剩余空间检测
                    {
                        return -1;
                    }
                    memcpy(pOutBuffer++, pSplit, 1);

                    /******* 如果数据太长，只记录数据的长度 ***********/
                    if(lWeChartTagLen >= BINMAXLEN)
                    {
                        lStatusMachine = ST_SHOW_LENGTH;
                        break;
                    }

                    /******* 如果长度为0，说明该字段没有对应的数据部  ***********/
                    if(0 == lWeChartTagLen)
                    {
                        lStatusMachine = ST_SHOW_NULL;
                        break;
                    }

                    /******* 如果是二进制存储的数据， 且数据长度小于 256字节,则转16进制显示 ***********/
                    if(isUTF8(pData, lWeChartTagLen) <= 0)
                    {
                        lStatusMachine = ST_ValueBin2Hex;
                        break;
                    }

                    /******* 此处全是长度小于 BINMAXLEN 的可见字符  ***********/
                    if((OutBufferSize - (pOutBuffer - OutBuffer)) < lWeChartTagLen) //剩余空间检测
                    {
                        return -1;
                    }
                    memcpy(pOutBuffer, pData, lWeChartTagLen);
                    pOutBuffer    += lWeChartTagLen;
                    lStatusMachine = ST_NORMAL;                                              /* 恢复为初始状态                                   */
                    pData         += lWeChartTagLen;                                         /* 偏移到下一个TAG的Len首地址                       */
                    break;
                }

            case ST_ValueBin2Hex:
                {
                    memset(szBuffer, 0, sizeof(szBuffer));
                    lHexLen = YV_BinToHex(pData, lWeChartTagLen, szBuffer, sizeof(szBuffer), 0);
                    if((OutBufferSize - (pOutBuffer - OutBuffer)) < lHexLen) //剩余空间检测
                    {
                        return -1;
                    }
                    memcpy(pOutBuffer, szBuffer, lHexLen);
                    pOutBuffer    += lHexLen;
                    pData         += lWeChartTagLen;                                         /* 偏移到下一个TAG的Len首地址                       */
                    lStatusMachine = ST_NORMAL;                                              /* 恢复为初始状态                                   */
                    break;
                }

            case ST_SHOW_LENGTH:
                {
                    memset(szBuffer, 0, sizeof(szBuffer));
                    snprintf(szBuffer, sizeof(szBuffer), "%u_Bytes", lWeChartTagLen);
                    if((unsigned)(OutBufferSize - (pOutBuffer - OutBuffer)) < strlen(szBuffer)) //剩余空间检测
                    {
                        return -1;
                    }
                    memcpy(pOutBuffer, szBuffer, strlen(szBuffer));
                    pOutBuffer    += strlen(szBuffer);
                    pData         += lWeChartTagLen;                                         /* 偏移到下一个TAG的Len首地址                       */
                    lStatusMachine = ST_NORMAL;                                              /* 恢复为初始状态                                   */
                    break;
                }

            case ST_SHOW_NULL:
                {
                    pStr = "_null_";
                    if((unsigned)(OutBufferSize - (pOutBuffer - OutBuffer)) < strlen(pStr)) //剩余空间检测
                    {
                        return -1;
                    }
                    memcpy(pOutBuffer, pStr, strlen(pStr));
                    pOutBuffer    += 6;
                    pData         += lWeChartTagLen;
                    lStatusMachine = ST_NORMAL;
                    break;
                }

            default:
                lStatusMachine = ST_NORMAL;
                break;

        }/* end of switch */
    } /* end of while */
    *pOutBuffer = '\0';/* 返回的时候， 带上零结尾 */
    return pOutBuffer - OutBuffer;
}

/*****************************************************************
*Function    :PrivateProto_Is_WeChat_POST
*Description :微信客户端 POST 报文探测
*Input       :POST 原始数据与长度
*Output      :none
*Return      :yes 返回0， no 返回-1
*Others      :微信的POST 内容格式: length(4Byte) KEY(str)  len(4byte) Value(str) len(4Byte) KEY(str) len(4Byte) Value(str)...
*****************************************************************/
int PrivateProto_Is_WeChat_POST(const char *inPut, int inPutLen)
{
    int lDataLen                = inPutLen;
    const char *pData           = inPut;
    unsigned int lGetLen        = 0;
    int lLoop                   = 0;

    lLoop = 4; /* 符合4个， 就认定为是微信私有协议 */
    while(pData - inPut < (lDataLen - 4) && lLoop-- > 0) //至少要有4个字节长， 用于获取长度
    {
        memcpy(&lGetLen, pData, 4);
        lGetLen = ntohl(lGetLen);                                      /* 得到 Data 的长度                    */
        pData  += 4;                                                   /* 偏移到4个字节的 Length 后面         */

        if(lGetLen <= 0)
        {
            return -1;
        }

        if(isUTF8(pData, lGetLen) <= 0)
        {
            return -1;                                                  /* 内容不可见                          */
        }

        pData     += lGetLen;                                           /* 偏移掉数据部分，指向下一个Length     */
    }
    return 0; /* 表明这是一个这正常的 微信的POST  */
}

/******** ADD_S_BY_chunli 私有协议 解析 *********************************************/
typedef struct PrivateProtoParse
{
    int (* ProtoDetect)(const char*, int);
    int (* ProtoParsre)(const char*, int, char *, unsigned int);
    const char *pProtoName;
} S_PrivateProtoParse ;

S_PrivateProtoParse g_st_PrivateProtoParse[] =
{
    { .ProtoDetect = PrivateProto_Is_WeChat_POST,          .ProtoParsre = PrivateProtoParse_WeChat_POST,            .pProtoName = "WeChat_POST"    },
    { .ProtoDetect = NULL,                                 .ProtoParsre = NULL,                                     .pProtoName = NULL             }

};
/******** ADD_E_BY_chunli 私有协议 解析 *********************************************/

static const char *filetype(const char*p, int l)
{
    struct
    {
        const char *prefix;
        int            len;
        const char *detail;
    } file_hdr[] = {
        {"\x89\x50\x4E\x47\x0D\x0A\x1A\x0A", 8,   "PNG"},
        {"\x52\x49\x46\x46", 4,   "webp"},
        {"\x47\x49\x46\x38\x37\x61", 6,   "gif"},
        {"\x47\x49\x46\x38\x39\x61", 6,   "gif"},
        {"\xFF\xD8\xFF\xE0\x00\x10\x4A\x46\x49\x46\x00\x01", 12,   "jpeg"},
        {"\xFF\xD8\xFF\xEE", 4,   "JPG"},
        {"\xFF\xD8\xFF\xE1", 4,   "JPG"},
        {"\x00\x00\x01\xBA", 4,   "mpeg"},
        {"\x25\x50\x44\x46\x2d", 5,   "pdf"},
        {"\x19\xF1\x03\x00", 4, "tencent_mmtls"},
        {"\x47\x40", 2, "MPEG-TS"},
        {"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1", 8, "Microsoft Office"},
        {"\x50\x4B\x03\x04\x14\x00\x06\x00", 8, "Microsoft Office 2016"},
        {"\x52\x61\x72\x21\x1A\x07\x00", 7,   "rar"},
        {"\x52\x61\x72\x21\x1A\x07\x01\x00", 8,   "rar"},
        {"\x50\x4B\x03\x04", 4,   "zip"},
        {"\x50\x4B\x05\x06", 4,   "zip"},
        {"\x50\x4B\x07\x08", 4,   "zip"},
        {"\x75\x73\x74\x61\x72\x00\x30\x30", 8,   "tar"},
        {"\x75\x73\x74\x61\x72\x20\x20\x00", 8,   "tar"},
        {"\x37\x7A\xBC\xAF\x27\x1C", 6,   "7Z"},
        {"\x1F\x9D", 2,   "tar.z"},
        {"\x1F\xA0", 2,   "tar.z"},
        {"\x42\x5A\x68", 3,   "bz2"},
        {"\xed\xab\xee\xdb", 4,   "rpm"},
        {"\x66\x74\x79\x70\x33\x67", 6,   "3gp"},
        {"\x5A\x4D", 2,   "exe"},
        {"\x30\x26\xB2\x75\x8E\x66\xCF\x11", 8,   "wma"},
        {"\xA6\xD9\x00\xAA\x00\x62\xCE\x6C", 8,   "wmv"},
        {"\x52\x49\x46\x46", 4,   "avi"},
        {"\xFF\xFB", 2,   "mp3"},
        {"\xFF\xF3", 2,   "mp3"},
        {"\xFF\xF2", 2,   "mp3"},
        {"\x49\x44\x33", 3,   "mp3"},
        {"\x43\x44\x30\x30\x31", 5,   "ios"},
        {"\x66\x4C\x61\x43", 4,   "flac"},
        {"\x1A\x45\xDF\xA3", 4,   "mkv"},
        {NULL, 0,   NULL},
    };

    for(int i = 0; 0 != file_hdr[i].len; i++)
    {
        if(l >= file_hdr[i].len && 0 == memcmp(p, file_hdr[i].prefix, file_hdr[i].len))
        {
            return file_hdr[i].detail;
        }
    }
    return NULL;
}

/*****************************************************************
*Function    :YV_HttpPostParse_ParseRAW
*Description :POST解析 最原始的接口
*Input       :post的ContentType, post的ContentEncoding，数据的长度
*Output      :解析后的明文, 以 \0 结尾
*Return      :返回字符串的实际长度
*Others      :none
*****************************************************************/
int YV_HttpPostParse_ParseRAW(ST_PostInfo* pstPostInfo, const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    int                      lRetLen;
    char                    *pPostData;
    int                      lPostDataLen;
    char                     szDeCompress[OUTPUT_BUFFER_MAX_LEN];
    char                     szUrlDecode[OUTPUT_BUFFER_MAX_LEN];
    char                     szInputData[OUTPUT_BUFFER_MAX_LEN];
    const char*              strfiletype;


    lRetLen                 = 0;
    pPostData               = NULL;
    lPostDataLen            = 0;
    szDeCompress[0]         = 0;
    szUrlDecode[0]          = 0;

    /* 将数据存入缓存区 */
    lPostDataLen = inPutLen < (OUTPUT_BUFFER_MAX_LEN - 1) ? inPutLen : (OUTPUT_BUFFER_MAX_LEN - 1);
    memcpy(szInputData, inPut, lPostDataLen);
    szInputData[lPostDataLen] = '\0';
    pPostData = szInputData;
    lPostDataLen = lPostDataLen;

    YV_HttpPostParse_WriteLog("RAW_DATA:[len=%d][%02X][%02X][%02X][%02X]\n", 
            lPostDataLen, (unsigned char)pPostData[0], (unsigned char)pPostData[1], (unsigned char)pPostData[2], (unsigned char)pPostData[3]);

    /* 2.2 post 数据压缩探测 返回解压数据长度*/
    lRetLen= YV_ProcessDeCompress(pPostData, lPostDataLen,  szDeCompress, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen < 0)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_DeCompress");
        return -1; /* 程序运行出错;可能 post gzip 数据不完整 */
    }
    szDeCompress[lRetLen] = '\0';
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    lPostDataLen = lRetLen;
    pPostData = szDeCompress;

    strfiletype = filetype(pPostData,lPostDataLen);
    if(strfiletype)
    {
        int lCopyLength = (unsigned)lPostDataLen < (OutBufferSize-1) ? lPostDataLen : (signed)(OutBufferSize-1);
        memcpy(OutBuffer, pPostData, lCopyLength);
        OutBuffer[lCopyLength] = '\0';
        return lCopyLength;
    }

    /* 3.2 URLencode -> URLdecode 返回 URLdecode 后的长度 */
    lRetLen = YV_ProcessURLDecode(pPostData, lPostDataLen, szUrlDecode, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen < 0)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_URL_Decodoe");
        return -1;
    }
    szUrlDecode[lRetLen] = '\0';
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    pPostData    = szUrlDecode;
    lPostDataLen = lRetLen;


    /* 4 字符层处理 */
    char szConvertCharsetBuffer[OUTPUT_BUFFER_MAX_LEN];

    /* 4.2 编码检测 */
    lRetLen = CharacterSetConvert(pPostData, lPostDataLen, szConvertCharsetBuffer, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen <= 0)/* 4.3.1 说明是乱码*/
    {
        /********** 1 是不是私有协议 ? **************************/
        {
            YV_HttpPostParse_WriteLog("是不是私有协议[%02X][%02X][%02X][%02X]\n", 
                    (unsigned char)inPut[0], (unsigned char)inPut[1], (unsigned char)inPut[2], (unsigned char)inPut[3]);
            int i           = 0;
            int ShouldParse = 0;
            for(i = 0; g_st_PrivateProtoParse[i].ProtoDetect != NULL && g_st_PrivateProtoParse[i].ProtoParsre != NULL; i++)
            {
                ShouldParse = g_st_PrivateProtoParse[i].ProtoDetect(inPut, inPutLen); /* 此协议能不能解 ?  */
                if(ShouldParse >= 0)                                                  /* 大于0， 代表能解  */
                {
                    YV_HttpPostParse_WriteLog("Should_Parse_By[%s]\n",g_st_PrivateProtoParse[i].pProtoName);
                    return g_st_PrivateProtoParse[i].ProtoParsre(inPut, inPutLen, OutBuffer, OutBufferSize); /* 开始解析 */
                }
            }
        }

        /********* 2 乱码中是否含有价值的信息? *********************/
        if(printable(pPostData, lPostDataLen, g_MIN_PrintableData) > 0)
        {
            lRetLen = ConvertConfusionCode(pPostData, lPostDataLen, szConvertCharsetBuffer, OUTPUT_BUFFER_MAX_LEN);
            szConvertCharsetBuffer[lRetLen] = '\0';
        }
        else /* 乱码中无价值信息，返回错误 */
        {
            snprintf(OutBuffer, OutBufferSize, "ERR_Cant_Decode");
            return -1;
        }
    }
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    szConvertCharsetBuffer[lRetLen] = '\0';
    pPostData    = szConvertCharsetBuffer;
    lPostDataLen = lRetLen;

    /************************ 运行到此, 全是 UTF-8 编码的字符, 换行符转空格 ***********************/
    char szBufferAllInline[OUTPUT_BUFFER_MAX_LEN];
    lRetLen = AllInline(pPostData, lPostDataLen, szBufferAllInline, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen < 0)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_ALLInLine!");
        return -1;
    }
    szBufferAllInline[lRetLen] = '\0';
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    pPostData    = szBufferAllInline;
    lPostDataLen = lRetLen;

    /*  最后1公里！将有效的数据返回  */
    int lCopyLength = (unsigned)lPostDataLen < (OutBufferSize-1) ? lPostDataLen : (signed)(OutBufferSize-1);
    memcpy(OutBuffer, pPostData, lCopyLength);
    OutBuffer[lCopyLength] = '\0';
    return lCopyLength;
}

int YV_HttpContentDataParse_ParseRAW(ST_PostInfo* pstPostInfo, const char *inPut, int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    int                      lRetLen;
    char                    *pPostData;
    int                      lPostDataLen;
    char                     szDeCompress[OUTPUT_BUFFER_MAX_LEN];
    //char                   szUrlDecode[OUTPUT_BUFFER_MAX_LEN];
    char                     szInputData[OUTPUT_BUFFER_MAX_LEN];
    const char*              strfiletype;


    lRetLen                 = 0;
    pPostData               = NULL;
    lPostDataLen            = 0;
    szDeCompress[0]         = 0;
    //szUrlDecode[0]          = 0;

    /* 将数据存入缓存区 */
    lPostDataLen = inPutLen < (OUTPUT_BUFFER_MAX_LEN - 1) ? inPutLen : (OUTPUT_BUFFER_MAX_LEN - 1);
    memcpy(szInputData, inPut, lPostDataLen);
    szInputData[lPostDataLen] = '\0';
    pPostData = szInputData;
    lPostDataLen = lPostDataLen;

    YV_HttpPostParse_WriteLog("RAW_DATA:[len=%d][%02X][%02X][%02X][%02X]\n", 
            lPostDataLen, (unsigned char)pPostData[0], (unsigned char)pPostData[1], (unsigned char)pPostData[2], (unsigned char)pPostData[3]);

    /* 2.2 post 数据压缩探测 返回解压数据长度*/
    lRetLen= YV_ProcessDeCompress(pPostData, lPostDataLen,  szDeCompress, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen < 0)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_DeCompress");
        return -1; /* 程序运行出错;可能 post gzip 数据不完整 */
    }
    szDeCompress[lRetLen] = '\0';
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    lPostDataLen = lRetLen;
    pPostData = szDeCompress;

    strfiletype = filetype(pPostData,lPostDataLen);
    if(strfiletype)
    {
        int lCopyLength = (unsigned)lPostDataLen < (OutBufferSize-1) ? lPostDataLen : (signed)(OutBufferSize-1);
        memcpy(OutBuffer, pPostData, lCopyLength);
        OutBuffer[lCopyLength] = '\0';
        return lCopyLength;
    }

    #if 0
    /* 3.2 URLencode -> URLdecode 返回 URLdecode 后的长度 */
    lRetLen = YV_ProcessURLDecode(pPostData, lPostDataLen, szUrlDecode, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen < 0)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_URL_Decodoe");
        return -1;
    }
    szUrlDecode[lRetLen] = '\0';
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    pPostData    = szUrlDecode;
    lPostDataLen = lRetLen;
    #endif

    /* 4 字符层处理 */
    char szConvertCharsetBuffer[OUTPUT_BUFFER_MAX_LEN];

    /* 4.2 编码检测 */
    lRetLen = CharacterSetConvert(pPostData, lPostDataLen, szConvertCharsetBuffer, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen <= 0)/* 4.3.1 说明是乱码*/
    {
        /********** 1 是不是私有协议 ? **************************/
        {
            YV_HttpPostParse_WriteLog("是不是私有协议[%02X][%02X][%02X][%02X]\n", 
                    (unsigned char)inPut[0], (unsigned char)inPut[1], (unsigned char)inPut[2], (unsigned char)inPut[3]);
            int i           = 0;
            int ShouldParse = 0;
            for(i = 0; g_st_PrivateProtoParse[i].ProtoDetect != NULL && g_st_PrivateProtoParse[i].ProtoParsre != NULL; i++)
            {
                ShouldParse = g_st_PrivateProtoParse[i].ProtoDetect(inPut, inPutLen); /* 此协议能不能解 ?  */
                if(ShouldParse >= 0)                                                  /* 大于0， 代表能解  */
                {
                    YV_HttpPostParse_WriteLog("Should_Parse_By[%s]\n",g_st_PrivateProtoParse[i].pProtoName);
                    return g_st_PrivateProtoParse[i].ProtoParsre(inPut, inPutLen, OutBuffer, OutBufferSize); /* 开始解析 */
                }
            }
        }

        /********* 2 乱码中是否含有价值的信息? *********************/
        if(printable(pPostData, lPostDataLen, g_MIN_PrintableData) > 0)
        {
            lRetLen = ConvertConfusionCode(pPostData, lPostDataLen, szConvertCharsetBuffer, OUTPUT_BUFFER_MAX_LEN);
            szConvertCharsetBuffer[lRetLen] = '\0';
        }
        else /* 乱码中无价值信息，返回错误 */
        {
            snprintf(OutBuffer, OutBufferSize, "ERR_Cant_Decode");
            return -1;
        }
    }
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    szConvertCharsetBuffer[lRetLen] = '\0';
    pPostData    = szConvertCharsetBuffer;
    lPostDataLen = lRetLen;

    #if 0
    /************************ 运行到此, 全是 UTF-8 编码的字符, 换行符转空格 ***********************/
    char szBufferAllInline[OUTPUT_BUFFER_MAX_LEN];
    lRetLen = AllInline(pPostData, lPostDataLen, szBufferAllInline, OUTPUT_BUFFER_MAX_LEN);
    if(lRetLen < 0)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_ALLInLine!");
        return -1;
    }
    szBufferAllInline[lRetLen] = '\0';
    /* 经过上述过程之后，对POST数据与长度进行标记 */
    pPostData    = szBufferAllInline;
    lPostDataLen = lRetLen;
    #endif

    /*  最后1公里！将有效的数据返回  */
    int lCopyLength = (unsigned)lPostDataLen < (OutBufferSize-1) ? lPostDataLen : (signed)(OutBufferSize-1);
    memcpy(OutBuffer, pPostData, lCopyLength);
    OutBuffer[lCopyLength] = '\0';
    return lCopyLength;
}


/*****************************************************************
*Function    :YV_HttpPostParse_Parse
*Description :POST解析接口
*Input       :post的ContentType, post的ContentEncoding，数据的长度
*Output      :解析后的明文
*Return      :0代表成功， -1代表失败
*Others      :none
*****************************************************************/
int YV_HttpPostParse_Parse(hParsePost phParsePost, ST_PostInfo* pstPostInfo, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == phParsePost || NULL == pstPostInfo || NULL == OutBuffer || OutBufferSize <= 0)
    {
        return -1;
    }

    int                      lStatus;
    char                    *pRAWRecoder;
    ST_HttpPostPareHandle   *pstHttpPostPareHandle;

    lStatus                 = 0;
    pRAWRecoder             = NULL;
    pstHttpPostPareHandle   = NULL;

    YV_HttpPostParse_WriteLog("开始解析:[Offset=%d][Len=%d]\n", pstPostInfo->lContentOffset, pstPostInfo->lContentLength);

    /* 数据类型转换 */
    pstHttpPostPareHandle = (ST_HttpPostPareHandle*)(phParsePost);

    /******************* ADD_S_By_chunli POST_Offset_容错处理 ****************************************/
    if(-1 == pstHttpPostPareHandle->lFirstOffset)
    {
        //为了兼顾黄老师的BUG， 记下第一条offset的值(绝大多数第一条offset应该为0)
        pstHttpPostPareHandle->lFirstOffset = pstPostInfo->lContentOffset;
    }

    /* 判断长度是否过小  */
    if(pstPostInfo->lContentLength <= MIN_POST_DATA_LEN)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_Data_Too_Short [len=%llu]", pstPostInfo->lContentLength);
        return -1;
    }

    unsigned long long int lOrignOffset = pstPostInfo->lContentOffset;
    pstPostInfo->lContentOffset -= pstHttpPostPareHandle->lFirstOffset;            /* 传过来的偏移值，减去第一条偏移值， 得到真实的便宜 */
    /******************* ADD_E_By_chunli POST_Offset_容错处理 ****************************************/

    pRAWRecoder = pstHttpPostPareHandle->pMmapBuffer + pstPostInfo->lContentOffset;/* 根据post传来的偏移,从post文件中取出原始记录数据 */

    /* ContentOffset值检测 */
    if(pstPostInfo->lContentOffset > (pstHttpPostPareHandle->lMmapBufferLen - 10)) /* 4字节Magic，4字节len，2个压缩magic, 防止指向文件末端 */
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_ContentOffset=%llu, FileSize=%llu\n", pstPostInfo->lContentOffset, pstHttpPostPareHandle->lMmapBufferLen);
        YV_POST_ERR_Info_Recoder(pstHttpPostPareHandle->PostFilename, OutBuffer); /* 记录错误的POST与TBL文件 */
        return -1;
    }

    /* ContentLength值检测 */
    if((pstPostInfo->lContentOffset + pstPostInfo->lContentLength) > pstHttpPostPareHandle->lMmapBufferLen)
    {
        snprintf(OutBuffer, OutBufferSize, "ERR_ContentLength! ContentOffset=%llu, ContentLength=%llu, FileSize=%llu\n",
                pstPostInfo->lContentOffset, pstPostInfo->lContentLength, pstHttpPostPareHandle->lMmapBufferLen);
        YV_POST_ERR_Info_Recoder(pstHttpPostPareHandle->PostFilename,  OutBuffer); /* 记录错误的POST与TBL文件 */
        return -1;
    }

    /* 1 POST文件 格式检测 */
    lStatus = YV_ProcessPostFormat(pRAWRecoder, pstPostInfo->lContentLength);
    if(0 != lStatus)
    {
        if(-1 == lStatus)
        {
            snprintf(OutBuffer, OutBufferSize, "ERR_Magic: FirstOffset=%lld, Offset=%llu, 转换后的Offset=%llu\n",
                    pstHttpPostPareHandle->lFirstOffset,  lOrignOffset, pstPostInfo->lContentOffset);
            YV_POST_ERR_Info_Recoder(pstHttpPostPareHandle->PostFilename,  OutBuffer); /* 记录错误的POST与TBL文件 */
            return -1;
        }
        else if(-2 == lStatus || -3 == lStatus)
        {
            snprintf(OutBuffer, OutBufferSize, "ERR_Post_File_Len");
            return -1;
        }
    }


    /* 通过POST格式检测后, 数据指针往后偏移8字节(4字节 Magic_code, 4字节的Length) */
    pRAWRecoder = pRAWRecoder + 8;

    lStatus = YV_HttpPostParse_ParseRAW(pstPostInfo, pRAWRecoder, pstPostInfo->lContentLength, OutBuffer, OutBufferSize);
    if(lStatus >= 0 )
    {
        return 0;
    }

    return -1;
}
