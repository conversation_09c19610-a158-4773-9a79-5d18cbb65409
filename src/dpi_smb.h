/* 
 * File:   dpi_smb.h
 * Author: xu
 *
 * Created on 2019年2月27日, 上午10:34
 */
#ifndef DPI_SMB_H
#define DPI_SMB_H

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

static const struct int_to_string smb_cmd_vals[] = {
    { 0x00, "Create Directory" },
    { 0x01, "Delete Directory" },
    { 0x02, "Open" },
    { 0x03, "Create" },
    { 0x04, "Close" },
    { 0x05, "Flush" },
    { 0x06, "Delete" },
    { 0x07, "Rename" },
    { 0x08, "Query Information" },
    { 0x09, "Set Information" },
    { 0x0A, "Read" },
    { 0x0B, "Write" },
    { 0x0C, "Lock Byte Range" },
    { 0x0D, "Unlock Byte Range" },
    { 0x0E, "Create Temp" },
    { 0x0F, "Create New" },
    { 0x10, "Check Directory" },
    { 0x11, "Process Exit" },
    { 0x12, "Seek" },
    { 0x13, "Lock And Read" },
    { 0x14, "Write And Unlock" },
    { 0x15, "unknown-0x15" },
    { 0x16, "unknown-0x16" },
    { 0x17, "unknown-0x17" },
    { 0x18, "unknown-0x18" },
    { 0x19, "unknown-0x19" },
    { 0x1A, "Read Raw" },
    { 0x1B, "Read MPX" },
    { 0x1C, "Read MPX Secondary" },
    { 0x1D, "Write Raw" },
    { 0x1E, "Write MPX" },
    { 0x1F, "Write MPX Secondary" },
    { 0x20, "Write Complete" },

    /*
     * To quote
     *
     *    http://msdn.microsoft.com/en-us/library/ee442098.aspx
     *
     * "This command was introduced in the NT LAN Manager dialect, and
     * was reserved but not implemented.
     *
     * Clients SHOULD NOT send requests using this command code, and
     * servers receiving requests with this command code SHOULD return
     * STATUS_NOT_IMPLEMENTED (ERRDOS/ERRbadfunc)."
     */
    { 0x21, "Query Server (reserved)" },

    { 0x22, "Set Information2" },
    { 0x23, "Query Information2" },
    { 0x24, "Locking AndX" },
    { 0x25, "Trans" },
    { 0x26, "Trans Secondary" },
    { 0x27, "IOCTL" },
    { 0x28, "IOCTL Secondary" },
    { 0x29, "Copy" },
    { 0x2A, "Move" },
    { 0x2B, "Echo" },
    { 0x2C, "Write And Close" },
    { 0x2D, "Open AndX" },
    { 0x2E, "Read AndX" },
    { 0x2F, "Write AndX" },

    /*
     * To quote
     *
     *    http://msdn.microsoft.com/en-us/library/ee442127.aspx
     *
     * "This command was reserved but not implemented. It was also never
     * defined. It is listed in [SNIA], but it is not defined in that
     * document and does not appear in any other references.
     *
     * Clients SHOULD NOT send requests using this command code, and
     * servers receiving requests with this command code SHOULD return
     * STATUS_NOT_IMPLEMENTED (ERRDOC/ERRbadfunc)."
     */
    { 0x30, "New File Size (reserved)" },

    { 0x31, "Close And Tree Disconnect" },
    { 0x32, "Trans2" },
    { 0x33, "Trans2 Secondary" },
    { 0x34, "Find Close2" },
    { 0x35, "Find Notify Close" },
    { 0x70, "Tree Connect" },
    { 0x71, "Tree Disconnect" },
    { 0x72, "Negotiate Protocol" },
    { 0x73, "Session Setup AndX" },
    { 0x74, "Logoff AndX" },
    { 0x75, "Tree Connect AndX" },
    { 0x80, "Query Information Disk" },
    { 0x81, "Search" },
    { 0x82, "Find" },
    { 0x83, "Find Unique" },
    { 0x84, "Find Close" },
    { 0xA0, "NT Trans" },
    { 0xA1, "NT Trans Secondary" },
    { 0xA2, "NT Create AndX" },
    { 0xA3, "unknown-0xA3" },
    { 0xA4, "NT Cancel" },
    { 0xA5, "NT Rename" },
    { 0xC0, "Open Print File" },
    { 0xC1, "Write Print File" },
    { 0xC2, "Close Print File" },
    { 0xC3, "Get Print Queue" },
    { 0xD0, "Send Single Block Message" },
    { 0xD1, "Send Broadcast Message" },
    { 0xD2, "Forward User Name" },
    { 0xD3, "Cancel Forward" },
    { 0xD4, "Get Machine Name" },
    { 0xD5, "Send Start of Multi-block Message" },
    { 0xD6, "Send End of Multi-block Message" },
    { 0xD7, "Send Text of Multi-block Message" },
    { 0xD8, "SMBreadbulk" },
    { 0xD9, "SMBwritebulk" },
    { 0xDA, "SMBwritebulkdata" },
    { 0xFE, "SMBinvalid" },
    { 0x00, NULL },
};

/*
 * NT error codes.
 *
 * From
 *
 *    http://www.wildpackets.com/elements/misc/SMB_NT_Status_Codes.txt
 */
const struct int_to_string NT_errors[] = {
    { 0x00000000, "STATUS_SUCCESS" },
    /*{ 0x00000000, "STATUS_WAIT_0" }, */
    { 0x00000001, "STATUS_WAIT_1" },
    { 0x00000002, "STATUS_WAIT_2" },
    { 0x00000003, "STATUS_WAIT_3" },
    { 0x0000003F, "STATUS_WAIT_63" },
    { 0x00000080, "STATUS_ABANDONED" },
    /*{ 0x00000080, "STATUS_ABANDONED_WAIT_0" },*/
    { 0x000000BF, "STATUS_ABANDONED_WAIT_63" },
    { 0x000000C0, "STATUS_USER_APC" },
    { 0x00000100, "STATUS_KERNEL_APC" },
    { 0x00000101, "STATUS_ALERTED" },
    { 0x00000102, "STATUS_TIMEOUT" },
    { 0x00000103, "STATUS_PENDING" },
    { 0x00000104, "STATUS_REPARSE" },
    { 0x00000105, "STATUS_MORE_ENTRIES" },
    { 0x00000106, "STATUS_NOT_ALL_ASSIGNED" },
    { 0x00000107, "STATUS_SOME_NOT_MAPPED" },
    { 0x00000108, "STATUS_OPLOCK_BREAK_IN_PROGRESS" },
    { 0x00000109, "STATUS_VOLUME_MOUNTED" },
    { 0x0000010A, "STATUS_RXACT_COMMITTED" },
    { 0x0000010B, "STATUS_NOTIFY_CLEANUP" },
    { 0x0000010C, "STATUS_NOTIFY_ENUM_DIR" },
    { 0x0000010D, "STATUS_NO_QUOTAS_FOR_ACCOUNT" },
    { 0x0000010E, "STATUS_PRIMARY_TRANSPORT_CONNECT_FAILED" },
    { 0x00000110, "STATUS_PAGE_FAULT_TRANSITION" },
    { 0x00000111, "STATUS_PAGE_FAULT_DEMAND_ZERO" },
    { 0x00000112, "STATUS_PAGE_FAULT_COPY_ON_WRITE" },
    { 0x00000113, "STATUS_PAGE_FAULT_GUARD_PAGE" },
    { 0x00000114, "STATUS_PAGE_FAULT_PAGING_FILE" },
    { 0x00000115, "STATUS_CACHE_PAGE_LOCKED" },
    { 0x00000116, "STATUS_CRASH_DUMP" },
    { 0x00000117, "STATUS_BUFFER_ALL_ZEROS" },
    { 0x00000118, "STATUS_REPARSE_OBJECT" },
    { 0x0000045C, "STATUS_NO_SHUTDOWN_IN_PROGRESS" },
    { 0x40000000, "STATUS_OBJECT_NAME_EXISTS" },
    { 0x40000001, "STATUS_THREAD_WAS_SUSPENDED" },
    { 0x40000002, "STATUS_WORKING_SET_LIMIT_RANGE" },
    { 0x40000003, "STATUS_IMAGE_NOT_AT_BASE" },
    { 0x40000004, "STATUS_RXACT_STATE_CREATED" },
    { 0x40000005, "STATUS_SEGMENT_NOTIFICATION" },
    { 0x40000006, "STATUS_LOCAL_USER_SESSION_KEY" },
    { 0x40000007, "STATUS_BAD_CURRENT_DIRECTORY" },
    { 0x40000008, "STATUS_SERIAL_MORE_WRITES" },
    { 0x40000009, "STATUS_REGISTRY_RECOVERED" },
    { 0x4000000A, "STATUS_FT_READ_RECOVERY_FROM_BACKUP" },
    { 0x4000000B, "STATUS_FT_WRITE_RECOVERY" },
    { 0x4000000C, "STATUS_SERIAL_COUNTER_TIMEOUT" },
    { 0x4000000D, "STATUS_NULL_LM_PASSWORD" },
    { 0x4000000E, "STATUS_IMAGE_MACHINE_TYPE_MISMATCH" },
    { 0x4000000F, "STATUS_RECEIVE_PARTIAL" },
    { 0x40000010, "STATUS_RECEIVE_EXPEDITED" },
    { 0x40000011, "STATUS_RECEIVE_PARTIAL_EXPEDITED" },
    { 0x40000012, "STATUS_EVENT_DONE" },
    { 0x40000013, "STATUS_EVENT_PENDING" },
    { 0x40000014, "STATUS_CHECKING_FILE_SYSTEM" },
    { 0x40000015, "STATUS_FATAL_APP_EXIT" },
    { 0x40000016, "STATUS_PREDEFINED_HANDLE" },
    { 0x40000017, "STATUS_WAS_UNLOCKED" },
    { 0x40000018, "STATUS_SERVICE_NOTIFICATION" },
    { 0x40000019, "STATUS_WAS_LOCKED" },
    { 0x4000001A, "STATUS_LOG_HARD_ERROR" },
    { 0x4000001B, "STATUS_ALREADY_WIN32" },
    { 0x4000001C, "STATUS_WX86_UNSIMULATE" },
    { 0x4000001D, "STATUS_WX86_CONTINUE" },
    { 0x4000001E, "STATUS_WX86_SINGLE_STEP" },
    { 0x4000001F, "STATUS_WX86_BREAKPOINT" },
    { 0x40000020, "STATUS_WX86_EXCEPTION_CONTINUE" },
    { 0x40000021, "STATUS_WX86_EXCEPTION_LASTCHANCE" },
    { 0x40000022, "STATUS_WX86_EXCEPTION_CHAIN" },
    { 0x40000023, "STATUS_IMAGE_MACHINE_TYPE_MISMATCH_EXE" },
    { 0x40000024, "STATUS_NO_YIELD_PERFORMED" },
    { 0x40000025, "STATUS_TIMER_RESUME_IGNORED" },
    { 0x40000294, "STATUS_WAKE_SYSTEM" },
    { 0x40020056, "RPC_NT_UUID_LOCAL_ONLY" },
    { 0x400200AF, "RPC_NT_SEND_INCOMPLETE" },
    { 0x80000001, "STATUS_GUARD_PAGE_VIOLATION" },
    { 0x80000002, "STATUS_DATATYPE_MISALIGNMENT" },
    { 0x80000003, "STATUS_BREAKPOINT" },
    { 0x80000004, "STATUS_SINGLE_STEP" },
    { 0x80000005, "STATUS_BUFFER_OVERFLOW" },
    { 0x80000006, "STATUS_NO_MORE_FILES" },
    { 0x80000007, "STATUS_WAKE_SYSTEM_DEBUGGER" },
    { 0x8000000A, "STATUS_HANDLES_CLOSED" },
    { 0x8000000B, "STATUS_NO_INHERITANCE" },
    { 0x8000000C, "STATUS_GUID_SUBSTITUTION_MADE" },
    { 0x8000000D, "STATUS_PARTIAL_COPY" },
    { 0x8000000E, "STATUS_DEVICE_PAPER_EMPTY" },
    { 0x8000000F, "STATUS_DEVICE_POWERED_OFF" },
    { 0x80000010, "STATUS_DEVICE_OFF_LINE" },
    { 0x80000011, "STATUS_DEVICE_BUSY" },
    { 0x80000012, "STATUS_NO_MORE_EAS" },
    { 0x80000013, "STATUS_INVALID_EA_NAME" },
    { 0x80000014, "STATUS_EA_LIST_INCONSISTENT" },
    { 0x80000015, "STATUS_INVALID_EA_FLAG" },
    { 0x80000016, "STATUS_VERIFY_REQUIRED" },
    { 0x80000017, "STATUS_EXTRANEOUS_INFORMATION" },
    { 0x80000018, "STATUS_RXACT_COMMIT_NECESSARY" },
    { 0x8000001A, "STATUS_NO_MORE_ENTRIES" },
    { 0x8000001B, "STATUS_FILEMARK_DETECTED" },
    { 0x8000001C, "STATUS_MEDIA_CHANGED" },
    { 0x8000001D, "STATUS_BUS_RESET" },
    { 0x8000001E, "STATUS_END_OF_MEDIA" },
    { 0x8000001F, "STATUS_BEGINNING_OF_MEDIA" },
    { 0x80000020, "STATUS_MEDIA_CHECK" },
    { 0x80000021, "STATUS_SETMARK_DETECTED" },
    { 0x80000022, "STATUS_NO_DATA_DETECTED" },
    { 0x80000023, "STATUS_REDIRECTOR_HAS_OPEN_HANDLES" },
    { 0x80000024, "STATUS_SERVER_HAS_OPEN_HANDLES" },
    { 0x80000025, "STATUS_ALREADY_DISCONNECTED" },
    { 0x80000026, "STATUS_LONGJUMP" },
    { 0x8000002D, "STATUS_STOPPED_ON_SYMLINK" },
    { 0x80000288, "STATUS_DEVICE_REQUIRES_CLEANING" },
    { 0x80000289, "STATUS_DEVICE_DOOR_OPEN" },
    { 0x80040111, "MAPI_E_LOGON_FAILED" },
    { 0x80090300, "SEC_E_INSUFFICIENT_MEMORY" },
    { 0x80090301, "SEC_E_INVALID_HANDLE" },
    { 0x80090302, "SEC_E_UNSUPPORTED_FUNCTION" },
    { 0x8009030B, "SEC_E_NO_IMPERSONATION" },
    { 0x8009030D, "SEC_E_UNKNOWN_CREDENTIALS" },
    { 0x8009030E, "SEC_E_NO_CREDENTIALS" },
    { 0x8009030F, "SEC_E_MESSAGE_ALTERED" },
    { 0x80090310, "SEC_E_OUT_OF_SEQUENCE" },
    { 0x80090311, "SEC_E_NO_AUTHENTICATING_AUTHORITY" },
    { 0xC0000001, "STATUS_UNSUCCESSFUL" },
    { 0xC0000002, "STATUS_NOT_IMPLEMENTED" },
    { 0xC0000003, "STATUS_INVALID_INFO_CLASS" },
    { 0xC0000004, "STATUS_INFO_LENGTH_MISMATCH" },
    { 0xC0000005, "STATUS_ACCESS_VIOLATION" },
    { 0xC0000006, "STATUS_IN_PAGE_ERROR" },
    { 0xC0000007, "STATUS_PAGEFILE_QUOTA" },
    { 0xC0000008, "STATUS_INVALID_HANDLE" },
    { 0xC0000009, "STATUS_BAD_INITIAL_STACK" },
    { 0xC000000A, "STATUS_BAD_INITIAL_PC" },
    { 0xC000000B, "STATUS_INVALID_CID" },
    { 0xC000000C, "STATUS_TIMER_NOT_CANCELED" },
    { 0xC000000D, "STATUS_INVALID_PARAMETER" },
    { 0xC000000E, "STATUS_NO_SUCH_DEVICE" },
    { 0xC000000F, "STATUS_NO_SUCH_FILE" },
    { 0xC0000010, "STATUS_INVALID_DEVICE_REQUEST" },
    { 0xC0000011, "STATUS_END_OF_FILE" },
    { 0xC0000012, "STATUS_WRONG_VOLUME" },
    { 0xC0000013, "STATUS_NO_MEDIA_IN_DEVICE" },
    { 0xC0000014, "STATUS_UNRECOGNIZED_MEDIA" },
    { 0xC0000015, "STATUS_NONEXISTENT_SECTOR" },
    { 0xC0000016, "STATUS_MORE_PROCESSING_REQUIRED" },
    { 0xC0000017, "STATUS_NO_MEMORY" },
    { 0xC0000018, "STATUS_CONFLICTING_ADDRESSES" },
    { 0xC0000019, "STATUS_NOT_MAPPED_VIEW" },
    { 0xC000001A, "STATUS_UNABLE_TO_FREE_VM" },
    { 0xC000001B, "STATUS_UNABLE_TO_DELETE_SECTION" },
    { 0xC000001C, "STATUS_INVALID_SYSTEM_SERVICE" },
    { 0xC000001D, "STATUS_ILLEGAL_INSTRUCTION" },
    { 0xC000001E, "STATUS_INVALID_LOCK_SEQUENCE" },
    { 0xC000001F, "STATUS_INVALID_VIEW_SIZE" },
    { 0xC0000020, "STATUS_INVALID_FILE_FOR_SECTION" },
    { 0xC0000021, "STATUS_ALREADY_COMMITTED" },
    { 0xC0000022, "STATUS_ACCESS_DENIED" },
    { 0xC0000023, "STATUS_BUFFER_TOO_SMALL" },
    { 0xC0000024, "STATUS_OBJECT_TYPE_MISMATCH" },
    { 0xC0000025, "STATUS_NONCONTINUABLE_EXCEPTION" },
    { 0xC0000026, "STATUS_INVALID_DISPOSITION" },
    { 0xC0000027, "STATUS_UNWIND" },
    { 0xC0000028, "STATUS_BAD_STACK" },
    { 0xC0000029, "STATUS_INVALID_UNWIND_TARGET" },
    { 0xC000002A, "STATUS_NOT_LOCKED" },
    { 0xC000002B, "STATUS_PARITY_ERROR" },
    { 0xC000002C, "STATUS_UNABLE_TO_DECOMMIT_VM" },
    { 0xC000002D, "STATUS_NOT_COMMITTED" },
    { 0xC000002E, "STATUS_INVALID_PORT_ATTRIBUTES" },
    { 0xC000002F, "STATUS_PORT_MESSAGE_TOO_LONG" },
    { 0xC0000030, "STATUS_INVALID_PARAMETER_MIX" },
    { 0xC0000031, "STATUS_INVALID_QUOTA_LOWER" },
    { 0xC0000032, "STATUS_DISK_CORRUPT_ERROR" },
    { 0xC0000033, "STATUS_OBJECT_NAME_INVALID" },
    { 0xC0000034, "STATUS_OBJECT_NAME_NOT_FOUND" },
    { 0xC0000035, "STATUS_OBJECT_NAME_COLLISION" },
    { 0xC0000037, "STATUS_PORT_DISCONNECTED" },
    { 0xC0000038, "STATUS_DEVICE_ALREADY_ATTACHED" },
    { 0xC0000039, "STATUS_OBJECT_PATH_INVALID" },
    { 0xC000003A, "STATUS_OBJECT_PATH_NOT_FOUND" },
    { 0xC000003B, "STATUS_OBJECT_PATH_SYNTAX_BAD" },
    { 0xC000003C, "STATUS_DATA_OVERRUN" },
    { 0xC000003D, "STATUS_DATA_LATE_ERROR" },
    { 0xC000003E, "STATUS_DATA_ERROR" },
    { 0xC000003F, "STATUS_CRC_ERROR" },
    { 0xC0000040, "STATUS_SECTION_TOO_BIG" },
    { 0xC0000041, "STATUS_PORT_CONNECTION_REFUSED" },
    { 0xC0000042, "STATUS_INVALID_PORT_HANDLE" },
    { 0xC0000043, "STATUS_SHARING_VIOLATION" },
    { 0xC0000044, "STATUS_QUOTA_EXCEEDED" },
    { 0xC0000045, "STATUS_INVALID_PAGE_PROTECTION" },
    { 0xC0000046, "STATUS_MUTANT_NOT_OWNED" },
    { 0xC0000047, "STATUS_SEMAPHORE_LIMIT_EXCEEDED" },
    { 0xC0000048, "STATUS_PORT_ALREADY_SET" },
    { 0xC0000049, "STATUS_SECTION_NOT_IMAGE" },
    { 0xC000004A, "STATUS_SUSPEND_COUNT_EXCEEDED" },
    { 0xC000004B, "STATUS_THREAD_IS_TERMINATING" },
    { 0xC000004C, "STATUS_BAD_WORKING_SET_LIMIT" },
    { 0xC000004D, "STATUS_INCOMPATIBLE_FILE_MAP" },
    { 0xC000004E, "STATUS_SECTION_PROTECTION" },
    { 0xC000004F, "STATUS_EAS_NOT_SUPPORTED" },
    { 0xC0000050, "STATUS_EA_TOO_LARGE" },
    { 0xC0000051, "STATUS_NONEXISTENT_EA_ENTRY" },
    { 0xC0000052, "STATUS_NO_EAS_ON_FILE" },
    { 0xC0000053, "STATUS_EA_CORRUPT_ERROR" },
    { 0xC0000054, "STATUS_FILE_LOCK_CONFLICT" },
    { 0xC0000055, "STATUS_LOCK_NOT_GRANTED" },
    { 0xC0000056, "STATUS_DELETE_PENDING" },
    { 0xC0000057, "STATUS_CTL_FILE_NOT_SUPPORTED" },
    { 0xC0000058, "STATUS_UNKNOWN_REVISION" },
    { 0xC0000059, "STATUS_REVISION_MISMATCH" },
    { 0xC000005A, "STATUS_INVALID_OWNER" },
    { 0xC000005B, "STATUS_INVALID_PRIMARY_GROUP" },
    { 0xC000005C, "STATUS_NO_IMPERSONATION_TOKEN" },
    { 0xC000005D, "STATUS_CANT_DISABLE_MANDATORY" },
    { 0xC000005E, "STATUS_NO_LOGON_SERVERS" },
    { 0xC000005F, "STATUS_NO_SUCH_LOGON_SESSION" },
    { 0xC0000060, "STATUS_NO_SUCH_PRIVILEGE" },
    { 0xC0000061, "STATUS_PRIVILEGE_NOT_HELD" },
    { 0xC0000062, "STATUS_INVALID_ACCOUNT_NAME" },
    { 0xC0000063, "STATUS_USER_EXISTS" },
    { 0xC0000064, "STATUS_NO_SUCH_USER" },
    { 0xC0000065, "STATUS_GROUP_EXISTS" },
    { 0xC0000066, "STATUS_NO_SUCH_GROUP" },
    { 0xC0000067, "STATUS_MEMBER_IN_GROUP" },
    { 0xC0000068, "STATUS_MEMBER_NOT_IN_GROUP" },
    { 0xC0000069, "STATUS_LAST_ADMIN" },
    { 0xC000006A, "STATUS_WRONG_PASSWORD" },
    { 0xC000006B, "STATUS_ILL_FORMED_PASSWORD" },
    { 0xC000006C, "STATUS_PASSWORD_RESTRICTION" },
    { 0xC000006D, "STATUS_LOGON_FAILURE" },
    { 0xC000006E, "STATUS_ACCOUNT_RESTRICTION" },
    { 0xC000006F, "STATUS_INVALID_LOGON_HOURS" },
    { 0xC0000070, "STATUS_INVALID_WORKSTATION" },
    { 0xC0000071, "STATUS_PASSWORD_EXPIRED" },
    { 0xC0000072, "STATUS_ACCOUNT_DISABLED" },
    { 0xC0000073, "STATUS_NONE_MAPPED" },
    { 0xC0000074, "STATUS_TOO_MANY_LUIDS_REQUESTED" },
    { 0xC0000075, "STATUS_LUIDS_EXHAUSTED" },
    { 0xC0000076, "STATUS_INVALID_SUB_AUTHORITY" },
    { 0xC0000077, "STATUS_INVALID_ACL" },
    { 0xC0000078, "STATUS_INVALID_SID" },
    { 0xC0000079, "STATUS_INVALID_SECURITY_DESCR" },
    { 0xC000007A, "STATUS_PROCEDURE_NOT_FOUND" },
    { 0xC000007B, "STATUS_INVALID_IMAGE_FORMAT" },
    { 0xC000007C, "STATUS_NO_TOKEN" },
    { 0xC000007D, "STATUS_BAD_INHERITANCE_ACL" },
    { 0xC000007E, "STATUS_RANGE_NOT_LOCKED" },
    { 0xC000007F, "STATUS_DISK_FULL" },
    { 0xC0000080, "STATUS_SERVER_DISABLED" },
    { 0xC0000081, "STATUS_SERVER_NOT_DISABLED" },
    { 0xC0000082, "STATUS_TOO_MANY_GUIDS_REQUESTED" },
    { 0xC0000083, "STATUS_GUIDS_EXHAUSTED" },
    { 0xC0000084, "STATUS_INVALID_ID_AUTHORITY" },
    { 0xC0000085, "STATUS_AGENTS_EXHAUSTED" },
    { 0xC0000086, "STATUS_INVALID_VOLUME_LABEL" },
    { 0xC0000087, "STATUS_SECTION_NOT_EXTENDED" },
    { 0xC0000088, "STATUS_NOT_MAPPED_DATA" },
    { 0xC0000089, "STATUS_RESOURCE_DATA_NOT_FOUND" },
    { 0xC000008A, "STATUS_RESOURCE_TYPE_NOT_FOUND" },
    { 0xC000008B, "STATUS_RESOURCE_NAME_NOT_FOUND" },
    { 0xC000008C, "STATUS_ARRAY_BOUNDS_EXCEEDED" },
    { 0xC000008D, "STATUS_FLOAT_DENORMAL_OPERAND" },
    { 0xC000008E, "STATUS_FLOAT_DIVIDE_BY_ZERO" },
    { 0xC000008F, "STATUS_FLOAT_INEXACT_RESULT" },
    { 0xC0000090, "STATUS_FLOAT_INVALID_OPERATION" },
    { 0xC0000091, "STATUS_FLOAT_OVERFLOW" },
    { 0xC0000092, "STATUS_FLOAT_STACK_CHECK" },
    { 0xC0000093, "STATUS_FLOAT_UNDERFLOW" },
    { 0xC0000094, "STATUS_INTEGER_DIVIDE_BY_ZERO" },
    { 0xC0000095, "STATUS_INTEGER_OVERFLOW" },
    { 0xC0000096, "STATUS_PRIVILEGED_INSTRUCTION" },
    { 0xC0000097, "STATUS_TOO_MANY_PAGING_FILES" },
    { 0xC0000098, "STATUS_FILE_INVALID" },
    { 0xC0000099, "STATUS_ALLOTTED_SPACE_EXCEEDED" },
    { 0xC000009A, "STATUS_INSUFFICIENT_RESOURCES" },
    { 0xC000009B, "STATUS_DFS_EXIT_PATH_FOUND" },
    { 0xC000009C, "STATUS_DEVICE_DATA_ERROR" },
    { 0xC000009D, "STATUS_DEVICE_NOT_CONNECTED" },
    { 0xC000009E, "STATUS_DEVICE_POWER_FAILURE" },
    { 0xC000009F, "STATUS_FREE_VM_NOT_AT_BASE" },
    { 0xC00000A0, "STATUS_MEMORY_NOT_ALLOCATED" },
    { 0xC00000A1, "STATUS_WORKING_SET_QUOTA" },
    { 0xC00000A2, "STATUS_MEDIA_WRITE_PROTECTED" },
    { 0xC00000A3, "STATUS_DEVICE_NOT_READY" },
    { 0xC00000A4, "STATUS_INVALID_GROUP_ATTRIBUTES" },
    { 0xC00000A5, "STATUS_BAD_IMPERSONATION_LEVEL" },
    { 0xC00000A6, "STATUS_CANT_OPEN_ANONYMOUS" },
    { 0xC00000A7, "STATUS_BAD_VALIDATION_CLASS" },
    { 0xC00000A8, "STATUS_BAD_TOKEN_TYPE" },
    { 0xC00000A9, "STATUS_BAD_MASTER_BOOT_RECORD" },
    { 0xC00000AA, "STATUS_INSTRUCTION_MISALIGNMENT" },
    { 0xC00000AB, "STATUS_INSTANCE_NOT_AVAILABLE" },
    { 0xC00000AC, "STATUS_PIPE_NOT_AVAILABLE" },
    { 0xC00000AD, "STATUS_INVALID_PIPE_STATE" },
    { 0xC00000AE, "STATUS_PIPE_BUSY" },
    { 0xC00000AF, "STATUS_ILLEGAL_FUNCTION" },
    { 0xC00000B0, "STATUS_PIPE_DISCONNECTED" },
    { 0xC00000B1, "STATUS_PIPE_CLOSING" },
    { 0xC00000B2, "STATUS_PIPE_CONNECTED" },
    { 0xC00000B3, "STATUS_PIPE_LISTENING" },
    { 0xC00000B4, "STATUS_INVALID_READ_MODE" },
    { 0xC00000B5, "STATUS_IO_TIMEOUT" },
    { 0xC00000B6, "STATUS_FILE_FORCED_CLOSED" },
    { 0xC00000B7, "STATUS_PROFILING_NOT_STARTED" },
    { 0xC00000B8, "STATUS_PROFILING_NOT_STOPPED" },
    { 0xC00000B9, "STATUS_COULD_NOT_INTERPRET" },
    { 0xC00000BA, "STATUS_FILE_IS_A_DIRECTORY" },
    { 0xC00000BB, "STATUS_NOT_SUPPORTED" },
    { 0xC00000BC, "STATUS_REMOTE_NOT_LISTENING" },
    { 0xC00000BD, "STATUS_DUPLICATE_NAME" },
    { 0xC00000BE, "STATUS_BAD_NETWORK_PATH" },
    { 0xC00000BF, "STATUS_NETWORK_BUSY" },
    { 0xC00000C0, "STATUS_DEVICE_DOES_NOT_EXIST" },
    { 0xC00000C1, "STATUS_TOO_MANY_COMMANDS" },
    { 0xC00000C2, "STATUS_ADAPTER_HARDWARE_ERROR" },
    { 0xC00000C3, "STATUS_INVALID_NETWORK_RESPONSE" },
    { 0xC00000C4, "STATUS_UNEXPECTED_NETWORK_ERROR" },
    { 0xC00000C5, "STATUS_BAD_REMOTE_ADAPTER" },
    { 0xC00000C6, "STATUS_PRINT_QUEUE_FULL" },
    { 0xC00000C7, "STATUS_NO_SPOOL_SPACE" },
    { 0xC00000C8, "STATUS_PRINT_CANCELLED" },
    { 0xC00000C9, "STATUS_NETWORK_NAME_DELETED" },
    { 0xC00000CA, "STATUS_NETWORK_ACCESS_DENIED" },
    { 0xC00000CB, "STATUS_BAD_DEVICE_TYPE" },
    { 0xC00000CC, "STATUS_BAD_NETWORK_NAME" },
    { 0xC00000CD, "STATUS_TOO_MANY_NAMES" },
    { 0xC00000CE, "STATUS_TOO_MANY_SESSIONS" },
    { 0xC00000CF, "STATUS_SHARING_PAUSED" },
    { 0xC00000D0, "STATUS_REQUEST_NOT_ACCEPTED" },
    { 0xC00000D1, "STATUS_REDIRECTOR_PAUSED" },
    { 0xC00000D2, "STATUS_NET_WRITE_FAULT" },
    { 0xC00000D3, "STATUS_PROFILING_AT_LIMIT" },
    { 0xC00000D4, "STATUS_NOT_SAME_DEVICE" },
    { 0xC00000D5, "STATUS_FILE_RENAMED" },
    { 0xC00000D6, "STATUS_VIRTUAL_CIRCUIT_CLOSED" },
    { 0xC00000D7, "STATUS_NO_SECURITY_ON_OBJECT" },
    { 0xC00000D8, "STATUS_CANT_WAIT" },
    { 0xC00000D9, "STATUS_PIPE_EMPTY" },
    { 0xC00000DA, "STATUS_CANT_ACCESS_DOMAIN_INFO" },
    { 0xC00000DB, "STATUS_CANT_TERMINATE_SELF" },
    { 0xC00000DC, "STATUS_INVALID_SERVER_STATE" },
    { 0xC00000DD, "STATUS_INVALID_DOMAIN_STATE" },
    { 0xC00000DE, "STATUS_INVALID_DOMAIN_ROLE" },
    { 0xC00000DF, "STATUS_NO_SUCH_DOMAIN" },
    { 0xC00000E0, "STATUS_DOMAIN_EXISTS" },
    { 0xC00000E1, "STATUS_DOMAIN_LIMIT_EXCEEDED" },
    { 0xC00000E2, "STATUS_OPLOCK_NOT_GRANTED" },
    { 0xC00000E3, "STATUS_INVALID_OPLOCK_PROTOCOL" },
    { 0xC00000E4, "STATUS_INTERNAL_DB_CORRUPTION" },
    { 0xC00000E5, "STATUS_INTERNAL_ERROR" },
    { 0xC00000E6, "STATUS_GENERIC_NOT_MAPPED" },
    { 0xC00000E7, "STATUS_BAD_DESCRIPTOR_FORMAT" },
    { 0xC00000E8, "STATUS_INVALID_USER_BUFFER" },
    { 0xC00000E9, "STATUS_UNEXPECTED_IO_ERROR" },
    { 0xC00000EA, "STATUS_UNEXPECTED_MM_CREATE_ERR" },
    { 0xC00000EB, "STATUS_UNEXPECTED_MM_MAP_ERROR" },
    { 0xC00000EC, "STATUS_UNEXPECTED_MM_EXTEND_ERR" },
    { 0xC00000ED, "STATUS_NOT_LOGON_PROCESS" },
    { 0xC00000EE, "STATUS_LOGON_SESSION_EXISTS" },
    { 0xC00000EF, "STATUS_INVALID_PARAMETER_1" },
    { 0xC00000F0, "STATUS_INVALID_PARAMETER_2" },
    { 0xC00000F1, "STATUS_INVALID_PARAMETER_3" },
    { 0xC00000F2, "STATUS_INVALID_PARAMETER_4" },
    { 0xC00000F3, "STATUS_INVALID_PARAMETER_5" },
    { 0xC00000F4, "STATUS_INVALID_PARAMETER_6" },
    { 0xC00000F5, "STATUS_INVALID_PARAMETER_7" },
    { 0xC00000F6, "STATUS_INVALID_PARAMETER_8" },
    { 0xC00000F7, "STATUS_INVALID_PARAMETER_9" },
    { 0xC00000F8, "STATUS_INVALID_PARAMETER_10" },
    { 0xC00000F9, "STATUS_INVALID_PARAMETER_11" },
    { 0xC00000FA, "STATUS_INVALID_PARAMETER_12" },
    { 0xC00000FB, "STATUS_REDIRECTOR_NOT_STARTED" },
    { 0xC00000FC, "STATUS_REDIRECTOR_STARTED" },
    { 0xC00000FD, "STATUS_STACK_OVERFLOW" },
    { 0xC00000FE, "STATUS_NO_SUCH_PACKAGE" },
    { 0xC00000FF, "STATUS_BAD_FUNCTION_TABLE" },
    { 0xC0000100, "STATUS_VARIABLE_NOT_FOUND" },
    { 0xC0000101, "STATUS_DIRECTORY_NOT_EMPTY" },
    { 0xC0000102, "STATUS_FILE_CORRUPT_ERROR" },
    { 0xC0000103, "STATUS_NOT_A_DIRECTORY" },
    { 0xC0000104, "STATUS_BAD_LOGON_SESSION_STATE" },
    { 0xC0000105, "STATUS_LOGON_SESSION_COLLISION" },
    { 0xC0000106, "STATUS_NAME_TOO_LONG" },
    { 0xC0000107, "STATUS_FILES_OPEN" },
    { 0xC0000108, "STATUS_CONNECTION_IN_USE" },
    { 0xC0000109, "STATUS_MESSAGE_NOT_FOUND" },
    { 0xC000010A, "STATUS_PROCESS_IS_TERMINATING" },
    { 0xC000010B, "STATUS_INVALID_LOGON_TYPE" },
    { 0xC000010C, "STATUS_NO_GUID_TRANSLATION" },
    { 0xC000010D, "STATUS_CANNOT_IMPERSONATE" },
    { 0xC000010E, "STATUS_IMAGE_ALREADY_LOADED" },
    { 0xC000010F, "STATUS_ABIOS_NOT_PRESENT" },
    { 0xC0000110, "STATUS_ABIOS_LID_NOT_EXIST" },
    { 0xC0000111, "STATUS_ABIOS_LID_ALREADY_OWNED" },
    { 0xC0000112, "STATUS_ABIOS_NOT_LID_OWNER" },
    { 0xC0000113, "STATUS_ABIOS_INVALID_COMMAND" },
    { 0xC0000114, "STATUS_ABIOS_INVALID_LID" },
    { 0xC0000115, "STATUS_ABIOS_SELECTOR_NOT_AVAILABLE" },
    { 0xC0000116, "STATUS_ABIOS_INVALID_SELECTOR" },
    { 0xC0000117, "STATUS_NO_LDT" },
    { 0xC0000118, "STATUS_INVALID_LDT_SIZE" },
    { 0xC0000119, "STATUS_INVALID_LDT_OFFSET" },
    { 0xC000011A, "STATUS_INVALID_LDT_DESCRIPTOR" },
    { 0xC000011B, "STATUS_INVALID_IMAGE_NE_FORMAT" },
    { 0xC000011C, "STATUS_RXACT_INVALID_STATE" },
    { 0xC000011D, "STATUS_RXACT_COMMIT_FAILURE" },
    { 0xC000011E, "STATUS_MAPPED_FILE_SIZE_ZERO" },
    { 0xC000011F, "STATUS_TOO_MANY_OPENED_FILES" },
    { 0xC0000120, "STATUS_CANCELLED" },
    { 0xC0000121, "STATUS_CANNOT_DELETE" },
    { 0xC0000122, "STATUS_INVALID_COMPUTER_NAME" },
    { 0xC0000123, "STATUS_FILE_DELETED" },
    { 0xC0000124, "STATUS_SPECIAL_ACCOUNT" },
    { 0xC0000125, "STATUS_SPECIAL_GROUP" },
    { 0xC0000126, "STATUS_SPECIAL_USER" },
    { 0xC0000127, "STATUS_MEMBERS_PRIMARY_GROUP" },
    { 0xC0000128, "STATUS_FILE_CLOSED" },
    { 0xC0000129, "STATUS_TOO_MANY_THREADS" },
    { 0xC000012A, "STATUS_THREAD_NOT_IN_PROCESS" },
    { 0xC000012B, "STATUS_TOKEN_ALREADY_IN_USE" },
    { 0xC000012C, "STATUS_PAGEFILE_QUOTA_EXCEEDED" },
    { 0xC000012D, "STATUS_COMMITMENT_LIMIT" },
    { 0xC000012E, "STATUS_INVALID_IMAGE_LE_FORMAT" },
    { 0xC000012F, "STATUS_INVALID_IMAGE_NOT_MZ" },
    { 0xC0000130, "STATUS_INVALID_IMAGE_PROTECT" },
    { 0xC0000131, "STATUS_INVALID_IMAGE_WIN_16" },
    { 0xC0000132, "STATUS_LOGON_SERVER_CONFLICT" },
    { 0xC0000133, "STATUS_TIME_DIFFERENCE_AT_DC" },
    { 0xC0000134, "STATUS_SYNCHRONIZATION_REQUIRED" },
    { 0xC0000135, "STATUS_DLL_NOT_FOUND" },
    { 0xC0000136, "STATUS_OPEN_FAILED" },
    { 0xC0000137, "STATUS_IO_PRIVILEGE_FAILED" },
    { 0xC0000138, "STATUS_ORDINAL_NOT_FOUND" },
    { 0xC0000139, "STATUS_ENTRYPOINT_NOT_FOUND" },
    { 0xC000013A, "STATUS_CONTROL_C_EXIT" },
    { 0xC000013B, "STATUS_LOCAL_DISCONNECT" },
    { 0xC000013C, "STATUS_REMOTE_DISCONNECT" },
    { 0xC000013D, "STATUS_REMOTE_RESOURCES" },
    { 0xC000013E, "STATUS_LINK_FAILED" },
    { 0xC000013F, "STATUS_LINK_TIMEOUT" },
    { 0xC0000140, "STATUS_INVALID_CONNECTION" },
    { 0xC0000141, "STATUS_INVALID_ADDRESS" },
    { 0xC0000142, "STATUS_DLL_INIT_FAILED" },
    { 0xC0000143, "STATUS_MISSING_SYSTEMFILE" },
    { 0xC0000144, "STATUS_UNHANDLED_EXCEPTION" },
    { 0xC0000145, "STATUS_APP_INIT_FAILURE" },
    { 0xC0000146, "STATUS_PAGEFILE_CREATE_FAILED" },
    { 0xC0000147, "STATUS_NO_PAGEFILE" },
    { 0xC0000148, "STATUS_INVALID_LEVEL" },
    { 0xC0000149, "STATUS_WRONG_PASSWORD_CORE" },
    { 0xC000014A, "STATUS_ILLEGAL_FLOAT_CONTEXT" },
    { 0xC000014B, "STATUS_PIPE_BROKEN" },
    { 0xC000014C, "STATUS_REGISTRY_CORRUPT" },
    { 0xC000014D, "STATUS_REGISTRY_IO_FAILED" },
    { 0xC000014E, "STATUS_NO_EVENT_PAIR" },
    { 0xC000014F, "STATUS_UNRECOGNIZED_VOLUME" },
    { 0xC0000150, "STATUS_SERIAL_NO_DEVICE_INITED" },
    { 0xC0000151, "STATUS_NO_SUCH_ALIAS" },
    { 0xC0000152, "STATUS_MEMBER_NOT_IN_ALIAS" },
    { 0xC0000153, "STATUS_MEMBER_IN_ALIAS" },
    { 0xC0000154, "STATUS_ALIAS_EXISTS" },
    { 0xC0000155, "STATUS_LOGON_NOT_GRANTED" },
    { 0xC0000156, "STATUS_TOO_MANY_SECRETS" },
    { 0xC0000157, "STATUS_SECRET_TOO_LONG" },
    { 0xC0000158, "STATUS_INTERNAL_DB_ERROR" },
    { 0xC0000159, "STATUS_FULLSCREEN_MODE" },
    { 0xC000015A, "STATUS_TOO_MANY_CONTEXT_IDS" },
    { 0xC000015B, "STATUS_LOGON_TYPE_NOT_GRANTED" },
    { 0xC000015C, "STATUS_NOT_REGISTRY_FILE" },
    { 0xC000015D, "STATUS_NT_CROSS_ENCRYPTION_REQUIRED" },
    { 0xC000015E, "STATUS_DOMAIN_CTRLR_CONFIG_ERROR" },
    { 0xC000015F, "STATUS_FT_MISSING_MEMBER" },
    { 0xC0000160, "STATUS_ILL_FORMED_SERVICE_ENTRY" },
    { 0xC0000161, "STATUS_ILLEGAL_CHARACTER" },
    { 0xC0000162, "STATUS_UNMAPPABLE_CHARACTER" },
    { 0xC0000163, "STATUS_UNDEFINED_CHARACTER" },
    { 0xC0000164, "STATUS_FLOPPY_VOLUME" },
    { 0xC0000165, "STATUS_FLOPPY_ID_MARK_NOT_FOUND" },
    { 0xC0000166, "STATUS_FLOPPY_WRONG_CYLINDER" },
    { 0xC0000167, "STATUS_FLOPPY_UNKNOWN_ERROR" },
    { 0xC0000168, "STATUS_FLOPPY_BAD_REGISTERS" },
    { 0xC0000169, "STATUS_DISK_RECALIBRATE_FAILED" },
    { 0xC000016A, "STATUS_DISK_OPERATION_FAILED" },
    { 0xC000016B, "STATUS_DISK_RESET_FAILED" },
    { 0xC000016C, "STATUS_SHARED_IRQ_BUSY" },
    { 0xC000016D, "STATUS_FT_ORPHANING" },
    { 0xC000016E, "STATUS_BIOS_FAILED_TO_CONNECT_INTERRUPT" },
    { 0xC0000172, "STATUS_PARTITION_FAILURE" },
    { 0xC0000173, "STATUS_INVALID_BLOCK_LENGTH" },
    { 0xC0000174, "STATUS_DEVICE_NOT_PARTITIONED" },
    { 0xC0000175, "STATUS_UNABLE_TO_LOCK_MEDIA" },
    { 0xC0000176, "STATUS_UNABLE_TO_UNLOAD_MEDIA" },
    { 0xC0000177, "STATUS_EOM_OVERFLOW" },
    { 0xC0000178, "STATUS_NO_MEDIA" },
    { 0xC000017A, "STATUS_NO_SUCH_MEMBER" },
    { 0xC000017B, "STATUS_INVALID_MEMBER" },
    { 0xC000017C, "STATUS_KEY_DELETED" },
    { 0xC000017D, "STATUS_NO_LOG_SPACE" },
    { 0xC000017E, "STATUS_TOO_MANY_SIDS" },
    { 0xC000017F, "STATUS_LM_CROSS_ENCRYPTION_REQUIRED" },
    { 0xC0000180, "STATUS_KEY_HAS_CHILDREN" },
    { 0xC0000181, "STATUS_CHILD_MUST_BE_VOLATILE" },
    { 0xC0000182, "STATUS_DEVICE_CONFIGURATION_ERROR" },
    { 0xC0000183, "STATUS_DRIVER_INTERNAL_ERROR" },
    { 0xC0000184, "STATUS_INVALID_DEVICE_STATE" },
    { 0xC0000185, "STATUS_IO_DEVICE_ERROR" },
    { 0xC0000186, "STATUS_DEVICE_PROTOCOL_ERROR" },
    { 0xC0000187, "STATUS_BACKUP_CONTROLLER" },
    { 0xC0000188, "STATUS_LOG_FILE_FULL" },
    { 0xC0000189, "STATUS_TOO_LATE" },
    { 0xC000018A, "STATUS_NO_TRUST_LSA_SECRET" },
    { 0xC000018B, "STATUS_NO_TRUST_SAM_ACCOUNT" },
    { 0xC000018C, "STATUS_TRUSTED_DOMAIN_FAILURE" },
    { 0xC000018D, "STATUS_TRUSTED_RELATIONSHIP_FAILURE" },
    { 0xC000018E, "STATUS_EVENTLOG_FILE_CORRUPT" },
    { 0xC000018F, "STATUS_EVENTLOG_CANT_START" },
    { 0xC0000190, "STATUS_TRUST_FAILURE" },
    { 0xC0000191, "STATUS_MUTANT_LIMIT_EXCEEDED" },
    { 0xC0000192, "STATUS_NETLOGON_NOT_STARTED" },
    { 0xC0000193, "STATUS_ACCOUNT_EXPIRED" },
    { 0xC0000194, "STATUS_POSSIBLE_DEADLOCK" },
    { 0xC0000195, "STATUS_NETWORK_CREDENTIAL_CONFLICT" },
    { 0xC0000196, "STATUS_REMOTE_SESSION_LIMIT" },
    { 0xC0000197, "STATUS_EVENTLOG_FILE_CHANGED" },
    { 0xC0000198, "STATUS_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT" },
    { 0xC0000199, "STATUS_NOLOGON_WORKSTATION_TRUST_ACCOUNT" },
    { 0xC000019A, "STATUS_NOLOGON_SERVER_TRUST_ACCOUNT" },
    { 0xC000019B, "STATUS_DOMAIN_TRUST_INCONSISTENT" },
    { 0xC000019C, "STATUS_FS_DRIVER_REQUIRED" },
    { 0xC0000202, "STATUS_NO_USER_SESSION_KEY" },
    { 0xC0000203, "STATUS_USER_SESSION_DELETED" },
    { 0xC0000204, "STATUS_RESOURCE_LANG_NOT_FOUND" },
    { 0xC0000205, "STATUS_INSUFF_SERVER_RESOURCES" },
    { 0xC0000206, "STATUS_INVALID_BUFFER_SIZE" },
    { 0xC0000207, "STATUS_INVALID_ADDRESS_COMPONENT" },
    { 0xC0000208, "STATUS_INVALID_ADDRESS_WILDCARD" },
    { 0xC0000209, "STATUS_TOO_MANY_ADDRESSES" },
    { 0xC000020A, "STATUS_ADDRESS_ALREADY_EXISTS" },
    { 0xC000020B, "STATUS_ADDRESS_CLOSED" },
    { 0xC000020C, "STATUS_CONNECTION_DISCONNECTED" },
    { 0xC000020D, "STATUS_CONNECTION_RESET" },
    { 0xC000020E, "STATUS_TOO_MANY_NODES" },
    { 0xC000020F, "STATUS_TRANSACTION_ABORTED" },
    { 0xC0000210, "STATUS_TRANSACTION_TIMED_OUT" },
    { 0xC0000211, "STATUS_TRANSACTION_NO_RELEASE" },
    { 0xC0000212, "STATUS_TRANSACTION_NO_MATCH" },
    { 0xC0000213, "STATUS_TRANSACTION_RESPONDED" },
    { 0xC0000214, "STATUS_TRANSACTION_INVALID_ID" },
    { 0xC0000215, "STATUS_TRANSACTION_INVALID_TYPE" },
    { 0xC0000216, "STATUS_NOT_SERVER_SESSION" },
    { 0xC0000217, "STATUS_NOT_CLIENT_SESSION" },
    { 0xC0000218, "STATUS_CANNOT_LOAD_REGISTRY_FILE" },
    { 0xC0000219, "STATUS_DEBUG_ATTACH_FAILED" },
    { 0xC000021A, "STATUS_SYSTEM_PROCESS_TERMINATED" },
    { 0xC000021B, "STATUS_DATA_NOT_ACCEPTED" },
    { 0xC000021C, "STATUS_NO_BROWSER_SERVERS_FOUND" },
    { 0xC000021D, "STATUS_VDM_HARD_ERROR" },
    { 0xC000021E, "STATUS_DRIVER_CANCEL_TIMEOUT" },
    { 0xC000021F, "STATUS_REPLY_MESSAGE_MISMATCH" },
    { 0xC0000220, "STATUS_MAPPED_ALIGNMENT" },
    { 0xC0000221, "STATUS_IMAGE_CHECKSUM_MISMATCH" },
    { 0xC0000222, "STATUS_LOST_WRITEBEHIND_DATA" },
    { 0xC0000223, "STATUS_CLIENT_SERVER_PARAMETERS_INVALID" },
    { 0xC0000224, "STATUS_PASSWORD_MUST_CHANGE" },
    { 0xC0000225, "STATUS_NOT_FOUND" },
    { 0xC0000226, "STATUS_NOT_TINY_STREAM" },
    { 0xC0000227, "STATUS_RECOVERY_FAILURE" },
    { 0xC0000228, "STATUS_STACK_OVERFLOW_READ" },
    { 0xC0000229, "STATUS_FAIL_CHECK" },
    { 0xC000022A, "STATUS_DUPLICATE_OBJECTID" },
    { 0xC000022B, "STATUS_OBJECTID_EXISTS" },
    { 0xC000022C, "STATUS_CONVERT_TO_LARGE" },
    { 0xC000022D, "STATUS_RETRY" },
    { 0xC000022E, "STATUS_FOUND_OUT_OF_SCOPE" },
    { 0xC000022F, "STATUS_ALLOCATE_BUCKET" },
    { 0xC0000230, "STATUS_PROPSET_NOT_FOUND" },
    { 0xC0000231, "STATUS_MARSHALL_OVERFLOW" },
    { 0xC0000232, "STATUS_INVALID_VARIANT" },
    { 0xC0000233, "STATUS_DOMAIN_CONTROLLER_NOT_FOUND" },
    { 0xC0000234, "STATUS_ACCOUNT_LOCKED_OUT" },
    { 0xC0000235, "STATUS_HANDLE_NOT_CLOSABLE" },
    { 0xC0000236, "STATUS_CONNECTION_REFUSED" },
    { 0xC0000237, "STATUS_GRACEFUL_DISCONNECT" },
    { 0xC0000238, "STATUS_ADDRESS_ALREADY_ASSOCIATED" },
    { 0xC0000239, "STATUS_ADDRESS_NOT_ASSOCIATED" },
    { 0xC000023A, "STATUS_CONNECTION_INVALID" },
    { 0xC000023B, "STATUS_CONNECTION_ACTIVE" },
    { 0xC000023C, "STATUS_NETWORK_UNREACHABLE" },
    { 0xC000023D, "STATUS_HOST_UNREACHABLE" },
    { 0xC000023E, "STATUS_PROTOCOL_UNREACHABLE" },
    { 0xC000023F, "STATUS_PORT_UNREACHABLE" },
    { 0xC0000240, "STATUS_REQUEST_ABORTED" },
    { 0xC0000241, "STATUS_CONNECTION_ABORTED" },
    { 0xC0000242, "STATUS_BAD_COMPRESSION_BUFFER" },
    { 0xC0000243, "STATUS_USER_MAPPED_FILE" },
    { 0xC0000244, "STATUS_AUDIT_FAILED" },
    { 0xC0000245, "STATUS_TIMER_RESOLUTION_NOT_SET" },
    { 0xC0000246, "STATUS_CONNECTION_COUNT_LIMIT" },
    { 0xC0000247, "STATUS_LOGIN_TIME_RESTRICTION" },
    { 0xC0000248, "STATUS_LOGIN_WKSTA_RESTRICTION" },
    { 0xC0000249, "STATUS_IMAGE_MP_UP_MISMATCH" },
    { 0xC0000250, "STATUS_INSUFFICIENT_LOGON_INFO" },
    { 0xC0000251, "STATUS_BAD_DLL_ENTRYPOINT" },
    { 0xC0000252, "STATUS_BAD_SERVICE_ENTRYPOINT" },
    { 0xC0000253, "STATUS_LPC_REPLY_LOST" },
    { 0xC0000254, "STATUS_IP_ADDRESS_CONFLICT1" },
    { 0xC0000255, "STATUS_IP_ADDRESS_CONFLICT2" },
    { 0xC0000256, "STATUS_REGISTRY_QUOTA_LIMIT" },
    { 0xC0000257, "STATUS_PATH_NOT_COVERED" },
    { 0xC0000258, "STATUS_NO_CALLBACK_ACTIVE" },
    { 0xC0000259, "STATUS_LICENSE_QUOTA_EXCEEDED" },
    { 0xC000025A, "STATUS_PWD_TOO_SHORT" },
    { 0xC000025B, "STATUS_PWD_TOO_RECENT" },
    { 0xC000025C, "STATUS_PWD_HISTORY_CONFLICT" },
    { 0xC000025E, "STATUS_PLUGPLAY_NO_DEVICE" },
    { 0xC000025F, "STATUS_UNSUPPORTED_COMPRESSION" },
    { 0xC0000260, "STATUS_INVALID_HW_PROFILE" },
    { 0xC0000261, "STATUS_INVALID_PLUGPLAY_DEVICE_PATH" },
    { 0xC0000262, "STATUS_DRIVER_ORDINAL_NOT_FOUND" },
    { 0xC0000263, "STATUS_DRIVER_ENTRYPOINT_NOT_FOUND" },
    { 0xC0000264, "STATUS_RESOURCE_NOT_OWNED" },
    { 0xC0000265, "STATUS_TOO_MANY_LINKS" },
    { 0xC0000266, "STATUS_QUOTA_LIST_INCONSISTENT" },
    { 0xC0000267, "STATUS_FILE_IS_OFFLINE" },
    { 0xC0000268, "STATUS_EVALUATION_EXPIRATION" },
    { 0xC0000269, "STATUS_ILLEGAL_DLL_RELOCATION" },
    { 0xC000026A, "STATUS_LICENSE_VIOLATION" },
    { 0xC000026B, "STATUS_DLL_INIT_FAILED_LOGOFF" },
    { 0xC000026C, "STATUS_DRIVER_UNABLE_TO_LOAD" },
    { 0xC000026D, "STATUS_DFS_UNAVAILABLE" },
    { 0xC000026E, "STATUS_VOLUME_DISMOUNTED" },
    { 0xC000026F, "STATUS_WX86_INTERNAL_ERROR" },
    { 0xC0000270, "STATUS_WX86_FLOAT_STACK_CHECK" },
    { 0xC0000271, "STATUS_VALIDATE_CONTINUE" },
    { 0xC0000272, "STATUS_NO_MATCH" },
    { 0xC0000273, "STATUS_NO_MORE_MATCHES" },
    { 0xC0000275, "STATUS_NOT_A_REPARSE_POINT" },
    { 0xC0000276, "STATUS_IO_REPARSE_TAG_INVALID" },
    { 0xC0000277, "STATUS_IO_REPARSE_TAG_MISMATCH" },
    { 0xC0000278, "STATUS_IO_REPARSE_DATA_INVALID" },
    { 0xC0000279, "STATUS_IO_REPARSE_TAG_NOT_HANDLED" },
    { 0xC0000280, "STATUS_REPARSE_POINT_NOT_RESOLVED" },
    { 0xC0000281, "STATUS_DIRECTORY_IS_A_REPARSE_POINT" },
    { 0xC0000282, "STATUS_RANGE_LIST_CONFLICT" },
    { 0xC0000283, "STATUS_SOURCE_ELEMENT_EMPTY" },
    { 0xC0000284, "STATUS_DESTINATION_ELEMENT_FULL" },
    { 0xC0000285, "STATUS_ILLEGAL_ELEMENT_ADDRESS" },
    { 0xC0000286, "STATUS_MAGAZINE_NOT_PRESENT" },
    { 0xC0000287, "STATUS_REINITIALIZATION_NEEDED" },
    { 0xC000028A, "STATUS_ENCRYPTION_FAILED" },
    { 0xC000028B, "STATUS_DECRYPTION_FAILED" },
    { 0xC000028C, "STATUS_RANGE_NOT_FOUND" },
    { 0xC000028D, "STATUS_NO_RECOVERY_POLICY" },
    { 0xC000028E, "STATUS_NO_EFS" },
    { 0xC000028F, "STATUS_WRONG_EFS" },
    { 0xC0000290, "STATUS_NO_USER_KEYS" },
    { 0xC0000291, "STATUS_FILE_NOT_ENCRYPTED" },
    { 0xC0000292, "STATUS_NOT_EXPORT_FORMAT" },
    { 0xC0000293, "STATUS_FILE_ENCRYPTED" },
    { 0xC0000295, "STATUS_WMI_GUID_NOT_FOUND" },
    { 0xC0000296, "STATUS_WMI_INSTANCE_NOT_FOUND" },
    { 0xC0000297, "STATUS_WMI_ITEMID_NOT_FOUND" },
    { 0xC0000298, "STATUS_WMI_TRY_AGAIN" },
    { 0xC0000299, "STATUS_SHARED_POLICY" },
    { 0xC000029A, "STATUS_POLICY_OBJECT_NOT_FOUND" },
    { 0xC000029B, "STATUS_POLICY_ONLY_IN_DS" },
    { 0xC000029C, "STATUS_VOLUME_NOT_UPGRADED" },
    { 0xC000029D, "STATUS_REMOTE_STORAGE_NOT_ACTIVE" },
    { 0xC000029E, "STATUS_REMOTE_STORAGE_MEDIA_ERROR" },
    { 0xC000029F, "STATUS_NO_TRACKING_SERVICE" },
    { 0xC00002A0, "STATUS_SERVER_SID_MISMATCH" },
    { 0xC00002A1, "STATUS_DS_NO_ATTRIBUTE_OR_VALUE" },
    { 0xC00002A2, "STATUS_DS_INVALID_ATTRIBUTE_SYNTAX" },
    { 0xC00002A3, "STATUS_DS_ATTRIBUTE_TYPE_UNDEFINED" },
    { 0xC00002A4, "STATUS_DS_ATTRIBUTE_OR_VALUE_EXISTS" },
    { 0xC00002A5, "STATUS_DS_BUSY" },
    { 0xC00002A6, "STATUS_DS_UNAVAILABLE" },
    { 0xC00002A7, "STATUS_DS_NO_RIDS_ALLOCATED" },
    { 0xC00002A8, "STATUS_DS_NO_MORE_RIDS" },
    { 0xC00002A9, "STATUS_DS_INCORRECT_ROLE_OWNER" },
    { 0xC00002AA, "STATUS_DS_RIDMGR_INIT_ERROR" },
    { 0xC00002AB, "STATUS_DS_OBJ_CLASS_VIOLATION" },
    { 0xC00002AC, "STATUS_DS_CANT_ON_NON_LEAF" },
    { 0xC00002AD, "STATUS_DS_CANT_ON_RDN" },
    { 0xC00002AE, "STATUS_DS_CANT_MOD_OBJ_CLASS" },
    { 0xC00002AF, "STATUS_DS_CROSS_DOM_MOVE_FAILED" },
    { 0xC00002B0, "STATUS_DS_GC_NOT_AVAILABLE" },
    { 0xC00002B1, "STATUS_DIRECTORY_SERVICE_REQUIRED" },
    { 0xC00002B2, "STATUS_REPARSE_ATTRIBUTE_CONFLICT" },
    { 0xC00002B3, "STATUS_CANT_ENABLE_DENY_ONLY" },
    { 0xC00002B4, "STATUS_FLOAT_MULTIPLE_FAULTS" },
    { 0xC00002B5, "STATUS_FLOAT_MULTIPLE_TRAPS" },
    { 0xC00002B6, "STATUS_DEVICE_REMOVED" },
    { 0xC00002B7, "STATUS_JOURNAL_DELETE_IN_PROGRESS" },
    { 0xC00002B8, "STATUS_JOURNAL_NOT_ACTIVE" },
    { 0xC00002B9, "STATUS_NOINTERFACE" },
    { 0xC00002C1, "STATUS_DS_ADMIN_LIMIT_EXCEEDED" },
    { 0xC00002C2, "STATUS_DRIVER_FAILED_SLEEP" },
    { 0xC00002C3, "STATUS_MUTUAL_AUTHENTICATION_FAILED" },
    { 0xC00002C4, "STATUS_CORRUPT_SYSTEM_FILE" },
    { 0xC00002C5, "STATUS_DATATYPE_MISALIGNMENT_ERROR" },
    { 0xC00002C6, "STATUS_WMI_READ_ONLY" },
    { 0xC00002C7, "STATUS_WMI_SET_FAILURE" },
    { 0xC00002C8, "STATUS_COMMITMENT_MINIMUM" },
    { 0xC00002C9, "STATUS_REG_NAT_CONSUMPTION" },
    { 0xC00002CA, "STATUS_TRANSPORT_FULL" },
    { 0xC00002CB, "STATUS_DS_SAM_INIT_FAILURE" },
    { 0xC00002CC, "STATUS_ONLY_IF_CONNECTED" },
    { 0xC00002CD, "STATUS_DS_SENSITIVE_GROUP_VIOLATION" },
    { 0xC00002CE, "STATUS_PNP_RESTART_ENUMERATION" },
    { 0xC00002CF, "STATUS_JOURNAL_ENTRY_DELETED" },
    { 0xC00002D0, "STATUS_DS_CANT_MOD_PRIMARYGROUPID" },
    { 0xC00002D1, "STATUS_SYSTEM_IMAGE_BAD_SIGNATURE" },
    { 0xC00002D2, "STATUS_PNP_REBOOT_REQUIRED" },
    { 0xC00002D3, "STATUS_POWER_STATE_INVALID" },
    { 0xC00002D4, "STATUS_DS_INVALID_GROUP_TYPE" },
    { 0xC00002D5, "STATUS_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN" },
    { 0xC00002D6, "STATUS_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN" },
    { 0xC00002D7, "STATUS_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER" },
    { 0xC00002D8, "STATUS_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER" },
    { 0xC00002D9, "STATUS_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER" },
    { 0xC00002DA, "STATUS_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER" },
    { 0xC00002DB, "STATUS_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER" },
    { 0xC00002DC, "STATUS_DS_HAVE_PRIMARY_MEMBERS" },
    { 0xC00002DD, "STATUS_WMI_NOT_SUPPORTED" },
    { 0xC00002DE, "STATUS_INSUFFICIENT_POWER" },
    { 0xC00002DF, "STATUS_SAM_NEED_BOOTKEY_PASSWORD" },
    { 0xC00002E0, "STATUS_SAM_NEED_BOOTKEY_FLOPPY" },
    { 0xC00002E1, "STATUS_DS_CANT_START" },
    { 0xC00002E2, "STATUS_DS_INIT_FAILURE" },
    { 0xC00002E3, "STATUS_SAM_INIT_FAILURE" },
    { 0xC00002E4, "STATUS_DS_GC_REQUIRED" },
    { 0xC00002E5, "STATUS_DS_LOCAL_MEMBER_OF_LOCAL_ONLY" },
    { 0xC00002E6, "STATUS_DS_NO_FPO_IN_UNIVERSAL_GROUPS" },
    { 0xC00002E7, "STATUS_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED" },
    { 0xC00002E8, "STATUS_MULTIPLE_FAULT_VIOLATION" },
    { 0xC0000300, "STATUS_NOT_SUPPORTED_ON_SBS" },
    { 0xC000035C, "STATUS_NETWORK_SESSION_EXPIRED" },
    { 0xC0009898, "STATUS_WOW_ASSERTION" },
    { 0xC0020001, "RPC_NT_INVALID_STRING_BINDING" },
    { 0xC0020002, "RPC_NT_WRONG_KIND_OF_BINDING" },
    { 0xC0020003, "RPC_NT_INVALID_BINDING" },
    { 0xC0020004, "RPC_NT_PROTSEQ_NOT_SUPPORTED" },
    { 0xC0020005, "RPC_NT_INVALID_RPC_PROTSEQ" },
    { 0xC0020006, "RPC_NT_INVALID_STRING_UUID" },
    { 0xC0020007, "RPC_NT_INVALID_ENDPOINT_FORMAT" },
    { 0xC0020008, "RPC_NT_INVALID_NET_ADDR" },
    { 0xC0020009, "RPC_NT_NO_ENDPOINT_FOUND" },
    { 0xC002000A, "RPC_NT_INVALID_TIMEOUT" },
    { 0xC002000B, "RPC_NT_OBJECT_NOT_FOUND" },
    { 0xC002000C, "RPC_NT_ALREADY_REGISTERED" },
    { 0xC002000D, "RPC_NT_TYPE_ALREADY_REGISTERED" },
    { 0xC002000E, "RPC_NT_ALREADY_LISTENING" },
    { 0xC002000F, "RPC_NT_NO_PROTSEQS_REGISTERED" },
    { 0xC0020010, "RPC_NT_NOT_LISTENING" },
    { 0xC0020011, "RPC_NT_UNKNOWN_MGR_TYPE" },
    { 0xC0020012, "RPC_NT_UNKNOWN_IF" },
    { 0xC0020013, "RPC_NT_NO_BINDINGS" },
    { 0xC0020014, "RPC_NT_NO_PROTSEQS" },
    { 0xC0020015, "RPC_NT_CANT_CREATE_ENDPOINT" },
    { 0xC0020016, "RPC_NT_OUT_OF_RESOURCES" },
    { 0xC0020017, "RPC_NT_SERVER_UNAVAILABLE" },
    { 0xC0020018, "RPC_NT_SERVER_TOO_BUSY" },
    { 0xC0020019, "RPC_NT_INVALID_NETWORK_OPTIONS" },
    { 0xC002001A, "RPC_NT_NO_CALL_ACTIVE" },
    { 0xC002001B, "RPC_NT_CALL_FAILED" },
    { 0xC002001C, "RPC_NT_CALL_FAILED_DNE" },
    { 0xC002001D, "RPC_NT_PROTOCOL_ERROR" },
    { 0xC002001F, "RPC_NT_UNSUPPORTED_TRANS_SYN" },
    { 0xC0020021, "RPC_NT_UNSUPPORTED_TYPE" },
    { 0xC0020022, "RPC_NT_INVALID_TAG" },
    { 0xC0020023, "RPC_NT_INVALID_BOUND" },
    { 0xC0020024, "RPC_NT_NO_ENTRY_NAME" },
    { 0xC0020025, "RPC_NT_INVALID_NAME_SYNTAX" },
    { 0xC0020026, "RPC_NT_UNSUPPORTED_NAME_SYNTAX" },
    { 0xC0020028, "RPC_NT_UUID_NO_ADDRESS" },
    { 0xC0020029, "RPC_NT_DUPLICATE_ENDPOINT" },
    { 0xC002002A, "RPC_NT_UNKNOWN_AUTHN_TYPE" },
    { 0xC002002B, "RPC_NT_MAX_CALLS_TOO_SMALL" },
    { 0xC002002C, "RPC_NT_STRING_TOO_LONG" },
    { 0xC002002D, "RPC_NT_PROTSEQ_NOT_FOUND" },
    { 0xC002002E, "RPC_NT_PROCNUM_OUT_OF_RANGE" },
    { 0xC002002F, "RPC_NT_BINDING_HAS_NO_AUTH" },
    { 0xC0020030, "RPC_NT_UNKNOWN_AUTHN_SERVICE" },
    { 0xC0020031, "RPC_NT_UNKNOWN_AUTHN_LEVEL" },
    { 0xC0020032, "RPC_NT_INVALID_AUTH_IDENTITY" },
    { 0xC0020033, "RPC_NT_UNKNOWN_AUTHZ_SERVICE" },
    { 0xC0020034, "EPT_NT_INVALID_ENTRY" },
    { 0xC0020035, "EPT_NT_CANT_PERFORM_OP" },
    { 0xC0020036, "EPT_NT_NOT_REGISTERED" },
    { 0xC0020037, "RPC_NT_NOTHING_TO_EXPORT" },
    { 0xC0020038, "RPC_NT_INCOMPLETE_NAME" },
    { 0xC0020039, "RPC_NT_INVALID_VERS_OPTION" },
    { 0xC002003A, "RPC_NT_NO_MORE_MEMBERS" },
    { 0xC002003B, "RPC_NT_NOT_ALL_OBJS_UNEXPORTED" },
    { 0xC002003C, "RPC_NT_INTERFACE_NOT_FOUND" },
    { 0xC002003D, "RPC_NT_ENTRY_ALREADY_EXISTS" },
    { 0xC002003E, "RPC_NT_ENTRY_NOT_FOUND" },
    { 0xC002003F, "RPC_NT_NAME_SERVICE_UNAVAILABLE" },
    { 0xC0020040, "RPC_NT_INVALID_NAF_ID" },
    { 0xC0020041, "RPC_NT_CANNOT_SUPPORT" },
    { 0xC0020042, "RPC_NT_NO_CONTEXT_AVAILABLE" },
    { 0xC0020043, "RPC_NT_INTERNAL_ERROR" },
    { 0xC0020044, "RPC_NT_ZERO_DIVIDE" },
    { 0xC0020045, "RPC_NT_ADDRESS_ERROR" },
    { 0xC0020046, "RPC_NT_FP_DIV_ZERO" },
    { 0xC0020047, "RPC_NT_FP_UNDERFLOW" },
    { 0xC0020048, "RPC_NT_FP_OVERFLOW" },
    { 0xC0020049, "RPC_NT_CALL_IN_PROGRESS" },
    { 0xC002004A, "RPC_NT_NO_MORE_BINDINGS" },
    { 0xC002004B, "RPC_NT_GROUP_MEMBER_NOT_FOUND" },
    { 0xC002004C, "EPT_NT_CANT_CREATE" },
    { 0xC002004D, "RPC_NT_INVALID_OBJECT" },
    { 0xC002004F, "RPC_NT_NO_INTERFACES" },
    { 0xC0020050, "RPC_NT_CALL_CANCELLED" },
    { 0xC0020051, "RPC_NT_BINDING_INCOMPLETE" },
    { 0xC0020052, "RPC_NT_COMM_FAILURE" },
    { 0xC0020053, "RPC_NT_UNSUPPORTED_AUTHN_LEVEL" },
    { 0xC0020054, "RPC_NT_NO_PRINC_NAME" },
    { 0xC0020055, "RPC_NT_NOT_RPC_ERROR" },
    { 0xC0020057, "RPC_NT_SEC_PKG_ERROR" },
    { 0xC0020058, "RPC_NT_NOT_CANCELLED" },
    { 0xC0021007, "RPC_P_RECEIVE_ALERTED" },
    { 0xC0021008, "RPC_P_CONNECTION_CLOSED" },
    { 0xC0021009, "RPC_P_RECEIVE_FAILED" },
    { 0xC002100A, "RPC_P_SEND_FAILED" },
    { 0xC002100B, "RPC_P_TIMEOUT" },
    { 0xC002100C, "RPC_P_SERVER_TRANSPORT_ERROR" },
    { 0xC002100E, "RPC_P_EXCEPTION_OCCURED" },
    { 0xC0021012, "RPC_P_CONNECTION_SHUTDOWN" },
    { 0xC0021015, "RPC_P_THREAD_LISTENING" },
    { 0xC0030001, "RPC_NT_NO_MORE_ENTRIES" },
    { 0xC0030002, "RPC_NT_SS_CHAR_TRANS_OPEN_FAIL" },
    { 0xC0030003, "RPC_NT_SS_CHAR_TRANS_SHORT_FILE" },
    { 0xC0030004, "RPC_NT_SS_IN_NULL_CONTEXT" },
    { 0xC0030005, "RPC_NT_SS_CONTEXT_MISMATCH" },
    { 0xC0030006, "RPC_NT_SS_CONTEXT_DAMAGED" },
    { 0xC0030007, "RPC_NT_SS_HANDLES_MISMATCH" },
    { 0xC0030008, "RPC_NT_SS_CANNOT_GET_CALL_HANDLE" },
    { 0xC0030009, "RPC_NT_NULL_REF_POINTER" },
    { 0xC003000A, "RPC_NT_ENUM_VALUE_OUT_OF_RANGE" },
    { 0xC003000B, "RPC_NT_BYTE_COUNT_TOO_SMALL" },
    { 0xC003000C, "RPC_NT_BAD_STUB_DATA" },
    { 0xC0030059, "RPC_NT_INVALID_ES_ACTION" },
    { 0xC003005A, "RPC_NT_WRONG_ES_VERSION" },
    { 0xC003005B, "RPC_NT_WRONG_STUB_VERSION" },
    { 0xC003005C, "RPC_NT_INVALID_PIPE_OBJECT" },
    { 0xC003005D, "RPC_NT_INVALID_PIPE_OPERATION" },
    { 0xC003005E, "RPC_NT_WRONG_PIPE_VERSION" },
    { 0xC05C0000, "STATUS_SVHDX_ERROR_STORED" },
    { 0xC05CFF00, "STATUS_SVHDX_ERROR_NOT_AVAILABLE" },
    { 0xC05CFF01, "STATUS_SVHDX_UNIT_ATTENTION_AVAILABLE" },
    { 0xC05CFF02, "STATUS_SVHDX_UNIT_ATTENTION_CAPACITY_DATA_CHANGED" },
    { 0xC05CFF03, "STATUS_SVHDX_UNIT_ATTENTION_RESERVATIONS_PREEMPTED" },
    { 0xC05CFF04, "STATUS_SVHDX_UNIT_ATTENTION_RESERVATIONS_RELEASED" },
    { 0xC05CFF05, "STATUS_SVHDX_UNIT_ATTENTION_REGISTRATIONS_PREEMPTED" },
    { 0xC05CFF06, "STATUS_SVHDX_UNIT_ATTENTION_OPERATING_DEFINITION_CHANGED" },
    { 0xC05CFF07, "STATUS_SVHDX_RESERVATION_CONFLICT" },
    { 0xC05CFF08, "STATUS_SVHDX_WRONG_FILE_TYPE" },
    { 0xC05CFF09, "STATUS_SVHDX_VERSION_MISMATCH" },
    { 0xC05CFF0A, "STATUS_VHD_SHARED" },
    { 0,          NULL }
};

//todo .... smb邮槽协议：基于udp的报文中目前只支持固定长度为82字节的 netBIOS 协议(前82字节)
#define SMB_UDP_NETBIOS_LEN 82

#define SMB_FLAGS_DIRN                  0x80
#define SMB_COM_CREATE_DIRECTORY        0x00
#define SMB_COM_DELETE_DIRECTORY        0x01
#define SMB_COM_OPEN                    0x02
#define SMB_COM_CREATE                  0x03
#define SMB_COM_CLOSE                   0x04
#define SMB_COM_FLUSH                   0x05
#define SMB_COM_DELETE                  0x06
#define SMB_COM_RENAME                  0x07
#define SMB_COM_QUERY_INFORMATION        0x08
#define SMB_COM_SET_INFORMATION            0x09
#define SMB_COM_READ                    0x0A
#define SMB_COM_WRITE                   0x0B
#define SMB_COM_LOCK_BYTE_RANGE            0x0C
#define SMB_COM_UNLOCK_BYTE_RANGE        0x0D
#define SMB_COM_CREATE_TEMPORARY        0x0E
#define SMB_COM_CREATE_NEW              0x0F
#define SMB_COM_CHECK_DIRECTORY            0x10
#define SMB_COM_PROCESS_EXIT            0x11
#define SMB_COM_SEEK                    0x12
#define SMB_COM_LOCK_AND_READ            0x13
#define SMB_COM_WRITE_AND_UNLOCK        0x14
#define SMB_COM_READ_RAW                0x1A
#define SMB_COM_READ_MPX                0x1B
#define SMB_COM_READ_MPX_SECONDARY        0x1C
#define SMB_COM_WRITE_RAW               0x1D
#define SMB_COM_WRITE_MPX               0x1E
#define SMB_COM_WRITE_MPX_SECONDARY        0x1F
#define SMB_COM_WRITE_COMPLETE            0x20
#define SMB_COM_QUERY_SERVER            0x21
#define SMB_COM_SET_INFORMATION2        0x22
#define SMB_COM_QUERY_INFORMATION2        0x23
#define SMB_COM_LOCKING_ANDX            0x24
#define SMB_COM_TRANSACTION             0x25
#define SMB_COM_TRANSACTION_SECONDARY    0x26
#define SMB_COM_IOCTL                   0x27
#define SMB_COM_IOCTL_SECONDARY            0x28
#define SMB_COM_COPY                    0x29
#define SMB_COM_MOVE                    0x2A
#define SMB_COM_ECHO                    0x2B
#define SMB_COM_WRITE_AND_CLOSE            0x2C
#define SMB_COM_OPEN_ANDX               0x2D
#define SMB_COM_READ_ANDX               0x2E
#define SMB_COM_WRITE_ANDX              0x2F
#define SMB_COM_NEW_FILE_SIZE            0x30
#define SMB_COM_CLOSE_AND_TREE_DISC        0x31
#define SMB_COM_TRANSACTION2            0x32
#define SMB_COM_TRANSACTION2_SECONDARY    0x33
#define SMB_COM_FIND_CLOSE2             0x34
#define SMB_COM_FIND_NOTIFY_CLOSE        0x35
/* Used by Xenix/Unix        0x60-0x6E */
#define SMB_COM_TREE_CONNECT            0x70
#define SMB_COM_TREE_DISCONNECT            0x71
#define SMB_COM_NEGOTIATE               0x72
#define SMB_COM_SESSION_SETUP_ANDX        0x73
#define SMB_COM_LOGOFF_ANDX             0x74
#define SMB_COM_TREE_CONNECT_ANDX        0x75
#define SMB_COM_QUERY_INFORMATION_DISK    0x80
#define SMB_COM_SEARCH                  0x81
#define SMB_COM_FIND                    0x82
#define SMB_COM_FIND_UNIQUE             0x83
#define SMB_COM_FIND_CLOSE              0x84
#define SMB_COM_NT_TRANSACT             0xA0
#define SMB_COM_NT_TRANSACT_SECONDARY    0xA1
#define SMB_COM_NT_CREATE_ANDX            0xA2
#define SMB_COM_NT_CANCEL               0xA4
#define SMB_COM_NT_RENAME               0xA5
#define SMB_COM_OPEN_PRINT_FILE            0xC0
#define SMB_COM_WRITE_PRINT_FILE        0xC1
#define SMB_COM_CLOSE_PRINT_FILE        0xC2
#define SMB_COM_GET_PRINT_QUEUE            0xC3
#define SMB_COM_READ_BULK               0xD8
#define SMB_COM_WRITE_BULK              0xD9
#define SMB_COM_WRITE_BULK_DATA            0xDA

#define SERVER_CAP_RAW_MODE             0x00000001
#define SERVER_CAP_MPX_MODE             0x00000002
#define SERVER_CAP_UNICODE              0x00000004
#define SERVER_CAP_LARGE_FILES          0x00000008
#define SERVER_CAP_NT_SMBS              0x00000010
#define SERVER_CAP_RPC_REMOTE_APIS      0x00000020
#define SERVER_CAP_STATUS32             0x00000040
#define SERVER_CAP_LEVEL_II_OPLOCKS     0x00000080
#define SERVER_CAP_LOCK_AND_READ        0x00000100
#define SERVER_CAP_NT_FIND              0x00000200
#define SERVER_CAP_DFS                  0x00001000
#define SERVER_CAP_INFOLEVEL_PASSTHRU   0x00002000
#define SERVER_CAP_LARGE_READX          0x00004000
#define SERVER_CAP_LARGE_WRITEX         0x00008000
#define SERVER_CAP_LWIO                 0x00010000
#define SERVER_CAP_UNIX                 0x00800000
#define SERVER_CAP_COMPRESSED_DATA      0x02000000
#define SERVER_CAP_DYNAMIC_REAUTH       0x20000000
#define SERVER_CAP_EXTENDED_SECURITY    0x80000000

#define NTLMSSP_NEGOTIATE_UNICODE                  0x00000001
#define NTLMSSP_NEGOTIATE_OEM                      0x00000002
#define NTLMSSP_REQUEST_TARGET                     0x00000004
#define NTLMSSP_NEGOTIATE_00000008                 0x00000008
#define NTLMSSP_NEGOTIATE_SIGN                     0x00000010
#define NTLMSSP_NEGOTIATE_SEAL                     0x00000020
#define NTLMSSP_NEGOTIATE_DATAGRAM                 0x00000040
#define NTLMSSP_NEGOTIATE_LM_KEY                   0x00000080
#define NTLMSSP_NEGOTIATE_00000100                 0x00000100
#define NTLMSSP_NEGOTIATE_NTLM                     0x00000200
#define NTLMSSP_NEGOTIATE_NT_ONLY                  0x00000400
#define NTLMSSP_NEGOTIATE_ANONYMOUS                0x00000800
#define NTLMSSP_NEGOTIATE_OEM_DOMAIN_SUPPLIED      0x00001000
#define NTLMSSP_NEGOTIATE_OEM_WORKSTATION_SUPPLIED 0x00002000
#define NTLMSSP_NEGOTIATE_00004000                 0x00004000
#define NTLMSSP_NEGOTIATE_ALWAYS_SIGN              0x00008000
#define NTLMSSP_TARGET_TYPE_DOMAIN                 0x00010000
#define NTLMSSP_TARGET_TYPE_SERVER                 0x00020000
#define NTLMSSP_TARGET_TYPE_SHARE                  0x00040000
#define NTLMSSP_NEGOTIATE_EXTENDED_SECURITY        0x00080000
#define NTLMSSP_NEGOTIATE_IDENTIFY                 0x00100000
#define NTLMSSP_NEGOTIATE_00200000                 0x00200000
#define NTLMSSP_REQUEST_NON_NT_SESSION             0x00400000
#define NTLMSSP_NEGOTIATE_TARGET_INFO              0x00800000
#define NTLMSSP_NEGOTIATE_01000000                 0x01000000
#define NTLMSSP_NEGOTIATE_VERSION                  0x02000000
#define NTLMSSP_NEGOTIATE_04000000                 0x04000000
#define NTLMSSP_NEGOTIATE_08000000                 0x08000000
#define NTLMSSP_NEGOTIATE_10000000                 0x10000000
#define NTLMSSP_NEGOTIATE_128                      0x20000000
#define NTLMSSP_NEGOTIATE_KEY_EXCH                 0x40000000
#define NTLMSSP_NEGOTIATE_56                       0x80000000

#define NTLM_TARGET_INFO_END               0x0000
#define NTLM_TARGET_INFO_NB_COMPUTER_NAME  0x0001
#define NTLM_TARGET_INFO_NB_DOMAIN_NAME    0x0002
#define NTLM_TARGET_INFO_DNS_COMPUTER_NAME 0x0003
#define NTLM_TARGET_INFO_DNS_DOMAIN_NAME   0x0004
#define NTLM_TARGET_INFO_DNS_TREE_NAME     0x0005
#define NTLM_TARGET_INFO_FLAGS             0x0006
#define NTLM_TARGET_INFO_TIMESTAMP         0x0007
#define NTLM_TARGET_INFO_RESTRICTIONS      0x0008
#define NTLM_TARGET_INFO_TARGET_NAME       0x0009
#define NTLM_TARGET_INFO_CHANNEL_BINDINGS  0x000A

#define NTLMSSP_NEGOTIATE   1
#define NTLMSSP_CHALLENGE   2
#define NTLMSSP_AUTH        3
#define NTLMSSP_UNKNOWN     4
#define NTLMSSP_KEY_LEN     16

#define CALL_NAMED_PIPE        0x54
#define WAIT_NAMED_PIPE        0x53
#define PEEK_NAMED_PIPE        0x23
#define Q_NM_P_HAND_STATE    0x21
#define SET_NM_P_HAND_STATE    0x01
#define Q_NM_PIPE_INFO        0x22
#define TRANSACT_NM_PIPE    0x26
#define RAW_READ_NM_PIPE    0x11
#define RAW_WRITE_NM_PIPE    0x31

#define G_GUINT64_CONSTANT(val)    (val##UL)


#define BMT_NO_FLAGS    0x00    /**< Don't use any flags */
#define BMT_NO_APPEND    0x01    /**< Don't change the title at all */
#define BMT_NO_INT      0x02    /**< Don't add integral (non-boolean) fields to title */
#define BMT_NO_FALSE    0x04    /**< Don't add booleans unless they're TRUE */
#define BMT_NO_TFS      0x08    /**< Don't use true_false_string while formatting booleans */

#define BASE_RANGE_STRING       0x0100
#define BASE_EXT_STRING         0x0200
#define BASE_VAL64_STRING       0x0400
#define BASE_ALLOW_ZERO         0x0800  /**< Display <none> instead of <MISSING> for zero sized byte array */
#define BASE_UNIT_STRING        0x1000  /**< Add unit text to the field value */
#define BASE_NO_DISPLAY_VALUE   0x2000  /**< Just display the field name with no value.  Intended for
                                             byte arrays or header fields above a subtree */
#define BASE_PROTOCOL_INFO      0x4000  /**< protocol_t in [FIELDCONVERT].  Internal use only. */
#define BASE_SPECIAL_VALS       0x8000  /**< field will not display "Unknown" if value_string match is not found */

#define ITEM_LABEL_LENGTH    240
#define DIALECTS_LEN        50
#define DIALECTS_NAME_LEN   128

#define NT_TRANS_CREATE        1
#define NT_TRANS_IOCTL        2
#define NT_TRANS_SSD        3
#define NT_TRANS_NOTIFY        4
#define NT_TRANS_RENAME        5
#define NT_TRANS_QSD        6
#define NT_TRANS_GET_USER_QUOTA    7
#define NT_TRANS_SET_USER_QUOTA 8

#define ACL_REVISION_NT4        2
#define ACL_REVISION_ADS        4

#define ACE_TYPE_ACCESS_ALLOWED        0
#define ACE_TYPE_ACCESS_DENIED        1
#define ACE_TYPE_SYSTEM_AUDIT        2
#define ACE_TYPE_SYSTEM_ALARM        3
#define ACE_TYPE_ALLOWED_COMPOUND    4
#define ACE_TYPE_ACCESS_ALLOWED_OBJECT    5
#define ACE_TYPE_ACCESS_DENIED_OBJECT    6
#define ACE_TYPE_SYSTEM_AUDIT_OBJECT    7
#define ACE_TYPE_SYSTEM_ALARM_OBJECT    8
#define ACE_TYPE_ACCESS_ALLOWED_CALLBACK         9
#define ACE_TYPE_ACCESS_DENIED_CALLBACK         10
#define ACE_TYPE_ACCESS_ALLOWED_CALLBACK_OBJECT 11
#define ACE_TYPE_ACCESS_DENIED_CALLBACK_OBJECT  12
#define ACE_TYPE_SYSTEM_AUDIT_CALLBACK          13
#define ACE_TYPE_SYSTEM_ALARM_CALLBACK          14
#define ACE_TYPE_SYSTEM_AUDIT_CALLBACK_OBJECT   15
#define ACE_TYPE_SYSTEM_ALARM_CALLBACK_OBJECT   16
#define ACE_TYPE_SYSTEM_MANDATORY_LABEL         17

#define WORD_COUNT    \
    /* Word Count */                \
    if (-1 == dpi_get_uint8(pkt, offset, &wc)) return offset;        \
    offset += 1;                    \
    if (wc == 0) goto bytecount;

#define BYTE_COUNT    \
    bytecount:                    \
    if (-1 == dpi_get_le16(pkt, offset, &bc)) return offset;        \
    offset += 2;                    \
    if (bc == 0) goto endofcommand;
    
#define CHECK_BYTE_COUNT(len)    \
        if (bc < len) goto endofcommand;
        
#define COUNT_BYTES(len)    {\
                uint32_t tmp;        \
                tmp = len;        \
                offset += tmp;        \
                bc -= tmp;            \
            }
    
#define END_OF_SMB    \
        offset = pkt->payload_len;                \
        endofcommand:

#define CHECK_BYTE_COUNT_TRANS(len)    \
    if (bc < len) return offset;

#define CHECK_STRING_TRANS(fn)    \
    if (fn == NULL) return offset;

#define COUNT_BYTES_TRANS(len)    \
    offset += len;        \
    bc -= len;

#define CHECK_BYTE_COUNT_TRANS_SUBR(len)    \
    if (*bcp < len) return offset;

#define CHECK_STRING_TRANS_SUBR(fn)    \
    if (fn == NULL) return offset;

#define COUNT_BYTES_TRANS_SUBR(len)    \
    offset += len;            \
    *bcp -= len;

enum  smb_index_em{
    EM_SMB_PROTOCOL,
    EM_SMB_COMMAND,
    EM_SMB_NTSTATUS,
    EM_SMB_FLAGS,
    EM_SMB_FLAGS2,
    EM_SMB_PIDHIGH,
    EM_SMB_SIGNATURE,
    EM_SMB_TID,
    EM_SMB_PIDLOW,
    EM_SMB_UID,
    EM_SMB_MID,
    EM_SMB_WORDCOUNT,
    EM_SMB_BYTECOUNT,
    EM_SMB_DIALECTNUMS,
    EM_SMB_DIALECTS,
    EM_SMB_SERVERDIALECTINDEX,
    EM_SMB_SERVERSECURITYMODE,
    EM_SMB_SERVERMAXMPXCOUNT,
    EM_SMB_SERVERMAXNUMBERVCS,
    EM_SMB_SERVERMAXBUFFERSIZE,
    EM_SMB_SERVERMAXRAWSIZE,
    EM_SMB_SERVERSESSIONKEY,
    EM_SMB_SERVERCAPABILITIES,
    EM_SMB_SERVERSYSTEMTIME,
    EM_SMB_SERVERTIMEZONE,
    EM_SMB_SERVERKEYLENGTH,
    EM_SMB_SERVERGUID,
    EM_SMB_SERVERNEGPROTSECURITYBLOB,
    EM_SMB_SERVERENCRYPTIONKEY,
    EM_SMB_SERVERDOMAINNAME,
    EM_SMB_SERVERNAME,
    EM_SMB_NTLMSSPIDENTIFIER,
    EM_SMB_NTLMMESSAGETYPE,
    EM_SMB_CLIENTSESSSETUPXANDXCOMMAND,
    EM_SMB_CLIENTSESSSETUPXANDXOFFSET,
    EM_SMB_CLIENTMAXBUFFERSIZE,
    EM_SMB_CLIENTMAXMPXCOUNT,
    EM_SMB_CLIENTVCNUMBER,
    EM_SMB_CLIENTSESSIONKEY,
    EM_SMB_CLIENTANSIPASSWORDLEN,
    EM_SMB_CLIENTUNICODEPASSWORDLEN,
    EM_SMB_CLIENTSECURITYBLOBLENGTH,
    EM_SMB_CLIENTCAPABILITIES,
    EM_SMB_CLIENTSB_NEGOTIATEFLAGS,
    EM_SMB_CLIENTSB_CALLINGWORKSTATIONDOMAIN,
    EM_SMB_CLIENTSB_CALLINGWORKSTATIONNAME,
    EM_SMB_CLIENTSB_VERSION,
    EM_SMB_CLIENTSB_LANMANAGERRESPONSE,
    EM_SMB_CLIENTSB_NTLMRESPONSE,
    EM_SMB_CLIENTSB_DOMAINNAME,
    EM_SMB_CLIENTSB_USERNAME,
    EM_SMB_CLIENTSB_HOSTNAME,
    EM_SMB_CLIENTSB_SESSIONKEY,
    EM_SMB_CLIENTANSIPASSWORD,
    EM_SMB_CLIENTUNICODEPASSWORD,
    EM_SMB_CLIENTACCOUNTNAME,
    EM_SMB_CLIENTPRIMARYDOMAIN,
    EM_SMB_CLIENTNATIVEOS,
    EM_SMB_CLIENTNATIVELANMAN,
    EM_SMB_SERVERSESSSETUPXANDXCOMMAND,
    EM_SMB_SERVERSESSSETUPXANDXOFFSET,
    EM_SMB_SERVERACTION,
    EM_SMB_SERVERSECURITYBLOBLENGTH,
    EM_SMB_SERVERSB_TARGETNAME,
    EM_SMB_SERVERSB_NEGOTIATEFLAGS,
    EM_SMB_SERVERSB_NTLMSERVERCHALLENGE,
    EM_SMB_SERVERSB_NETBIOSDOMAINNAME,
    EM_SMB_SERVERSB_NETBIOSCOMPUTERNAME,
    EM_SMB_SERVERSB_DNSDOMAINNAME,
    EM_SMB_SERVERSB_DNSCOMPUTERNAME,
    EM_SMB_SERVERSB_TIMESTAMP,
    EM_SMB_SERVERSB_VERSION,
    EM_SMB_SERVERNATIVEOS,
    EM_SMB_SERVERNATIVELANMAN,
    EM_SMB_SERVERPRIMARYDOMAIN,
    EM_SMB_CLIENTTCONXANDXCOMMAND,
    EM_SMB_CLIENTTCONXANDXOFFSET,
    EM_SMB_CLIENTFLAGS,
    EM_SMB_CLIENTPASSWORDLENGTH,
    EM_SMB_CLIENTPASSWORD,
    EM_SMB_CLIENTPATH,
    EM_SMB_CLIENTSERVICE,
    EM_SMB_SERVERTCONXANDXCOMMAND,
    EM_SMB_SERVERTCONXANDXOFFSET,
    EM_SMB_SERVEROPTIONALSUPPORT,
    EM_SMB_MAXIMALSHAREACCESSRIGHTS,
    EM_SMB_GUESTMAXIMALSHAREACCESSRIGHTS,
    EM_SMB_SERVERSERVICE,
    EM_SMB_SERVERNATIVEFILESYSTEM,
    EM_SMB_SERVERSHOREDIRNAME,
    EM_SMB_LOADWAY,
    EM_SMB_FILENAME,
    EM_SMB_FILENAMECNT,
    EM_SMB_PIPENAME,
    EM_SMB_FILEID,
    EM_SMB_MAILSLOTNAME,
    EM_SMB_FILESIZE,
    EM_SMB_FILEATTR,
    EM_SMB_ENDOFFILE,
    EM_SMB_SEARCHATTR,
    EM_SMB_REFERRALVERSION,
    EM_SMB_INFOLEVEL,
    EM_SMB_DIALECTNAME,
    EM_SMB_SMBEXTATTR,
    EM_SMB_AUTH_TYPE,
    EM_SMB_AUTH_PATHPWD,
    EM_SMB_ACCRGHTS,
    EM_SMB_INFOFILENM,
    EM_SMB_STREAMINFO,
    EM_SMB_MAX
};

typedef struct _fragment_item
{
    struct _fragment_item *next;
    uint32_t frame;
    uint32_t offset;
    uint32_t len;
    uint32_t fragment_nr_offset;
    uint32_t datalen;
    uint32_t reassembled_in;
    uint8_t reas_in_layer_num;
    uint32_t flags;
    const char *error;
} fragment_item, fragment_head;

#define MAX_DIALECTS 20
typedef struct negprot_dialects {
    int   num;
    char *name[MAX_DIALECTS+1];
}DIALECTS;

typedef struct _nt_trans_data {
    int     subcmd;
    uint32_t sd_len;
    uint32_t ea_len;
} nt_trans_data;

struct smb_session {
    uint8_t cmd;
    uint16_t tid, pid, uid, mid;
    uint32_t  nt_status;
    uint8_t unicode;        /* Are strings in this SMB Unicode? */
    uint8_t request;        /* Is this a request? */
    uint8_t unidir;
    int info_level;
    int info_count;

    uint32_t ntlmssp_flags;
    //smb_saved_info_t *sip;    /* smb_saved_info_t, if any, for this */
    //conv_tables_t       *ct;
};

typedef struct 
{
    int    subcmd;
    int    fid_type;
    uint32_t ioctl_function;
} smb_nt_transact_info_t;

struct smb_info
{
    const char *Command;
    char NTStatus[64];
    char Flags[128];
    char Flags2[128];
    uint16_t PIDHigh;
    char Signature[32];
    uint16_t TID;
    uint16_t PIDLow;
    uint16_t UID;
    uint16_t MID;
    uint8_t WordCount;
    uint16_t ByteCount;
    char request_dir[256];
    uint8_t DialectNums;
    char Dialects[256];
    uint16_t ServerDialectIndex;
    char ServerSecurityMode[16];
    uint16_t ServerMaxMpxCount;
    uint16_t ServerMaxNumberVcs;
    uint32_t ServerMaxBufferSize;
    uint32_t ServerMaxRawSize;
    char ServerSessionKey[16];
    char ServerCapabilities[16];
    char ServerSystemTime[128];
    char ServerTimeZone[64];
    //ServerKeyLength
    char ServerGUID[64];
    char ServerNegprotSecurityBlob[128];
    //ServerEncryptionKey
    //ServerDomainName
    char ServerName[64];
    //NTLMSSPIdentifier
    //NTLMMessageType
    const char *ClientSesssetupXAndXCommand;
    uint16_t ClientSesssetupXAndXOffset;
    uint16_t ClientMaxBufferSize;
    uint16_t ClientMaxMpxCount;
    uint16_t ClientVcNumber;
    char ClientSessionKey[16];
    uint16_t ClientANSIPasswordLen;
    uint16_t ClientUnicodePasswordLen;
    uint16_t ClientSecurityBlobLength;
    char ClientCapabilities[16];

    char ClientSB_NegotiateFlags[16];
    char ClientSB_Callingworkstationdomain[256];
    char ClientSB_Callingworkstationname[256];
    char ClientSB_Version[64];
    char ClientSB_LanManagerResponse[64];
    char ClientSB_NTLMResponse[64];
    char ClientSB_Domainname[64];
    char ClientSB_Username[64];
    char ClientSB_Hostname[64];
    char ClientSB_SessionKey[64];
    
    char ClientANSIPassword[256];
    char ClientUnicodePassword[256];
    char ClientAccountName[256];
    char ClientPrimaryDomain[256];
    char ClientNativeOS[256];
    char ClientNativeLanMan[256];

    const char *ServerSesssetupXAndXCommand;
    uint16_t ServerSesssetupXAndXOffset;
    const char *ServerAction;
    uint16_t ServerSecurityBlobLength;
    
    char ServerSB_TargetName[64];
    char ServerSB_NegotiateFlags[64];
    char ServerSB_NTLMServerChallenge[64];
    char ServerSB_NetBIOSdomainname[64];
    char ServerSB_NetBIOScomputername[64];
    char ServerSB_DNSdomainname[64];
    char ServerSB_DNScomputername[64];
    char ServerSB_Timestamp[64];
    char ServerSB_Version[64];

    char ServerNativeOS[256];
    char ServerNativeLanMan[256];
    char ServerPrimaryDomain[256];
    
    const char *ClientTConXAndXCommand;
    uint16_t ClientTConXAndXOffset;
    char ClientFlags[16];
    uint16_t ClientPasswordLength;
    char ClientPassword[64];
    char ClientPath[256];
    char ClientService[256];
    const char *ServerTConXAndXCommand;
    uint16_t ServerTConXAndXOffset;
    char ServerOptionalSupport[16];
    char MaximalShareAccessRights[16];
    char GuestMaximalShareAccessRights[16];
    char ServerService[256];
    char ServerNativeFileSystem[256];
    char LoadWay;
    uint32_t FileNameCnt;
    char FileName[256];
    char PipeName[256];
    uint16_t FileId;
    char MailslotName[256];
    uint32_t FileSize;
    uint16_t FileAttr;
    uint64_t end_of_file;
    uint16_t searchAttr;
    uint16_t ReferralVersion;
    uint16_t InfoLevel;
    char DialectName[DIALECTS_NAME_LEN];
    char tmp_dialect_name[DIALECTS_LEN][DIALECTS_NAME_LEN];
    char SmbExtAttr[256];
    uint16_t FileChunkLen;
    uint16_t FileChunkOffset;
	const uint8_t *authtype;
    uint32_t AccessRights;
};

//static int get_unicode_or_ascii_string(struct dpi_pkt_st *pkt, uint32_t *offsetp,
//        uint8_t useunicode, int *len, uint8_t nopad, uint8_t exactlen, uint16_t *bcp,
//        char *result, int max_len);
int smb_compute_offset(const struct dpi_pkt_st* pkt, const int offset, uint32_t *offset_ptr);
uint32_t smb_reported_length_remaining(struct dpi_pkt_st* pkt, const int offset);
static void identify_udp_smb(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);
int dissect_tree_connect_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
int dissect_transaction2_request_parameters(struct dpi_pkt_st *pkt, struct smb_info* pinfo, struct smb_session *si, int offset, int subcmd, uint16_t bc);
int dissect_transaction_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_read_file_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info);
static int dissect_nt_create_andx_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_nt_create_andx_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_transaction_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_delete_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_rename_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_nt_rename_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);
static int dissect_read_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);

static int dissect_nt_64bit_time_ex(struct dpi_pkt_st *pkt, int offset, char *time_str, int len);

void dissect_get_machine_name_response(const uint8_t *payload,struct smb_info* pinfo, const uint32_t payload_len, int offset);

// static void write_smb_log(struct flow_info *flow, int direction, struct smb_info *info);

static int dissect_smb_command(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, uint8_t cmd,
        struct smb_session *si, struct smb_info *info);

#endif /* DPI_SMB_H */
