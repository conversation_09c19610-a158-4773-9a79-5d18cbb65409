﻿/****************************************************************************************
 * 文 件 名 : dpi_forward_datatype.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: hongll           2022/08/12
编码: hongll           2022/08/12
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef _DPI_FORWARD_DATATYPE_H_
#define _DPI_FORWARD_DATATYPE_H_

#include <stdint.h>
#include <stdlib.h>

typedef enum _forward_type {
	FWD_UNKNOWN			= 0x00,			/* 未知类型， 不做处理 */
	FWD_MAC_FRAME		= 0x10,			/* 转发原始MAC帧 */
	FWD_DATA_COLLECT	= 0x20,			/* 转发数据采集，即命中的pcap包 */
	FWD_MASK			= 0xff			/* 掩码 */
} forward_type;

/* 数据采集文件的类型 */
struct collect_field_info {
	uint64_t	DBEGINTIME;				// 创建文件时间戳
	uint8_t     NGROUPNO[64];			// 规则组ID
	uint8_t		SGROUPNAME[128];		// 规则组名称
	uint32_t	LINENO1;				// 全局线路号-类型编号
	uint32_t	LINENO2;				// 全局线路号-光层编号
	uint32_t	LINENO3;				// 全局线路号-电层编号
	uint32_t	LINENO4;				// 全局线路号-时隙位图
	uint8_t		sLineName[96];			// 全局线路号
	uint8_t		TASK_ID[32];			// 任务ID
	uint8_t		SSYSFROM[10];			// 系统来源
	uint32_t	NFILELENGTH;			// 文件大小
	uint8_t		SFILEPATH[256];			// 文件路径
	char		DATAFROM[64];			// 数据来源：ML，KS，ZW
	uint8_t		SIGTYPE;				/* 0x1 : 信号处理节点机
											0x2: IP统计设备
											0x3: 业务解承载设备
											0x4:整体信号分流器
											0x5:通道化信号分流器（高阶通道化）
											0x6:高阶虚级联处理设备（通道化分流器）
											0x7:低阶虚级联处理设备0x8:ATM处理设备
											0x9:DDN处理设备
											0xa－0xf:保留
										*/
	uint8_t		reserved[6];			// 保留

}__attribute__((packed));


typedef struct _St_Ethdr {
	uint8_t dmac[6];//目的MAC
	uint8_t smac[6];//源MAC
	uint16_t protocol;
} Ethdr;


typedef struct DIRECT_IP_CUSTOM_TAIL {
#define TASK_ID_LENGTH  32
	uint8_t		Lineno[16];
	uint8_t		TaskID[TASK_ID_LENGTH];
	uint32_t	TimeSpan;
	uint8_t		Eth_Len;
	uint8_t		Eth_Type;
	uint32_t    Group_ID;
	uint8_t		PacketInfo[40];

}__attribute__((packed)) Direct_IP_Custom_Tail;


#endif
