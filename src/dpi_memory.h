#ifndef YA_DPI_MEMORY_H_
#define YA_DPI_MEMORY_H_

#include <stdint.h>
#include <unistd.h>

#include "dpi_common.h"

/*************** 内存管理器 ********************/
typedef struct MemoryAlloc_  MemoryAlloc;


//内存管理器 -- 创建并初始化接口
MemoryAlloc* alloc_init();

//内存管理器 -- 销毁接口
void alloc_destory(MemoryAlloc *alloc);

//内存管理器 -- 内存申请接口
void* alloc_memory(MemoryAlloc *alloc, int len);

//内存管理器 -- 内存复制接口
void* alloc_memdup(MemoryAlloc *alloc, const void *from, int len);

/*************** 内存管理器 ********************/


MemoryAlloc* get_global_memAc();


#endif // YA_DPI_MEMORY_H_