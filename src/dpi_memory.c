#include "dpi_memory.h"

#include <stdlib.h>
#include <string.h>
#include <assert.h>

#include "dpi_detect.h"

typedef struct MemoryAlloc_ {
    int         mutex;
    int         top;
    int         size;
    void      **space;
} MemoryAlloc;


//内存管理器 -- 创建并初始化接口
MemoryAlloc* alloc_init()
{
    MemoryAlloc *memAc = (MemoryAlloc*)malloc(sizeof(MemoryAlloc));

    if (memAc) {
        memset(memAc, 0, sizeof(MemoryAlloc));
    }

    return memAc;
}


//会话内存管理器 -- 内存释放接口
void  alloc_destory(MemoryAlloc *alloc)
{
    if (NULL == alloc)
        return;

    while(alloc->top)
    {
        free(alloc->space[--alloc->top]);
    }
    free(alloc->space);
    alloc->space = NULL;
    alloc->size = 0;
    alloc->top = 0;
    free(alloc);
}


//会话内存管理器 -- 内存申请接口
void* alloc_memory(MemoryAlloc *alloc, int len)
{
    sdt_atomic_lock(&alloc->mutex);

    //检测是否需要调整SIZE大小
    //第一次时: alloc->top 等于 alloc->size
    if(alloc->top >= alloc->size)
    {
        //Resize 每次递增50
        int resize = alloc->size + 100;
        void **newspace = malloc(sizeof(void*) * resize);

        //初始时, alloc->space 是 NULL, 不会进入
        if(alloc->space)
        {
            memcpy(newspace, alloc->space, (sizeof(void*) * alloc->size));
            free(alloc->space);
        }

        //无缝衔接 -- 完美过度
        alloc->size  = resize;
        alloc->space = newspace;
    }

    void *space = malloc(len);
    alloc->space[alloc->top++] = space;

    sdt_atomic_unlock(&alloc->mutex);

    return space;
}


void* alloc_memdup(MemoryAlloc *alloc, const void *from, int len)
{
    assert(0 != len);
    void *mem = alloc_memory(alloc, len+1);
    memcpy(mem, from, len);
    *(((char*)mem)+len) = '\0';
    return mem;
}


MemoryAlloc* get_global_memAc()
{
    static MemoryAlloc *global_memac = NULL;

    if (global_memac == NULL) {
        global_memac = alloc_init();
    }

    return global_memac;
}