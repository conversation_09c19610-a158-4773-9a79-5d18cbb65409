#ifndef _DPI_B_TREE_H_
#define _DPI_B_TREE_H_

#include "dpi_typedefs.h"

void * ndpi_tsearch(const void *vkey, void **vrootp, int (*compar)(const void *, const void *));
void * ndpi_tfind(const void *vkey, void *vrootp, int (*compar)(const void *, const void *));
void ndpi_twalk(const void *vroot, void (*action)(const void *, ndpi_VISIT, int, void *), void *user_data);
void ndpi_tdestroy(void *vrootp, void (*freefct)(void *));
void * ndpi_tdelete(const void *vkey, void **vrootp, int (*compar)(const void *, const void *));

#endif
