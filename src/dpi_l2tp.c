/****************************************************************************************
 * 文 件 名 : dpi_l2tp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 设计: wangy2018/07/06
 编码: wangy2018/07/06
 修改: licl          20180810
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2018 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

 *****************************************************************************************/

#include <arpa/inet.h>
#include <rte_mbuf.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"

#define UDP_PORT_L2TP   1701
#define PFC_BIT 0x01
#define VALUE_MAX_LEN    40

#define CONTROL_BIT(msg_info)        (msg_info & 0x8000) /* Type bit control = 1 data = 0 */
#define LENGTH_BIT(msg_info)         (msg_info & 0x4000) /* Length bit = 1  */
#define SEQUENCE_BIT(msg_info)       (msg_info & 0x0800) /* SEQUENCE bit = 1 Ns and Nr fields */
#define OFFSET_BIT(msg_info)         (msg_info & 0x0200) /* Offset */
#define PRIORITY_BIT(msg_info)       (msg_info & 0x0100) /* Priority */
#define L2TP_VERSION(msg_info)       (msg_info & 0x000f) /* Version of l2tp */
#define AVP_LENGTH(msg_info)         (msg_info & 0x03ff) /* AVP Length */
#define HIDDEN_BIT(msg_info)         (msg_info & 0x4000) /* Hidden = 1 */


#define  CONTROL_MESSAGE               0
#define  RESULT_ERROR_CODE             1
#define  PROTOCOL_VERSION              2
#define  FRAMING_CAPABILITIES          3
#define  BEARER_CAPABILITIES           4
#define  TIE_BREAKER                   5
#define  FIRMWARE_REVISION             6
#define  HOST_NAME                     7
#define  VENDOR_NAME                   8
#define  ASSIGNED_TUNNEL_ID            9
#define  RECEIVE_WINDOW_SIZE          10
#define  CHALLENGE                    11
#define  CAUSE_CODE                   12
#define  CHALLENGE_RESPONSE           13
#define  ASSIGNED_SESSION             14
#define  CALL_SERIAL_NUMBER           15
#define  MINIMUM_BPS                  16
#define  MAXIMUM_BPS                  17
#define  BEARER_TYPE                  18
#define  FRAMING_TYPE                 19
#define  CALLED_NUMBER                21
#define  CALLING_NUMBER               22
#define  SUB_ADDRESS                  23
#define  TX_CONNECT_SPEED             24
#define  PHYSICAL_CHANNEL             25
#define  INITIAL_RECEIVED_LCP_CONFREQ 26
#define  LAST_SENT_LCP_CONFREQ        27
#define  LAST_RECEIVED_LCP_CONFREQ    28
#define  PROXY_AUTHEN_TYPE            29
#define  PROXY_AUTHEN_NAME            30
#define  PROXY_AUTHEN_CHALLENGE       31
#define  PROXY_AUTHEN_ID              32
#define  PROXY_AUTHEN_RESPONSE        33
#define  CALL_STATUS_AVPS             34
#define  ACCM                         35
#define  RANDOM_VECTOR                36
#define  PRIVATE_GROUP_ID             37
#define  RX_CONNECT_SPEED             38
#define  SEQUENCING_REQUIRED          39
#define  PPP_DISCONNECT_CAUSE_CODE    46    /* RFC 3145 */
#define  EXTENDED_VENDOR_ID           58
#define  MESSAGE_DIGEST               59
#define  ROUTER_ID                    60
#define  ASSIGNED_CONTROL_CONN_ID     61
#define  PW_CAPABILITY_LIST           62
#define  LOCAL_SESSION_ID             63
#define  REMOTE_SESSION_ID            64
#define  ASSIGNED_COOKIE              65
#define  REMOTE_END_ID                66
#define  PW_TYPE                      68
#define  L2_SPECIFIC_SUBLAYER         69
#define  DATA_SEQUENCING              70
#define  CIRCUIT_STATUS               71
#define  PREFERRED_LANGUAGE           72
#define  CTL_MSG_AUTH_NONCE           73
#define  TX_CONNECT_SPEED_V3          74
#define  RX_CONNECT_SPEED_V3          75
#define  CONNECT_SPEED_UPDATE         97


#define MESSAGE_TYPE_SCCRQ         1
#define MESSAGE_TYPE_SCCRP         2
#define MESSAGE_TYPE_SCCCN         3
#define MESSAGE_TYPE_StopCCN       4
#define MESSAGE_TYPE_Reserved_5    5
#define MESSAGE_TYPE_HELLO         6
#define MESSAGE_TYPE_OCRQ          7
#define MESSAGE_TYPE_OCRP          8
#define MESSAGE_TYPE_OCCN          9
#define MESSAGE_TYPE_ICRQ         10
#define MESSAGE_TYPE_ICRP         11
#define MESSAGE_TYPE_ICCN         12
#define MESSAGE_TYPE_Reserved_13  13
#define MESSAGE_TYPE_CDN          14
#define MESSAGE_TYPE_WEN          15
#define MESSAGE_TYPE_SLI          16
#define MESSAGE_TYPE_MDMST        17
#define MESSAGE_TYPE_SRRQ         18
#define MESSAGE_TYPE_SRRP         19
#define MESSAGE_TYPE_ACK          20
#define MESSAGE_TYPE_FSQ          21
#define MESSAGE_TYPE_FSR          22
#define MESSAGE_TYPE_MSRQ         23
#define MESSAGE_TYPE_MSRP         24
#define MESSAGE_TYPE_MSE          25
#define MESSAGE_TYPE_MSI          26
#define MESSAGE_TYPE_MSEN         27
#define MESSAGE_TYPE_CSUN         28
#define MESSAGE_TYPE_CSURQ        29

extern __thread char    g_protoinfo[MAX_CONTENT_SIZE];
extern __thread uint16_t g_proto_layer[32];
extern __thread uint8_t  g_proto_layer_cnt;

enum l2tp_index_em {
    EM_L2TP_TUNNEL_VERSION,
    EM_L2TP_TUNNEL_ID,
    EM_L2TP_SESSION_ID,
    EM_L2TP_L2TP_TYPE,
    EM_L2TP_MESSAGE_TYPE,
    EM_L2TP_PROTOCOL_VERSION,
    EM_L2TP_PROTOCOL_REVERSION,
    EM_L2TP_MINIMUM_BPS,
    EM_L2TP_MAXIMUM_BPS,
    EM_L2TP_FIRMWAVE_REVISION,
    EM_L2TP_HOST_NAME,
    EM_L2TP_VENDOR_NAME,
    EM_L2TP_ASSIGNED_TUNNEL_ID,
    EM_L2TP_ASSIGNED_SESSION_ID,
    EM_L2TP_VENDOR_SPECIFIC_AVP,
    EM_L2TP_BEAR_TYPE,
    EM_L2TP_BEAR_CAPABILITIES,
    EM_L2TP_FARMING_TYPE,
    EM_L2TP_FARMING_CAPABILITIES,
    EM_L2TP_CALL_SERIAL_NUMBER,
    EM_L2TP_CALLED_NUMBER,
    EM_L2TP_CALLING_NUMBER,
    EM_L2TP_RX_CONNECT_SPEED,
    EM_L2TP_TX_CONNECT_SPEED,
    EM_L2TP_PHYSICAL_CHANNEL,
    EM_L2TP_PRIVATE_GROUP_ID,
    EM_L2TP_ROUTER_ID,
    EM_L2TP_ASSIGNED_COOKIE,
    EM_L2TP_CHAP_CHALLENGE,
    EM_L2TP_CHAP_CHALLENGE_RESPONSE,
    EM_L2TP_PROXY_AUTHEN_TYPE,
    EM_L2TP_PROXY_AUTHEN_NAME,
    EM_L2TP_PROXY_AUTHEN_CHALLENGE,
    EM_L2TP_PROXY_AUTHEN_ID,
    EM_L2TP_PROXY_AUTHEN_RESPONSE,
    EM_L2TP_INITIAL_RECEIVED_LCP_CONFREQ,
    EM_L2TP_LAST_SENT_LCP_CONFREQ,
    EM_L2TP_LAST_RECEIVED_LCP_CONFREQ,

    EM_L2TP3_STOP_CCN_RESULT_CODE,
    EM_L2TP3_RESULT_CODE,
    EM_L2TP3_ERROR_CODE,
    EM_L2TP3_TIE_BREAKER,
    EM_L2TP3_RECV_WIN_SIZE,
    EM_L2TP3_CAUSE_CODE,
    EM_L2TP3_SUB_ADDRESS,
    EM_L2TP3_STAT_CRC_ERROR,
    EM_L2TP3_STAT_FARMING_ERRORS,
    EM_L2TP3_STAT_HARDWARE_OVERRUNS,
    EM_L2TP3_STAT_BUFFER_OVERRUNS,
    EM_L2TP3_STAT_TIMEOUT_ERRORS,
    EM_L2TP3_STAT_ALIGNMENT_ERRORS,
    EM_L2TP3_STAT_SEND_ACCM,
    EM_L2TP3_STAT_RECV_ACCM,
    EM_L2TP3_RANDOM_VECTOR,
    EM_L2TP3_PRIVATE_GROUPID,
    EM_L2TP3_DISC_CODE,
    EM_L2TP3_DISC_CON_PROTO_NUMBER,
    EM_L2TP3_DISC_CAUSE_CODE_DIRECT,
    EM_L2TP3_LOCAL_SESSION_ID,
    EM_L2TP3_REMOTE_SESSION_ID,
    EM_L2TP3_REMOTE_END_ID,
    EM_L2TP3_PW_PSEUDOWIRE_TYPE,
    EM_L2TP3_SPEC_SUBLAYER,
    EM_L2TP3_DATA_SEQUENCING,
    EM_L2TP3_CIRCUIT_STATUS,
    EM_L2TP3_CIRCUIT_TYPE,
    EM_L2TP3_TX_CONN_SPEED,
    EM_L2TP3_CSU_REMOTE_SESSIONID,
    EM_L2TP3_CSU_CURRENT_TX_SPEED,
    EM_L2TP3_CSU_CURRENT_RX_SPEED,
    EM_L2TP3_ASSIGNED_CCID,

    EM_L2TP_INNERIPSRC,
    EM_L2TP_INNERIPDST,
    EM_L2TP_INNERIPPROTO,
    EM_L2TP_INNERSRCPORT,
    EM_L2TP_INNERDSTPORT,
    EM_L2TP_MAX

};

static dpi_field_table  l2tp_field_array[] = {
    DPI_FIELD_D(EM_L2TP_TUNNEL_VERSION,                    YA_FT_UINT16,                 "version"),
    DPI_FIELD_D(EM_L2TP_TUNNEL_ID,                         YA_FT_UINT16,                 "tunnel_id"),
    DPI_FIELD_D(EM_L2TP_SESSION_ID,                        YA_FT_UINT16,                 "session_id"),
    DPI_FIELD_D(EM_L2TP_L2TP_TYPE,                         YA_FT_STRING,                 "l2tp_type"),
    DPI_FIELD_D(EM_L2TP_MESSAGE_TYPE,                      YA_FT_STRING,                 "message_type"),
    DPI_FIELD_D(EM_L2TP_PROTOCOL_VERSION,                  YA_FT_UINT8,                  "Protocol_version"),
    DPI_FIELD_D(EM_L2TP_PROTOCOL_REVERSION,                YA_FT_UINT8,                  "Protocol_reversion"),
    DPI_FIELD_D(EM_L2TP_MINIMUM_BPS,                       YA_FT_UINT32,                 "Minimum_bps"),
    DPI_FIELD_D(EM_L2TP_MAXIMUM_BPS,                       YA_FT_UINT32,                 "Maximum_bps"),
    DPI_FIELD_D(EM_L2TP_FIRMWAVE_REVISION,                 YA_FT_UINT16,                 "Firmwave_revision"),
    DPI_FIELD_D(EM_L2TP_HOST_NAME,                         YA_FT_STRING,                 "Host_name"),
    DPI_FIELD_D(EM_L2TP_VENDOR_NAME,                       YA_FT_STRING,                 "Vendor_name"),
    DPI_FIELD_D(EM_L2TP_ASSIGNED_TUNNEL_ID,                YA_FT_UINT16,                 "Assigned_tunnel_id"),
    DPI_FIELD_D(EM_L2TP_ASSIGNED_SESSION_ID,               YA_FT_UINT16,                 "Assigned_session_id"),
    DPI_FIELD_D(EM_L2TP_VENDOR_SPECIFIC_AVP,               YA_FT_STRING,                 "Vendor_Specific_AVP"),
    DPI_FIELD_D(EM_L2TP_BEAR_TYPE,                         YA_FT_UINT32,                 "Bear_type"),
    DPI_FIELD_D(EM_L2TP_BEAR_CAPABILITIES,                 YA_FT_UINT32,                 "Bear_capabilities"),
    DPI_FIELD_D(EM_L2TP_FARMING_TYPE,                      YA_FT_UINT32,                 "Farming_type"),
    DPI_FIELD_D(EM_L2TP_FARMING_CAPABILITIES,              YA_FT_UINT32,                 "Farming_capabilities"),
    DPI_FIELD_D(EM_L2TP_CALL_SERIAL_NUMBER,                YA_FT_UINT32,                 "Call_serial_number"),
    DPI_FIELD_D(EM_L2TP_CALLED_NUMBER,                     YA_FT_STRING,                 "Called_number"),
    DPI_FIELD_D(EM_L2TP_CALLING_NUMBER,                    YA_FT_STRING,                 "Calling_number"),
    DPI_FIELD_D(EM_L2TP_RX_CONNECT_SPEED,                  YA_FT_STRING,                 "Rx_connect_speed"),
    DPI_FIELD_D(EM_L2TP_TX_CONNECT_SPEED,                  YA_FT_STRING,                 "Tx_connect_speed"),
    DPI_FIELD_D(EM_L2TP_PHYSICAL_CHANNEL,                  YA_FT_UINT32,                 "Physical_channel"),
    DPI_FIELD_D(EM_L2TP_PRIVATE_GROUP_ID,                  YA_FT_STRING,                 "Private_group_id"),
    DPI_FIELD_D(EM_L2TP_ROUTER_ID,                         YA_FT_UINT32,                 "Router_id"),
    DPI_FIELD_D(EM_L2TP_ASSIGNED_COOKIE,                   YA_FT_UINT16,                 "Assigned_cookie"),
    DPI_FIELD_D(EM_L2TP_CHAP_CHALLENGE,                    YA_FT_STRING,                 "Chap_challenge"),
    DPI_FIELD_D(EM_L2TP_CHAP_CHALLENGE_RESPONSE,           YA_FT_STRING,                 "Chap_challenge_response"),
    DPI_FIELD_D(EM_L2TP_PROXY_AUTHEN_TYPE,                 YA_FT_UINT16,                 "Proxy_authen_type"),
    DPI_FIELD_D(EM_L2TP_PROXY_AUTHEN_NAME,                 YA_FT_STRING,                 "Proxy_authen_name"),
    DPI_FIELD_D(EM_L2TP_PROXY_AUTHEN_CHALLENGE,            YA_FT_STRING,                 "Proxy_authen_challenge"),
    DPI_FIELD_D(EM_L2TP_PROXY_AUTHEN_ID,                   YA_FT_UINT16,                 "Proxy_authen_id"),
    DPI_FIELD_D(EM_L2TP_PROXY_AUTHEN_RESPONSE,             YA_FT_STRING,                 "Proxy_authen_response"),
    DPI_FIELD_D(EM_L2TP_INITIAL_RECEIVED_LCP_CONFREQ,      YA_FT_STRING,                 "Initial_received_lcp_confreq"),
    DPI_FIELD_D(EM_L2TP_LAST_SENT_LCP_CONFREQ,             YA_FT_STRING,                 "Last_sent_lcp_confreq"),
    DPI_FIELD_D(EM_L2TP_LAST_RECEIVED_LCP_CONFREQ,         YA_FT_STRING,                 "Last_received_lcp_confreq"),

    DPI_FIELD_D(EM_L2TP3_STOP_CCN_RESULT_CODE,             YA_FT_UINT16,                 "v3StopCCNResultCode"),
    DPI_FIELD_D(EM_L2TP3_RESULT_CODE,                      YA_FT_UINT16,                 "v3ResultCode"),
    DPI_FIELD_D(EM_L2TP3_ERROR_CODE,                       YA_FT_UINT16,                 "v3ErrorCode"),
    DPI_FIELD_D(EM_L2TP3_TIE_BREAKER,                      YA_FT_STRING,                 "v3TieBreaker"),
    DPI_FIELD_D(EM_L2TP3_RECV_WIN_SIZE,                    YA_FT_UINT16,                 "v3ReceiveWindowSize"),
    DPI_FIELD_D(EM_L2TP3_CAUSE_CODE,                       YA_FT_UINT16,                 "v3CauseCode"),
    DPI_FIELD_D(EM_L2TP3_SUB_ADDRESS,                      YA_FT_STRING,                 "v3SubAddress"),
    DPI_FIELD_D(EM_L2TP3_STAT_CRC_ERROR,                   YA_FT_UINT32,                 "v3StatCrcError"),
    DPI_FIELD_D(EM_L2TP3_STAT_FARMING_ERRORS,              YA_FT_UINT32,                 "v3StatFarmingError"),
    DPI_FIELD_D(EM_L2TP3_STAT_HARDWARE_OVERRUNS,           YA_FT_UINT32,                 "v3StatHardwareOverruns"),
    DPI_FIELD_D(EM_L2TP3_STAT_BUFFER_OVERRUNS,             YA_FT_UINT32,                 "v3StatBufferOverruns"),
    DPI_FIELD_D(EM_L2TP3_STAT_TIMEOUT_ERRORS,              YA_FT_UINT32,                 "v3StatTimeoutErrors"),
    DPI_FIELD_D(EM_L2TP3_STAT_ALIGNMENT_ERRORS,            YA_FT_UINT32,                 "v3StatAlignmentErrors"),
    DPI_FIELD_D(EM_L2TP3_STAT_RECV_ACCM,                   YA_FT_UINT32,                 "v3SendAccm"),
    DPI_FIELD_D(EM_L2TP3_STAT_SEND_ACCM,                   YA_FT_UINT32,                 "v3RecvAccm"),
    DPI_FIELD_D(EM_L2TP3_RANDOM_VECTOR,                    YA_FT_STRING,                 "v3RandomVector"),
    DPI_FIELD_D(EM_L2TP3_PRIVATE_GROUPID,                  YA_FT_STRING,                 "v3PrivateGroupId"),
    DPI_FIELD_D(EM_L2TP3_DISC_CODE,                        YA_FT_UINT16,                 "v3DiscCode"),
    DPI_FIELD_D(EM_L2TP3_DISC_CON_PROTO_NUMBER,            YA_FT_UINT32,                 "v3DiscConProtoNumber"),
    DPI_FIELD_D(EM_L2TP3_DISC_CAUSE_CODE_DIRECT,           YA_FT_UINT32,                 "v3DiscCauseCodeDirect"),
    DPI_FIELD_D(EM_L2TP3_LOCAL_SESSION_ID,                 YA_FT_UINT32,                 "v3LocalSesionId"),
    DPI_FIELD_D(EM_L2TP3_REMOTE_SESSION_ID,                YA_FT_UINT32,                 "v3RemoteSessionId"),
    DPI_FIELD_D(EM_L2TP3_REMOTE_END_ID,                    YA_FT_STRING,                 "v3RemoteEndId"),
    DPI_FIELD_D(EM_L2TP3_PW_PSEUDOWIRE_TYPE,               YA_FT_UINT16,                 "v3PWPseudowireType"),
    DPI_FIELD_D(EM_L2TP3_SPEC_SUBLAYER,                    YA_FT_UINT16,                 "v3SpecSublayer"),
    DPI_FIELD_D(EM_L2TP3_DATA_SEQUENCING,                  YA_FT_UINT16,                 "v3DataSequencing"),
    DPI_FIELD_D(EM_L2TP3_CIRCUIT_STATUS,                   YA_FT_UINT16,                 "v3CircuitStatus"),
    DPI_FIELD_D(EM_L2TP3_CIRCUIT_TYPE,                     YA_FT_UINT16,                 "v3CircuitType"),
    DPI_FIELD_D(EM_L2TP3_TX_CONN_SPEED,                    YA_FT_STRING,                 "v3TxConnSpeed"),
    DPI_FIELD_D(EM_L2TP3_CSU_REMOTE_SESSIONID,             YA_FT_UINT16,                 "v3CsuRemoteSessionId"),
    DPI_FIELD_D(EM_L2TP3_CSU_CURRENT_TX_SPEED,             YA_FT_UINT16,                 "v3CsuCurrentTxSpeed"),
    DPI_FIELD_D(EM_L2TP3_CSU_CURRENT_RX_SPEED,             YA_FT_UINT16,                 "v3CsuCurrentRxSpeed"),
    DPI_FIELD_D(EM_L2TP3_ASSIGNED_CCID,                    YA_FT_UINT32,                 "v3AssignedCCID"),
    DPI_FIELD_D(EM_L2TP_INNERIPSRC,                        YA_FT_STRING,                 "InnerIPSrc"),
    DPI_FIELD_D(EM_L2TP_INNERIPDST,                        YA_FT_STRING,                 "InnerIPDst"),
    DPI_FIELD_D(EM_L2TP_INNERIPPROTO,                      YA_FT_STRING,                 "InnerIPProto"),
    DPI_FIELD_D(EM_L2TP_INNERSRCPORT,                      YA_FT_STRING,                 "InnerSrcPort"),
    DPI_FIELD_D(EM_L2TP_INNERDSTPORT,                      YA_FT_STRING,                 "InnerDstPort"),
};


typedef struct _value_string {
    uint32_t      value;
    const void   *strptr;
} value_string;


static const value_string message_type_vals[] = {
    { 0,                        ""},
    { MESSAGE_TYPE_SCCRQ,       "Start_Control_Request" },
    { MESSAGE_TYPE_SCCRP,       "Start_Control_Reply" },
    { MESSAGE_TYPE_SCCCN,       "Start_Control_Connected" },
    { MESSAGE_TYPE_StopCCN,     "Stop_Control_Notification" },
    { MESSAGE_TYPE_Reserved_5,  "Reserved" },
    { MESSAGE_TYPE_HELLO,       "Hello" },
    { MESSAGE_TYPE_OCRQ,        "Outgoing_Call_Request" },
    { MESSAGE_TYPE_OCRP,        "Outgoing_Call_Reply" },
    { MESSAGE_TYPE_OCCN,        "Outgoing_Call_Connected" },
    { MESSAGE_TYPE_ICRQ,        "Incoming_Call_Request" },
    { MESSAGE_TYPE_ICRP,        "Incoming_Call_Reply" },
    { MESSAGE_TYPE_ICCN,        "Incoming_Call_Connected" },
    { MESSAGE_TYPE_Reserved_13, "Reserved" },
    { MESSAGE_TYPE_CDN,         "Call_Disconnect_Notification" },
    { MESSAGE_TYPE_WEN,         "WAN_Error_Notify" },
    { MESSAGE_TYPE_SLI,         "Set_Link_Info" },
    { MESSAGE_TYPE_MDMST,       "Modem_Status" },
    { MESSAGE_TYPE_SRRQ,        "Service_Relay_Request_Msg" },
    { MESSAGE_TYPE_SRRP,        "Service_Relay_Reply_Message" },
    { MESSAGE_TYPE_ACK,         "Explicit_Acknowledgement" },
    { MESSAGE_TYPE_FSQ,         "Failover_Session_Query_Message" },     /* Fail Over Extensions - RFC4951 */
    { MESSAGE_TYPE_FSR,         "Failover_Session_Response_Message" },
    { MESSAGE_TYPE_MSRQ,        "Multicast-Session-Request" },          /* Multicast Management - RFC4045 */
    { MESSAGE_TYPE_MSRP,        "Multicast-Session-Response" },
    { MESSAGE_TYPE_MSE,         "Multicast-Session-Establishment" },
    { MESSAGE_TYPE_MSI,         "Multicast-Session-Information" },
    { MESSAGE_TYPE_MSEN,        "Multicast-Session-End-Notify" },
    { MESSAGE_TYPE_CSUN,        "Connect-Speed-Update-Notification" },
    { MESSAGE_TYPE_CSURQ,       "Connect-Speed-Update-Request" },
};


extern struct global_config g_config;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct rte_mempool *tbl_log_mempool;
void dissect_ppp_family(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int);

struct l2tp_header {
    uint32_t ccid;  // for v3
    uint16_t sztunnel_id;
    uint16_t szsession_id;
    uint16_t szl2tp_type;
    uint16_t version;
};

typedef struct l2tp_dissect_result
{
    struct l2tp_header *head;

    const char *szmessage_type;

    uint8_t szProtocol_version_flag;
    uint8_t szProtocol_version;
    uint8_t szProtocol_reversion;

    value_string szHost_name, szVendor_name, szPrivate_group_id, szChap_challenge, szChap_challenge_response;
    value_string szCalled_number, szCalling_number, szProxy_authen_name, szProxy_authen_challenge, szProxy_authen_response;
    value_string szInitial_received_lcp_confreq, szLast_sent_lcp_confreq, szLast_received_lcp_confreq, szAssigned_cookie;

    uint8_t  szAssigned_tunnel_id_flag, szAssigned_session_id_flag, szProxy_authen_id_flag, szProxy_authen_type_flag, szFirmwave_revision_flag;
    uint16_t szAssigned_tunnel_id, szAssigned_session_id, szProxy_authen_id, szProxy_authen_type, szFirmwave_revision;

    uint8_t  szBear_type_flag, szBear_capabilities_flag, szFarming_type_flag, szFarming_capabilities_flag, szCall_serial_number_flag,
             szRx_connect_speed_flag, szTx_connect_speed_flag, szPhysical_channel_flag, szRouter_id_flag, szMinimum_bps_flag, szMaximum_bps_flag;
    uint32_t szBear_type, szBear_capabilities, szFarming_type, szFarming_capabilities, szCall_serial_number,
             szRx_connect_speed, szTx_connect_speed, szPhysical_channel, szRouter_id, szMinimum_bps, szMaximum_bps;

    uint8_t            v3_stop_ccn_result_code_f;
    uint16_t        v3_stop_ccn_result_code;
    uint16_t        v3_result_code;
    uint16_t        v3_avp_error_code;
    value_string    v3_avp_error_message;

    uint8_t            v3_tie_breaker_f;
    value_string    v3_tie_breaker;

    uint8_t            v3_recv_win_size_f;
    uint16_t        v3_recv_win_size;

    uint8_t            v3_cause_code_f;
    uint16_t        v3_cause_code;
    uint8_t            v3_cause_msg;

    value_string    v3_sub_address;

    uint8_t            v3_stat_crc_errors_f;
    uint32_t        v3_stat_crc_errors;
    uint8_t            v3_stat_farming_errors_f;
    uint32_t        v3_stat_farming_errors;
    uint8_t            v3_stat_hardware_overruns_f;
    uint32_t        v3_stat_hardware_overruns;
    uint8_t            v3_stat_buffer_overruns_f;
    uint32_t        v3_stat_buffer_overruns;
    uint8_t            v3_stat_timeout_errors_f;
    uint32_t        v3_stat_timeout_errors;
    uint8_t            v3_stat_aligment_errors_f;
    uint32_t        v3_stat_aligment_errors;


    uint8_t            v3_accm_sent_f;
    uint32_t        v3_accm_sent;
    uint8_t            v3_accm_recv_f;
    uint32_t        v3_accm_recv;

    value_string    v3_random_vector;
    value_string    v3_private_groupid;

    uint8_t            v3_disc_code_f;
    uint16_t        v3_disc_code;
    uint8_t            v3_disc_con_proto_no_f;
    uint32_t        v3_disc_con_proto_no;
    uint8_t            v3_disc_cause_code_direct_f;
    uint32_t        v3_disc_cause_code_direct;

    uint8_t            v3_local_session_id_f;
    uint32_t        v3_local_session_id;

    uint8_t            v3_remote_session_id_f;
    uint32_t        v3_remote_session_id;

    value_string    v3_remote_end_id;

    uint8_t            v3_pw_pseudowire_type_f;
    uint16_t        v3_pw_pseudowire_type;

    uint8_t            v3_spec_sublayer_f;
    uint16_t        v3_spec_sublayer;

    uint8_t            v3_data_sequencing_f;
    uint16_t        v3_data_sequencing;

    uint8_t            v3_circuit_status_f;
    uint16_t        v3_circuit_status;
    uint8_t            v3_circuit_type_f;
    uint16_t        v3_circuit_type;

    value_string    v3_tx_conn_speed;

    uint8_t            v2_csu_f;
    uint16_t        v2_csu_res;
    uint16_t        v2_csu_remote_sessionid;
    uint32_t        v2_csu_cur_tx_speed;
    uint32_t        v2_csu_cur_rx_speed;

    uint8_t            v3_csu_f;
    uint16_t        v3_csu_remote_sessionid;
    uint64_t        v3_csu_tx_speed;
    uint64_t        v3_csu_rx_speed;

    uint8_t            v3_assigned_ccid_f;
    uint32_t        v3_assigned_ccid;

    //char szVendor_Specific_AVP[VALUE_MAX_LEN];
} ST_l2tp_dissect_result;


static int write_l2tp_control(struct flow_info *flow, int direction, ST_l2tp_dissect_result *pl2tp_dissect_result)
{
    int             idx     = 0;
    struct tbl_log *log_ptr = NULL;


    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "l2tp");

    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->head->version);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  pl2tp_dissect_result->head->sztunnel_id);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  pl2tp_dissect_result->head->szsession_id);
    write_one_str_reconds(log_ptr->record,  &idx, TBL_LOG_MAX_LEN,  "Control_Message",  15);

    write_string_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szmessage_type);
    if(pl2tp_dissect_result->szProtocol_version_flag){
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProtocol_version);
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProtocol_reversion);
    }
    else{
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 2);
    }

    if(pl2tp_dissect_result->szMinimum_bps_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szMinimum_bps);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szMaximum_bps_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szMaximum_bps);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szFirmwave_revision_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szFirmwave_revision);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szHost_name.strptr,   pl2tp_dissect_result->szHost_name.value);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szVendor_name.strptr, pl2tp_dissect_result->szVendor_name.value);

    if(pl2tp_dissect_result->szAssigned_tunnel_id_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szAssigned_tunnel_id);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
    if(pl2tp_dissect_result->szAssigned_session_id_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szAssigned_session_id);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1); //szVendor_Specific_AVP

    if(pl2tp_dissect_result->szBear_type_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szBear_type);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szBear_capabilities_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szBear_capabilities);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szFarming_type_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szFarming_type);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szFarming_capabilities_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szFarming_capabilities);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szCall_serial_number_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szCall_serial_number);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szCalled_number.strptr,   pl2tp_dissect_result->szCalled_number.value);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szCalling_number.strptr,  pl2tp_dissect_result->szCalling_number.value);

    if(pl2tp_dissect_result->szRx_connect_speed_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szRx_connect_speed);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szTx_connect_speed_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szTx_connect_speed);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if(pl2tp_dissect_result->szPhysical_channel_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szPhysical_channel);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szPrivate_group_id.strptr, pl2tp_dissect_result->szPrivate_group_id.value);

    if(pl2tp_dissect_result->szRouter_id_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szRouter_id);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szAssigned_cookie.strptr,  pl2tp_dissect_result->szAssigned_cookie.value);

    write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szChap_challenge.strptr, pl2tp_dissect_result->szChap_challenge.value);
    write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szChap_challenge_response.strptr, pl2tp_dissect_result->szChap_challenge_response.value);

    if(pl2tp_dissect_result->szProxy_authen_type_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProxy_authen_type);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProxy_authen_name.strptr,       pl2tp_dissect_result->szProxy_authen_name.value);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProxy_authen_challenge.strptr,  pl2tp_dissect_result->szProxy_authen_challenge.value);

    if(pl2tp_dissect_result->szProxy_authen_id_flag)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProxy_authen_id);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szProxy_authen_response.strptr,   pl2tp_dissect_result->szProxy_authen_response.value);

    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szInitial_received_lcp_confreq.strptr,   pl2tp_dissect_result->szInitial_received_lcp_confreq.value);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szLast_sent_lcp_confreq.strptr,          pl2tp_dissect_result->szLast_sent_lcp_confreq.value);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->szLast_received_lcp_confreq.strptr,      pl2tp_dissect_result->szLast_received_lcp_confreq.value);

    if (pl2tp_dissect_result->head->version != 3) {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_L2TP_INNERIPSRC - EM_L2TP3_STOP_CCN_RESULT_CODE);
    }
    else {
        if (pl2tp_dissect_result->v3_stop_ccn_result_code_f) {
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stop_ccn_result_code);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_result_code);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_avp_error_code);
        }
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 3);

        if (pl2tp_dissect_result->v3_tie_breaker_f && pl2tp_dissect_result->v3_tie_breaker.strptr)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_tie_breaker.strptr, pl2tp_dissect_result->v3_tie_breaker.value);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_recv_win_size_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_recv_win_size);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_cause_code_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_cause_code);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_sub_address.strptr)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_sub_address.strptr, pl2tp_dissect_result->v3_sub_address.value);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_stat_crc_errors_f) {
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stat_crc_errors);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stat_farming_errors);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stat_hardware_overruns);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stat_buffer_overruns);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stat_timeout_errors);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_stat_aligment_errors);
        }
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 6);

        if (pl2tp_dissect_result->v3_accm_sent_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_accm_sent);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_accm_recv_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_accm_recv);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_random_vector.strptr)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_random_vector.strptr, pl2tp_dissect_result->v3_random_vector.value);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_private_groupid.strptr)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_private_groupid.strptr, pl2tp_dissect_result->v3_private_groupid.value);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_disc_code_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_disc_code);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_disc_con_proto_no_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_disc_con_proto_no);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_disc_cause_code_direct_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_disc_cause_code_direct);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_local_session_id_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_local_session_id);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_remote_session_id_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_remote_session_id);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_remote_end_id.strptr)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_remote_end_id.strptr, pl2tp_dissect_result->v3_remote_end_id.value);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_pw_pseudowire_type_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_pw_pseudowire_type);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_spec_sublayer_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_spec_sublayer);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_data_sequencing_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_data_sequencing);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_circuit_status_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_circuit_status);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_circuit_type_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_circuit_type);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_tx_conn_speed.strptr)
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_tx_conn_speed.strptr, pl2tp_dissect_result->v3_tx_conn_speed.value);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        if (pl2tp_dissect_result->v3_csu_f) {
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_csu_remote_sessionid);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_csu_tx_speed);
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_csu_rx_speed);
        }
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 3);

        if (pl2tp_dissect_result->v3_assigned_ccid_f)
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, pl2tp_dissect_result->v3_assigned_ccid);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
    }

    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 5); //inner five tuple

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_L2TP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}

/*****************************************************************
 *Function    : dissect_l2tp_control
 *Description : l2tp 协议 头部 解析
 *Input       : payload, payload_len, index, header
 *Output      :
 *Return      : 返回已经解析的字节数, 0 control message , -1无意义,不写
 *****************************************************************/
static int dissect_l2tp_control(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, struct l2tp_header *head)
{
    const uint8_t *pData = payload;
    uint16_t    lAVP_Flags, lAvp_len, lAvp_type; //lAvp_vendor_id

    ST_l2tp_dissect_result l2tp_dissect_result;
    memset(&l2tp_dissect_result, 0, sizeof(ST_l2tp_dissect_result));

    ST_l2tp_dissect_result *pl2tp_dissect_result = &l2tp_dissect_result;
    pl2tp_dissect_result->head = head;


    /******** 解析每一个 L2TP AVP *****************************/
    while(payload + 6 - pData < payload_len)
    {
        lAVP_Flags        = get_uint16_ntohs(payload, 0);
        //lAvp_vendor_id  = get_uint16_ntohs(payload, 2);
        lAvp_type         = get_uint16_ntohs(payload, 4);
        lAvp_len          = (0x03FF & lAVP_Flags);

        /***** AVP 最短结构: 2Byte:Mandatory_Hidden_len, 2Byte:VendorID, 2Byte:MessageType **/
        if (lAvp_len <= 6 || payload + lAvp_len - pData > payload_len)
            return 0;

        /***  AVP Flags 标记 ***/
        if(0X40 & payload[0]){
            payload += (lAvp_len - 6);
            continue;
        }

        payload += 6;

        /*****  payload 指向 AVP Value ********/
        switch (lAvp_type)
        {
            case CONTROL_MESSAGE:
                {
                    uint16_t lmessage_type = get_uint16_ntohs(payload, 0);
                    if(lmessage_type == 6) //Hello useless
                        return 0;
                    if(lmessage_type < 30)
                        pl2tp_dissect_result->szmessage_type  = message_type_vals[lmessage_type].strptr;
                    break;
                }

            case PROTOCOL_VERSION:
                pl2tp_dissect_result->szProtocol_version_flag = 1;
                pl2tp_dissect_result->szProtocol_version = payload[0];
                pl2tp_dissect_result->szProtocol_reversion = payload[1];
                break;

            case MINIMUM_BPS:
                pl2tp_dissect_result->szMinimum_bps_flag = 1;
                pl2tp_dissect_result->szMinimum_bps = get_uint32_ntohl(payload, 0);
                break;

            case MAXIMUM_BPS:
                pl2tp_dissect_result->szMaximum_bps_flag = 1;
                pl2tp_dissect_result->szMaximum_bps = get_uint32_ntohl(payload, 0);
                break;

            case FIRMWARE_REVISION:
                pl2tp_dissect_result->szFirmwave_revision_flag = 1;
                pl2tp_dissect_result->szFirmwave_revision = get_uint16_ntohs(payload, 0);
                break;

            case HOST_NAME:
                pl2tp_dissect_result->szHost_name.value  = lAvp_len - 6;
                pl2tp_dissect_result->szHost_name.strptr = payload;
                break;

            case VENDOR_NAME:
                pl2tp_dissect_result->szVendor_name.value  = lAvp_len - 6;
                pl2tp_dissect_result->szVendor_name.strptr = payload;
                break;

            case ASSIGNED_TUNNEL_ID:
                pl2tp_dissect_result->szAssigned_tunnel_id_flag = 1;
                pl2tp_dissect_result->szAssigned_tunnel_id = get_uint16_ntohs(payload, 0);
                break;

            case ASSIGNED_SESSION:
                pl2tp_dissect_result->szAssigned_session_id_flag = 1;
                pl2tp_dissect_result->szAssigned_session_id = get_uint16_ntohs(payload, 0);
                break;

            case BEARER_TYPE:
                pl2tp_dissect_result->szBear_type_flag = 1;
                pl2tp_dissect_result->szBear_type = get_uint32_ntohl(payload, 0);
                break;

            case BEARER_CAPABILITIES:
                pl2tp_dissect_result->szBear_capabilities_flag = 1;
                pl2tp_dissect_result->szBear_capabilities = get_uint32_ntohl(payload, 0);
                break;

            case FRAMING_TYPE:
                pl2tp_dissect_result->szFarming_type_flag = 1;
                pl2tp_dissect_result->szFarming_type = get_uint32_ntohl(payload, 0);
                break;

            case FRAMING_CAPABILITIES:
                pl2tp_dissect_result->szFarming_capabilities_flag = 1;
                pl2tp_dissect_result->szFarming_capabilities = get_uint32_ntohl(payload, 0);
                break;

            case CALL_SERIAL_NUMBER:
                pl2tp_dissect_result->szCall_serial_number_flag = 1;
                pl2tp_dissect_result->szCall_serial_number = get_uint32_ntohl(payload, 0);
                break;

            case CALLED_NUMBER:
                pl2tp_dissect_result->szCalled_number.value  = lAvp_len - 6;
                pl2tp_dissect_result->szCalled_number.strptr = payload;
                break;

            case CALLING_NUMBER:
                pl2tp_dissect_result->szCalling_number.value  = lAvp_len - 6;
                pl2tp_dissect_result->szCalling_number.strptr = payload;
                break;

            case RX_CONNECT_SPEED:
                pl2tp_dissect_result->szRx_connect_speed = 1;
                pl2tp_dissect_result->szRx_connect_speed = get_uint32_ntohl(payload, 0);
                break;

            case TX_CONNECT_SPEED:
                pl2tp_dissect_result->szTx_connect_speed = 1;
                pl2tp_dissect_result->szTx_connect_speed = get_uint32_ntohl(payload, 0);
                break;

            case PHYSICAL_CHANNEL:
                pl2tp_dissect_result->szPhysical_channel_flag = 1;
                pl2tp_dissect_result->szPhysical_channel = get_uint32_ntohl(payload, 0);
                break;

            case PRIVATE_GROUP_ID:
                pl2tp_dissect_result->szPrivate_group_id.value  = lAvp_len - 6;
                pl2tp_dissect_result->szPrivate_group_id.strptr = payload;
                break;

            case ROUTER_ID:
                pl2tp_dissect_result->szRouter_id_flag = 1;
                pl2tp_dissect_result->szRouter_id = get_uint32_ntohl(payload, 0);
                break;

            case ASSIGNED_COOKIE:
                pl2tp_dissect_result->szAssigned_cookie.value  = lAvp_len - 6;
                pl2tp_dissect_result->szAssigned_cookie.strptr = payload;
                break;

            case CHALLENGE:
                pl2tp_dissect_result->szChap_challenge.value  = lAvp_len - 6;
                pl2tp_dissect_result->szChap_challenge.strptr = payload;
                break;

            case CHALLENGE_RESPONSE:
                pl2tp_dissect_result->szChap_challenge_response.value  = lAvp_len - 6;
                pl2tp_dissect_result->szChap_challenge_response.strptr = payload;
                break;

            case PROXY_AUTHEN_TYPE:
                pl2tp_dissect_result->szProxy_authen_type_flag = 1;
                pl2tp_dissect_result->szProxy_authen_type = get_uint16_ntohs(payload, 0);
                break;

            case PROXY_AUTHEN_NAME:
                pl2tp_dissect_result->szProxy_authen_name.value  = lAvp_len - 6;
                pl2tp_dissect_result->szProxy_authen_name.strptr = payload;
                break;

            case PROXY_AUTHEN_CHALLENGE:
                pl2tp_dissect_result->szProxy_authen_challenge.value  = lAvp_len - 6;
                pl2tp_dissect_result->szProxy_authen_challenge.strptr = payload;
                break;

            case PROXY_AUTHEN_ID:
                pl2tp_dissect_result->szProxy_authen_id_flag= 1;
                pl2tp_dissect_result->szProxy_authen_id = get_uint16_ntohs(payload, 0);
                break;

            case PROXY_AUTHEN_RESPONSE:
                pl2tp_dissect_result->szProxy_authen_response.value  = lAvp_len - 6;
                pl2tp_dissect_result->szProxy_authen_response.strptr = payload;
                break;

            case INITIAL_RECEIVED_LCP_CONFREQ:
                pl2tp_dissect_result->szInitial_received_lcp_confreq.value  = lAvp_len - 6;
                pl2tp_dissect_result->szInitial_received_lcp_confreq.strptr = payload;
                break;

            case LAST_SENT_LCP_CONFREQ:
                pl2tp_dissect_result->szLast_sent_lcp_confreq.value  = lAvp_len - 6;
                pl2tp_dissect_result->szLast_sent_lcp_confreq.strptr = payload;
                break;

            case LAST_RECEIVED_LCP_CONFREQ:
                pl2tp_dissect_result->szLast_received_lcp_confreq.value  = lAvp_len - 6;
                pl2tp_dissect_result->szLast_received_lcp_confreq.strptr = payload;
                break;

            default:
                break;
        }
        payload += (lAvp_len - 6);
    }/*** end of while ***/

    write_l2tp_control(flow, direction, pl2tp_dissect_result);

    return 0;
}

/*****************************************************************
 *Function    : dissect_l2tp_inner
 *Description : l2tp 获取内部 IP 端口
 *Input       : payload, payload_len, index, pl2tp_dissect_result
 *Output      : 填充 ST_l2tp_dissect_result
 *Return      : 0 代表没有 获取内部 IP 端口， 大于0 为 解析ok
 *Others      : none
 *****************************************************************/
static int dissect_l2tp_inner(const uint8_t *payload, const uint32_t payload_len, struct flow_info *flow, int direction, struct l2tp_header *head)
{

#define IP_PROTO_UDP       17    /* user datagram protocol - RFC768 */
#define IP_PROTO_TCP       6     /* TCP - RFC792 */

    const uint8_t *pData = payload;

    if(payload[0] == 0xff && payload[1] == 0x03)
        payload += 2;

    uint16_t ppp_Protocol = 0;
    if(get_uint8_t(payload, 0) == 0x0021 || get_uint8_t(payload, 0) == 0x0057){
        ppp_Protocol = get_uint8_t(payload, 0);
        payload++;
    } else {
        ppp_Protocol = get_uint16_ntohs(payload, 0);
        payload += 2;
    }

    const uint8_t *pIPpayload = payload;
    uint16_t      lIPpayloadLen = payload_len - (pIPpayload - pData)+flow->tunnel_ip_len;

    if(PPP_IP == ppp_Protocol || 0x0057 == ppp_Protocol)
    {
        if (PPP_IP == ppp_Protocol)
            payload += 24; //IP + 2 port
        else
            payload += 44; // IPV6 + 2 port

        /* add by liugh */
        struct pkt_info  pkt_data;
        memset(&pkt_data, 0, sizeof(struct pkt_info ));

        struct five_tuple outer;
        const struct dpi_iphdr *iph4 = (const struct dpi_iphdr *)pIPpayload;
        if (direction == FLOW_DIR_SRC2DST)
        {
            memcpy(&outer, &flow->tuple.inner, sizeof(outer));
        }
        else
        {
            memcpy(&outer, &flow->tuple_reverse.inner, sizeof(outer));
        }

         /* add by liugh */
        const uint8_t *inner_l4;
        uint32_t inner_l4_offset = iph4->ihl * 4;
        inner_l4 = ((const uint8_t *)pIPpayload + inner_l4_offset);
        uint16_t  chunck_total_len=lIPpayloadLen-inner_l4_offset;

        pkt_data.ipversion=iph4->version;
        uint8_t  proto=0;
        const struct dpi_ipv6hdr *iph6 = NULL;
        if (iph4->version == 4) {
            append_hardlink_proto_info(ETH_P_IP);
            proto = iph4->protocol;
            pkt_data.iph4=iph4;
        } else if (iph4->version == 6) {
            iph6 = (const struct dpi_ipv6hdr *)pIPpayload;
            proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
            pkt_data.iph6=iph6;
        }
        pkt_data.proto=proto;

        if (iph4->protocol == IPPROTO_TCP && chunck_total_len >= 20) {
            append_hardlink_proto_info(ETH_P_IPV6);
            const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)inner_l4;
            pkt_data.tcph=tcph;
        } else if(iph4->protocol == IPPROTO_UDP && chunck_total_len >= 8) {
            const struct dpi_udphdr *udph = (const struct dpi_udphdr *)inner_l4;
            pkt_data.udph=udph;
        }

        g_proto_layer[g_proto_layer_cnt++] = PROTOCOL_L2TP;
        g_proto_layer[g_proto_layer_cnt++] = ETHERTYPE_PPP + PROTOCOL_MAX;

        SdtAclMatchedRuleInfo acl = {0};
        dpi_dissect_inherit_out_layer(flow, &pkt_data, &acl);
        if(pkt_data.proto){
            append_hardlink_proto_info(pkt_data.proto);
        }
        dpi_packet_processing_ip_layer(&flow_thread_info[flow->thread_id],
                                    g_config.g_now_time_usec,
                                    &outer,
                                    &pkt_data,
                                    iph4/* IP头 */,
                                    lIPpayloadLen/* IP数据长度 */,
                                    payload_len, &acl);

        return 1;
    }

    return 0;
}

static void process_l2tpv3_avps(struct flow_info *flow, int direction, const uint8_t *payload, uint32_t paylaod_len, struct l2tp_header * head) {
    int idx = 0;
    // int tmp_idx;
    // uint16_t length;

    ST_l2tp_dissect_result l2tp_dissect_result;
    memset(&l2tp_dissect_result, 0, sizeof(ST_l2tp_dissect_result));

    ST_l2tp_dissect_result *l2tp_res = &l2tp_dissect_result;
    l2tp_res->head = head;

    uint32_t sid = get_uint32_ntohl(payload, idx);
    idx += 4;

    UNUSED(direction);
    UNUSED(paylaod_len);
    UNUSED(sid);
}

static void process_l2tpv3_control(struct flow_info *flow, int direction, const uint8_t * payload, uint32_t payload_len, int offset, struct l2tp_header *head) {

    int idx = offset;
    int tmp_idx;
    uint16_t length;
    uint16_t control;

    ST_l2tp_dissect_result l2tp_dissect_result;
    memset(&l2tp_dissect_result, 0, sizeof(ST_l2tp_dissect_result));

    ST_l2tp_dissect_result *l2tp_res = &l2tp_dissect_result;
    l2tp_res->head = head;

    control = get_uint16_ntohs(payload, idx);
    idx += 2;

    if (LENGTH_BIT(control)) {
        length = get_uint16_ntohs(payload, idx);
        idx += 2;
    }

    /* Get Control Channel ID */
    head->ccid = get_uint16_ntohs(payload, idx);
    idx += 4;

    if (SEQUENCE_BIT(control)) {
        // Ns
        idx += 2;
        // Nr
        idx += 2;
    }

    if (!LENGTH_BIT(control))
        return;

    int msg_type = 0;
    uint8_t is_stop_ccn = 0;
    uint32_t avp_vendor_id;
    int avp_type;
    uint16_t avp_len;
    uint16_t ver_len_hidden;
    int        digest_idx = 0;
    uint16_t digest_avp_len = 0;

    while (idx < length) {
        ver_len_hidden = get_uint16_ntohs(payload, idx);
        avp_len = AVP_LENGTH(ver_len_hidden);
        avp_vendor_id = get_uint16_ntohs(payload, idx + 2);
        avp_type = get_uint16_ntohs(payload, idx + 4);

        if (avp_len < 6) {
            break;
        }

        if (avp_vendor_id != 0) {   // VENDOR_IETF
            // 暂时不处理
            break;
        }

        if (HIDDEN_BIT(ver_len_hidden)) { /* don't try do display hidden */
            idx += avp_len;
            continue;
        }

        idx += 2;
        avp_len -= 2;

        /* Special Case for handling Extended Vendor Id */
        if (avp_type == EXTENDED_VENDOR_ID) {
            idx += 2;

            // avp_vendor_id
            idx += 4;
            continue;
        }
        else {
            // avp_vendor_id
            idx += 2;
            avp_len -= 2;
        }

        idx += 2;
        avp_len -= 2;

        switch (avp_type) {
        case CONTROL_MESSAGE:
        {
            msg_type = get_uint16_ntohs(payload, idx);
            if (msg_type < 30)
                l2tp_res->szmessage_type = message_type_vals[msg_type].strptr;
            if (msg_type == MESSAGE_TYPE_StopCCN)
                is_stop_ccn = 1;
            break;
        }
        case RESULT_ERROR_CODE:
        {
            if (avp_len < 2)
                break;

            l2tp_res->v3_stop_ccn_result_code_f = 1;
            if (is_stop_ccn)
                l2tp_res->v3_stop_ccn_result_code = get_uint16_ntohs(payload, idx);
            else
                l2tp_res->v3_result_code = get_uint16_ntohs(payload, idx);

            idx += 2;
            avp_len -= 2;

            if (avp_len < 2)
                break;
            l2tp_res->v3_avp_error_code = get_uint16_ntohs(payload, idx);
            idx += 2;
            avp_len -= 2;

            if (avp_len == 0)
                break;

            l2tp_res->v3_avp_error_message.strptr = payload + idx;
            l2tp_res->v3_avp_error_message.value = avp_len;
            break;
        }

        case PROTOCOL_VERSION:
        {
            if (avp_len < 1)
                break;

            l2tp_res->szProtocol_version_flag = 1;
            l2tp_res->szProtocol_version = payload[idx];
            l2tp_res->szProtocol_reversion = payload[idx + 1];
            break;
        }
        case FRAMING_CAPABILITIES:
        {
            l2tp_res->szFarming_capabilities_flag = 1;
            l2tp_res->szFarming_capabilities = get_uint32_ntohl(payload, idx);
            break;
        }
        case BEARER_CAPABILITIES:
        {
            l2tp_res->szBear_capabilities_flag = 1;
            l2tp_res->szBear_capabilities = get_uint32_ntohl(payload, idx);
            break;
        }
        case TIE_BREAKER:
            l2tp_res->v3_tie_breaker_f = 1;
            l2tp_res->v3_tie_breaker.strptr = payload + idx;
            l2tp_res->v3_tie_breaker.value = 8;
            break;
        case FIRMWARE_REVISION:
            l2tp_res->szFirmwave_revision_flag = 1;
            l2tp_res->szFirmwave_revision = get_uint16_ntohs(payload, idx);
            break;
        case HOST_NAME:
            l2tp_res->szHost_name.value = avp_len;
            l2tp_res->szHost_name.strptr = payload + idx;
            break;
        case VENDOR_NAME:
            l2tp_res->szVendor_name.value = avp_len;
            l2tp_res->szVendor_name.strptr = payload + idx;
            break;
        case ASSIGNED_TUNNEL_ID:
            l2tp_res->szAssigned_tunnel_id_flag = 1;
            l2tp_res->szAssigned_tunnel_id = get_uint16_ntohs(payload, idx);
            break;
        case RECEIVE_WINDOW_SIZE:
            l2tp_res->v3_recv_win_size_f = 1;
            l2tp_res->v3_recv_win_size = get_uint16_ntohs(payload, idx);
            break;
        case CHALLENGE:
            l2tp_res->szChap_challenge.value = avp_len;
            l2tp_res->szChap_challenge.strptr = payload + idx;
            break;
        case CAUSE_CODE:
            if (avp_len < 2)
                break;
            l2tp_res->v3_cause_code_f = 1;
            l2tp_res->v3_cause_code = get_uint16_ntohs(payload, idx);
            idx += 2;
            avp_len -= 2;
            if (avp_len < 1)
                break;

            l2tp_res->v3_cause_msg = payload[idx];
            idx += 1;
            avp_len -= 1;

            if (avp_len == 0)
                break;
            break;
        case CHALLENGE_RESPONSE:
            l2tp_res->szChap_challenge_response.value = avp_len;
            l2tp_res->szChap_challenge_response.strptr = payload + idx;
            break;
        case ASSIGNED_SESSION:
            l2tp_res->szAssigned_session_id_flag = 1;
            l2tp_res->szAssigned_session_id = get_uint16_ntohs(payload, idx);
            break;
        case CALL_SERIAL_NUMBER:
            l2tp_res->szCall_serial_number_flag = 1;
            l2tp_res->szCall_serial_number = get_uint32_ntohl(payload, idx);
            break;
        case MINIMUM_BPS:
            l2tp_res->szMinimum_bps_flag = 1;
            l2tp_res->szMinimum_bps = get_uint32_ntohl(payload, idx);
            break;
        case MAXIMUM_BPS:
            l2tp_res->szMaximum_bps_flag = 1;
            l2tp_res->szMaximum_bps = get_uint32_ntohl(payload, idx);
            break;
        case BEARER_TYPE:
            l2tp_res->szBear_type_flag = 1;
            l2tp_res->szBear_type = get_uint32_ntohl(payload, idx);
            break;
        case FRAMING_TYPE:
            l2tp_res->szFarming_type_flag = 1;
            l2tp_res->szFarming_type = get_uint32_ntohl(payload, idx);
            break;
        case CALLED_NUMBER:
            l2tp_res->szCalled_number.value = avp_len;
            l2tp_res->szCalled_number.strptr = payload + idx;
            break;
        case CALLING_NUMBER:
            l2tp_res->szCalling_number.value = avp_len;
            l2tp_res->szCalling_number.strptr = payload + idx;
            break;
        case SUB_ADDRESS:
            if (avp_len == 0)
                break;
            l2tp_res->v3_sub_address.strptr = payload + idx;
            l2tp_res->v3_sub_address.value = avp_len;
            break;
        case TX_CONNECT_SPEED:
            l2tp_res->szTx_connect_speed = 1;
            l2tp_res->szTx_connect_speed = get_uint32_ntohl(payload, idx);
            break;
        case PHYSICAL_CHANNEL:
            l2tp_res->szPhysical_channel_flag = 1;
            l2tp_res->szPhysical_channel = get_uint32_ntohl(payload, idx);
            break;
        case INITIAL_RECEIVED_LCP_CONFREQ:
            l2tp_res->szInitial_received_lcp_confreq.value = avp_len;
            l2tp_res->szInitial_received_lcp_confreq.strptr = payload + idx;
            break;
        case LAST_SENT_LCP_CONFREQ:
            l2tp_res->szLast_sent_lcp_confreq.value = avp_len;
            l2tp_res->szLast_sent_lcp_confreq.strptr = payload + idx;
            break;
        case LAST_RECEIVED_LCP_CONFREQ:
            l2tp_res->szLast_received_lcp_confreq.value = avp_len;
            l2tp_res->szLast_received_lcp_confreq.strptr = payload + idx;
            break;
        case PROXY_AUTHEN_TYPE:
            l2tp_res->szProxy_authen_type_flag = 1;
            l2tp_res->szProxy_authen_type = get_uint16_ntohs(payload, idx);
            break;
        case PROXY_AUTHEN_NAME:
            l2tp_res->szProxy_authen_name.value = avp_len;
            l2tp_res->szProxy_authen_name.strptr = payload + idx;
            break;
        case PROXY_AUTHEN_CHALLENGE:
            l2tp_res->szProxy_authen_challenge.value = avp_len;
            l2tp_res->szProxy_authen_challenge.strptr = payload + idx;
            break;
        case PROXY_AUTHEN_ID:
            l2tp_res->szProxy_authen_id_flag = 1;
            l2tp_res->szProxy_authen_id = get_uint16_ntohs(payload, idx);
            break;
        case PROXY_AUTHEN_RESPONSE:
            l2tp_res->szProxy_authen_response.value = avp_len;
            l2tp_res->szProxy_authen_response.strptr = payload + idx;
            break;
        case CALL_STATUS_AVPS:
            if (avp_len < 2)
                break;
            idx += 2;
            avp_len -= 2;

            if (avp_len < 4)
                break;
            l2tp_res->v3_stat_crc_errors_f = 1;
            l2tp_res->v3_stat_crc_errors = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;

            if (avp_len < 4)
                break;
            l2tp_res->v3_stat_farming_errors_f = 1;
            l2tp_res->v3_stat_farming_errors = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;

            if (avp_len < 4)
                break;
            l2tp_res->v3_stat_hardware_overruns_f = 1;
            l2tp_res->v3_stat_hardware_overruns = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;

            if (avp_len < 4)
                break;
            l2tp_res->v3_stat_buffer_overruns_f = 1;
            l2tp_res->v3_stat_buffer_overruns = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;

            if (avp_len < 4)
                break;
            l2tp_res->v3_stat_timeout_errors_f = 1;
            l2tp_res->v3_stat_timeout_errors = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;

            if (avp_len < 4)
                break;
            l2tp_res->v3_stat_aligment_errors_f = 1;
            l2tp_res->v3_stat_aligment_errors = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;
            break;
        case ACCM:
            if (avp_len < 2)
                break;
            idx += 2;
            avp_len -= 2;

            if (avp_len < 4)
                break;
            l2tp_res->v3_accm_sent_f = 1;
            l2tp_res->v3_accm_sent = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;

            if (avp_len < 4)
                break;
            l2tp_res->v3_accm_recv_f = 1;
            l2tp_res->v3_accm_recv = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;
            break;
        case RANDOM_VECTOR:
            l2tp_res->v3_random_vector.strptr = payload + idx;
            l2tp_res->v3_random_vector.value = avp_len;
            break;
        case PRIVATE_GROUP_ID:
            l2tp_res->v3_private_groupid.strptr = payload + idx;
            l2tp_res->v3_private_groupid.value = avp_len;
            break;
        case RX_CONNECT_SPEED:
            l2tp_res->szRx_connect_speed = 1;
            l2tp_res->szRx_connect_speed = get_uint32_ntohl(payload, idx);
            break;
        case PPP_DISCONNECT_CAUSE_CODE:
            if (avp_len < 2)
                break;
            l2tp_res->v3_disc_code_f = 1;
            l2tp_res->v3_disc_code = get_uint16_ntohs(payload, idx);
            idx += 2;
            avp_len -= 2;

            if (avp_len < 2)
                break;
            l2tp_res->v3_disc_con_proto_no_f = 1;
            l2tp_res->v3_disc_con_proto_no = get_uint16_ntohs(payload, idx);
            idx += 2;
            avp_len -= 2;

            if (avp_len < 1)
                break;
            l2tp_res->v3_disc_cause_code_direct_f = 1;
            l2tp_res->v3_disc_cause_code_direct = payload[idx];
            idx += 1;
            avp_len -= 1;

            if (avp_len == 0)
                break;

            break;
        case MESSAGE_DIGEST:
        {
            digest_idx = idx;
            digest_avp_len = avp_len;
            break;
        }
        case ROUTER_ID:
            l2tp_res->szRouter_id_flag = 1;
            l2tp_res->szRouter_id = get_uint32_ntohl(payload, idx);
            break;
        case ASSIGNED_CONTROL_CONN_ID:
            if (avp_len < 4)
                break;
            // store ccid
            l2tp_res->v3_assigned_ccid_f = 1;
            l2tp_res->v3_assigned_ccid = get_uint32_ntohl(payload, idx);
            idx += 4;
            avp_len -= 4;
            break;
        case PW_CAPABILITY_LIST:
            while (avp_len >= 2) {
                // pw_type
                idx += 2;
                avp_len -= 2;
            }
            break;
        case LOCAL_SESSION_ID:
            l2tp_res->v3_local_session_id_f = 1;
            l2tp_res->v3_local_session_id = get_uint32_ntohl(payload, idx);
            break;
        case REMOTE_SESSION_ID:
            l2tp_res->v3_remote_session_id_f = 1;
            l2tp_res->v3_remote_session_id = get_uint32_ntohl(payload, idx);
            break;
        case ASSIGNED_COOKIE:
            break;
        case REMOTE_END_ID:
            l2tp_res->v3_remote_end_id.strptr = payload + idx;
            l2tp_res->v3_remote_end_id.value = avp_len;
            break;
        case PW_TYPE:
            l2tp_res->v3_pw_pseudowire_type_f = 1;
            l2tp_res->v3_pw_pseudowire_type = get_uint16_ntohs(payload, idx);
            break;
        case L2_SPECIFIC_SUBLAYER:
            l2tp_res->v3_spec_sublayer_f = 1;
            l2tp_res->v3_spec_sublayer = get_uint16_ntohs(payload, idx);
            break;
        case DATA_SEQUENCING:
            l2tp_res->v3_data_sequencing_f = 1;
            l2tp_res->v3_data_sequencing = get_uint16_ntohs(payload, idx);
            break;
        case CIRCUIT_STATUS:
            l2tp_res->v3_circuit_status_f = 1;
            l2tp_res->v3_circuit_status = get_uint16_ntohs(payload, idx);
            l2tp_res->v3_circuit_type_f = 1;
            l2tp_res->v3_circuit_type = get_uint16_ntohs(payload, idx);
            break;
        case PREFERRED_LANGUAGE:
            break;
        case CTL_MSG_AUTH_NONCE:
            break;
        case TX_CONNECT_SPEED_V3:
            if (avp_len < 8)
                break;

            l2tp_res->v3_tx_conn_speed.strptr = payload + idx;
            l2tp_res->v3_tx_conn_speed.value = 8;
            break;
        case CONNECT_SPEED_UPDATE:
        {
            if (avp_len == 12) {
                l2tp_res->v2_csu_f = 1;
                l2tp_res->v2_csu_res = get_uint16_ntohs(payload, idx + 2);
                l2tp_res->v2_csu_cur_tx_speed = get_uint32_ntohl(payload, idx + 4);
                l2tp_res->v2_csu_cur_rx_speed = get_uint32_ntohl(payload, idx + 8);
            }
            else {
                l2tp_res->v3_csu_f = 1;
                l2tp_res->v3_csu_remote_sessionid = get_uint32_ntohl(payload, idx);
                l2tp_res->v3_csu_tx_speed = get_uint64_t(payload, idx + 4);
                l2tp_res->v3_csu_rx_speed = get_uint64_t(payload, idx + 12);
            }

            break;
        }

        default:
            break;
        }

        idx += avp_len;
    }

    write_l2tp_control(flow, direction, l2tp_res);

    UNUSED(payload_len);
    UNUSED(tmp_idx);
    UNUSED(digest_idx);
    UNUSED(digest_avp_len);
}

int dissect_l2tp_ip(struct flow_info *flow, int direction, const uint8_t *payload, uint32_t payload_len, uint8_t flag _U_)
{
    if (payload_len < 6)
    {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_L2TP);
        return 0;
    }

    struct l2tp_header head;
    memset(&head, 0, sizeof(struct l2tp_header));

    int idx = 0;
    uint32_t sid = get_uint32_ntohl(payload, idx);
    if (sid == 0) {
        head.version = 3;
        process_l2tpv3_control(flow, direction, payload, payload_len, 4, &head);
    }
    else {
        process_l2tpv3_avps(flow, direction, payload, payload_len, &head);
    }

    return 0;
}

/*****************************************************************
 *Function    : dissect_l2tp_udp
 *Description : l2tp 协议解析
 *Input       :
 *Output      :
 *Return      :
 *Others      : none
 *****************************************************************/
static int dissect_l2tp_udp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if (payload_len < 6)
    {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_L2TP);
        return 0;
    }

    struct l2tp_header head;
    memset(&head, 0, sizeof(struct l2tp_header));

    head.version = (0x0f & payload[1]);      /*** L2TP 版本判断      ***/
    if(head.version != 2 && head.version != 3) /*** L2TP V3 后续再开发 ***/
        return 0;

    uint32_t idx = 0;
    uint16_t control = get_uint16_ntohs(payload, 0);

    if (head.version == 2) {
        idx += 2; /* 偏移到 control 域后 */

    head.szl2tp_type  = CONTROL_BIT(control) ;

    if (LENGTH_BIT(control))
    {
        if(get_uint16_ntohs(payload, idx) > payload_len)
            return 0;
        idx+=2; /* 偏移到 length 域后 */
    }

    head.sztunnel_id  = get_uint16_ntohs(payload, idx);
    head.szsession_id = get_uint16_ntohs(payload, idx + 2);
    idx+=4; /* 偏移到 session_id 域后 */

    flow->tunnel_id = head.sztunnel_id;

    /** 检测是否存在 NS  NR **/
    if (SEQUENCE_BIT(control))
    {
        //header_ns = get_uint16_ntohs(payload, idx);
        //header_nr = get_uint16_ntohs(payload, idx+2);
        idx+=4; /* 偏移到 nr 域后 */
    }

    if (OFFSET_BIT(control))
    {
        //offset_size = get_uint16_ntohs(payload, idx);
        idx+=2; /* 偏移到 offset 域后 */
    }

    if(idx + 6 > payload_len) //ZLB NO,  AVP or PPP need more
        return 0;

    payload     += idx;
    payload_len -= idx;

        if (head.szl2tp_type) //control mesage
        {
            dissect_l2tp_control(flow, direction, payload, payload_len, &head);
        }
        else //PPP
        {
            //子协议ppp为单字节或双字节
            if ((get_uint8_t(payload, 0) == 0x0021 && payload_len > 28) // IPV4 + UDP
                || (get_uint8_t(payload, 0) == 0x0057 && payload_len > 48)//IPV6
                || ((get_uint16_ntohs(payload, 0) == 0x0021 || get_uint16_ntohs(payload, 2) == 0x0021) && payload_len > 28) // IPV4 + UDP
                || ((get_uint16_ntohs(payload, 0) == 0x0057 || get_uint16_ntohs(payload, 2) == 0x0057) && payload_len > 48) )//IPV6
                dissect_l2tp_inner(payload, payload_len, flow, direction, &head);
            else
                dissect_ppp_family(flow, payload, payload_len, PROTOCOL_L2TP);
        }
    }
    else if (head.version == 3) {  // add by hongll
        if (CONTROL_BIT(control)) {
            /* Call to process l2tp v3 control message */
            process_l2tpv3_control(flow, direction, payload, payload_len, 0, &head);
        }
        else {
            /* Call to process l2tp v3 data message */
            process_l2tpv3_avps(flow, direction, payload, payload_len, &head);
        }
    }

    return 0;

    UNUSED(seq);
    UNUSED(flag);
}



/*****************************************************************
 *Function    : identify_l2tp_udp
 *Description : l2tp 协议识别接口
 *Input       : flow, payload, payload_len
 *Output      : none
 *Return      : void
 *Others      : none
 *****************************************************************/
static void identify_l2tp_udp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_L2TP] == 0 || payload_len < 6)
    {
        return;
    }
    if (ntohs(flow->tuple.inner.port_src) != UDP_PORT_L2TP && ntohs(flow->tuple.inner.port_dst) != UDP_PORT_L2TP)
    {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_L2TP);
        return ;
    }

    int version = (0x0F & payload[1]);
    if(version == 2 || version == 3){
        flow->real_protocol_id = PROTOCOL_L2TP;
        return;
    }

}


static void init_l2tp_dissector(void)
{

    dpi_register_proto_schema(l2tp_field_array,EM_L2TP_MAX,"l2tp");
    port_add_proto_head(IPPROTO_UDP, UDP_PORT_L2TP, PROTOCOL_L2TP);

    udp_detection_array[PROTOCOL_L2TP].proto         = PROTOCOL_L2TP;
    udp_detection_array[PROTOCOL_L2TP].identify_func = identify_l2tp_udp;
    udp_detection_array[PROTOCOL_L2TP].dissect_func  = dissect_l2tp_udp;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_L2TP].excluded_protocol_bitmask, PROTOCOL_L2TP);

    map_fields_info_register(l2tp_field_array,PROTOCOL_L2TP, EM_L2TP_MAX, "l2tp");

    return;
}


static __attribute((constructor)) void    before_init_l2tp(void){
    register_tbl_array(TBL_LOG_L2TP, 0, "l2tp", init_l2tp_dissector);
}

