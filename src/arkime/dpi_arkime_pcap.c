/*保存pcap - Arkime兼容的PCAP文件管理模块*/

#include <pthread.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>

#include "cJSON.h"
#include "dpi_arkime_if.h"

extern struct global_config g_config;

// PCAP文件头结构定义
struct pcap_file_header {
  uint32_t magic;
  uint16_t version_major;
  uint16_t version_minor;
  int32_t  thiszone;
  uint32_t sigfigs;
  uint32_t snaplen;
  uint32_t linktype;
};

// PCAP包头结构定义
struct pcap_pkthdr {
  uint32_t ts_sec;
  uint32_t ts_usec;
  uint32_t caplen;
  uint32_t len;
};

// 全局文件序号管理 - 使用原子操作替代互斥锁
static char              g_global_file_num_file[] = "/tmp/.g_global_file_num_file";
static volatile uint64_t g_global_file_num = 0;
FILE                    *g_global_file_num_file_fp = NULL;
/*
 * pthread_mutex优化：移除g_file_num_mutex
 * 原因：使用原子操作__sync_fetch_and_add替代互斥锁
 * 优势：更高的性能，无锁竞争，原子性保证
 */

// 全局文件信息存储结构
typedef struct arkime_file_info {
  uint64_t                 num;           // 文件ID（唯一标识）
  char                     name[512];     // 文件完整路径
  uint64_t                 first;         // 文件首包时间戳（毫秒）
  char                     node[64];      // 捕获节点名称
  uint64_t                 filesize;      // 文件大小（字节）
  int                      locked;        // 锁定状态：1=处理中，0=完成
  time_t                   create_time;   // 文件创建时间
  FILE                    *fp;            // 文件指针
  uint32_t                 packet_count;  // 包计数
  struct arkime_file_info *next;          // 链表指针
} arkime_file_info_t;

// 全局文件信息链表
static arkime_file_info_t *g_file_list_head = NULL;
static pthread_mutex_t     g_file_list_mutex = PTHREAD_MUTEX_INITIALIZER;

/*
 * pthread_mutex分析：g_file_list_mutex
 * 用途：保护全局文件信息链表的并发访问
 * 必要性：防止多线程同时修改链表结构导致数据竞争和内存错误
 * 优化建议：可以考虑使用无锁数据结构或读写锁，减少锁竞争
 */

// 当前活跃文件（每个线程一个）
static __thread arkime_file_info_t *current_file = NULL;

// 配置参数（从配置文件读取或使用默认值）
static uint64_t max_file_size_bytes = 1024 * 1024 * 1024;  // 默认1GB
static uint32_t max_file_time_seconds = 3600;              // 默认1小时
static char     pcap_dir[512] = "/tmp/tbls/arkime/raw";    // 默认PCAP目录
static char     node_name[64] = "localhost";               // 默认节点名

// 申请一个全局文件序号,线程安全 - 使用原子操作
uint64_t dpi_arkime_file_num_creat() {
  // 使用原子操作递增并返回新值，确保线程安全且性能更好
  __sync_add_and_fetch(&g_global_file_num, 1);
  return g_global_file_num;
}

// 创建文件信息JSON数据
static char *create_file_info_json(arkime_file_info_t *file_info) {
  if (!file_info) {
    return NULL;
  }

  cJSON *file_json = cJSON_CreateObject();
  if (!file_json) {
    return NULL;
  }

  cJSON_AddNumberToObject(file_json, "num", file_info->num);
  cJSON_AddStringToObject(file_json, "name", file_info->name + strlen(g_config.tbl_out_dir) + 1);
  cJSON_AddNumberToObject(file_json, "first", file_info->first);
  cJSON_AddStringToObject(file_json, "node", g_config.sdt_out_produce_data_dev_name);
  cJSON_AddNumberToObject(file_json, "filesize", file_info->filesize);
  cJSON_AddNumberToObject(file_json, "locked", file_info->locked);

  char *json_string = cJSON_PrintUnformatted(file_json);
  cJSON_Delete(file_json);

  return json_string;
}

int dpi_arkime_save_file_info_json(uint64_t file_id, const char *file_json) {
  if (!file_json || strlen(file_json) == 0) {
    DPI_LOG(DPI_LOG_WARNING, "Empty file JSON data");
    return -1;
  }

  char filename[512];
  snprintf(filename, sizeof(filename), "%s/pcap_index/%s-%s-pcap_index-%lu.json", g_config.tbl_out_dir,
      g_config.sdt_out_produce_data_dev_name, g_config.sdx_config.sdx_ip_str, file_id);

  DPI_LOG(DPI_LOG_DEBUG, "save file info to: %s", filename);
  FILE *fp = fopen(filename, "wb");
  if (!fp) {
    return -1;
  }
  fwrite(file_json, strlen(file_json), 1, fp);
  fclose(fp);
  return 1;
}

// 发送文件信息到ES
static int save_file_info_to_json(arkime_file_info_t *file_info) {
  if (!file_info || !g_config.es_config.enabled) {
    return 0;  // ES未启用时直接返回成功
  }
  int   result = 0;
  char *file_json = create_file_info_json(file_info);
  if (!file_json) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to create file info JSON for file %lu", file_info->num);
    return -1;
  }
  if (g_config.es_config.enabled == 2) {
    result = dpi_arkime_es_send_file_info(file_info->num, file_json);
    if (result == 0) {
      DPI_LOG(DPI_LOG_DEBUG, "Sent file info to ES: file_num=%lu, name=%s", file_info->num, file_info->name);
    } else {
      DPI_LOG(DPI_LOG_WARNING, "Failed to send file info to ES: file_num=%lu", file_info->num);
    }
  } else {
    result = dpi_arkime_save_file_info_json(file_info->num, file_json);
  }

  free(file_json);
  return result;
}

// 初始化配置参数
void dpi_arkime_pcap_init_config(dictionary *ini) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping pcap config initialization");
    return;
  }
  // 从[arkime]配置段读取参数
  // 读取配置参数
  const char *pcap_dir_str = iniparser_getstring(ini, "arkime:ARKIME_PCAP_DIR", "/opt/arkime/raw");
  uint32_t    max_file_size_mb = iniparser_getint(ini, "arkime:ARKIME_MAX_FILE_SIZE_MB", 1024);
  uint32_t    max_file_time_sec = iniparser_getint(ini, "arkime:ARKIME_MAX_FILE_TIME_SEC", 3600);
  const char *node_name_str = iniparser_getstring(ini, "arkime:ARKIME_NODE_NAME", "localhost");

  // 设置全局变量
  max_file_size_bytes = (uint64_t)max_file_size_mb * 1024 * 1024;
  max_file_time_seconds = max_file_time_sec;
  snprintf(pcap_dir, sizeof(pcap_dir), "%s", pcap_dir_str);
  snprintf(node_name, sizeof(node_name), "%s", node_name_str);

  printf("Arkime PCAP配置:\n");
  printf("  - 存储目录: %s\n", pcap_dir);
  printf("  - 最大文件大小: %u MB\n", max_file_size_mb);
  printf("  - 最大文件时间: %u 秒\n", max_file_time_sec);
  printf("  - 节点名称: %s\n", node_name);

  // 确保PCAP目录存在
  struct stat st = {0};
  if (stat(pcap_dir, &st) == -1) {
    if (mkdir(pcap_dir, 0755) == 0) {
      printf("创建PCAP目录: %s\n", pcap_dir);
    } else {
      printf("错误: 无法创建PCAP目录: %s\n", pcap_dir);
    }
  }

  char dir[512] = {0};
  snprintf(dir, sizeof(dir), "%s/pcap_index/", g_config.tbl_out_dir);

  if (access(dir, F_OK)) {
    if (mkdir(dir, 0755) == 0) {
      printf("创建PCAP索引目录: %s\n", dir);
    } else {
      printf("错误: 无法创建PCAP索引目录: %s\n", dir);
    }
  }

  if (access(g_global_file_num_file, F_OK)) {
    g_global_file_num_file_fp = fopen(g_global_file_num_file, "w");
    fwrite("0", 1, 1, g_global_file_num_file_fp);
    fflush(g_global_file_num_file_fp);
  } else {
    g_global_file_num_file_fp = fopen(g_global_file_num_file, "r+");
    char buff[16] = {0};
    fread(buff, sizeof(buff), 1, g_global_file_num_file_fp);
    g_global_file_num = atoll(buff);
  }
}

// 创建新的PCAP文件
arkime_file_info_t *dpi_arkime_create_pcap_file(uint64_t first_packet_time) {
  arkime_file_info_t *file_info = malloc(sizeof(arkime_file_info_t));
  if (!file_info) {
    return NULL;
  }

  memset(file_info, 0, sizeof(arkime_file_info_t));

  // 生成文件ID
  file_info->num = dpi_arkime_file_num_creat();

  // 生成文件名（格式：节点名-文件ID.pcap）
  time_t     now = time(NULL);
  struct tm *tm_info = localtime(&now);
  snprintf(file_info->name, sizeof(file_info->name), "%s/%s-%s-raw_pcap-%lu.pcap",
  pcap_dir,
  g_config.sdt_out_produce_data_dev_name,
  g_config.sdx_config.sdx_ip_str,
  file_info->num);

  // 设置其他字段
  file_info->first = first_packet_time;
  snprintf(file_info->node, sizeof(file_info->node), "%s", node_name);
  file_info->filesize = 0;
  file_info->locked = 1;  // 创建时锁定
  file_info->create_time = now;
  file_info->packet_count = 0;
  file_info->next = NULL;

  // 打开文件
  file_info->fp = fopen(file_info->name, "wb");
  if (!file_info->fp) {
    free(file_info);
    return NULL;
  }

  // 写入PCAP文件头
  struct pcap_file_header pcap_hdr;
  pcap_hdr.magic = 0xa1b2c3d4;
  pcap_hdr.version_major = 2;
  pcap_hdr.version_minor = 4;
  pcap_hdr.thiszone = 0;
  pcap_hdr.sigfigs = 0;
  pcap_hdr.snaplen = 65535;
  pcap_hdr.linktype = 1;  // DLT_EN10MB

  fwrite(&pcap_hdr, sizeof(pcap_hdr), 1, file_info->fp);
  file_info->filesize += sizeof(pcap_hdr);

  // 添加到全局文件列表
  pthread_mutex_lock(&g_file_list_mutex);
  file_info->next = g_file_list_head;
  g_file_list_head = file_info;
  if (g_global_file_num_file_fp) {
    ftell(g_global_file_num_file_fp);
    char buff[16] = {0};
    snprintf(buff, sizeof(buff), "%lu", file_info->num);
    fseek(g_global_file_num_file_fp, 0, SEEK_SET);
    fwrite(buff, sizeof(buff), 1, g_global_file_num_file_fp);
    fflush(g_global_file_num_file_fp);
  }
  pthread_mutex_unlock(&g_file_list_mutex);
  save_file_info_to_json(file_info);

  return file_info;
}

// 从全局文件列表中删除文件信息
static int remove_file_from_list(uint64_t file_num) {
  pthread_mutex_lock(&g_file_list_mutex);

  arkime_file_info_t *current = g_file_list_head;
  arkime_file_info_t *prev = NULL;

  while (current) {
    if (current->num == file_num) {
      // 找到要删除的文件
      if (prev) {
        prev->next = current->next;
      } else {
        g_file_list_head = current->next;
      }

      DPI_LOG(DPI_LOG_DEBUG, "Removed file from list: file_num=%lu, name=%s", current->num, current->name);

      // 发送文件信息到ES（在删除前）
      current->locked = 0;
      save_file_info_to_json(current);

      free(current);
      pthread_mutex_unlock(&g_file_list_mutex);
      return 0;
    }
    prev = current;
    current = current->next;
  }

  pthread_mutex_unlock(&g_file_list_mutex);
  DPI_LOG(DPI_LOG_WARNING, "File not found in list: file_num=%lu", file_num);
  return -1;
}

// 关闭PCAP文件
void dpi_arkime_close_pcap_file(arkime_file_info_t *file_info) {
  if (!file_info)
    return;

  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    // ES未启用时仍然需要关闭文件，但不发送到ES
    if (file_info->fp) {
      fclose(file_info->fp);
      file_info->fp = NULL;
    }
    return;
  }

  if (file_info->fp) {
    fclose(file_info->fp);
    file_info->fp = NULL;
  }

  // 更新文件大小
  struct stat st;
  if (stat(file_info->name, &st) == 0) {
    file_info->filesize = st.st_size;
  }

  // 设置为锁定状态
  file_info->locked = 1;

  DPI_LOG(DPI_LOG_INFO, "Closed PCAP file: %s (size: %lu bytes)", file_info->name, file_info->filesize);

  // 发送文件信息到ES并从列表中删除
  remove_file_from_list(file_info->num);
}

// 检查是否需要轮换文件
int dpi_arkime_need_rotate_file(arkime_file_info_t *file_info, uint32_t packet_len) {
  if (!file_info)
    return 1;

  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    // ES未启用时不进行文件轮换
    return 0;
  }

  time_t now = time(NULL);

  // 检查文件大小
  if (file_info->filesize + packet_len + sizeof(struct pcap_pkthdr) > max_file_size_bytes) {
    return 1;
  }

  // 检查文件时间
  if (now - file_info->create_time > max_file_time_seconds) {
    return 1;
  }

  return 0;
}

// 写入包数据到PCAP文件
int dpi_arkime_write_packet(
    arkime_file_info_t *file_info, const uint8_t *packet_data, uint32_t packet_len, uint64_t timestamp_usec) {
  if (!file_info || !file_info->fp || !packet_data) {
    return -1;
  }

  // 写入包头
  struct pcap_pkthdr pkt_hdr;
  pkt_hdr.ts_sec = timestamp_usec / 1000000;
  pkt_hdr.ts_usec = timestamp_usec % 1000000;
  pkt_hdr.caplen = packet_len;
  pkt_hdr.len = packet_len;

  size_t written = fwrite(&pkt_hdr, sizeof(pkt_hdr), 1, file_info->fp);
  if (written != 1) {
    return -1;
  }

  // 写入包数据
  written = fwrite(packet_data, packet_len, 1, file_info->fp);
  if (written != 1) {
    return -1;
  }

  // 更新文件信息
  file_info->filesize += sizeof(pkt_hdr) + packet_len;
  file_info->packet_count++;

  // 定期刷新文件
  if (file_info->packet_count % 100 == 0) {
    fflush(file_info->fp);
  }

  return 0;
}

// 获取当前线程的活跃文件，如果不存在或需要轮换则创建新文件
arkime_file_info_t *dpi_arkime_get_current_file(uint64_t timestamp_usec, uint32_t packet_len) {
  // 检查当前文件是否需要轮换
  if (current_file && dpi_arkime_need_rotate_file(current_file, packet_len)) {
    dpi_arkime_close_pcap_file(current_file);
    current_file = NULL;
  }

  // 如果没有当前文件，创建新文件
  if (!current_file) {
    current_file = dpi_arkime_create_pcap_file(timestamp_usec / 1000000);  // 转换为毫秒
  }

  return current_file;
}

// 主要接口：存入pcap包
int dpi_arkime_store_packet(
    const uint8_t *packet_data, uint32_t packet_len, uint64_t timestamp_usec, uint64_t *file_num, uint64_t *file_pos) {
  if (!packet_data || packet_len == 0) {
    return -1;
  }

  // 获取当前文件（如果需要会自动轮换）
  arkime_file_info_t *file_info = dpi_arkime_get_current_file(timestamp_usec, packet_len);
  if (!file_info) {
    return -1;
  }

  // 记录写入前的文件位置（这就是包的偏移量）
  uint64_t packet_offset = file_info->filesize;

  // 写入包数据
  int result = dpi_arkime_write_packet(file_info, packet_data, packet_len, timestamp_usec);

  // 如果写入成功，返回文件信息
  if (result == 0) {
    if (file_num) {
      *file_num = file_info->num;
    }
    if (file_pos) {
      *file_pos = packet_offset;
    }
  }

  return result;
}

// 获取文件信息的JSON格式字符串（用于调试和监控）
char *dpi_arkime_get_file_info_json(arkime_file_info_t *file_info) {
  if (!file_info)
    return NULL;

  static char json_buffer[1024];
  snprintf(json_buffer, sizeof(json_buffer),
      "{\n"
      "  \"num\": %lu,\n"
      "  \"name\": \"%s\",\n"
      "  \"first\": %lu,\n"
      "  \"node\": \"%s\",\n"
      "  \"filesize\": %lu,\n"
      "  \"locked\": %d\n"
      "}",
      file_info->num, file_info->name, file_info->first, file_info->node, file_info->filesize, file_info->locked);

  return json_buffer;
}

// 获取当前文件信息
char *dpi_arkime_get_current_file_info() {
  if (!current_file) {
    return NULL;
  }
  return dpi_arkime_get_file_info_json(current_file);
}

// 清理资源
void dpi_arkime_pcap_cleanup() {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping pcap cleanup");
    return;
  }

  // 关闭当前文件
  if (current_file) {
    dpi_arkime_close_pcap_file(current_file);
    current_file = NULL;
  }

  // 清理文件列表
  pthread_mutex_lock(&g_file_list_mutex);
  arkime_file_info_t *current = g_file_list_head;
  while (current) {
    arkime_file_info_t *next = current->next;
    if (current->fp) {
      fclose(current->fp);
      struct stat st;
      if (stat(current->name, &st) == 0) {
        current->filesize = st.st_size;
      }
      current->locked = 0;
      save_file_info_to_json(current);
    }
    free(current);
    current = next;
  }
  g_file_list_head = NULL;
  fclose(g_global_file_num_file_fp);
  g_global_file_num_file_fp = 0;
  pthread_mutex_unlock(&g_file_list_mutex);
}

// Arkime文件信息管理函数实现 - 链表版本

// 添加文件信息到flow的链表中 - 区分TCP与非TCP协议
int dpi_arkime_flow_add_file_info(struct flow_info *flow, uint64_t file_num, uint64_t file_pos) {
  if (!flow) {
    return -1;
  }

  // 判断是否为TCP协议
  bool is_tcp = false;
  if (flow->tuple.inner.proto == IPPROTO_TCP) {
    is_tcp = true;
  }

  // 非TCP协议：清空现有链表，只保存当前帧信息（一帧对应一条session记录）
  if (!is_tcp) {
    // 清空现有链表
    dpi_arkime_flow_clear_file_info(flow);

    // 使用内存池分配节点对（优化UDP流性能）
    struct arkime_file_pos_node *file_node, *pos_node;
    if (dpi_arkime_node_alloc_pair(&file_node, &pos_node) == NULL) {
      DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for UDP flow nodes from mempool");
      return -1;
    }

    // 设置文件标识节点
    file_node->value = -1 * (int64_t)file_num;  // 负值表示文件号
    file_node->next = pos_node;
    flow->arkime_file_pos_list = file_node;

    // 设置文件位置节点
    pos_node->value = (int64_t)file_pos;  // 正值表示文件偏移量
    pos_node->next = NULL;

    DPI_LOG(DPI_LOG_DEBUG, "Added arkime file info to non-TCP flow %lu: file_num=%lu, file_pos=%lu (mempool)", flow->flow_id,
        file_num, file_pos);
    return 0;
  }

  // TCP协议：追加到现有链表（一条记录对应多个报文）
  // 检查当前链表的最后一个节点
  struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
  struct arkime_file_pos_node *last = NULL;

  // 找到链表尾部
  while (current) {
    last = current;
    current = current->next;
  }

  // 检查是否需要插入文件标识（-1 * file_num）
  bool need_file_marker = true;

  // 如果链表为空，需要插入文件标识
  if (!last) {
    need_file_marker = true;
  } else {
    // 从链表尾部向前查找最近的文件标识
    struct arkime_file_pos_node *search = flow->arkime_file_pos_list;
    struct arkime_file_pos_node *last_file_marker = NULL;

    while (search) {
      if (search->value < 0) {
        last_file_marker = search;  // 记录最后一个文件标识
      }
      search = search->next;
    }

    // 如果找到了文件标识，检查是否是同一个文件
    if (last_file_marker) {
      uint64_t last_file_num = (uint64_t)(-last_file_marker->value);
      if (last_file_num == file_num) {
        need_file_marker = false;  // 同一个文件，不需要插入文件标识
      }
    }
  }

  // 如果需要，先插入文件标识
  if (need_file_marker) {
    struct arkime_file_pos_node *file_node = dpi_arkime_node_alloc();
    if (!file_node) {
      DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for file marker node from mempool");
      return -1;
    }

    file_node->value = -1 * (int64_t)file_num;  // 负值表示文件号
    file_node->next = NULL;

    if (last) {
      last->next = file_node;
    } else {
      flow->arkime_file_pos_list = file_node;
    }
    last = file_node;
  }

  // 插入文件位置
  struct arkime_file_pos_node *pos_node = dpi_arkime_node_alloc();
  if (!pos_node) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for position node from mempool");
    return -1;
  }

  pos_node->value = (int64_t)file_pos;  // 正值表示文件偏移量
  pos_node->next = NULL;

  if (last) {
    last->next = pos_node;
  } else {
    flow->arkime_file_pos_list = pos_node;
  }

  DPI_LOG(DPI_LOG_DEBUG, "Added arkime file info to TCP flow %lu: file_num=%lu, file_pos=%lu (mempool)", flow->flow_id, file_num,
      file_pos);

  return 0;
}

// 清空flow的文件信息链表
void dpi_arkime_flow_clear_file_info(struct flow_info *flow) {
  if (!flow) {
    return;
  }

  struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
  while (current) {
    struct arkime_file_pos_node *next = current->next;
    dpi_arkime_node_free(current);  // 使用内存池释放
    current = next;
  }

  flow->arkime_file_pos_list = NULL;

  DPI_LOG(DPI_LOG_DEBUG, "Cleared arkime file info for flow %lu (mempool)", flow->flow_id);
}

// 获取flow的文件信息数量（统计链表中的文件数）
int arkime_flow_get_file_count(struct flow_info *flow) {
  if (!flow) {
    return 0;
  }

  int                          file_count = 0;
  struct arkime_file_pos_node *current = flow->arkime_file_pos_list;

  while (current) {
    if (current->value < 0) {  // 负值表示文件标识
      file_count++;
    }
    current = current->next;
  }

  return file_count;
}

// 获取flow的文件位置数组（用于JSON构造）
int arkime_flow_get_file_pos_array(struct flow_info *flow, int64_t **pos_array, int *array_size) {
  if (!flow || !pos_array || !array_size) {
    return -1;
  }

  // 先统计链表长度
  int                          count = 0;
  struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
  while (current) {
    count++;
    current = current->next;
  }

  if (count == 0) {
    *pos_array = NULL;
    *array_size = 0;
    return 0;
  }

  // 分配数组内存
  *pos_array = malloc(count * sizeof(int64_t));
  if (!*pos_array) {
    return -1;
  }

  // 复制链表数据到数组
  int index = 0;
  current = flow->arkime_file_pos_list;
  while (current) {
    (*pos_array)[index++] = current->value;
    current = current->next;
  }

  *array_size = count;
  return 0;
}

// ProtoRecord arkime信息管理函数实现 - 直接操作arkime_file_pos_node链表

// 复制arkime_file_pos_node链表
int dpi_arkime_record_copy_file_info(struct arkime_file_pos_node *src_list, struct arkime_file_pos_node **dst_list) {
  if (!dst_list) {
    return -1;
  }

  // 先清空目标链表
  arkime_record_clear_file_info_list(dst_list);

  if (!src_list) {
    *dst_list = NULL;
    return 0;
  }

  // 复制源链表
  struct arkime_file_pos_node *src_current = src_list;
  struct arkime_file_pos_node *dst_last = NULL;

  while (src_current) {
    struct arkime_file_pos_node *new_node = malloc(sizeof(struct arkime_file_pos_node));
    if (!new_node) {
      DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for file info node");
      arkime_record_clear_file_info_list(dst_list);  // 清理已分配的节点
      return -1;
    }

    new_node->value = src_current->value;
    new_node->next = NULL;

    if (dst_last) {
      dst_last->next = new_node;
    } else {
      *dst_list = new_node;
    }

    dst_last = new_node;
    src_current = src_current->next;
  }

  DPI_LOG(DPI_LOG_DEBUG, "Copied arkime file info chain");
  return 0;
}

// 清空arkime_file_pos_node链表
void arkime_record_clear_file_info_list(struct arkime_file_pos_node **list) {
  if (!list || !*list) {
    return;
  }

  struct arkime_file_pos_node *current = *list;
  while (current) {
    struct arkime_file_pos_node *next = current->next;
    free(current);
    current = next;
  }

  *list = NULL;
  DPI_LOG(DPI_LOG_DEBUG, "Cleared arkime file info list");
}

// 获取arkime_file_pos_node链表中的文件数量
int arkime_record_get_file_count_from_list(struct arkime_file_pos_node *list) {
  int                          file_count = 0;
  struct arkime_file_pos_node *current = list;

  while (current) {
    if (current->value < 0) {  // 负值表示文件标识
      file_count++;
    }
    current = current->next;
  }

  return file_count;
}
// 获取arkime_file_pos_node链表的文件位置数组（用于JSON构造）
int arkime_record_get_file_pos_array_from_list(struct arkime_file_pos_node *list, int64_t **pos_array, int *array_size) {
  if (!pos_array || !array_size) {
    return -1;
  }

  // 先统计链表长度
  int                          count = 0;
  struct arkime_file_pos_node *current = list;
  while (current) {
    count++;
    current = current->next;
  }

  if (count == 0) {
    *pos_array = NULL;
    *array_size = 0;
    return 0;
  }

  // 分配数组内存
  *pos_array = malloc(count * sizeof(int64_t));
  if (!*pos_array) {
    return -1;
  }

  // 复制链表数据到数组
  int index = 0;
  current = list;
  while (current) {
    (*pos_array)[index++] = current->value;
    current = current->next;
  }

  *array_size = count;
  return 0;
}
