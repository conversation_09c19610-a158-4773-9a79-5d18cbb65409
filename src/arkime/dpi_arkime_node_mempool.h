/*
 * Arkime文件位置节点内存池管理器
 *
 * 功能：
 * 1. 为arkime_file_pos_node提供高效的内存分配和回收
 * 2. 减少频繁malloc/free的开销，特别是UDP流的处理
 * 3. 支持线程安全的并发访问
 * 4. 提供节点对分配优化，减少UDP流的分配次数
 */

#ifndef DPI_ARKIME_NODE_MEMPOOL_
#define DPI_ARKIME_NODE_MEMPOOL_

#include "dpi_arkime_if.h"
#ifdef DPDK_MEMPOOL
#include <rte_atomic.h>
#include <rte_mempool.h>
#endif

// 内存池管理结构
struct arkime_node_mempool_manager {
  struct rte_mempool *pool;         // DPDK内存池
  uint32_t            total_nodes;  // 总节点数
  uint32_t            cache_size;   // 缓存大小
  char                name[64];     // 内存池名称
  rte_atomic32_t      alloc_count;  // 分配计数器
  rte_atomic32_t      free_count;   // 释放计数器
};

#ifdef DPDK_MEMPOOL


// 清理Arkime节点内存池
void dpi_arkime_node_mempool_cleanup(void);
// 分配单个节点
struct arkime_file_pos_node *dpi_arkime_node_alloc(void);

// 释放单个节点
void dpi_arkime_node_free(struct arkime_file_pos_node *node);

// 分配节点对（专为UDP流优化）
struct arkime_file_pos_node *dpi_arkime_node_alloc_pair(
    struct arkime_file_pos_node **file_node, struct arkime_file_pos_node **pos_node);

// 释放节点对
void dpi_arkime_node_free_pair(struct arkime_file_pos_node *file_node, struct arkime_file_pos_node *pos_node);

#else
// 非DPDK环境下的简单实现

void arkime_node_mempool_init(void);

void arkime_node_mempool_cleanup(void);

struct arkime_file_pos_node *arkime_node_alloc(void);
void                         arkime_node_free(struct arkime_file_pos_node *node);
struct arkime_file_pos_node *arkime_node_alloc_pair(
    struct arkime_file_pos_node **file_node, struct arkime_file_pos_node **pos_node);

void arkime_node_free_pair(struct arkime_file_pos_node *file_node, struct arkime_file_pos_node *pos_node);

#endif

#endif  //DPI_ARKIME_NODE_MEMPOOL_