/*
 * dpi_arkime_pcap.h - Arkime兼容的PCAP文件管理模块头文件
 */

#ifndef DPI_ARKIME_PCAP_H
#define DPI_ARKIME_PCAP_H

#include <stdint.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

// 文件信息结构体前向声明
typedef struct arkime_file_info arkime_file_info_t;
struct flow_info;


struct arkime_file_pos_node {
  int64_t                      value;  // 负值表示文件号(-1*file_num)，正值表示文件偏移量
  struct arkime_file_pos_node *next;
};

void arkime_record_clear_file_info_list(struct arkime_file_pos_node **list);
// ProtoRecord arkime信息管理函数 - 直接操作arkime_file_pos_node链表
int arkime_record_get_file_count_from_list(struct arkime_file_pos_node *list);
int arkime_record_get_file_pos_array_from_list(struct arkime_file_pos_node *list, int64_t **pos_array, int *array_size);

// Arkime文件信息管理函数 - 链表版本
int arkime_flow_get_file_count(struct flow_info *flow);
int arkime_flow_get_file_pos_array(struct flow_info *flow, int64_t **pos_array, int *array_size);

// iniparser字典类型前向声明
#ifndef INIPARSER_H
typedef struct _dictionary_ dictionary;
#endif

/**
 * 初始化配置参数
 * 从配置文件读取相关参数，创建必要的目录
 * @param ini 配置文件字典指针，如果为NULL则使用默认配置
 */
void dpi_arkime_pcap_init_config(dictionary *ini);

/**
 * 申请一个全局文件序号（线程安全）
 * @return 全局唯一的文件序号
 */
uint64_t dpi_arkime_file_num_creat(void);


/**
 * 获取当前文件信息的JSON格式字符串
 * @return JSON格式的文件信息字符串，如果没有当前文件返回NULL
 */
char *dpi_arkime_get_current_file_info(void);

/**
 * 清理资源
 * 关闭所有打开的文件，释放内存
 */
void dpi_arkime_pcap_cleanup(void);

#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_PCAP_H */
