#ifndef DPI_ARKIME_IF_H
#define DPI_ARKIME_IF_H
#include "dpi_arkime_es.h"
#include "dpi_arkime_field.h"
#include "dpi_arkime_pcap.h"
#include "dpi_arkime_session.h"
#include "dpi_arkime_node_mempool.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 注册所有已注册协议的字段到Elasticsearch
 * 遍历所有通过record注册的协议和字段，生成ES字段定义并发送
 * 字段格式：
 * - type: 全部为termfield
 * - _id: layer.field
 * - friendlyName: field_name
 * - group: layer名
 * - help: 不填充
 * - dbField2: 与_id一样
 *
 * @return 注册的字段总数，失败返回-1
 */
int dpi_arkime_register_all_fields(void);

/**
 * 从配置文件初始化ES模块配置
 * @param ini 配置文件字典
 * @return 0成功，-1失败
 */
void dpi_arkime_init_config(void *ini);

//全局清理
void dpi_arkime_clean();

/**
 * 发送会话数据到ES - 完全基于record
 * @param record 协议记录指针（包含所有必要信息）
 * @return 0成功，-1失败
 */
int dpi_arkime_send_session_to_es(struct flow_info *flow, precord_t *record);

int dpi_arkime_record_copy_file_info(struct arkime_file_pos_node *src_list, struct arkime_file_pos_node **dst_list);

int dpi_arkime_flow_add_file_info(struct flow_info *flow, uint64_t file_num, uint64_t file_pos);

void dpi_arkime_flow_clear_file_info(struct flow_info *flow);

/**
 * 存储数据包到PCAP文件
 * 这是主要的接口函数，会自动处理文件轮换
 *
 * @param packet_data 包数据指针
 * @param packet_len 包数据长度
 * @param timestamp_usec 时间戳（微秒）
 * @param file_num 输出参数，返回文件序号
 * @param file_pos 输出参数，返回包在文件中的偏移量
 * @return 0成功，-1失败
 */
int dpi_arkime_store_packet(const uint8_t *packet_data, uint32_t packet_len, uint64_t timestamp_usec,
                            uint64_t *file_num, uint64_t *file_pos);

// 初始化Arkime节点内存池
void dpi_arkime_node_mempool_init(void);

//输出flow_i流表
int write_flow_i_log(struct flow_info *flow);
void proto_register_flow_m_common(pschema_t  *schema );
void proto_register_flow_m_user_layer(pschema_t  *schema );

#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_IF_H */
