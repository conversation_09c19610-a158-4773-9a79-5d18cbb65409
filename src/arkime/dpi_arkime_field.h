/*
 * Arkime字段注册模块头文件
 * 遍历record注册过的字段，生成ES字段注册消息
 */

#ifndef DPI_ARKIME_FIELD_H
#define DPI_ARKIME_FIELD_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif



/**
 * 注册指定协议的字段到Elasticsearch
 * 用于动态注册新协议的字段
 *
 * @param proto_name 协议名称
 * @return 注册的字段数量，失败返回-1
 */
int dpi_arkime_register_proto_fields(const char *proto_name);

/**
 * 重置字段注册状态
 * 主要用于测试，允许重新注册字段
 */
void dpi_arkime_reset_field_registration(void);

/**
 * 检查字段是否已注册
 * @return 1已注册，0未注册
 */
int dpi_arkime_is_fields_registered(void);

/**
 * 获取字段注册统计信息
 * @param proto_count 输出参数，协议数量
 * @param field_count 输出参数，字段总数
 * @return 0成功，-1失败
 */
int dpi_arkime_get_field_stats(int *proto_count, int *field_count);

#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_FIELD_H */
