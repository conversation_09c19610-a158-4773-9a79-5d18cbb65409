/*打包session的json结构*/

#include <arpa/inet.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>
#include <yaProtoRecord/precord.h>

#include "../../include/cJSON.h"
#include "dpi_arkime_if.h"

struct flow_info;
extern struct global_config g_config;

static void fvalue_to_json_object(cJSON *json_obj, const char *field_names, ya_fvalue_t *fvalue) {
  if (fvalue) {
    char *value_str = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
    if (!value_str) {
      return;
    }
    // 智能类型转换：尝试解析为数字，失败则作为字符串
    char     *endptr;
    long long num_val = strtoll(value_str, &endptr, 10);
    if (*endptr == '\0') {
      // 成功解析为整数
      cJSON_AddNumberToObject(json_obj, field_names, num_val);
    } else {
      // 作为字符串处理
      cJSON_AddStringToObject(json_obj, field_names, value_str);
    }
    free(value_str);
  }
}
// 辅助函数：从record中提取字段并添加到指定的cJSON对象中
static void add_fields_to_json_object(precord_t *record, cJSON *json_obj, char *field_names[], int field_count) {
  if (!record || !json_obj || !field_names) {
    return;
  }

  for (int i = 0; i < field_count; i++) {
    pfield_t    *field  = precord_field_get_by_name(record, field_names[i]);
    ya_fvalue_t *fvalue = precord_field_get_fvalue(field);
    fvalue_to_json_object(json_obj, field_names[i], fvalue);
  }
}


// 从record创建会话JSON数据
char *dpi_arkime_create_session_json_from_record(struct flow_info *flow,  precord_t *record) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return NULL;  // ES未启用时返回NULL
  }

  if (!record) {
    DPI_LOG(DPI_LOG_ERROR, "Invalid record pointer");
    return NULL;
  }

  cJSON *session = cJSON_CreateObject();
  if (!session) {
    return NULL;
  }

  // 添加@timestamp字段（当前时间戳，毫秒）
  struct timeval current_time;
  gettimeofday(&current_time, NULL);
  uint64_t timestamp_ms = ((uint64_t)current_time.tv_sec) * 1000 + ((uint64_t)current_time.tv_usec) / 1000;
  cJSON_AddNumberToObject(session, "@timestamp", timestamp_ms);

  // 创建protocols数组
  cJSON *protocols_array = cJSON_CreateArray();
  if (!protocols_array) {
    cJSON_Delete(session);
    return NULL;
  }

  // 创建source和destination对象
  cJSON *source      = cJSON_CreateObject();
  cJSON *destination = cJSON_CreateObject();
  cJSON *network     = cJSON_CreateObject();
  if (!source || !destination || !network) {
    cJSON_Delete(session);
    cJSON_Delete(protocols_array);
    if (source) cJSON_Delete(source);
    if (destination) cJSON_Delete(destination);
    if (network) cJSON_Delete(network);
    return NULL;
  }

  // 添加node字段
  cJSON_AddStringToObject(session, "node", g_config.sdt_out_produce_data_dev_name);

  // 从flow中获取基本会话信息
  cJSON_AddNumberToObject(session, "firstPacket", flow->create_time/1000);
  cJSON_AddNumberToObject(session, "lastPacket", flow->last_seen/1000);
  cJSON_AddNumberToObject(session, "length", flow->last_seen - flow->create_time);

  // 从flow的五元组中获取协议信息
  cJSON_AddNumberToObject(session, "ipProtocol", flow->tuple.inner.proto);

  // 添加总数据字节数
  uint64_t total_data_bytes = flow->src2dst_bytes + flow->dst2src_bytes;
  cJSON_AddNumberToObject(session, "totDataBytes", total_data_bytes);

  // 从flow中获取源地址和端口信息
  char src_ip_str[INET6_ADDRSTRLEN];
  char dst_ip_str[INET6_ADDRSTRLEN];

  if (flow->ip_version == 4) {
    // IPv4地址转换
    struct in_addr src_addr, dst_addr;
    memcpy(&src_addr.s_addr, flow->tuple.inner.ip_src, 4);
    memcpy(&dst_addr.s_addr, flow->tuple.inner.ip_dst, 4);
    inet_ntop(AF_INET, &src_addr, src_ip_str, INET_ADDRSTRLEN);
    inet_ntop(AF_INET, &dst_addr, dst_ip_str, INET_ADDRSTRLEN);
  } else {
    // IPv6地址转换
    struct in6_addr src_addr6, dst_addr6;
    memcpy(&src_addr6, flow->tuple.inner.ip_src, 16);
    memcpy(&dst_addr6, flow->tuple.inner.ip_dst, 16);
    inet_ntop(AF_INET6, &src_addr6, src_ip_str, INET6_ADDRSTRLEN);
    inet_ntop(AF_INET6, &dst_addr6, dst_ip_str, INET6_ADDRSTRLEN);
  }

  // 添加source字段（从flow中获取）
  cJSON_AddStringToObject(source, "ip", src_ip_str);
  cJSON_AddNumberToObject(source, "port", flow->tuple.inner.port_src);
  cJSON_AddNumberToObject(source, "bytes", flow->src2dst_bytes);
  cJSON_AddNumberToObject(source, "packets", flow->src2dst_packets);

  // 添加source MAC地址（从flow的session_ethhdr中获取）
  cJSON *src_mac_array = cJSON_CreateArray();
  if (src_mac_array) {
    char src_mac_str[18];
    snprintf(src_mac_str, sizeof(src_mac_str), "%02x:%02x:%02x:%02x:%02x:%02x",
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_source[0],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_source[1],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_source[2],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_source[3],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_source[4],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_source[5]);
    cJSON_AddItemToArray(src_mac_array, cJSON_CreateString(src_mac_str));
    cJSON_AddItemToObject(source, "mac", src_mac_array);
  }

  // 添加destination字段（从flow中获取）
  cJSON_AddStringToObject(destination, "ip", dst_ip_str);
  cJSON_AddNumberToObject(destination, "port", flow->tuple.inner.port_dst);
  cJSON_AddNumberToObject(destination, "bytes", flow->dst2src_bytes);
  cJSON_AddNumberToObject(destination, "packets", flow->dst2src_packets);

  // 添加destination MAC地址（从flow的session_ethhdr中获取）
  cJSON *dst_mac_array = cJSON_CreateArray();
  if (dst_mac_array) {
    char dst_mac_str[18];
    snprintf(dst_mac_str, sizeof(dst_mac_str), "%02x:%02x:%02x:%02x:%02x:%02x",
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_dest[0],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_dest[1],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_dest[2],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_dest[3],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_dest[4],
             flow->session_ethhdr[FLOW_DIR_SRC2DST].h_dest[5]);
    cJSON_AddItemToArray(dst_mac_array, cJSON_CreateString(dst_mac_str));
    cJSON_AddItemToObject(destination, "mac", dst_mac_array);
  }

  // 计算并添加network统计（从flow中获取）
  uint64_t total_bytes   = flow->src2dst_bytes + flow->dst2src_bytes;
  uint64_t total_packets = flow->src2dst_packets + flow->dst2src_packets;

  cJSON_AddNumberToObject(network, "bytes", total_bytes);
  cJSON_AddNumberToObject(network, "packets", total_packets);

  // 添加packet_pos和fileId数组（从record的文件信息链表中获取）
  cJSON *packet_pos = cJSON_CreateArray();
  cJSON *file_id    = cJSON_CreateArray();
  if (packet_pos && file_id) {
    // 获取record的文件位置数组
    int64_t *pos_array  = NULL;
    int      array_size = 0;

    // 使用函数获取文件位置数组
    if (arkime_record_get_file_pos_array_from_list(flow->arkime_file_pos_list, &pos_array, &array_size) == 0 && pos_array && array_size > 0) {
      // 添加所有文件位置信息到packet_pos数组
      for (int i = 0; i < array_size; i++) {
        cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(pos_array[i]));
      }

      // 提取文件ID到fileId数组（负值表示文件ID）
      for (int i = 0; i < array_size; i++) {
        if (pos_array[i] < 0) {  // 负值表示文件ID
          cJSON_AddItemToArray(file_id, cJSON_CreateNumber(-pos_array[i]));
        }
      }

      free(pos_array);  // 释放分配的内存
    }
    // 如果没有文件信息，不添加默认值，保持数组为空
  }

  // 将所有对象添加到session
  cJSON_AddItemToObject(session, "source", source);
  cJSON_AddItemToObject(session, "destination", destination);
  cJSON_AddItemToObject(session, "network", network);
  cJSON_AddItemToObject(session, "protocols", protocols_array);
  if (packet_pos) cJSON_AddItemToObject(session, "packet_pos", packet_pos);
  if (file_id) cJSON_AddItemToObject(session, "file_id", file_id);
  int protocol_num = 0;
  if (flow->tuple.inner.proto == 6) {
    protocol_num++;
    // 添加协议到protocols数组
    cJSON_AddItemToArray(protocols_array, cJSON_CreateString("tcp"));
  } else if (flow->tuple.inner.proto == 17) {
    cJSON_AddItemToArray(protocols_array, cJSON_CreateString("udp"));
  }
  // 遍历协议层
  for (player_t *layer = precord_layer_get_first(record); layer != NULL;
       layer           = precord_layer_get_next(record, layer)) {
    const char *layer_name = precord_layer_get_layer_name(layer);
    precord_layer_move_cursor(record, layer_name);
    if (strcmp("common", layer_name) == 0 || strcmp("general", layer_name) == 0 || strcmp("link", layer_name) == 0) {
      continue;
    }
    // 添加协议到protocols数组
    cJSON *protocol_name = cJSON_CreateString(layer_name);
    if (protocol_name) {
      cJSON_AddItemToArray(protocols_array, protocol_name);
    }
    protocol_num ++;
    // 检查是否已存在该协议的对象
    cJSON *proto_obj = cJSON_GetObjectItem(session, layer_name);
    if (!proto_obj) {
      proto_obj = cJSON_CreateObject();
      if (proto_obj) {
        cJSON_AddItemToObject(session, layer_name, proto_obj);
      }
    }

    // 遍历当前层的所有字段
    for (pfield_t *field = precord_field_get_first(record); field != NULL;
         field           = precord_field_get_next(record, field)) {
      pfield_desc_t *fdesc      = precord_field_get_fdesc(field);
      ya_fvalue_t   *fvalue     = precord_field_get_fvalue(field);
      const char    *field_name = pfdesc_get_name(fdesc);

      fvalue_to_json_object(proto_obj, field_name, fvalue);
    }
  }
  cJSON_AddNumberToObject(network, "protocolCnt", protocol_num);
  char *json_string = cJSON_PrintUnformatted(session);
  cJSON_Delete(session);
  DPI_LOG(DPI_LOG_DEBUG, "Created session JSON from record with file info and general fields");

  return json_string;
}

// 发送会话数据到ES - 完全基于record
int dpi_arkime_send_session_to_es(struct flow_info *flow, precord_t *record) {
  if (!record || !flow || 2 != g_config.es_config.enabled) {
    return 0;  // ES未启用或无效record时直接返回成功
  }

  char *session_json = dpi_arkime_create_session_json_from_record(flow, record);
  if (!session_json) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to create session JSON from record");
    return -1;
  }
  printf("session_json : %s \n",session_json);
  int result = dpi_arkime_es_send_session(session_json);
  if (result == 0) {
    DPI_LOG(DPI_LOG_DEBUG, "Sent session data to ES");
  } else {
    DPI_LOG(DPI_LOG_WARNING, "Failed to send session data to ES");
  }

  free(session_json);
  return result;
}