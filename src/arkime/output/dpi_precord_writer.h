#ifndef DPI_PRECORD_WRITER__H
#define DPI_PRECORD_WRITER__H

extern "C" {
#include "dpi_log.h"
#include "dpi_proto_ids.h"
}
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <fcntl.h>
#include <unordered_map>
#include <fstream>
#include <stdio.h>
#include <chrono>
class ProtoRecordWriterKeeper;

/*
 * 某协议的 record writer
 * 需求:
 * 1) 当没有调用任何 writeRecord 时，并不在磁盘上产生大小为零的空文件;
 * 2) 当一个文件超过 10s 没有发生任何写入时，将其关闭;
 * 3) 当一个文件行数超过 n 时，将其关闭;
 * 4) 支持进程存在时将任何输出文件删除后的文件重建，即文件可能随时需要重新创建;
 */
class ProtoRecordWriter
{
public:
    ProtoRecordWriter(ProtoRecordWriter<PERSON>eeper *keeper, const std::string &strProto, const std::string &strOutputRootPath,uint64_t protoFileSerialNumber,int outputFormat);

    int writeRecord(precord_t *record);
    // 支持用户自定义 record 合并到 proto record 中
    int writeRecord(precord_t *record, precord_t *app_record);
    // int writeRecord(precord_t *record, const uint8_t * pdata, int len);
    int onIdleCheck(uint64_t timeNow);

    int onProcessGoingToTerminate();

    int         setRecordCountPerFile(uint32_t recordCount);
    uint64_t    getFileSerialNumber() { return protoFileSerialNumber_; }
    void        setFileSerialNumber(uint32_t number = 0) { protoFileSerialNumber_ = number; }
    std::string getWriterProtoName() { return protoName_; }
    std::string getWriterUpperProtoName() { return UpperStrProtoName_; }
    inline void setOutputRootPath(const std::string strOutputRootPath) { outputRootPath_ = strOutputRootPath; }

  private:  // 生成文件路径
    std::string generateOutputParentDirPath();

    std::string generateOutputFileName();

    std::string generateOutputFilePath();

private: // 创建与关闭文件
    bool currentFileShouldRotate(uint64_t time_now);

    int openNewOutputFile();

    int closeCurrentFile();

private: // 写入记录
    int writeOneRecord_in_json(precord_t *record);
    int writeOneRecord_in_json(precord_t *record, precord_t *app_record);

    int WriteRecordToJson(precord_t * record);
    int writeOneRecord_in_xml(precord_t *record);
    int writeOneRecord_in_tbl(precord_t *record);

    uint64_t getNewFileSerialNumber(){
      return protoFileSerialNumber_++;
    }
private:
    uint8_t     writerOutputFormat_ = 0;
    ProtoRecordWriterKeeper *keeper_ = NULL;;
    std::string protoName_;
    std::string UpperStrProtoName_;
    std::string outputRootPath_;
    std::string outputParentDirPath_;
    uint32_t    recordCountsMaxPerFile_ = 5000;
    uint32_t    writingTimeout_inSeconds_ = 10; // 超过该时间没有进行写入该文件将关闭
    uint64_t    protoFileSerialNumber_ = 0;     // 此协议的writer当前写入了多少文件，在更新文件时追加到map中，重置自己
    uint64_t    lastWriteInTime_ = 0;
private:
    std::string currentFileNameWriting_;
    std::string currentFileName_;
    std::string currentFilePath_;
    FILE *      currentFileHandle_ = NULL;
    uint64_t    currentFileCreateTime_;
    uint32_t    currentFileWriteRecordCounts_;
    void *      xmlRoot_ = NULL;
};

class ProtoRecordWriterKeeper
{
public:
    ProtoRecordWriterKeeper(const std::string &strOutputRootPath,int outputFormat);

    ProtoRecordWriter* getProtoRecordWriterOf(const std::string &strProtoName, uint32_t record_count_per_file = 5000);

    uint64_t getNewFileSerialNumber();

    int setExtraProperty(const std::string &name, const std::string &value);

    std::string getExtraProperty(const std::string &name);

    int foreachRecordWriter(std::function<int(ProtoRecordWriter *)> func);
    ~ProtoRecordWriterKeeper(){}
public:
  void     getHistoryFileNum();
  void     updateFileSerialNumber();
  void     initProtoFileNumCountMap();
  void     updateFileSerialNumberDateChange(const std::string &protoName);
  uint64_t getProtoFileNum(const std::string &strProtoName);

public:
    static int                      createSomeProtoRecordWriterKeeper(uint32_t count, const std::string &strOutputRootPath,int OutputFormat);
    static ProtoRecordWriterKeeper* getProtoRecordWriterKeeperByIndex(uint32_t index);
    inline static void              SetMaxRecordNum(uint32_t num) { max_record_count_ = num; }


    inline static uint32_t          GetMaxRecordNum() { return max_record_count_;}

    std::fstream fdDay_;    // 按天记录的文件

private:
  time_t          lastRecordTime;
  uint64_t        protoFileSerialNumber_ = 0;
  std::string     outputRootPath_;
  std::string     protoName[PROTOCOL_MAX];
  pthread_mutex_t recordMutex;
  // chrono time
  std::chrono::time_point<std::chrono::high_resolution_clock> time_record_;
private:
    std::map<std::string, std::unique_ptr<ProtoRecordWriter>> mapping_protoName2RecordWriter_;
    std::map<std::string, std::string> extraPropertyMap_;

private:
  static std::vector<std::unique_ptr<ProtoRecordWriterKeeper>> cs_perThreadProtoRecordWriterKeeper;
  static uint32_t                                              max_record_count_;
  int                                                          KeeperOutputFormat_;
};

#endif /* DPI_PRECORD_WRITER_H */
