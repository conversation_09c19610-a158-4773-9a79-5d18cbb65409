#ifndef DPI_PRECORD_WRITER_IF_H
#define DPI_PRECORD_WRITER_IF_H

typedef struct ProtoRecord        precord_t;

#include <stdint.h>
#include <yaProtoRecord/precord.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

int dpi_output_create_writer_keeper_for_n_thread(uint32_t write_thread_count, const char *output_root_path , int output_format);

/*
*  @brief: 修改某一个协议的输出根目录，目前是针对于通联日志，因为通联日志的输出根目录是 sdt
*  @param: write_thread_index: 写线程的索引
*  @param: proto_name: 协议名称
*  @param: root_path: 输出根目录
*/
int dpi_output_set_record_rootpath(uint32_t write_thread_index, const char * proto_name, const char * root_path);
int dpi_output_set_writer_record_count_per_file(uint32_t write_thread_intdex, uint32_t record_count);

int dpi_output_set_writer_property(uint32_t write_thread_index, const char *name, const char *value);

int dpi_output_set_writer_property_number(uint32_t write_thread_index, const char *name, uint64_t number);

int dpi_output_write_one_record(uint32_t write_thread_index, precord_t *record);


/*
* @brief: write one record to output file, and subjoin the app_record  to record
*/
int dpi_output_write_one_record_subjoin(uint32_t write_thread_index,
                                        precord_t *record,
                                        precord_t *app_record);

int dpi_output_do_idle_check(uint32_t write_thread_index, uint64_t time_now);

int dpi_output_on_process_going_to_terminate(uint32_t write_thread_index);

#ifdef __cplusplus
}
#endif

#endif /* DPI_PRECORD_WRITER_IF_H */
