#include "dpi_precord_writer_IF.h"
#include "dpi_detect.h"
#include "dpi_lua_adapt.h"
#include "dpi_log.h"
#include "dpi_tbl_log.h"

#include <rte_ring.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/syscall.h>
#include <string.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <errno.h>
#include <maxminddb.h>
#include <stdlib.h>
#include <assert.h>
#include <fcntl.h>
#include <string.h>
#include <time.h>
#include <endian.h>
#include <glib.h>

extern struct global_config g_config;
extern struct rte_ring     *tbl_ring[TBL_RING_MAX_NUM];
extern struct rte_mempool  *tbl_log_mempool;
extern  uint8_t tbl_out_running;
pid_t gettid(void);

void *write_tbl_log_to_file_func(void *arg) {
  if(g_config.log_thread_type == 1){
      printf("流表模式不支持输出筛选!命令行参数有误\n");
      exit(0);
  }
  DpiTblThreadCtx *ctx = (DpiTblThreadCtx *)arg;

  long             write_thread_index = ctx->ring_id;
  long             core_id = ctx->core_id;
  int              is_tll = -1;
  int              ret = 0;
  pthread_t        thread = pthread_self();
  padapt_engine_t *adapt_engine = NULL;
  precord_t       *adapt_record = NULL;
  cpu_set_t        cpuset;
  struct tbl_log  *tbl, *tbl_burst[TBL_MAX_BURST];
  char             thread_name[16];
  uint8_t         *pdata = NULL;
  int              pdata_len = 0;

  snprintf(thread_name, 16, "dpi_output_%ld", core_id);
  pthread_setname_np(pthread_self(), thread_name);
  ret = dpi_pthread_setaffinity(thread, core_id);
  if (!ret) {
    DPI_LOG(DPI_LOG_ERROR, "Logging thread failed to bind core_id!!!");
  }
  printf("Logging running thread %ld, thread_id %ld. on core %ld\n", syscall(SYS_gettid), thread, core_id);

  if (g_config.adapt_type == 0) {
    adapt_engine = dpi_padapt_engine_create();
    dpi_padapt_engine_set_clibs_patch(adapt_engine, g_config.adapt_dir_clibs);
  }

  /* 对 output writer 进行配置 */
  dpi_output_set_writer_property(write_thread_index, "dev_ip", g_config.sdx_config.sdx_ip_str);
  dpi_output_set_writer_property(write_thread_index, "dev_name", g_config.sdt_out_produce_data_dev_name);
  dpi_output_set_writer_property_number(write_thread_index, "thread_id", gettid());

  dpi_output_set_writer_record_count_per_file(write_thread_index, g_config.log_max_num);

  time_t timer, now, time_cycle;
  time(&timer);

  while (1) {
    // time_cycle = now - timer;

    /* tbl_ring 还未初始化 */
    if (tbl_ring[write_thread_index] == NULL) {
      continue;
    }

    /* output 线程该退出了 */
    if ((unlikely(tbl_out_running == 0)) && rte_ring_count(tbl_ring[write_thread_index]) == 0) {
      g_config.tblwrite_flag_per_core[write_thread_index] = 1;
      dpi_output_on_process_going_to_terminate(write_thread_index);
      if (g_config.adapt_type == 0) {
        dpi_padapt_engine_destroy(adapt_engine);
      }
      printf("\n[EXIT]tbl thread (%ld) exit normal\n", core_id);
      pthread_exit(0);
    }

    /* 将 precord 出队列进行消费 */
    int dequeue_num = 0;
    dequeue_num = rte_ring_sc_dequeue_burst(tbl_ring[write_thread_index], (void **)tbl_burst, TBL_MAX_BURST, NULL);

    /* 没有需要消费的 precord, 执行 idle check */
    if (dequeue_num == 0) {
      time(&now);
      dpi_output_do_idle_check(write_thread_index, now);
      usleep(100000);
      continue;
    }

    /* 消费 precord */
    for (int i = 0; i < dequeue_num; i++) {
      tbl = tbl_burst[i];

      precord_t *record = tbl->record;
      // const char *proto_name = dpi_precord_get_proto_name(record);
      if (g_config.output_write != 1) {
        dpi_tbl_free(tbl);
        continue;
      }

      if (g_config.adapt_type == 0) {
        ret = dpi_padapt_record_adapt(adapt_engine, &adapt_record, record);
        if (ret < 0) {
          DPI_LOG(DPI_LOG_DEBUG, "加载脚本出错, %s\n", precord_misc_get_last_error());
        }
        if (adapt_record) {
          dpi_output_write_one_record(write_thread_index, adapt_record);
          precord_destroy(adapt_record);
          adapt_record = NULL;
        } else {
          dpi_output_write_one_record(write_thread_index, record);
        }
      } else {
        dpi_output_write_one_record(write_thread_index, record);
      }

      dpi_tbl_free(tbl);
    }
  }
}