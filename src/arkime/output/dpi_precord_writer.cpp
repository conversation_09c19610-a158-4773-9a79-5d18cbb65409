extern "C" {
#include <dpi_common.h> // for mkdirs
#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_utils.h"
}

#include "dpi_precord_writer.h"
#include "dpi_precord_writer_IF.h"
#include "microxml.h"

#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include "cJSON.h"
#include <stdio.h>
#include <unistd.h>
#include <algorithm>

#include <yyjson.h>
std::map<std::string ,uint64_t> protoFileNumCountMap_;

#ifdef DEBUG
    #define DEBUG_EXEC(code)  code
#else
    #define DEBUG_EXEC(code)
#endif


//
static void field_optmize(char * value) {
  if (value == NULL) {
    return;
  }

  int len = strlen(value);
  int i = 0;
  int num_flag = 1;  // 字符串中是否只包含整数和分隔符， 比如  , ; 空格

  for (i = 0; i < len; ++i) {
    if (isdigit(value[i]) == 0 && value[i] != ',' && value[i] != ';' ) {
      num_flag = 0;
      break;
    }
  }

  // 如果是纯数字合并，只保留第一个数字
  if (num_flag == 1) {
    i = 0;
    while (value[i] != '\0' && value[i] != ',' && value[i] != ';') {
      i++;
    }
    value[i] = '\0'; // 截断字符串
  }

  len = strlen(value);
  // 去除字段中的双引号
  for (i = 0; i < len; ++i) {
    if (value[i] == '\"') {
        value[i] = ' ';
    }
  }

}

ProtoRecordWriter::ProtoRecordWriter(ProtoRecordWriterKeeper *keeper, const std::string &strProto, const std::string &strOutputRootPath,uint64_t protoFileSerialNumber,int outputFormat)
    : writerOutputFormat_ (outputFormat)
    , keeper_(keeper)
    , protoName_(strProto)
    , outputRootPath_(strOutputRootPath)
    , protoFileSerialNumber_(protoFileSerialNumber)
{
    std::transform(protoName_.begin(),protoName_.end(),std::back_inserter(UpperStrProtoName_),::toupper);

}

int show_precord_layer(player_t *layer)
{
    const char *layer_name = precord_layer_get_layer_name(layer);
    printf("--- layer %s:\n", layer_name);

    for (pfield_t *field = precord_field_get_first_from_layer_of(layer);
         field != NULL;
         field = precord_field_get_next_from_layer_of(layer, field))
    {
        pfield_desc_t *desc  = precord_field_get_fdesc(field);
        ya_fvalue_t   *value = precord_field_get_fvalue(field);

        if (NULL == value)
        {
            printf("\tfname: %s, fvalue: %s, ftype:%s\n",
                   pfdesc_get_name(desc),
                   "_NONE_",
                   pfdesc_get_type_name(desc));
            continue;
        }

        char *value_string = ya_fvalue_to_string_repr(value, BASE_NONE);
        printf("\tfname: %s, fvalue: %s, ftype:%s\n", pfdesc_get_name(desc), value_string, ya_fvalue_type_name(value));
        ya_fvalue_free_string_repr(value_string);
    }

    return 0;
}


#define BUFFER_SIZE 4096 // 定义缓冲区大小
int ProtoRecordWriter::writeOneRecord_in_tbl(precord_t *record) {
    char buffer[BUFFER_SIZE];
    size_t buffer_index = 0;
    int idx= 0;
    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
        layer           = precord_layer_get_next(record, layer)) {
        const char *layer_name = precord_layer_get_layer_name(layer);
        precord_layer_move_cursor(record, layer_name);
        for (pfield_t *field = precord_field_get_first(record); field != NULL; field = precord_field_get_next(record, field)) {
          // if (g_config.custom_field) {
          //   pfield_desc_t *desc =  precord_field_get_fdesc(field);
          //   ya_fvalue_t *attr_val = pfdesc_get_attribute(desc, "is_custom");
          //   if (attr_val == NULL) continue;
          // }

            // pfield_desc_t *desc =  precord_field_get_fdesc(field);
          ya_fvalue_t *fvalue = precord_field_get_fvalue(field);
          if (fvalue == NULL) {
            if (buffer_index + 1 >= BUFFER_SIZE) {
              fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
              buffer_index = 0;
            }
            buffer[buffer_index++] = '|';
            continue;
          }
          char *fvalue_str = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
          size_t len = strlen(fvalue_str);
          int i = 0;
          while(fvalue_str[i]!= '\0')
          {
            if (fvalue_str[i] ==  '|'){
            fvalue_str[i] = '_';
            }
            if(fvalue_str[i]  == '\n'||fvalue_str[i] == '\r')
                fvalue_str[i] = ' ';

            i++;
          }

          if (len >= BUFFER_SIZE) {
            fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
            buffer_index = 0;
            fwrite(fvalue_str, sizeof(char), len, currentFileHandle_);
            fwrite("|", sizeof(char), 1, currentFileHandle_);
          } else {
            if (buffer_index + len + 1 >= BUFFER_SIZE) {
              fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
              buffer_index = 0;
            }
            memcpy(buffer + buffer_index, fvalue_str, len);
            buffer_index += len;
            buffer[buffer_index++] = '|';
          }

          ya_fvalue_free_string_repr(fvalue_str);
        }
    }
    buffer[buffer_index++] = '\n';

    if (buffer_index > 0) {
      fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
    }

    DEBUG_EXEC(fflush(currentFileHandle_);)
  return 0;
}

int ProtoRecordWriter::writeOneRecord_in_xml(precord_t *record)
{
    if (xmlRoot_ == NULL)
        return -1;

    mxml_node_t *root = (mxml_node_t *)xmlRoot_;
    mxml_node_t *attributefile = mxmlGetFirstChild(root);
    mxml_node_t *fields = mxmlNewElement(attributefile, precord_get_proto_name(record));


    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
        layer           = precord_layer_get_next(record, layer)) {
        const char *layer_name = precord_layer_get_layer_name(layer);
        precord_layer_move_cursor(record, layer_name);
        for (pfield_t *field = precord_field_get_first(record);
            field != NULL;
            field = precord_field_get_next(record, field))
        {
            pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);

            /*
            if (fvalue == NULL)
            {
                continue;
            }
            */

            char *value_string = NULL;
            if(fvalue)
                value_string = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
            mxml_node_t* object = mxmlNewElement(fields, pfdesc_get_name(fdesc));

            field_optmize(value_string);

            if(fvalue){
                mxmlNewText(object, 0, value_string);
                ya_fvalue_free_string_repr(value_string);
            }else
                mxmlNewText(object, 0, "");
        }
    }

    return 0;
}

int ProtoRecordWriter::WriteRecordToJson(precord_t *record)
{
    char * value_string = NULL;
    char * elems[2048];
    int   index = 0;
    yyjson_mut_doc * doc = yyjson_mut_doc_new(NULL);
    yyjson_mut_val * root = yyjson_mut_obj(doc);
    yyjson_mut_doc_set_root(doc, root);

    const char *proto_name = precord_get_proto_name(record);
    yyjson_mut_val * proto_key = yyjson_mut_str(doc, proto_name);
    yyjson_mut_val * proto_root = yyjson_mut_obj(doc);
    yyjson_mut_obj_add(root, proto_key, proto_root);

    // player_t* layer_top = precord_layer_get_top(record);
    // const char *layer_top_name = precord_layer_get_layer_name(layer_top);
    // if (strcmp(layer_top_name,"common") == 0) {
    // return 0 ;
    // }
    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
         layer           = precord_layer_get_next(record, layer)) {

        const char *layer_name = precord_layer_get_layer_name(layer);
        precord_layer_move_cursor(record, layer_name);

        for (pfield_t *field = precord_field_get_first(record);
            field != NULL;
            field = precord_field_get_next(record, field))
        {
            pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);

            yyjson_mut_val * field_name = yyjson_mut_str(doc, pfdesc_get_name(fdesc));

            if (fvalue == NULL ) {
                if(g_config.sdx_out_all_field) {
                    yyjson_mut_obj_add(proto_root, field_name, yyjson_mut_strn(doc, "", 0));
                }
                continue;
            }


            elems[index] = ya_fvalue_to_string_repr(fvalue, BASE_NONE);

            // yyjson_mut_val * val  = yyjson_mut_strcpy(doc, value_string);
            yyjson_mut_val * val  = yyjson_mut_str(doc, elems[index]);
            yyjson_mut_obj_add(proto_root, field_name, val);
            // ya_fvalue_free_string_repr(value_string);
            index++;
        }

    }
    static const yyjson_write_flag write_flags = YYJSON_WRITE_ALLOW_INVALID_UNICODE; // YYJSON_WRITE_ALLOW_INVALID_UNICODE;

    // yyjson_write_err err;

    yyjson_mut_write_fp(currentFileHandle_, doc, write_flags, NULL, NULL);
    // char *json_str = yyjson_mut_write_opts(doc, write_flags, NULL, NULL, &err);
    // if (json_str == NULL) {
    //     DPI_LOG(DPI_LOG_ERROR, "[%s] yyjson write error[%u]: %s\n", protoName_.c_str(), err.code, err.msg);
    //     yyjson_mut_doc_free(doc);
    //     return -1;
    // }

    // fprintf(currentFileHandle_, "%s\n", json_str);
    fwrite("\n", 1, 1, currentFileHandle_);

    // free(json_str);
    yyjson_mut_doc_free(doc);

    for (int i = 0 ; i < index; ++i) {
        ya_fvalue_free_string_repr(elems[i]);
    }

    return 0;
}

int ProtoRecordWriter::writeOneRecord_in_json(precord_t *record)
{
    // eg: {"netbios":{"ansTtl":"300000","ansClass":"IN", ...}}
    cJSON *root   = cJSON_CreateObject();
    cJSON *fields = cJSON_AddObjectToObject(root, precord_get_proto_name(record));

    for (pfield_t *field = precord_field_get_first(record);
         field != NULL;
         field = precord_field_get_next(record, field))
    {
        pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
        ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);

        if (fvalue == NULL)
        {
          if(g_config.sdx_out_all_field)
            cJSON_AddStringToObject(fields, pfdesc_get_name(fdesc), "");
          continue;
        }

        char *value_string = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
        cJSON_AddStringToObject(fields, pfdesc_get_name(fdesc), value_string);
        ya_fvalue_free_string_repr(value_string);
    }

    char *json_str = cJSON_PrintUnformatted(root);
    if (json_str == NULL) {
        free(json_str);
        cJSON_Delete(root);
        return -1;
    }
    fprintf(currentFileHandle_, "%s\n", json_str);

    cJSON_Delete(root);
    free(json_str);

    return 0;
}


int ProtoRecordWriter::writeOneRecord_in_json(precord_t *record, precord_t *app_record)
{
    if (app_record == NULL)
    {
        return writeOneRecord_in_json(record);
    }

    // eg: {"netbios":{"ansTtl":"300000","ansClass":"IN", ...}}
    cJSON *root   = cJSON_CreateObject();
    cJSON *fields = cJSON_AddObjectToObject(root, precord_get_proto_name(record));

    for (pfield_t *field = precord_field_get_first(record);
         field != NULL;
         field = precord_field_get_next(record, field))
    {
        pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
        ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);

        if (fvalue == NULL)
        {
          if(g_config.sdx_out_all_field)
            cJSON_AddStringToObject(fields, pfdesc_get_name(fdesc), "");
          continue;
        }

        char *value_string = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
        cJSON_AddStringToObject(fields, pfdesc_get_name(fdesc), value_string);
        ya_fvalue_free_string_repr(value_string);
    }

    for (pfield_t *field = precord_field_get_first(app_record);
         field != NULL;
         field = precord_field_get_next(app_record, field))
    {
        pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
        ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);

        if (fvalue == NULL)
        {
          if(g_config.sdx_out_all_field)
            cJSON_AddStringToObject(fields, pfdesc_get_name(fdesc), "");
          continue;
        }

        char *value_string = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
        cJSON_AddStringToObject(fields, pfdesc_get_name(fdesc), value_string);
        ya_fvalue_free_string_repr(value_string);
    }

    char *json_str = cJSON_PrintUnformatted(root);
    fprintf(currentFileHandle_, "%s\n", json_str);

    cJSON_Delete(root);
    free(json_str);

    return 0;
}

std::string ProtoRecordWriter::generateOutputParentDirPath()
{
    char buff[1024]    = { 0 };
    char time_buff[32] = { 0 };
    char time_day[10]  = { 0 };
    char time_hour[10] = { 0 };
    if(g_config.date_subdir_flag)
    {
      dpi_utils_strftime(time_buff, sizeof time_buff, NULL);
    }

    strncpy(time_day,  time_buff + 0, 8);
    strncpy(time_hour, time_buff + 8, 4);
    time_hour[3] = '0';

    // 输出格式: Metadata/协议名/日期
    snprintf(buff, sizeof buff, "%s/%s/%s/%s",
             outputRootPath_.c_str(), // 根路径
             protoName_.c_str(),      // 协议名
             time_day,                // 日期
             time_hour

        );
    if(protoName_.find("common") != std::string::npos){
        return buff;
    }
    if (access(buff, F_OK) != 0)
    {
        mkdirs(buff);
        protoFileSerialNumber_ = 0;
        std::string StrProtoName(UpperStrProtoName_);
        if (StrProtoName == "FTP" || StrProtoName == "ftp") {
          StrProtoName = "FTP_CONTROL";
        }
        keeper_->updateFileSerialNumberDateChange(StrProtoName);
    }

    return buff;
}

std::string ProtoRecordWriter::generateOutputFileName()
{
    char buff[1024]    = { 0 };
    char time_buff[15] = { 0 };

    dpi_utils_strftime(time_buff, sizeof time_buff, "%Y%m%d%H%M%S");
    std::string StrProtoName(UpperStrProtoName_);
    if (StrProtoName == "FTP" || StrProtoName == "ftp") {
      StrProtoName = "FTP_CONTROL";
    }
    auto search = protoFileNumCountMap_.find(StrProtoName);
    if (search != protoFileNumCountMap_.end()) {
      search->second ++;
      setFileSerialNumber();
      }
    // 设备名_协议名_生成时间_IP地址_线程号_流水号.json
    std::string dev_name = keeper_->getExtraProperty("dev_name");
    std::string dev_ip = keeper_->getExtraProperty("dev_ip");
    snprintf(buff, sizeof buff, "%s-%s-%s-%ld.%s",
            dev_name.c_str(),  // 设备名
            //  time_buff,                                      // 生成时间
            dev_ip.c_str(),    // IP地址
             protoName_.c_str(),                             // 协议名
            //  keeper_->getExtraProperty("thread_id").c_str(), // 线程号
            keeper_->getProtoFileNum(UpperStrProtoName_),
            g_config.yv_data_suffix);
    return buff;
}

std::string ProtoRecordWriter::generateOutputFilePath()
{
    outputParentDirPath_    = generateOutputParentDirPath(); // TODO: parentDir 一天才变化一次, 可以优化
    currentFileName_        = generateOutputFileName();
    currentFileNameWriting_ = currentFileName_ + ".tmp";
    currentFilePath_        = outputParentDirPath_ + "/" + currentFileNameWriting_;

    return currentFilePath_;
}

int ProtoRecordWriter::openNewOutputFile()
{
    currentFilePath_ = generateOutputFilePath();
    if(currentFilePath_.find("common") != std::string::npos){
      return -1;
    }
    currentFileHandle_ = fopen(currentFilePath_.c_str(), "wb");
    if (NULL == currentFileHandle_)
    {
        // TODO: log
        fprintf(stderr, "openNewOutputFile %s error %s\n", currentFilePath_.c_str(), strerror(errno));
        return -1;
    }

    if (writerOutputFormat_ == 3)
    {
        mxml_node_t *root   = mxmlNewXML("1.0");
        xmlRoot_ =  (void*)root;
        mxmlNewElement(root, "attributefile");
    }

    currentFileCreateTime_ = time(NULL);
    currentFileWriteRecordCounts_ = 0;
    return 0;
}

bool ProtoRecordWriter::currentFileShouldRotate(uint64_t time_now)
{
    if (NULL == currentFileHandle_)
    {
        return false;
    }

    if (currentFileWriteRecordCounts_ >= recordCountsMaxPerFile_)
    {
        return true;
    }

    // 至少写入了一条记录之后发生了"超时"
    if (currentFileWriteRecordCounts_ > 0
        && (time_now - lastWriteInTime_) > writingTimeout_inSeconds_)
    {
        // TODO: log
        return true;
    }

    return false;
}

int ProtoRecordWriter::closeCurrentFile()
{
    if (NULL == currentFileHandle_ || access(currentFilePath_.c_str(), F_OK) != 0) // 文件不存在，可能被意外删除
    {
        return 0;
    }

    //写入xml
    if(writerOutputFormat_ == 3 && xmlRoot_) {
        mxml_node_t *root = (mxml_node_t*)xmlRoot_;
        mxmlSaveFile(root, currentFileHandle_, MXML_NO_CALLBACK);
        mxmlDelete(root);
        xmlRoot_ = NULL;
    }

    fclose(currentFileHandle_);
    currentFileHandle_ = NULL;

    std::string currentFileFinalPath = outputParentDirPath_ + "/" + currentFileName_;
    if (rename(currentFilePath_.c_str(), currentFileFinalPath.c_str()) < 0)
    {
        return -1;
    }

    return 0;
}

int ProtoRecordWriter::writeRecord(precord_t *record)
{
    if ((NULL == currentFileHandle_
        DEBUG_EXEC(|| access(currentFilePath_.c_str(), F_OK) != 0) // 文件不存在，可能被意外删除
        ) && openNewOutputFile() < 0)
    {
        // TODO: log
        return -1;
    }
    int res = 0;
    if (writerOutputFormat_ == 0) {
     res =  writeOneRecord_in_tbl(record);
    } else if (writerOutputFormat_ == 1) {
    res = WriteRecordToJson(record);
    } else if (writerOutputFormat_ == 3) {
      res =writeOneRecord_in_xml(record);
    }else {
     res = writeOneRecord_in_tbl(record);
    }
    // writeOneRecord_in_json(record);
    if (res != 0) return -1;

    // 更新计数与写入时间
    currentFileWriteRecordCounts_++;
    lastWriteInTime_ = time(NULL);

    // 文件需要进行滚动，此时仅关闭旧文件，
    // 新文件会在下次有数据到来时才创建,否则会产生空文件;
    if (currentFileShouldRotate(lastWriteInTime_))
    {
        closeCurrentFile();
    }

    return 0;
}

int preallocate_and_write(FILE *fp, const void *data, size_t size) {
    // 获取文件描述符
    int fd = fileno(fp);
    if (fd == -1) {
        perror("fileno failed");
        return -1;
    }

    // 确保文件指针的当前位置
    off_t current_offset = ftello(fp);
    if (current_offset == -1) {
        perror("ftello failed");
        return -1;
    }

    // 预分配磁盘空间
    if (fallocate(fd, 0, current_offset, size) != 0) {
        perror("fallocate failed");
        return -1;
    }

    // 写入数据
    size_t written = fwrite(data, 1, size, fp);
    if (written != size) {
        perror("fwrite failed");
        return -1;
    }

    // 刷新缓冲区
    if (fflush(fp) != 0) {
        perror("fflush failed");
        return -1;
    }

    return 0;
}
#if 0
int ProtoRecordWriter::writeRecord(precord_t *record, const uint8_t * pdata, int len)
{
    if (pdata == NULL || len <= 0) return -1;

    if ((NULL == currentFileHandle_ || access(currentFilePath_.c_str(), F_OK) != 0) // 文件不存在，可能被意外删除
        && openNewOutputFile() < 0)
    {
        // TODO: log
        return -1;
    }

    if (writerOutputFormat_ != EM_OUTPUT_TLV) {
        return -1;
    }

    preallocate_and_write(currentFileHandle_, pdata, len);

    // 更新计数与写入时间
    currentFileWriteRecordCounts_++;
    lastWriteInTime_ = time(NULL);

    // 文件需要进行滚动，此时仅关闭旧文件，
    // 新文件会在下次有数据到来时才创建,否则会产生空文件;
    if (currentFileShouldRotate(lastWriteInTime_))
    {
        closeCurrentFile();
    }

    return 0;
}
#endif

int ProtoRecordWriter::writeRecord(precord_t * record, precord_t *app_record)
{
    if ((NULL == currentFileHandle_ || access(currentFilePath_.c_str(), F_OK) != 0) // 文件不存在，可能被意外删除
        && openNewOutputFile() < 0)
    {
        // TODO: log
        return -1;
    }

    if (app_record == NULL)
    {
        return writeRecord(record);
    }

    writeOneRecord_in_json(record, app_record);

    // 更新计数与写入时间
    currentFileWriteRecordCounts_++;
    lastWriteInTime_ = time(NULL);

    // 文件需要进行滚动，此时仅关闭旧文件，
    // 新文件会在下次有数据到来时才创建,否则会产生空文件;
    if (currentFileShouldRotate(lastWriteInTime_))
    {
        closeCurrentFile();
    }


    return 0;
}

int ProtoRecordWriter::onIdleCheck(uint64_t time_now)
{
    if (currentFileShouldRotate(time_now))
    {
        closeCurrentFile();
    }

    return 0;
}

int ProtoRecordWriter::onProcessGoingToTerminate()
{
    closeCurrentFile();
    return 0;
}

int ProtoRecordWriter::setRecordCountPerFile(uint32_t recordCount)
{
    recordCountsMaxPerFile_ = recordCount;
    return 0;
}

std::vector<std::unique_ptr<ProtoRecordWriterKeeper>> ProtoRecordWriterKeeper::cs_perThreadProtoRecordWriterKeeper;
// max_record_count_ init with 5000
uint32_t ProtoRecordWriterKeeper::max_record_count_ = 5000;

ProtoRecordWriterKeeper::ProtoRecordWriterKeeper( const std::string &strOutputRootPath,int OutputFormat)
: outputRootPath_(strOutputRootPath),KeeperOutputFormat_(OutputFormat)
{
    int i = PROTOCOL_UNKNOWN;

    lastRecordTime = time(NULL);


    getHistoryFileNum();

    lastRecordTime = time(NULL);
}

ProtoRecordWriter* ProtoRecordWriterKeeper::getProtoRecordWriterOf(const std::string &strProtoName, uint32_t record_count_per_file)
{
    auto findIter = mapping_protoName2RecordWriter_.find(strProtoName);
    if (findIter != mapping_protoName2RecordWriter_.end())
    {
        return findIter->second.get();
    }

    ProtoRecordWriter* newRecordWriter = new ProtoRecordWriter(this, strProtoName, outputRootPath_,0,KeeperOutputFormat_);
    newRecordWriter->setRecordCountPerFile(record_count_per_file);
    mapping_protoName2RecordWriter_.emplace(strProtoName, std::unique_ptr<ProtoRecordWriter>(newRecordWriter));

    return newRecordWriter;
}

int ProtoRecordWriterKeeper::createSomeProtoRecordWriterKeeper(uint32_t count, const std::string &strOutputRootPath,int OutputFormat)
{
    for (uint32_t i = 0; i < count; i++)
    {
        ProtoRecordWriterKeeper* newKeeper = new ProtoRecordWriterKeeper(strOutputRootPath,OutputFormat);
        cs_perThreadProtoRecordWriterKeeper.push_back(std::unique_ptr<ProtoRecordWriterKeeper>(newKeeper));
    }

    return 0;
}

ProtoRecordWriterKeeper* ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(uint32_t index)
{
    if (index >= cs_perThreadProtoRecordWriterKeeper.size())
    {
        return NULL;
    }

    return cs_perThreadProtoRecordWriterKeeper[index].get();
}

uint64_t ProtoRecordWriterKeeper::getNewFileSerialNumber()
{
    return protoFileSerialNumber_++;
}

int ProtoRecordWriterKeeper::setExtraProperty(const std::string &name, const std::string &value)
{
    extraPropertyMap_.emplace(name, value);
    return 0;
}

std::string ProtoRecordWriterKeeper::getExtraProperty(const std::string &name)
{
    auto findIter = extraPropertyMap_.find(name);
    if (findIter == extraPropertyMap_.end())
    {
        return "[" + name + "_" + "noValue" + "]";
    }

    return findIter->second;
}


int ProtoRecordWriterKeeper::foreachRecordWriter(std::function<int(ProtoRecordWriter *)> func)
{
    for (auto& pair : mapping_protoName2RecordWriter_)
    {
        func(pair.second.get());
    }

    return 0;
}

void ProtoRecordWriterKeeper::initProtoFileNumCountMap()
{
  int i = 0;
  while (i<PROTOCOL_MAX) {
      protoFileNumCountMap_.insert({protocol_name_array[i],0});
      i++;
  }
}
void ProtoRecordWriterKeeper::getHistoryFileNum() {
    if(protoFileNumCountMap_.begin()!=protoFileNumCountMap_.end())//已经初始化过了
      {return;}
    int i = PROTOCOL_UNKNOWN;
    std::string num1 = {0};
    std::string num2 = {0};
    char tmp[1024] = {0};
    int count = 0;
    // fdDay_.getline(tmp,10,'\n');
    // while (getline(fdDay_,line_buf) && i < PROTOCOL_MAX) {
    fdDay_.open(g_config.tbl_filenum_perday_path, std::fstream::in);
    if (!fdDay_.is_open())
        return;
    for (; fdDay_.getline(tmp, PROTOCOL_MAX, '\n');) {
        if(i>PROTOCOL_MAX-1){
          break ;
        }
        uint64_t recordPerday = 0;
        std::string line_buf(tmp);
        auto it1 = line_buf.find(':');
        auto it2 = line_buf.find('+');
        auto it3 = line_buf.find('*');
        if (it1 && it2 == std::string::npos && it3 == std::string::npos) {
          recordPerday = (uint64_t)atoll(&line_buf[it1 + 2]);
        } else if (it1 && it2 != std::string::npos && it3 != std::string::npos) {
          num1 = line_buf.substr(it1, it2 - it1 - 2 - 1);
          num2 = line_buf.substr(it2 + 2, it3 - it2 - 2 - 1);
          recordPerday = (uint64_t)atoll(num1.c_str());
        } else if(i>=PROTOCOL_MAX){
          DPI_LOG(DPI_LOG_ERROR, "HistoryFileNum file have extra erro bytes");
          break;
        }else {
          DPI_LOG(DPI_LOG_ERROR, "HistoryFileNum file format error");
        }
        protoFileNumCountMap_.insert({protocol_name_array[i],recordPerday+1});
        i++;
    }
    fdDay_.close();
    if(protoFileNumCountMap_.begin()==protoFileNumCountMap_.end()){
      initProtoFileNumCountMap();
    }
    return;
}
uint64_t ProtoRecordWriterKeeper::getProtoFileNum(const std::string &strProtoName) {
    std::string proto_name(strProtoName);
    if (strProtoName == "FTP" || strProtoName == "ftp") {
      proto_name = "FTP_CONTROL";
    }
    auto search = protoFileNumCountMap_.find(proto_name);
    if (search != protoFileNumCountMap_.end()) {
        return search->second;
    } else {
      protoFileNumCountMap_[proto_name] = 1;
      return 1;
    }
}
void ProtoRecordWriterKeeper::updateFileSerialNumberDateChange(const std::string &strProtoName){

  if(protoFileNumCountMap_.empty())
      return;
  protoFileNumCountMap_[strProtoName]= 0;


}
void ProtoRecordWriterKeeper::updateFileSerialNumber(){
    auto time_now = std::chrono::high_resolution_clock::now();
    // 每五分钟更新一次
    if (time_now - time_record_ < std::chrono::minutes(5)) {
        return;
    }
    time_record_ = time_now;
    int i = PROTOCOL_UNKNOWN;
    std::string line;
    std::string file_buffer;
    this->foreachRecordWriter([=](ProtoRecordWriter *writer)
    {
      std::string StrProtoName(writer->getWriterUpperProtoName());
      if (StrProtoName == "FTP" || StrProtoName == "ftp") {
        StrProtoName = "FTP_CONTROL";
      }
      auto search = protoFileNumCountMap_.find(StrProtoName);
      if (search != protoFileNumCountMap_.end()) {
        search->second += writer->getFileSerialNumber();
        writer->setFileSerialNumber();
      }
      return 0;
    });
    for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++) {
    std::string tmp(protocol_name_array[i]);
      line = tmp + " : " + std::to_string(protoFileNumCountMap_[protocol_name_array[i]]) + "\n";
      file_buffer += line;
    }
    fdDay_.open(g_config.tbl_filenum_perday_path, std::fstream::out|std::fstream::in|std::fstream::trunc);
    if(!fdDay_.is_open()){
      fdDay_.open(g_config.tbl_filenum_perday_path, std::fstream::out);
    }
    fdDay_.write(file_buffer.c_str(), file_buffer.length());
    fdDay_.flush();
    fdDay_.close();
}
int dpi_output_create_writer_keeper_for_n_thread(uint32_t write_thread_count, const char *output_root_path,int output_format)
{
    return ProtoRecordWriterKeeper::createSomeProtoRecordWriterKeeper(write_thread_count, output_root_path,output_format);
}

int dpi_output_set_record_rootpath(uint32_t write_thread_index, const char * proto_name, const char * root_path)
{
    if (NULL == proto_name || NULL == root_path) {
        return -1;
    }

    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper) {
            return -1;
    }
    ProtoRecordWriter* writer = keeper->getProtoRecordWriterOf(proto_name, keeper->GetMaxRecordNum());

    writer->setOutputRootPath(root_path);
    return 0;
}

static ProtoRecordWriter* dpi_output_get_writer_of(uint32_t write_thread_index, precord_t *record)
{
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return NULL;
    }

    const char *proto_to_write = precord_get_proto_name(record);
    ProtoRecordWriter* writer = keeper->getProtoRecordWriterOf(proto_to_write, keeper->GetMaxRecordNum());
    if (NULL == writer)
    {
        return NULL;
    }

    return writer;
}

int dpi_output_write_one_record(uint32_t write_thread_index, precord_t *record)
{
    ProtoRecordWriter* writer = dpi_output_get_writer_of(write_thread_index, record);
    if (NULL == writer)
    {
        return -1;
    }

    writer->writeRecord(record);
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return -1;
    }
    keeper->updateFileSerialNumber();
    return 0;
}
#if 0
sdx_tlvmsg_buff_t* dpi_output_write_tlv(uint32_t write_thread_index,  precord_t *record, uint8_t **ppdata, int *plen)
{
    ProtoRecordWriter* writer = dpi_output_get_writer_of(write_thread_index, record);
    if (NULL == writer)
    {
        return NULL;
    }

    // 将 record 打包为 tlvLog
    sdx_tlvmsg_buff_t*  tlvBuff         = sdx_tlvmsg_buff_create();
    int                 tlvLogBytes_len = sdx_tlvmsg_pack_precord_as_tlvlog(record, tlvBuff);
    *plen                               = sdx_tlvmsg_buff_get_data(tlvBuff, ppdata);
    // sdx_tlvmsg_buff_destroy(tlvBuff);

    writer->writeRecord(record, *ppdata, *plen);
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return tlvBuff;
    }
    keeper->updateFileSerialNumber();
    return tlvBuff;
}
#endif
int dpi_output_write_one_record_subjoin(uint32_t write_thread_index,
                                        precord_t *record,
                                        precord_t *app_record)
{
    ProtoRecordWriter* writer = dpi_output_get_writer_of(write_thread_index, record);
    if (NULL == writer)
    {
        return -1;
    }

    return writer->writeRecord(record, app_record);
}

int dpi_output_do_idle_check(uint32_t write_thread_index, uint64_t time_now)
{
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return -1;
    }

    keeper->foreachRecordWriter([=](ProtoRecordWriter *writer)
    {
        if(writer == NULL){
            return 0;
        }
        writer->onIdleCheck(time_now);
        return 0;
    });
    // keeper->updateFileSerialNumber();
    return 0;
}

int dpi_output_on_process_going_to_terminate(uint32_t write_thread_index)
{
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return -1;
    }

    keeper->foreachRecordWriter([=](ProtoRecordWriter *writer)
    {
        writer->onProcessGoingToTerminate();
        return 0;
    });
    keeper->updateFileSerialNumber();

    if(keeper->fdDay_.is_open())
        keeper->fdDay_.close();
    return 0;
}

int dpi_output_set_writer_property(uint32_t write_thread_index, const char *name, const char *value)
{
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return -1;
    }

    if (value && strlen(value) > 0)
    {
        keeper->setExtraProperty(name, value);
        return 0;
    }

    // NULL value
    std::string str_value = "[";
    str_value += name;
    str_value += "_noValue]";
    keeper->setExtraProperty(name, str_value);

    return 0;
}

int dpi_output_set_writer_property_number(uint32_t write_thread_index, const char *name, uint64_t number)
{
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return -1;
    }

    keeper->setExtraProperty(name, std::to_string(number));
    return 0;
}

int dpi_output_set_writer_record_count_per_file(uint32_t write_thread_index, uint32_t record_count)
{
    ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
    if (NULL == keeper)
    {
        return -1;
    }

    keeper->SetMaxRecordNum(record_count);

    // keeper->foreachRecordWriter([=](ProtoRecordWriter *writer)
    // {
    //     writer->setRecordCountPerFile(record_count);
    //     return 0;
    // });

    return 0;
}
