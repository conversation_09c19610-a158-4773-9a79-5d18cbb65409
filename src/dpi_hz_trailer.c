#include "dpi_trailer.h"

static int get_tlv_data(struct hz_trailer *trailer, const uint8_t *payload, uint8_t offset, uint8_t len)
{
	uint8_t type   = payload[offset];
	uint8_t length = payload[offset+1];
	int field;

	if(length + offset + 2 > len)
		return -1;

	switch(type){
		case 0x01:
			if(length != 8)
				return -1;
			trailer->imsi = be64toh(get_uint64_t(payload, offset+2));
			break;
		case 0x02:
			if(length != 8)
				return -1;
			trailer->msisdn = be64toh(get_uint64_t(payload, offset+2));
			break;
		case 0x03:
			if(length != 8)
				return -1;
			trailer->imei = be64toh(get_uint64_t(payload, offset+2));
			break;
		case 0x04:
			if(length != 1)
				return -1;
			trailer->ncode = get_uint8_t(payload, offset+2);
			break;
		case 0x05:
			if(length != 2)
				return -1;
			trailer->mcc = get_uint16_ntohs(payload, offset+2);
			break;
		case 0x06:
			if(length != 2)
				return -1;
			trailer->mnc = get_uint16_ntohs(payload, offset+2);
			break;
		case 0x07:
			if(length != 2)
				return -1;
			trailer->lac = get_uint16_ntohs(payload, offset+2);
			break;
		case 0x08:
			if(length != 2)
				return -1;
			trailer->ci = get_uint16_ntohs(payload, offset+2);
			break;
		case 0x09:
			if(length != 4)
				return -1;
			trailer->ecgi = get_uint32_ntohl(payload, offset+2);
			break;
		case 0x10:
			if(length != 2)
				return -1;
			trailer->tac = get_uint16_ntohs(payload, offset+2);
			break;
		default:
			return 0;
	}
	return 0;
}

void parse_hz_soft_trailer(struct hz_trailer *trailer, const uint8_t *payload, uint16_t payload_len)
{
	if(payload_len < 20 || (get_uint16_ntohs(payload, 0) != 0x9876))
		return;

	uint8_t offset = 0, length;
	uint8_t  len = get_uint16_ntohs(payload, offset+2);
	offset = offset + 4;

	while(offset < len){
		if(get_tlv_data(trailer, payload, offset, len) < 0)
			break;
		else
			offset += payload[offset+1] + 2;
	}

	return;
}

