#include "dpi_offline.h"

#include <pthread.h>
#include <unistd.h>
#include <netinet/ip6.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <sys/syscall.h>

#include <pcap.h>
#include <rte_ring.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "dpi_utils.h"
#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_numa.h"

#define DPI_OFFLINE_MAX_THREADS 4

struct rte_ring *g_pcap_ring;

extern rte_atomic64_t drop_pkts;
extern rte_atomic64_t drop_bytes;
extern rte_atomic64_t receive_pkts;
extern rte_atomic64_t receive_bytes;

extern struct global_config g_config;
extern struct rte_mempool  *pktmbuf_pool[2];
extern struct rte_ring     *packet_flow_ring[RTE_MAX_LCORE];
extern int pkt_arrive(const unsigned char *raw_packet, unsigned int raw_packet_len, int use_rss, struct rte_mbuf *mb);

static uint8_t   pcap_offline_running;
static pthread_t pcap_offline_pthreads[DPI_OFFLINE_MAX_THREADS] = {0};

static int _dpi_offline_get_rss(const unsigned char * packet, uint32_t pkt_len)
{
  uint16_t ether_type = ntohs(*(uint16_t *)(packet + 12));

  uint32_t src_ip_part = 0;
  uint32_t dst_ip_part = 0;
  uint16_t src_port    = 0;
  uint16_t dst_port    = 0;

  if (ether_type == 0x0800) {  // IPv4
    struct ip *ip_header = (struct ip *)(packet + 14);
    src_ip_part          = ntohl(*(uint32_t *)&ip_header->ip_src);
    dst_ip_part          = ntohl(*(uint32_t *)&ip_header->ip_dst);

    if (ip_header->ip_p == IPPROTO_TCP) {
      struct tcphdr *tcp_header = (struct tcphdr *)(packet + 14 + ip_header->ip_hl * 4);
      src_port                  = ntohs(tcp_header->source);
      dst_port                  = ntohs(tcp_header->dest);
    } else if (ip_header->ip_p == IPPROTO_UDP) {
      struct udphdr *udp_header = (struct udphdr *)(packet + 14 + ip_header->ip_hl * 4);
      src_port                  = ntohs(udp_header->source);
      dst_port                  = ntohs(udp_header->dest);
    }
  } else if (ether_type == 0x86DD) {  // IPv6
    struct ip6_hdr *ip6_header = (struct ip6_hdr *)(packet + 14);
    src_ip_part                = ntohl(*(uint32_t *)&ip6_header->ip6_src);
    dst_ip_part                = ntohl(*(uint32_t *)&ip6_header->ip6_dst);

    if (ip6_header->ip6_nxt == IPPROTO_TCP) {
      struct tcphdr *tcp_header = (struct tcphdr *)(packet + 14 + sizeof(struct ip6_hdr));
      src_port                  = ntohs(tcp_header->source);
      dst_port                  = ntohs(tcp_header->dest);
    } else if (ip6_header->ip6_nxt == IPPROTO_UDP) {
      struct udphdr *udp_header = (struct udphdr *)(packet + 14 + sizeof(struct ip6_hdr));
      src_port                  = ntohs(udp_header->source);
      dst_port                  = ntohs(udp_header->dest);
    }
  } else {
    return -1;  // Only process IPv4 and IPv6
  }

  return src_ip_part + dst_ip_part + src_port + dst_port;
}

static void file_traverse_callback(const char * path)
{
  DpiOfflinePcap  *offline_info = NULL;

  offline_info  = (DpiOfflinePcap *)malloc(sizeof(DpiOfflinePcap));

  strcpy(offline_info->file_name, path);

  rte_ring_sp_enqueue(g_pcap_ring, (void *)offline_info);
}

void *dpi_offline_dir_monitor(void *arg)
{
  pthread_t thread = pthread_self();
  long      core_id;
  int       ret;

  core_id = dpi_numa_get_suitable_core(g_config.socketid);
  dpi_numa_set_used_core(core_id);
  log_info("offline_dir 线程运行在 cpu %u", core_id);
  ret = dpi_pthread_setaffinity(thread, core_id);

  dpi_utils_traverse_dir(g_config.pcap_dir, file_traverse_callback);

  return NULL;
}

void *dpi_offline_pcap_process(void *arg)
{
  pcap_t  *handle = NULL;
  char errbuf[PCAP_ERRBUF_SIZE];
  struct pcap_pkthdr p_hdr;
  const u_char * packet = NULL;
  DpiOfflinePcap *p_info = NULL;
  struct rte_mbuf *mbuf = NULL;
  unsigned int free_space;

  pthread_t thread = pthread_self();
  long      core_id;
  int       ret;
  int   _receive_pkts, _receive_bytes;

  static int _thread_num = 0;
  ATOMIC_ADD_FETCH(&_thread_num);

  core_id = dpi_numa_get_suitable_core(g_config.socketid);
  dpi_numa_set_used_core(core_id);
  ret = dpi_pthread_setaffinity(thread, core_id);

  if (!ret) {
    printf("offline pcap thread failed to bind core_id!!!\n");
  }

  printf("offline pcap running thread %ld, thread_id %ld. on core %ld\n", syscall(SYS_gettid), thread, core_id);

  // 阻塞直到规则加载完毕
  while(pcap_offline_running && g_config.stop_rcv_pkts == 1)
    usleep(1000*500);

  while (1)
  {
    if (unlikely(pcap_offline_running == 0))
    {
      // 信号停止, 立即退出
      ATOMIC_SUB_FETCH(&_thread_num);
      return NULL;
    }

    if (rte_ring_empty(g_pcap_ring) /*&& g_config.offline_read_over_stop*/)
    {
      // pcap处理完成, 顺序退出
      break;
    }

    // get the pcap file name
    if (rte_ring_mc_dequeue(g_pcap_ring, (void **)&p_info) < 0) {
      continue;
    }

    if (p_info->file_name[0] == '.')
      continue;

    // process the pcap file
    const char * pcap_file = p_info->file_name;
    char *p = strrchr(pcap_file, '.');
    if (!p || (strcmp(p, ".pcap")
          && strcmp(p, ".pcapng")
          && strcmp(p, ".cap")))
    {
      free(p_info);
      continue;
    }

    printf("Processing pcap file %s\n", pcap_file);
    handle = pcap_open_offline(pcap_file, errbuf);
    if (handle == NULL) {
      printf("invalid pcap file %s\n", pcap_file);
      free(p_info);
      continue;
    }

    _receive_pkts  = 0;
    _receive_bytes = 0;

    while ((packet = pcap_next(handle, &p_hdr)) != NULL) {

      _receive_pkts++;
      _receive_bytes += p_hdr.caplen;

      if (p_hdr.caplen > g_config.mbuf_size) {
        log_debug("遇到巨帧: %u", p_hdr.caplen);
        continue;
      }

      // 不漏包
      while((mbuf = rte_pktmbuf_alloc(pktmbuf_pool[0])) == NULL)
      {
        // printf("mbuf alloc failed!!\n");
        usleep(1000*500);
      }

      mbuf->pkt_len = p_hdr.caplen;
      mbuf->data_len = p_hdr.caplen;
      rte_memcpy(rte_pktmbuf_mtod(mbuf, void *), packet, p_hdr.caplen);

      // 只有一个解析线程无须 rss
      if (g_config.dissector_thread_num == 1) {
        if (1 != rte_ring_mp_enqueue_burst(packet_flow_ring[0], (void * const *)&mbuf, 1, &free_space)) {
            rte_atomic64_inc(&drop_pkts);
            rte_atomic64_add(&drop_bytes, mbuf->data_len);
            rte_pktmbuf_free(mbuf);
        }
      } else {
        pkt_arrive(packet, p_hdr.caplen, 0, mbuf);
      }
    }

    rte_atomic64_add(&receive_pkts, _receive_pkts);
    rte_atomic64_add(&receive_bytes, _receive_bytes);

    pcap_close(handle);
    handle = NULL;
    free(p_info);
  }

  if (ATOMIC_SUB_FETCH(&_thread_num) == 0)
    raise(SIGINT);

  return NULL;
}

void dpi_offline_init()
{
  // 离线模式下初始状态为不读包, 待规则加载完毕后再读包
  g_config.stop_rcv_pkts = 1;

  // create a ring
  g_pcap_ring = rte_ring_create("offline_ring", 8192, SOCKET_ID_ANY, RING_F_SP_ENQ);
  if (g_pcap_ring == NULL)
  {
    rte_exit(EXIT_FAILURE, "Cannot create ring for offline processing\n");
  }

}

void dpi_offline_start()
{
  pcap_offline_running = 1;
  dpi_utils_traverse_dir(g_config.pcap_dir, file_traverse_callback);

  // create max libpcap threads
  for (int i = 0; i < g_config.nb_rxq; i++) {
    pthread_create(&pcap_offline_pthreads[i], NULL, dpi_offline_pcap_process, NULL);
  }
}

void dpi_offline_stop()
{
  pcap_offline_running = 0;

  for (int i = 0; i < g_config.nb_rxq; i++) {
    pthread_join(pcap_offline_pthreads[i], NULL);
  }
}