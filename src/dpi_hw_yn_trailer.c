#include "dpi_trailer.h"

uint32_t get_bcd(const uint8_t *src, uint8_t *dst, int len)
{
    int i = 0, j = 0, count0 = 0;
    while(i < len){
        dst[j] = src[i] & 0x0f;
        if(dst[j] == 0x0f){
            return j;
        }   
        else if(dst[j++] == 0){ 
            ++count0;
        }   
        dst[j] = src[i++] >> 4;
        if(dst[j] == 0x0f){
            return j;
        }   
        else if(dst[j++] == 0){ 
            ++count0;
        }   
        if(count0 == len * 2)
            return 0;
    }   
    return j;
}

void parse_hw_yn_trailer(struct hw_yn_trailer *trailer, const uint8_t *payload, uint16_t payload_len)
{
	if(payload_len < 81 || get_uint16_ntohs(payload, 0) != 0xFFFE){
		trailer->len = 0;
		return;
	}

	trailer->len = 81;
	memcpy(trailer->data, payload, 81);
	return;
}
