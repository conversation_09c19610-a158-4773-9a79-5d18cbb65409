/****************************************************************************************
 * 文 件 名 : dpi_sdp.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/18
编码: wangy            2018/07/18
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_SDP_H_
#define _DPI_SDP_H_

/*rtp head ，copy from wireshark ,
        now we do not have rtp dissector, but we need it ,so it is here*/


#define PT_PCMU           0  /* RFC 3551 */
#define PT_1016           1  /* RFC 1890 (reserved in RFC 3551) */
#define PT_G721           2  /* RFC 1890 (reserved in RFC 3551) */
#define PT_GSM            3  /* RFC 3551 */
#define PT_G723           4  /* From Vineet Kumar of Intel; see the Web page */
#define PT_DVI4_8000      5  /* RFC 3551 */
#define PT_DVI4_16000     6  /* RFC 3551 */
#define PT_LPC            7  /* RFC 3551 */
#define PT_PCMA           8  /* RFC 3551 */
#define PT_G722           9  /* RFC 3551 */
#define PT_L16_STEREO    10  /* RFC 3551 */
#define PT_L16_MONO      11  /* RFC 3551 */
#define PT_QCELP         12  /* Qualcomm Code Excited Linear Predictive coding? */
#define PT_CN            13  /* RFC 3389 */
#define PT_MPA           14  /* RFC 3551, RFC 2250 */
#define PT_G728          15  /* RFC 3551 */
#define PT_DVI4_11025    16  /* from Joseph Di Pol of Sun; see the Web page */
#define PT_DVI4_22050    17  /* from Joseph Di Pol of Sun; see the Web page */
#define PT_G729          18
#define PT_CN_OLD        19  /* Payload type reserved (old version Comfort Noise) */
#define PT_CELB          25  /* RFC 2029 */
#define PT_JPEG          26  /* RFC 2435 */
#define PT_NV            28  /* RFC 1890 */
#define PT_H261          31  /* RFC 2032 */
#define PT_MPV           32  /* RFC 2250 */
#define PT_MP2T          33  /* RFC 2250 */
#define PT_H263          34  /* from Chunrong Zhu of Intel; see the Web page */

/* Added to by Alex Lindberg to cover port ranges 96-127 - Dynamic RTP
   Some of these ports are used by Avaya for Modem and FAX support */

#define PT_UNDF_96       96  /* RFC 3551 */
#define PT_UNDF_97       97
#define PT_UNDF_98       98
#define PT_UNDF_99       99
#define PT_UNDF_100     100
#define PT_UNDF_101     101
#define PT_UNDF_102     102
#define PT_UNDF_103     103
#define PT_UNDF_104     104
#define PT_UNDF_105     105
#define PT_UNDF_106     106
#define PT_UNDF_107     107
#define PT_UNDF_108     108
#define PT_UNDF_109     109
#define PT_UNDF_110     110
#define PT_UNDF_111     111
#define PT_UNDF_112     112
#define PT_UNDF_113     113
#define PT_UNDF_114     114
#define PT_UNDF_115     115
#define PT_UNDF_116     116
#define PT_UNDF_117     117
#define PT_UNDF_118     118
#define PT_UNDF_119     119
#define PT_UNDF_120     120
#define PT_UNDF_121     121
#define PT_UNDF_122     122
#define PT_UNDF_123     123
#define PT_UNDF_124     124
#define PT_UNDF_125     125
#define PT_UNDF_126     126
#define PT_UNDF_127     127

typedef struct _value_string {
    uint32_t value;
    const char *strptr;
} value_string;


#define SDP_O_USERNAME            "o_username"
#define SDP_O_SESSIONID            "o_sessionid"
#define SDP_O_VERSION            "o_version"
#define SDP_O_NETWORK_TYPE        "o_network_type"
#define SDP_O_ADDRESS_TYPE        "o_address_type"
#define SDP_O_ADDRESS             "o_address"
#define SDP_C_NETWORK_TYPE        "c_network_type"
#define SDP_C_ADDRESS_TYPE        "c_address_type"
#define SDP_C_ADDRESS            "c_address"
#define SDP_T_TIME_START        "t_time_start"
#define SDP_T_TIME_END            "t_time_end"
#define SDP_M_TYPE                "m_type"
#define SDP_M_PORT                "m_port"
#define SDP_M_PROTO                "m_proto"
#define SDP_M_PAYLOADS            "m_payloads"
#define SDP_V_VERSION            "v_version"
#define SDP_S_NAME                "s_name"
#define SDP_I_INFO                "i_info"
#define SDP_U_URI                "u_uri"
#define SDP_E_EMAIL                "e_email"
#define SDP_P_PHONE                "p_phone"
#define SDP_B_BANDWIDTHS        "b_bandwidths"
#define SDP_R_REPEATTIME        "r_repeattime"
#define SDP_A_ATTRIBUTES        "a_attributes"

#define MEDIA_PAYLOAD_LEN_MAX 128

#define SDP_MEDIA_MAX_NUM 2
#define SDP_M_ATTR_MAX_NUM 16

struct sdp_m_info
{
    char m_media[256];

    char m_type[64];
    char m_port[64];
    char m_proto[64];
    char m_title[64];
    char m_payloads[MEDIA_PAYLOAD_LEN_MAX];
    char a_attributes[SDP_M_ATTR_MAX_NUM][64];
};

struct sdp_info
{
    char o_owner[256];
    char o_username[64];
    char o_sessionid[64];
    char o_version[64];
    char o_network_type[64];
    char o_address_type[64];
    char o_address[64];
    char c_info[256];
    char c_network_type[64];
    char c_address_type[64];
    char c_address[64];    
    char t_time[128];
    char t_time_start[64];
    char t_time_end[64];
    char v_version[64];
    char s_name[64];
    char i_info[64];
    char u_uri[64];
    char e_email[64];
    char p_phone[64];
    char b_bandwidths[64];
    char r_repeattime[64];
    
    char session_attribute[SDP_M_ATTR_MAX_NUM];
    
    struct sdp_m_info m_info[SDP_MEDIA_MAX_NUM];    
};

int dissect_sdp(const uint8_t *payload, const uint32_t payload_len, GHashTable *table);
int dissect_sdp_2(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, struct sdp_info *info);


#endif
