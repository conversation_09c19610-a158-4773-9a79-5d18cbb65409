/****************************************************************************************
 * 文 件 名 : dpi_cdp.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: xuxn          2019/02/13
编码: xuxn          2019/02/13
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef DPI_CDP_H
#define DPI_CDP_H

#define TLV_TYPE                0
#define TLV_LENGTH              2
#define PROTO_TYPE_NLPID        1
#define PROTO_TYPE_IEEE_802_2   2
#define NLPID_IP                0xcc
#define ETHERTYPE_IPv6          0x86DD

#define TYPE_DEVICE_ID          0x0001
#define TYPE_ADDRESS            0x0002
#define TYPE_PORT_ID            0x0003
#define TYPE_CAPABILITIES       0x0004
#define TYPE_IOS_VERSION        0x0005
#define TYPE_PLATFORM           0x0006
#define TYPE_IP_PREFIX          0x0007
#define TYPE_PROTOCOL_HELLO     0x0008  // Protocol Hello
#define TYPE_VTP_MGMT_DOMAIN    0x0009  // VTP Domain, CTPv2 - see second URL
#define TYPE_NATIVE_VLAN        0x000a  // Native VLAN, CTPv2 - see second URL
#define TYPE_DUPLEX             0x000b  // Full/Half Duplex - see second URL
#define TYPE_VOIP_VLAN_REPLY    0x000e  // VoIP VLAN reply
#define TYPE_VOIP_VLAN_QUERY    0x000f  // VoIP VLAN query
#define TYPE_POWER              0x0010  // Power consumption
#define TYPE_MTU                0x0011  // MTU
#define TYPE_TRUST_BITMAP       0x0012  // Trust bitmap
#define TYPE_UNTRUSTED_COS      0x0013  // Untrusted port CoS
#define TYPE_SYSTEM_NAME        0x0014  // System Name
#define TYPE_SYSTEM_OID         0x0015  // System OID
#define TYPE_MANAGEMENT_ADDR    0x0016  // Management Address(es)
#define TYPE_LOCATION           0x0017  // Location
#define TYPE_EXT_PORT_ID        0x0018  // External Port-ID
#define TYPE_POWER_REQUESTED    0x0019  // Power Requested
#define TYPE_POWER_AVAILABLE    0x001a  // Power Available
#define TYPE_PORT_UNIDIR        0x001b  // Port Unidirectional
#define TYPE_NRGYZ              0x001d  // EnergyWise over CDP
#define TYPE_SPARE_POE          0x001f  // Spare Pair PoE
#define TYPE_HP_BSSID           0x1000  // BSSID
#define TYPE_HP_SERIAL          0x1001  // Serial number
#define TYPE_HP_SSID            0x1002  // SSID
#define TYPE_HP_RADIO1_CH       0x1003  // Radio1 channel
#define TYPE_HP_SNMP_PORT       0x1006  // SNMP listening UDP port
#define TYPE_HP_MGMT_PORT       0x1007  // Web interface TCP port
#define TYPE_HP_SOURCE_MAC      0x1008  // Sender MAC address for the AP, bouth wired and wireless
#define TYPE_HP_RADIO2_CH       0x1009  // Radio2 channel
#define TYPE_HP_RADIO1_OMODE    0x100A  // Radio1 Operating mode
#define TYPE_HP_RADIO2_OMODE    0x100B  // Radio2 Operating mode
#define TYPE_HP_RADIO1_RMODE    0x100C  // Radio1 Radio mode
#define TYPE_HP_RADIO2_RMODE    0x100D  // Radio2 Radio mode
#define TYPE_HELLO_CLUSTER_MGMT 0x0112

#define MALLOC(x,y)                 \
    (x) = ((uint8_t *)malloc((y)));   \
    memset((x), 0, (y))

typedef struct __cdp_info
{
    uint8_t version;
    uint8_t ttl;
    uint16_t checksum;
    uint8_t checksum_status;  
    uint16_t tlvtype;
    uint16_t tlvlength; 
    uint16_t tlvtype_1;
    uint16_t tlvlength_1;
    uint16_t tlvtype_2;
    uint16_t tlvlength_2;
    uint16_t tlvtype_3;
    uint16_t tlvlength_3;
    uint16_t tlvtype_4;
    uint16_t tlvlength_4;
    uint16_t tlvtype_5;
    uint16_t tlvlength_5;
    uint16_t tlvtype_6;
    uint16_t tlvlength_6;
    uint16_t tlvtype_7;
    uint16_t tlvlength_7;
    uint16_t tlvtype_8;
    uint16_t tlvlength_8;
    uint16_t tlvtype_9;
    uint16_t tlvlength_9;
    uint16_t tlvtype_a;
    uint16_t tlvlength_a;
    uint16_t tlvtype_b;
    uint16_t tlvlength_b;
    uint16_t tlvtype_e;
    uint16_t tlvlength_e;
    uint16_t tlvtype_f;
    uint16_t tlvlength_f;
    uint16_t tlvtype_10;
    uint16_t tlvlength_10;
    uint16_t tlvtype_11;
    uint16_t tlvlength_11;
    uint16_t tlvtype_12;
    uint16_t tlvlength_12;
    uint16_t tlvtype_13;
    uint16_t tlvlength_13;
    uint16_t tlvtype_14;
    uint16_t tlvlength_14;
    uint16_t tlvtype_15;
    uint16_t tlvlength_15;
    uint16_t tlvtype_16;
    uint16_t tlvlength_16;
    uint16_t tlvtype_17;
    uint16_t tlvlength_17;
    uint16_t tlvtype_19;
    uint16_t tlvlength_19;
    uint16_t tlvtype_1a;
    uint16_t tlvlength_1a;
    uint16_t tlvtype_1d;
    uint16_t tlvlength_1d; 
    uint8_t* deviceid;
    uint8_t* portid;
    uint32_t naddresses_2;
    uint32_t naddresses_16;
    uint8_t* proto_type;
    uint8_t* proto_len;
    uint8_t* protocol;
    uint16_t* address_length;
    uint16_t* address;
    uint32_t* nrgyz_ip_address;
    uint32_t capabilities;
    uint32_t capabilities_router;
    uint32_t capabilities_trans_bridge;
    uint32_t capabilities_src_bridge;
    uint32_t capabilities_switch;
    uint32_t capabilities_host;
    uint32_t capabilities_igmp_capable;
    uint32_t capabilities_repeater;
    uint8_t* software_version;
    uint8_t* platform;  
    uint32_t odr_default_gateway;
    uint8_t oui_8[3];
    uint16_t proto_id;
    uint32_t cluster_master_ip;
    uint32_t cluster_ip;
    uint8_t cluster_version;
    uint8_t cluster_sub_version;
    uint8_t cluster_status;
    uint8_t cluster_unknown1;
    uint8_t cluster_commander_mac[6];
    uint8_t cluster_switch_mac[6];
    uint8_t cluster_unknown2;
    uint16_t cluster_management_vlan;
    uint8_t* hello_unknown; 
    uint8_t* vtp_management_domain;
    uint16_t native_vlan;
    uint8_t duplex;
    uint16_t data_e;
    uint16_t voice_vlan_e;
    uint16_t data_f;
    uint16_t voice_vlan_f;
    uint16_t power_consumption;
    uint32_t mtu;
    uint8_t trust_bitmap;
    uint8_t untrusted_port_cos;
    uint8_t* system_name;
    uint8_t* system_object_identifier; 
    uint8_t location_unknown;
    uint8_t* location;
    uint16_t request_id_19;
    uint16_t management_id_19;
    uint32_t power_requested;
    uint16_t request_id_1a;
    uint16_t management_id_1a;
    uint32_t power_available;
    uint8_t encrypted_data[20];
    uint32_t seen_sequence;
    uint32_t sequence_number;
    uint8_t model_number[16];
    uint16_t unknown_pad;
    uint8_t hardware_version_id[3];
    uint8_t system_serial_number[11];
    uint8_t nrgyz_unknown_values[8];
    uint16_t len_tlv_table;
    uint16_t num_tlvs_table;
    
   
} CDPINFO;

enum  cdp_index_em{
    EM_CDP_H_DST,
    EM_CDP_H_SRC,
    EM_CDP_H_PROTO,
    EM_CDP_DSAP,
    EM_CDP_SSAP,
    EM_CDP_CONTRAL,
    EM_CDP_OUI,
    EM_CDP_CISO_PID,
    EM_CDP_VERSION,
    EM_CDP_TTL,
    EM_CDP_CHECKSUM,
    EM_CDP_CHECKSUM_STATUS,
    EM_CDP_TLV_TYPE,
    EM_CDP_TLV_LENGTH,
    EM_CDP_TLV_TYPE_1,
    EM_CDP_TLV_LENGTH_1,
    EM_CDP_TLV_TYPE_2,
    EM_CDP_TLV_LENGTH_2,
    EM_CDP_TLV_TYPE_3,
    EM_CDP_TLV_LENGTH_3,
    EM_CDP_TLV_TYPE_4,
    EM_CDP_TLV_LENGTH_4,
    EM_CDP_TLV_TYPE_5,
    EM_CDP_TLV_LENGTH_5,
    EM_CDP_TLV_TYPE_6,
    EM_CDP_TLV_LENGTH_6,
    EM_CDP_TLV_TYPE_7,
    EM_CDP_TLV_LENGTH_7,
    EM_CDP_TLV_TYPE_8,
    EM_CDP_TLV_LENGTH_8,
    EM_CDP_TLV_TYPE_9,
    EM_CDP_TLV_LENGTH_9,
    EM_CDP_TLV_TYPE_A,
    EM_CDP_TLV_LENGTH_A,
    EM_CDP_TLV_TYPE_B,
    EM_CDP_TLV_LENGTH_B,
    EM_CDP_TLV_TYPE_E,
    EM_CDP_TLV_LENGTH_E,
    EM_CDP_TLV_TYPE_F,
    EM_CDP_TLV_LENGTH_F,
    EM_CDP_TLV_TYPE_10,
    EM_CDP_TLV_LENGTH_10,
    EM_CDP_TLV_TYPE_11,
    EM_CDP_TLV_LENGTH_11,
    EM_CDP_TLV_TYPE_12,
    EM_CDP_TLV_LENGTH_12,
    EM_CDP_TLV_TYPE_13,
    EM_CDP_TLV_LENGTH_13,
    EM_CDP_TLV_TYPE_14,
    EM_CDP_TLV_LENGTH_14,
    EM_CDP_TLV_TYPE_15,
    EM_CDP_TLV_LENGTH_15,
    EM_CDP_TLV_TYPE_16,
    EM_CDP_TLV_LENGTH_16,
    EM_CDP_TLV_TYPE_17,
    EM_CDP_TLV_LENGTH_17,
    EM_CDP_TLV_TYPE_19,
    EM_CDP_TLV_LENGTH_19,
    EM_CDP_TLV_TYPE_1A,
    EM_CDP_TLV_LENGTH_1A,
    EM_CDP_TLV_TYPE_1D,
    EM_CDP_TLV_LENGTH_1D, 
    EM_CDP_DEVICE_ID,
    EM_CDP_PORT_ID,
    EM_CDP_N_ADDRESS_2,
    EM_CDP_N_ADDRESS_16,
    EM_CDP_PROTO_TYPE,
    EM_CDP_PROTO_LEN,
    EM_CDP_PROTOCOL,
    EM_CDP_ADDRESS_LEN,
    EM_CDP_ADDRESS,
    EM_CDP_NRGYZ_IP_ADDRESS,
    EM_CDP_CAPABILITY,
    EM_CDP_CAPABILITY_ROUTER,
    EM_CDP_CAPABILITY_TRANS,
    EM_CDP_CAPABILITY_SRC,
    EM_CDP_CAPABILITY_SWITCH,
    EM_CDP_CAPABILITY_HOST,
    EM_CDP_CAPABILITY_IGMP,
    EM_CDP_CAPABILITY_REPEATER,
    EM_CDP_SOFTWARE_V,
    EM_CDP_PLATFORM,
    EM_CDP_ODR_DEFAULT_GATEWAY,
    EM_CDP_OUI_8,
    EM_CDP_PROTO_ID,
    EM_CDP_CLUSTER_MASTER_IP,
    EM_CDP_CLUSTER_IP,
    EM_CDP_CLUSTER_VERSION,
    EM_CDP_CLUSTER_SUB_V,
    EM_CDP_CLUSTER_STATUS,
    EM_CDP_CLUSTER_UNKNOW1,
    EM_CDP_CLUSTER_COMMAND_MAC,
    EM_CDP_CLUSTER_SWITCH_MAC,
    EM_CDP_CLUSTER_UNKNOW2,
    EM_CDP_CLUSTER_MANAGEMENT_VLAN,
    EM_CDP_HELLO_UNKNOE,
    EM_CDP_VTP_M_DOMAIN,
    EM_CDP_NATIVE_VLAN,
    EM_CDP_DUPLEX,
    EM_CDP_DATA_E,
    EM_CDP_VOICE_VLAN_E,
    EM_CDP_DATA_F,
    EM_CDP_VOICE_VLAN_F,
    EM_CDP_POWER_CONSUMPTION,
    EM_CDP_MTU,
    EM_CDP_TRUST_BITMAP,
    EM_CDP_UNTRUST_PORT_COS,
    EM_CDP_SYSTEM_NAME,
    EM_CDP_SYSTEM_OBJ_IDENT,
    EM_CDP_LOCATION_UNKNOW,
    EM_CDP_LOCATION,
    EM_CDP_REQ_ID19,
    EM_CDP_MANAGE_ID_19,
    EM_CDP_POWER_REQ,
    EM_CDP_REQ_ID_1A,
    EM_CDP_MANAGE_ID_1A,
    EM_CDP_POWER_AVAIL,
    EM_CDP_ENCRYPT_DATA,
    EM_CDP_SEEN_SEQUENCE,
    EM_CDP_SEQUENCE_NUM,
    EM_CDP_MODEL_NUM,
    EM_CDP_UNKNOW_PAD,
    EM_CDP_HARDWARE_V_ID,
    EM_CDP_SYSTEM_SERIAL_NUM,
    EM_CDP_NRGYZ_UNKNOW_VAL,
    EM_CDP_LEN_TLV_TABLE,
    EM_CDP_NUM_TLVS_TABLE,
    
    
    EM_CDP_MAX
};

void dissect_cdp(const uint8_t * cdp_payload, uint32_t cdp_len);
#endif /* DPI_CDP_H */
