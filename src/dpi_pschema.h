#ifndef DPI_PSCHEMA_H
#define DPI_PSCHEMA_H

#include <yaProtoRecord/precord.h>
typedef struct _dpi_field_table_t    dpi_field_table;
typedef struct ya_fvalue             ya_fvalue_t;
typedef struct ProtoRecord           precord_t;
//typedef struct ProtoRecordField            pfield_t;
typedef struct ProtoAdapterFieldDesc ProtoAdapterFieldDesc_t;

// extern cpp
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

int dpi_pschema_module_init();

int dpi_pschema_register_proto_field(const char *proto_name, dpi_field_table *field_table, int field_count, char *common_field_schema);

int dpi_pschema_register_ws_proto_field(const char *proto_name, ProtoAdapterFieldDesc_t *fieldsDesc, int field_count, char *common_field_schema);

int dpi_pschema_dump_proto_schemas(const char *pschema_output_dir);


/*
* @brief: 遍历接口
*/
pschema_t * dpi_pschema_get_first();
pschema_t * dpi_pschema_get_next(pschema_t * schema);

/*
* @brief: register proto schema
* @param: proto_name [in] proto name
* @param: proto_full_name [in] proto full name
* @param: common_field_schema [in] common field schema
*/
int dpi_pschema_register_proto(const char *proto_name, const char *proto_full_name, char *common_field_schema);

/*
* @brief: get proto schema by proto name
* @param: proto_name [in] proto name
*/
pschema_t * dpi_pschema_get_proto(const char *proto_name);

precord_t* dpi_pschema_new_record(const char *proto_name);

pfield_t* dpi_precord_fvalue_put_by_index(precord_t *record, int fieldIndex, ya_fvalue_t *field_value);

precord_t * dpi_precord_create(const char * proto_name);
void dpi_precord_destroy(precord_t ** record);
int dpi_fvalue_new_string_put(precord_t * record, ya_ftenum_t ftype, const char * field_name, const char * value, uint64_t len);

// debug
void  dpi_precord_print_layers(precord_t * record, const char * prefix);
void dpi_pschema_print_all();
void printf_record(precord_t *record);

#ifdef __cplusplus
}
#endif

#endif /* DPI_PSCHEMA_H */
