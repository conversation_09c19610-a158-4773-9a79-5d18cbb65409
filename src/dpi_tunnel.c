/****************************************************************************************
 * 文 件 名 : dpi_tunnel.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh              2018/10/08
编码: liugh            2018/10/08
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/



#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_trailer.h"


extern struct rte_mempool *tbl_log_mempool;
extern struct global_config g_config;


enum ah_index_em {
    EM_AH_NEXT_HD,
    EM_AH_LENGTH,
    EM_AH_SPI,
    EM_AH_SEQ,
    EM_AH_ICV,
    EM_AH_TOTAL_PACKETS,
    EM_AH_TOTAL_BYTES,
    EM_AH_MAX,
};

static dpi_field_table  ah_field_array[] = {
    DPI_FIELD_D(EM_AH_NEXT_HD,            EM_F_TYPE_UINT32,     "Next_Head"),
    DPI_FIELD_D(EM_AH_LENGTH,             EM_F_TYPE_UINT32,     "AH_length"),
    DPI_FIELD_D(EM_AH_SPI,                EM_F_TYPE_UINT32,     "AH_SPI"),
    DPI_FIELD_D(EM_AH_SEQ,                EM_F_TYPE_UINT32,     "AH_Sequence"),
    DPI_FIELD_D(EM_AH_ICV,                YA_FT_STRING,         "AH_ICV"),
    DPI_FIELD_D(EM_AH_TOTAL_PACKETS,      EM_F_TYPE_UINT32,     "total_packets"),
    DPI_FIELD_D(EM_AH_TOTAL_BYTES,        EM_F_TYPE_UINT32,     "total_bytes"),
};

static void write_ah_log(struct flow_info *flow, struct dpi_ahhd *ahhd)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, 0, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ah");

    for (i = 0; i < EM_AH_MAX; i++) {
        switch (ah_field_array[i].index) {
        case EM_AH_NEXT_HD:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ahhd->next_hd);
            break;
        case EM_AH_LENGTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ahhd->length);
            break;
        case EM_AH_SPI:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ahhd->ah_spi);
            break;
        case EM_AH_SEQ:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ahhd->ah_seq);
            break;
        case EM_AH_ICV:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ahhd->ah_icv, 0);
            break;
        default:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_AH;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
}

int dpi_dissect_ah(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(g_config.protocol_switch[PROTOCOL_AH] == 0 || payload_len < 12)
        return PKT_OK;

    struct dpi_ahhd ahhd;
    ahhd.next_hd = get_uint8_t(payload, 0);
    ahhd.length  = get_uint8_t(payload, 1) * 4;
    ahhd.ah_spi  = get_uint32_ntohl(payload, 4);
    ahhd.ah_seq  = get_uint32_ntohl(payload, 8);
    ahhd.ah_icv = NULL;

    write_ah_log(flow, &ahhd);
    return PKT_OK;
}

enum udpencap_index_em{
    EM_UE_UPPER,
    EM_UE_PKTS,
    EM_UE_BYTES,
    EM_UE_MAX,
};

static dpi_field_table udpencap_field_array[] = {
    DPI_FIELD_D(EM_UE_UPPER,     EM_F_TYPE_NULL,        "upper_layer"),
    DPI_FIELD_D(EM_UE_PKTS,      EM_F_TYPE_NULL,        "udpencap_total_pkts"),
    DPI_FIELD_D(EM_UE_BYTES,     EM_F_TYPE_NULL,        "udpencap_total_bytes"),
};

enum esp_index_em{
    EM_ESP_SPI,
    EM_ESP_SEQ,
    EM_ESP_PAYLOADLENGTH,
    EM_ESP_PADDINGLENGTH,
    EM_ESP_AUTHENTLENGTH,
    EM_ESP_TOTAL_PACKETS,
    EM_ESP_TOTAL_BYTES,
    EM_ESP_MAX,
};

static dpi_field_table  esp_field_array[] = {
    DPI_FIELD_D(EM_ESP_SPI,            EM_F_TYPE_UINT32,     "spi"),
    DPI_FIELD_D(EM_ESP_SEQ,            EM_F_TYPE_UINT32,     "seq"),
    DPI_FIELD_D(EM_ESP_PAYLOADLENGTH,  EM_F_TYPE_UINT32,     "payloadlength"),
    DPI_FIELD_D(EM_ESP_PADDINGLENGTH,  EM_F_TYPE_UINT32,     "paddinglength"),
    DPI_FIELD_D(EM_ESP_AUTHENTLENGTH,  EM_F_TYPE_UINT32,     "authentlength"),
    DPI_FIELD_D(EM_ESP_TOTAL_PACKETS,  EM_F_TYPE_UINT32,     "total_packets"),
    DPI_FIELD_D(EM_ESP_TOTAL_BYTES,    EM_F_TYPE_UINT32,     "total_bytes"),

};

static void write_esp_log(struct flow_info *flow, struct dpi_esphd *esphd)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;
    
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, 0, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "esp");

    for(i=0;i<EM_ESP_MAX;i++){
        switch(esp_field_array[i].index){

        case EM_ESP_SPI:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,  esphd->spi);
            break;
        case EM_ESP_SEQ:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, esphd->seq);
            break;
        case EM_ESP_PAYLOADLENGTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, esphd->payloadlength);
            break;
        case EM_ESP_PADDINGLENGTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, esphd->paddinglength);
            break;
        case EM_ESP_AUTHENTLENGTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, esphd->authentlength);
            break;
        default:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_ESP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
}

int dpi_dissect_esp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if(g_config.protocol_switch[PROTOCOL_ESP] == 0 || payload_len < 8)
        return PKT_OK;

    struct dpi_esphd esphd;
    esphd.spi=get_uint32_ntohl(payload, 0);
    esphd.seq=get_uint32_ntohl(payload, 1);
    esphd.payloadlength = payload_len - 8;
    esphd.paddinglength = 0;
    esphd.authentlength = 0;

    write_esp_log(flow, &esphd);

    return PKT_OK;
}

static void init_ah_dissector(void)
{
    dpi_register_proto_schema(ah_field_array, EM_AH_MAX, "ah");
    map_fields_info_register(ah_field_array,PROTOCOL_AH, EM_AH_MAX, "ah");
}

static void identify_ah_esp_n_udp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if(g_config.protocol_switch[PROTOCOL_AH_N] == 0 || g_config.protocol_switch[PROTOCOL_ESP_N] == 0)
        return;

    if((payload_len >12)
        && (htons(flow->tuple.inner.port_src) == 500 || htons(flow->tuple.inner.port_src) == 4500
            ||htons(flow->tuple.inner.port_dst) == 500 || htons(flow->tuple.inner.port_dst) == 4500))
    {
        if(payload[0] == 0x32)
            flow->real_protocol_id = PROTOCOL_AH_N;
        else if (payload_len > 28 && (payload[17]>>4 == 1 || payload[17]>>4 == 2) && get_uint32_ntohl(payload, 24) == payload_len)
            flow->real_protocol_id = PROTOCOL_ISAKMP;
        else
            flow->real_protocol_id = PROTOCOL_ESP_N;
    }
    return;
}

void write_udpencap_log(struct flow_info *flow, int direction, int upper);
void write_udpencap_log(struct flow_info *flow, int direction, int upper)
{
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
   player_t *layer = precord_layer_put_new_layer(log_ptr->record, "udpencap");

    if(upper == 1)
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "AH", 2);
    else
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "ESP", 3);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->src2dst_packets + flow->dst2src_packets);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->src2dst_bytes + flow->dst2src_bytes);

    log_ptr->log_type = TBL_LOG_UDPENCAP;
    log_ptr->log_len  = idx;

    if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
    return;

}

int write_ah_n_tbl_log(struct flow_info *flow, int direction);
int write_ah_n_tbl_log(struct flow_info *flow, int direction){

    write_udpencap_log(flow, direction, 1);

    if (g_config.protocol_switch[PROTOCOL_AH_N] == 0)
        return 0;

    int idx = 0;
    int i;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 1;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ah_n");

    for(i=0; i<EM_ESP_MAX; i++){
        switch(ah_field_array[i].index){
            case EM_ESP_TOTAL_PACKETS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->src2dst_packets + flow->dst2src_packets);
                break;
            case EM_ESP_TOTAL_BYTES:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->src2dst_bytes + flow->dst2src_bytes);
                break;
            default:
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,1);
                break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_AH;
    log_ptr->log_len  = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
    return 0;
}

static int dissect_ah_n_udp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    return 0;
}

static void init_ah_n_dissector(void)
{
//    dpi_register_proto_schema(ah_field_array, EM_AH_MAX, "ah_n");  //不写字段表，复用ah
//    port_add_proto_head(IPPROTO_UDP, 500,   PROTOCOL_AH_N);
//    port_add_proto_head(IPPROTO_UDP, 4500,  PROTOCOL_AH_N);

        udp_detection_array[PROTOCOL_AH_N].proto = PROTOCOL_AH_N;
        udp_detection_array[PROTOCOL_AH_N].identify_func = identify_ah_esp_n_udp;
        udp_detection_array[PROTOCOL_AH_N].dissect_func  = dissect_ah_n_udp;

        DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_AH_N].excluded_protocol_bitmask, PROTOCOL_AH_N);

    map_fields_info_register(ah_field_array,PROTOCOL_AH_N, EM_AH_MAX, "an_n");
}

static void init_esp_dissector(void)
{
    dpi_register_proto_schema(esp_field_array,EM_ESP_MAX,"esp");
    map_fields_info_register(esp_field_array,PROTOCOL_ESP, EM_ESP_MAX, "esp");
}

int write_esp_n_tbl_log(struct flow_info *flow, int direction);
int write_esp_n_tbl_log(struct flow_info *flow, int direction)
{
    write_udpencap_log(flow, direction, 2);

    if (g_config.protocol_switch[PROTOCOL_ESP_N] == 0)
        return 0;
    int idx = 0;
    int i;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 1;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "esp_n");

    for(i=0; i<EM_ESP_MAX; i++){
        switch(esp_field_array[i].index){
            case EM_ESP_TOTAL_PACKETS:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->src2dst_packets + flow->dst2src_packets);
                break;
            case EM_ESP_TOTAL_BYTES:
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, flow->src2dst_bytes + flow->dst2src_bytes);
                break;
            default:
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,1);
                break;
        }
    }
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_ESP;
    log_ptr->log_len  = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
    return 0;
}

static int dissect_esp_n_udp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    return 0;
}

static void init_esp_n_dissector(void)
{
    //dpi_register_proto_schema(esp_field_array, EM_ESP_MAX, "esp_n");   //不写字段表，复用esp
    //port_add_proto_head(IPPROTO_UDP, 500,   PROTOCOL_ESP_N);
    //port_add_proto_head(IPPROTO_UDP, 4500,  PROTOCOL_ESP_N);

    udp_detection_array[PROTOCOL_ESP_N].proto = PROTOCOL_ESP_N;
    udp_detection_array[PROTOCOL_ESP_N].identify_func = identify_ah_esp_n_udp;
    udp_detection_array[PROTOCOL_ESP_N].dissect_func  = dissect_esp_n_udp;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_ESP_N].excluded_protocol_bitmask, PROTOCOL_ESP_N);

    map_fields_info_register(esp_field_array,PROTOCOL_ESP_N, EM_ESP_MAX, "esp_n");
}

static void init_udpencap_dissector(void)
{
    dpi_register_proto_schema(udpencap_field_array, EM_UE_MAX, "udpencap");
}

static __attribute((constructor)) void    before_init_udpencap(void){
    register_tbl_array(TBL_LOG_UDPENCAP, 0, "udpencap", init_udpencap_dissector);
}

static __attribute((constructor)) void    before_init_ah(void) {
    register_tbl_array(TBL_LOG_AH, 0, "ah", init_ah_dissector);
}

//ah over udp
static __attribute((constructor)) void    before_init_ah_n(void) {
    register_tbl_array(TBL_LOG_AH_N, 0, "ah_n", init_ah_n_dissector);
}

static __attribute((constructor)) void    before_init_esp(void){
    register_tbl_array(TBL_LOG_ESP, 0, "esp", init_esp_dissector);
}

//esp over udp
static __attribute((constructor)) void    before_init_esp_n(void){
    register_tbl_array(TBL_LOG_ESP_N, 0, "esp_n", init_esp_n_dissector);
}

