/****************************************************************************************
 * 文 件 名 : dpi_imap.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy          2018/07/09
编码: wangy            2018/07/09
修改: xuxn          2019/03/19
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <stdint.h>
#include "dpi_imap.h"

#include <rte_mempool.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"
#include "dpi_utils.h"
#include "base64.h"
#include "dpi_email.h"

extern char *email_heads[];
extern const char *email_cmds[];

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

static dpi_field_table  imap_field_array[] = {
    //extend
    DPI_FIELD_D(EM_IMAP_AUTH_NAME,                                   EM_F_TYPE_STRING,             "Auth_name"),
    DPI_FIELD_D(EM_IMAP_AUTH_PASSWD,                                 EM_F_TYPE_STRING,             "Auth_passwd"),
    DPI_FIELD_D(EM_IMAP_MAIL_FILENAME,                               EM_F_TYPE_STRING,             "Mail_filename"),
    DPI_FIELD_D(EM_IMAP_DATE,                                        EM_F_TYPE_STRING,             "Date"),
    DPI_FIELD_D(EM_IMAP_FROM,                                        EM_F_TYPE_STRING,             "From"),
    DPI_FIELD_D(EM_IMAP_SENDER,                                      EM_F_TYPE_STRING,             "Sender_Name"),
    DPI_FIELD_D(EM_IMAP_TO,                                          EM_F_TYPE_STRING,             "To"),
    DPI_FIELD_D(EM_IMAP_RECEIVER,                                    EM_F_TYPE_STRING,             "Receiver_Name"),
    DPI_FIELD_D(EM_IMAP_CC,                                          EM_F_TYPE_STRING,             "Cc"),
    DPI_FIELD_D(EM_IMAP_BCC,                                         EM_F_TYPE_STRING,             "Bcc"),
    DPI_FIELD_D(EM_IMAP_MESSAGE_ID,                                  EM_F_TYPE_STRING,             "Message-ID"),
    DPI_FIELD_D(EM_IMAP_REPLY_TO,                                    EM_F_TYPE_STRING,             "Reply-To"),
    DPI_FIELD_D(EM_IMAP_IN_REPLY_TO,                                 EM_F_TYPE_STRING,             "In-Reply-To"),
    DPI_FIELD_D(EM_IMAP_REFERENCES,                                  EM_F_TYPE_STRING,             "References"),
    DPI_FIELD_D(EM_IMAP_SUBJECT,                                     EM_F_TYPE_STRING,             "Subject"),
    DPI_FIELD_D(EM_IMAP_RECEIVED1,                                   EM_F_TYPE_STRING,             "Received1"),
    DPI_FIELD_D(EM_IMAP_RECEIVED2,                                   EM_F_TYPE_STRING,             "Received2"),
    DPI_FIELD_D(EM_IMAP_X_MAILER,                                    EM_F_TYPE_STRING,             "X-Mailer"),
    DPI_FIELD_D(EM_IMAP_MIME_VERSION,                                EM_F_TYPE_STRING,             "MIME-Version"),
    DPI_FIELD_D(EM_IMAP_CONTENT_ID,                                  EM_F_TYPE_STRING,             "Content-ID"),
    DPI_FIELD_D(EM_IMAP_CONTENT_DESCRIPTION,                         EM_F_TYPE_STRING,             "Content-Description"),
    DPI_FIELD_D(EM_IMAP_CONTENT_TRANSFER_ENCODING,                   EM_F_TYPE_STRING,             "Content-Transfer-Encoding"),
    DPI_FIELD_D(EM_IMAP_CONTENT_TYPE,                                EM_F_TYPE_STRING,             "Content-Type"),
    DPI_FIELD_D(EM_IMAP_DELIVERY_DATE,                               EM_F_TYPE_STRING,             "Delivery-Date"),
    DPI_FIELD_D(EM_IMAP_LATEST_DELIVERY_TIME,                        EM_F_TYPE_STRING,             "Latest-Delivery-Time"),
    DPI_FIELD_D(EM_IMAP_RESENT_DATE,                                 EM_F_TYPE_STRING,             "Resent-Date"),
    DPI_FIELD_D(EM_IMAP_SEND_SERVER,                                 EM_F_TYPE_STRING,            "Sender-Server"),
    DPI_FIELD_D(EM_IMAP_SEND_SOFT,                                   EM_F_TYPE_STRING,            "Sender-Soft"),
    DPI_FIELD_D(EM_IMAP_X_ORIGINATING_IP,                            EM_F_TYPE_STRING,            "X-Originating-Ip"),
    DPI_FIELD_D(EM_IMAP_SENDER_SVR_IP,                               EM_F_TYPE_STRING,             "Sender-Svr-Ip"),
    DPI_FIELD_D(EM_IMAP_SENDER_SVR,                                  EM_F_TYPE_STRING,            "Sender-Svr"),
    DPI_FIELD_D(EM_IMAP_RECEIVER_SVR_IP,                             EM_F_TYPE_STRING,             "Receiver-Svr-Ip"),
    DPI_FIELD_D(EM_IMAP_RECEIVER_SVR,                                EM_F_TYPE_STRING,            "Receiver-Svr"),
    DPI_FIELD_D(EM_IMAP_X_PRIORITY,                                  EM_F_TYPE_STRING,            "X_Priority"),

    DPI_FIELD_D(EM_IMAP_PROXY_RESEND,                                EM_F_TYPE_EMPTY,                "Proxy_Resend"),
    DPI_FIELD_D(EM_IMAP_PROXY_USER,                                  EM_F_TYPE_STRING,                "Proxy_User"),
    DPI_FIELD_D(EM_IMAP_USER_OP_TYPE,                                EM_F_TYPE_EMPTY,                "User_Op_Type"),
    DPI_FIELD_D(EM_IMAP_USER_OP_TIME,                                EM_F_TYPE_EMPTY,                "User_Op_Time"),
    DPI_FIELD_D(EM_IMAP_USER_OP_RES,                                 EM_F_TYPE_EMPTY,                "User_Op_Res"),
    DPI_FIELD_D(EM_IMAP_RECEIVER_TYPE,                               EM_F_TYPE_EMPTY,                "Receiver_Type"),
    DPI_FIELD_D(EM_IMAP_SEND_DURATION,                               EM_F_TYPE_EMPTY,                "Send_Duration"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME0,                       EM_F_TYPE_STRING,             "Attachment_Filename0"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE0,                   EM_F_TYPE_STRING,             "Attachment_Content_Type0"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING0,                       EM_F_TYPE_STRING,             "Attachment_Encoding0"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION0,                    EM_F_TYPE_STRING,             "Attachment_Disposition0"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN0,                            EM_F_TYPE_UINT64,            "Attachment_len0"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME1,                       EM_F_TYPE_STRING,             "Attachment_Filename1"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE1,                   EM_F_TYPE_STRING,             "Attachment_Content_Type1"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING1,                       EM_F_TYPE_STRING,             "Attachment_Encoding1"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION1,                    EM_F_TYPE_STRING,             "Attachment_Disposition1"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN1,                            EM_F_TYPE_UINT64,            "Attachment_len1"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME2,                       EM_F_TYPE_STRING,             "Attachment_Filename2"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE2,                   EM_F_TYPE_STRING,             "Attachment_Content_Type2"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING2,                       EM_F_TYPE_STRING,             "Attachment_Encoding2"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION2,                    EM_F_TYPE_STRING,             "Attachment_Disposition2"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN2,                            EM_F_TYPE_UINT64,            "Attachment_len2"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME3,                       EM_F_TYPE_STRING,             "Attachment_Filename3"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE3,                   EM_F_TYPE_STRING,             "Attachment_Content_Type3"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING3,                       EM_F_TYPE_STRING,             "Attachment_Encoding3"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION3,                    EM_F_TYPE_STRING,             "Attachment_Disposition3"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN3,                            EM_F_TYPE_UINT64,            "Attachment_len3"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME4,                       EM_F_TYPE_STRING,             "Attachment_Filename4"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE4,                   EM_F_TYPE_STRING,             "Attachment_Content_Type4"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING4,                       EM_F_TYPE_STRING,             "Attachment_Encoding4"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION4,                    EM_F_TYPE_STRING,             "Attachment_Disposition4"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN4,                            EM_F_TYPE_UINT64,            "Attachment_len4"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME5,                       EM_F_TYPE_STRING,             "Attachment_Filename5"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE5,                   EM_F_TYPE_STRING,             "Attachment_Content_Type5"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING5,                       EM_F_TYPE_STRING,             "Attachment_Encoding5"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION5,                    EM_F_TYPE_STRING,             "Attachment_Disposition5"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN5,                            EM_F_TYPE_UINT64,            "Attachment_len5"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME6,                       EM_F_TYPE_STRING,             "Attachment_Filename6"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE6,                   EM_F_TYPE_STRING,             "Attachment_Content_Type6"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING6,                       EM_F_TYPE_STRING,             "Attachment_Encoding6"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION6,                    EM_F_TYPE_STRING,             "Attachment_Disposition6"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN6,                            EM_F_TYPE_UINT64,            "Attachment_len6"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME7,                       EM_F_TYPE_STRING,             "Attachment_Filename7"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE7,                   EM_F_TYPE_STRING,             "Attachment_Content_Type7"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING7,                       EM_F_TYPE_STRING,             "Attachment_Encoding7"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION7,                    EM_F_TYPE_STRING,             "Attachment_Disposition7"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN7,                            EM_F_TYPE_UINT64,            "Attachment_len7"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME8,                       EM_F_TYPE_STRING,             "Attachment_Filename8"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE8,                   EM_F_TYPE_STRING,             "Attachment_Content_Type8"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING8,                       EM_F_TYPE_STRING,             "Attachment_Encoding8"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION8,                    EM_F_TYPE_STRING,             "Attachment_Disposition8"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN8,                            EM_F_TYPE_UINT64,            "Attachment_len8"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_FILENAME9,                       EM_F_TYPE_STRING,             "Attachment_Filename9"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_CONTENT_TYPE9,                   EM_F_TYPE_STRING,             "Attachment_Content_Type9"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_ENCODING9,                       EM_F_TYPE_STRING,             "Attachment_Encoding9"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_DISPOSITION9,                    EM_F_TYPE_STRING,             "Attachment_Disposition9"),
    DPI_FIELD_D(EM_IMAP_ATTACHMENT_LEN9,                            EM_F_TYPE_UINT64,            "Attachment_len9"),

    /* new fields add by liugh*/
    DPI_FIELD_D(EM_IMAP_MAIL_CODE_FORMAT,                           EM_F_TYPE_STRING,             "MailContentCodeFormat"),
    DPI_FIELD_D(EM_IMAP_MAIL_CODE_BASE,                             EM_F_TYPE_STRING,             "MailContentCodeBase"),
    DPI_FIELD_D(EM_IMAP_MAIL_CONTENT_DATA,                          EM_F_TYPE_STRING,             "MailContentData"),

    DPI_FIELD_D(EM_IMAP_PROTO_TYPE,                                 EM_F_TYPE_STRING,             "Proto_Type"),

    DPI_FIELD_D(EM_IMAP_EML_FILESIZE,                               EM_F_TYPE_UINT32,            "eml_filesize"),

    DPI_FIELD_D(EM_IMAP_LOGIN_STATUS,                               EM_F_TYPE_STRING,            "LoginStatus")
};

uint16_t get_imap_field(const uint8_t *payload, uint32_t payload_len, const uint8_t** field);
void dissect_imap_attachments_info(const uint8_t *payload, uint32_t payload_len, struct imap_session *session, uint32_t offset);

/*
*imap的邮件的内容用一个单独的eml文件保存，通过tbl日志中的一个字段关联
*/
static int  get_imap_filename(uint8_t thread_id,char *name, int len)
{
    return get_special_filename(NULL, "imap", "eml", name, len, 1);

}

static int _find_space(const uint8_t *start, uint16_t max_len, int num)
{
    int _num = 0;
    uint16_t i = 0;

    while (i < max_len) {
        if (start[i] == ' ' && ++_num == num)
            return i;
        i++;
    }

    return -1;
}
static int imap_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct imap_session* session, int *idx, int i)
{
    //int local_idx=*idx;
    switch(i){
        case EM_IMAP_AUTH_NAME:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,(const uint8_t *)session->auth_name, strlen(session->auth_name));
            break;
        case EM_IMAP_AUTH_PASSWD:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,(const uint8_t *)session->auth_passwd, strlen(session->auth_passwd));
            break;
        case EM_IMAP_MAIL_FILENAME:
            if(strlen(session->mail_filename)>0){
                char filename[128]={0};
                if(get_filename(session->mail_filename, filename)){
                    write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,(const uint8_t *)filename, strlen(filename));
                    break;
                }
            }
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,(const uint8_t *)session->mail_filename, strlen(session->mail_filename));
            break;
        case EM_IMAP_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_date_ptr, session->mail_date_len);
            break;
        case EM_IMAP_FROM:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_from_ptr, session->mail_from_len);
            break;
        case EM_IMAP_SENDER:
            if(session->mail_sender_name_ptr)
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_sender_name_ptr, session->mail_sender_name_len);
            else
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_from_ptr, session->mail_from_len);
            break;
        case EM_IMAP_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_to_ptr, session->mail_to_len);
            break;
        //目前按to字段处理
        case EM_IMAP_RECEIVER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_to_ptr, session->mail_to_len);
            break;
        case EM_IMAP_CC:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_cc_ptr, session->mail_cc_len);
            break;
        case EM_IMAP_BCC:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_bcc_ptr, session->mail_bcc_len);
            break;
        case EM_IMAP_MESSAGE_ID:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_message_id_ptr, session->mail_message_id_len);
            break;
        case EM_IMAP_REPLY_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_reply_to_ptr, session->mail_reply_to_len);
            break;
        case EM_IMAP_IN_REPLY_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_in_reply_to_ptr, session->mail_in_reply_to_len);
            break;
        case EM_IMAP_REFERENCES:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_references_ptr, session->mail_references_len);
            break;
        case EM_IMAP_SUBJECT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_subject_ptr, session->mail_subject_len);
            break;
        case EM_IMAP_RECEIVED1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_received1_ptr, session->mail_received1_len);
            break;
        case EM_IMAP_RECEIVED2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_received2_ptr, session->mail_received2_len);
            break;
        case EM_IMAP_X_MAILER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_x_mailer_ptr, session->mail_x_mailer_len);
            break;
        case EM_IMAP_PROXY_USER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->mail_user_agent_ptr, session->mail_user_agent_len);
            break;
        case EM_IMAP_MIME_VERSION:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_mime_version_ptr, session->mail_mime_version_len);
            break;
        case EM_IMAP_CONTENT_ID:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_content_id_ptr, session->mail_content_id_len);
            break;
        case EM_IMAP_CONTENT_DESCRIPTION:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_content_description_ptr, session->mail_content_description_len);
            break;
        case EM_IMAP_CONTENT_TRANSFER_ENCODING:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_content_transfer_encoding_ptr, session->mail_content_transfer_encoding_len);
            break;
        case EM_IMAP_CONTENT_TYPE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_content_type_ptr, session->mail_content_type_len);
            break;
        case EM_IMAP_DELIVERY_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_delivery_date_ptr, session->mail_delivery_date_len);
            break;
        case EM_IMAP_LATEST_DELIVERY_TIME:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_latest_delivery_time_ptr, session->mail_latest_delivery_time_len);
            break;
        case EM_IMAP_RESENT_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_resent_date_ptr, session->mail_resent_date_len);
            break;
        case EM_IMAP_SEND_SERVER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_send_server_ptr, session->mail_send_server_len);
            break;
        case EM_IMAP_SEND_SOFT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_send_soft_ptr, session->mail_send_soft_len);
            break;
        case EM_IMAP_X_ORIGINATING_IP:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_x_originating_ip_ptr, session->mail_x_originating_ip_len);
            break;
        case EM_IMAP_SENDER_SVR_IP:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_sender_svr_ip_ptr, session->mail_sender_svr_ip_len);
            break;
        case EM_IMAP_RECEIVER_SVR_IP:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_receiver_svr_ip_ptr, session->mail_receiver_svr_ip_len);
            break;
        case EM_IMAP_SENDER_SVR:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_sender_svr_ptr, session->mail_sender_svr_len);
            break;
        case EM_IMAP_RECEIVER_SVR:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_receiver_svr_ptr, session->mail_receiver_svr_len);
            break;
        case EM_IMAP_X_PRIORITY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->mail_x_priority_ptr, session->mail_x_priority_len);
            break;
        case EM_IMAP_ATTACHMENT_FILENAME0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[0].attachment_filename, strlen((const char*)session->attachment_list[0].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[0].attachment_content_type, strlen((const char*)session->attachment_list[0].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[0].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[0].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[0].attachment_content_disposition, strlen((const char*)session->attachment_list[0].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[0].attachment_len);
            break;
        case EM_IMAP_ATTACHMENT_FILENAME1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[1].attachment_filename, strlen((const char*)session->attachment_list[1].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[1].attachment_content_type, strlen((const char*)session->attachment_list[1].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[1].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[1].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[1].attachment_content_disposition, strlen((const char*)session->attachment_list[1].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[1].attachment_len);
            break;
        case EM_IMAP_ATTACHMENT_FILENAME2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[2].attachment_filename, strlen((const char*)session->attachment_list[2].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[2].attachment_content_type, strlen((const char*)session->attachment_list[2].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[2].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[2].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[2].attachment_content_disposition, strlen((const char*)session->attachment_list[2].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[2].attachment_len);
            break;
        case EM_IMAP_ATTACHMENT_FILENAME3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[3].attachment_filename, strlen((const char*)session->attachment_list[3].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[3].attachment_content_type, strlen((const char*)session->attachment_list[3].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[3].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[3].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[3].attachment_content_disposition, strlen((const char*)session->attachment_list[3].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[3].attachment_len);
            break;
        case EM_IMAP_ATTACHMENT_FILENAME4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[4].attachment_filename, strlen((const char*)session->attachment_list[4].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[4].attachment_content_type, strlen((const char*)session->attachment_list[4].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[4].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[4].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[4].attachment_content_disposition, strlen((const char*)session->attachment_list[4].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,NULL, session->attachment_list[4].attachment_len);
            break;
        case EM_IMAP_ATTACHMENT_FILENAME5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[5].attachment_filename, strlen((const char*)session->attachment_list[5].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[5].attachment_content_type, strlen((const char*)session->attachment_list[5].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[5].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[5].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[5].attachment_content_disposition, strlen((const char*)session->attachment_list[5].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[5].attachment_len);
            break;

        case EM_IMAP_ATTACHMENT_FILENAME6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[6].attachment_filename, strlen((const char*)session->attachment_list[6].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[6].attachment_content_type, strlen((const char*)session->attachment_list[6].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[6].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[6].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[6].attachment_content_disposition, strlen((const char*)session->attachment_list[6].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[6].attachment_len);
            break;

        case EM_IMAP_ATTACHMENT_FILENAME7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[7].attachment_filename, strlen((const char*)session->attachment_list[7].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[7].attachment_content_type, strlen((const char*)session->attachment_list[7].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[7].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[7].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[7].attachment_content_disposition, strlen((const char*)session->attachment_list[7].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[7].attachment_len);
            break;

        case EM_IMAP_ATTACHMENT_FILENAME8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[8].attachment_filename, strlen((const char*)session->attachment_list[8].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[8].attachment_content_type, strlen((const char*)session->attachment_list[8].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[8].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[8].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[8].attachment_content_disposition, strlen((const char*)session->attachment_list[8].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[8].attachment_len);
            break;

        case EM_IMAP_ATTACHMENT_FILENAME9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[9].attachment_filename, strlen((const char*)session->attachment_list[9].attachment_filename));
            break;
        case EM_IMAP_ATTACHMENT_CONTENT_TYPE9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[9].attachment_content_type, strlen((const char*)session->attachment_list[9].attachment_content_type));
            break;
        case EM_IMAP_ATTACHMENT_ENCODING9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[9].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[9].attachment_content_transfer_encoding));
            break;
        case EM_IMAP_ATTACHMENT_DISPOSITION9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type,session->attachment_list[9].attachment_content_disposition, strlen((const char*)session->attachment_list[9].attachment_content_disposition));
            break;
        case EM_IMAP_ATTACHMENT_LEN9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->attachment_list[9].attachment_len);
            break;



        /* 添加主题内容提取 */
        case EM_IMAP_MAIL_CODE_FORMAT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->main_content_print, strlen((const char*)session->main_content_print));
            break;
        case EM_IMAP_MAIL_CODE_BASE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->main_content_code, strlen((const char*)session->main_content_code));
            break;
        case EM_IMAP_MAIL_CONTENT_DATA:
            if(session->main_content_len>MAX_CONTENT_SIZE){
                session->main_content_len=0;
                session->main_content_data=NULL;
            }
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, session->main_content_data, session->main_content_len);
            break;


        case EM_IMAP_PROTO_TYPE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, (const uint8_t*)"IMAP", 4);
            break;

        case EM_IMAP_EML_FILESIZE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, NULL, session->eml_filesize);
            break;
        case EM_IMAP_LOGIN_STATUS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, imap_field_array[i].type, (const uint8_t *)session->login_status, strlen(session->login_status));
            break;
        default:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;

	}

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = imap_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif

	return 0;
}


static int write_imap_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;
    struct imap_session * session = (struct imap_session *)field_info;
    if (!session)
        return 0;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        log_trace("not enough memory");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "imap");

    for(i=0;i<EM_IMAP_MAX;i++){
        imap_field_element(log_ptr, flow, direction, session, &idx, i);
    }

    log_ptr->thread_id= flow->thread_id;
    log_ptr->log_type = TBL_LOG_MAIL_IMAP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->flow        = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}
static uint16_t get_imap_main_content(const char *data_p, uint32_t len, struct imap_session *session)
{
    const char *cp0 = NULL;
    const char *cp1 = NULL;
    const char *pp0 = NULL;
    const char *pp1 = NULL;
    uint32_t tmp_len = 0;
    uint32_t index = 0;

    session->main_content_len = 0;
    session->main_content_data = NULL;

    pp0 = mail_memstr(data_p + index, "charset=", len);
    if (pp0 != NULL) {
        tmp_len = pp0 - data_p;
        index += tmp_len;
        pp1 = mail_memstr(pp0, "\r\n", len - index);
        if (pp1 != NULL) {
            tmp_len = pp1 - pp0;
            strncat(session->text_charsets, (const char *)(pp0 + strlen("charset=")), tmp_len + 1);
            session->text_charsets_num++;
            index += tmp_len;
            index += 2;

            if (!strncasecmp((const char *)data_p, "text/html", 9)) {
                session->content_charset_ptr = (const uint8_t *)(pp0 + strlen("charset="));
                session->content_charset_len = tmp_len + 1;
            }

            if (index >= len) { return PKT_DROP; }

        }
    }

    if (index >= len) { return PKT_DROP; }
    if (pp1 != NULL) {
        cp0 = mail_memstr(pp1, "Content-Transfer-Encoding: ", len - index);
        tmp_len = cp0 - pp1;
        index += tmp_len;
        if (index >= len) { return PKT_DROP; }
        if (cp0 != NULL) {
            cp1 = mail_memstr(cp0, "\r\n", len - index);
            if (cp1 != NULL) {
                tmp_len = cp1 - cp0;
                index += tmp_len;
                if (index >= len) { return PKT_DROP; }

                if (tmp_len > 50) { tmp_len = 50; }
                snprintf((char*)session->main_content_code, tmp_len + 1, "%s", cp0);
                strncat(session->body_tra_encs, cp0, tmp_len + 1);
                strcat(session->body_tra_encs, ",");
                session->body_tra_encs_num++;

                if (index + 2 < len) {
                    if (strncmp(cp1 + 2, "\r\n", 2) == 0) {
                        goto content_data;
                    }
                    else {
                        index += 2;
                    }
                }
            }
        }
    }
    else {
        cp0 = mail_memstr(data_p, "Content-Transfer-Encoding: ", len - index);
        tmp_len = cp0 - data_p;
        index += tmp_len;
        if (index >= len) { return PKT_DROP; }
        if (cp0 != NULL) {
            cp1 = mail_memstr(cp0, "\r\n", len - index);
            if (cp1 != NULL) {
                tmp_len = cp1 - cp0;
                index += tmp_len;
                if (index >= len) { return PKT_DROP; }

                if (tmp_len > 50) { tmp_len = 50; }
                snprintf((char*)session->main_content_code, tmp_len, "%s", cp0);
                strncat(session->body_tra_encs, cp0, tmp_len);
                strcat(session->body_tra_encs, ",");
                session->body_tra_encs_num++;

                if (index + 2 < len) {
                    if (strncmp(cp1 + 2, "\r\n", 2) == 0) {
                        goto content_data;
                    }
                    else {
                        index += 2;
                    }
                }
            }
        }
    }

    const char *p_tmp = NULL;
    p_tmp = mail_memstr(data_p + index, "\r\n\r\n", len - index);
    if (p_tmp != NULL) {
        index = p_tmp - data_p;
        if (index >= len) { return PKT_DROP; }
    }

content_data:
    index += 4;
    if (index >= len) { return PKT_DROP; }
    session->main_content_data = (const uint8_t *)data_p + index;

    const char *dp0 = NULL;
    dp0 = mail_memstr(data_p + index, ".\r\n", len - index);
    if (cp1 != NULL && dp0 != NULL) {
        tmp_len = dp0 - data_p;
        if (tmp_len < index) { return PKT_DROP; }
        session->main_content_len = tmp_len - index;
        if (session->main_content_len > MAX_CONTENT_SIZE) {
            session->main_content_len = MAX_CONTENT_SIZE;
        }
    }
    else {
        session->main_content_len = 0;
        session->main_content_data = NULL;
    }

    /* html标签的正文 */
    if (!strncasecmp((const char *)data_p, "text/html", 9)) {
        session->content_has_html_ptr = session->main_content_data;
        session->content_has_html_len = session->main_content_len;
    }

    return PKT_OK;
}

//解决某个字段值有多行的情况(每行都具有明确的 "\r\n"): 确定该字段值的长度
//适用条件: 该字段完整值之后(包括"\r\n")一定是含有 ": " 或 "\r\n\r\n" 的一整行
//          该字段到这一行首个元素的地址偏移量上所有字符(可再减2去掉最后的一个"\r\n"),
//            均作为该字段的值，本函数的返回值即是这个偏移量,代表该字段值的长度

uint16_t get_imap_field(const uint8_t *payload, uint32_t payload_len, const uint8_t** field)
{
    uint16_t field_len = 0;
    const uint8_t* tmp = *field;
    uint16_t check_len=MAX_CHECK_LEN;
    if(tmp && tmp - payload < payload_len)
    {
        const uint8_t* p0 = (const uint8_t*)strstr((const char*)tmp,"\r\n");
        const uint8_t* p1 = (const uint8_t*)strstr((const char*)tmp,": ");
        const uint8_t* p2 = (const uint8_t*)strstr((const char*)tmp,"\r\n\r\n");
        if(p1 && p2 && p1 > p2)        //最后一个标准字段
        {
            field_len = p2 - tmp;
            return field_len < payload_len ? field_len : 0;
        }
        if(p0 && p1 && p2 && p0 < p1 && p1 < p2)
        {
            const uint8_t* p2 = (const uint8_t*)strstr((const char*)p1, "\r\n");
            if(p2)
            {
                field_len = p0 - tmp + 2;
                int len = tmp - payload;
                p0 = tmp + field_len;
                while(p0 && (unsigned)len + field_len < payload_len && p0 < p1)
                {
                    if(check_len>(len-field_len)){
                        check_len=len-field_len;
                    }
                    p0 = (const uint8_t*)mail_memstr((const char*)tmp + field_len,"\r\n",check_len);
                    if(!p0) break;
                    if(p0 >= p2)
                    {
                        field_len -= 2;
                        break;
                    }
                    field_len = p0 - tmp + 2;
                    if(field_len<=0 || field_len>=payload_len){
                        break;
                    }
                    p0 = tmp + field_len;
                    check_len=MAX_CHECK_LEN;
                }
                return field_len < payload_len ? field_len : 0;
            }
        }
    }
    *field = NULL;
    return 0;
}


//解析附件信息,目前最多解析10个附件
void dissect_imap_attachments_info(const uint8_t *payload, uint32_t payload_len, struct imap_session *session, uint32_t offset)
{
    if(!session->mail_content_type_ptr) return;
        const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_content_type_ptr,"-=_");
    const uint8_t* p2 = (uint8_t*)strstr((const char*)session->mail_content_type_ptr,"\"\r\n");
    if(p1 && p2 && p1 < p2 && payload)
    {
        int fLen = 0, bLen = 0;
        fLen = p2 - p1 - 3;
        if(fLen > 0 && fLen < 128)
        {
            //获取附件boundary标志
            char boundary[fLen+1];
            memset(boundary, 0, fLen+1);
            memcpy(boundary, p1+3, fLen);   //例:boundary == "001_NextPart357835361643_=----"
            bLen = strlen((const char*)boundary);        //标志成功提取
            if(bLen)
            {
                int index = 0;
                //过滤掉第一个boundary
                const uint8_t* tmp = (uint8_t*)strstr((const char*)payload + offset, boundary);
                tmp = (NULL == tmp ? NULL : tmp + bLen);
                //解析附件从第二个boundary开始,目前支持最多10个附件
                for(index=0; tmp && index < IMAP_ATTACHMENT_NUM_MAX; index++)
                {
                    int tLen = 0, flags = 0;
                    const uint8_t* tmp0 = NULL, *tmp1 = NULL, *tmp2 = NULL;
                    const uint8_t* tmptmp = (uint8_t*)strstr((const char*)tmp, boundary);
                    if(tmptmp)
                    {
                        if(index != 0)
                            session->attachment_list[index-1].attachment_len = tmptmp - tmp -10;
                        tmp = tmptmp;
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-type:");
                        if(tmp0)
                        {
                            #if 1  //提取主体内容 add by liugh
                            uint32_t now_len=tmp0-payload+14;
                            uint32_t left_len=payload_len-now_len;
                            if(left_len >0 &&
                               ((strncasecmp((const char *)tmp0+14,"text/plain",10) == 0) ||
                               (strncasecmp((const char *)tmp0+14,"text/html",9) == 0))
                            ){
                                if(session->main_content_len==0 && left_len>0 && left_len<payload_len)
                                    get_imap_main_content((const char *)(const char *)tmp0+14, left_len, session);
                            }
                            #endif

                            int cLen = strlen("content-type:");
                            tmp1 = (uint8_t*)strstr((const char*)tmp0, ";");
                            if(!tmp1)
                            {
                                tmp2 = (uint8_t*)strstr((const char*)tmp0,"\r\n");
                                if(tmp2)
                                {
                                    tLen = tmp2 - tmp0 - cLen -1;
                                    if (tLen > 0) {
                                        memcpy((void*)session->attachment_list[index].attachment_content_type, (const void*)(tmp0 + cLen), tLen < IMAP_CONTENT_TYPE_LEN ? tLen : IMAP_CONTENT_TYPE_LEN - 1);
                                        strncat(session->body_type, (const char *)(tmp0 + cLen), tLen);
                                        strcat(session->body_type, ",");
                                        session->body_type_num++;
                                    }
                                }
                                //-1 说明此处未获取到filename
                                flags = -1;
                            }
                            else
                            {
                                tLen = tmp1 - tmp0 - cLen -1;
                                if(tLen > 0)
                                {
                                    memcpy((void*)session->attachment_list[index].attachment_content_type, (const void*)(tmp0+cLen+1), tLen<IMAP_CONTENT_TYPE_LEN ? tLen:IMAP_CONTENT_TYPE_LEN-1);
                                    strncat(session->body_type, (const char *)(tmp0 + cLen + 1), tLen);
                                    strcat(session->body_type, ",");
                                    session->body_type_num++;

                                    session->attachment_conttype_num++;

                                    tmp1 = (uint8_t*)strstr((const char*)tmp1,"name=\"");
                                    if(tmp1)
                                    {
                                        tmp2 = (uint8_t*)strstr((const char*)tmp1,"\r\n");
                                        if(tmp2)
                                        {
                                            int len = strlen("name=\"");
                                            tLen = tmp2 - tmp1 - len -1;
                                            if (tLen > 0) {
                                                session->attachment_filename_num++;
                                                memcpy((void*)session->attachment_list[index].attachment_filename, (const void*)(tmp1 + len), tLen < IMAP_FILE_NAME_LEN ? tLen : IMAP_FILE_NAME_LEN - 1);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-md5:");
                        if (tmp0)
                        {
                            tmp2 = (uint8_t*)strstr((const char*)tmp0, "\r\n");
                            if (tmp2)
                            {
                                int cLen = strlen("content-md5:");
                                int len = tmp2 - tmp0 - cLen - 1;
                                if (len > 0) {
                                    session->attachent_md5_num++;
                                    memcpy((void*)session->attachment_list[index].attachment_content_md5, (const void*)(tmp0 + cLen + 1), len < CONTENT_MD5_LEN ? len : CONTENT_MD5_LEN - 1);
                                }
                            }
                        }
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-transfer-encoding:");
                        if(tmp0)
                        {
                            tmp2 = (uint8_t*)strstr((const char*)tmp0,"\r\n");
                            if(tmp2)
                            {
                                int cLen = strlen("content-transfer-encoding:");
                                int len = tmp2 - tmp0 - cLen -1;
                                if(len > 0)
                                    memcpy((void*)session->attachment_list[index].attachment_content_transfer_encoding, (const void*)(tmp0+cLen+1), len<IMAP_ENCODING_LEN ? len:IMAP_ENCODING_LEN-1);
                            }
                        }
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-disposition:");
                        if(tmp0)
                        {
                            int cLen = strlen("content-disposition:");
                            tmp1 = (uint8_t*)strstr((const char*)tmp0, ";");
                            if(!tmp1)
                            {
                                tmp2 = (uint8_t*)strstr((const char*)tmp0,"\r\n");
                                if(tmp2)
                                {
                                    tLen = tmp2 - tmp0 - cLen -1;
                                    if(tLen > 0)
                                        memcpy((void*)session->attachment_list[index].attachment_content_disposition, (const void*)(tmp0+cLen), tLen<IMAP_DISPOSITION_LEN ? tLen:IMAP_DISPOSITION_LEN-1);
                                }
                                //-1 说明此处未获取到filename
                                flags = -1;
                            }
                            else
                            {
                                tmp2 = (uint8_t*)strstr((const char*)tmp1+4,"\r\n");
                                if(tmp2)
                                {
                                    tLen = tmp2 - tmp0 - cLen -1;
                                    if(tLen > 0)
                                    {
                                        memcpy((void*)session->attachment_list[index].attachment_content_disposition, (const void*)(tmp0+cLen), tLen<IMAP_DISPOSITION_LEN ? tLen:IMAP_DISPOSITION_LEN-1);
                                        if(flags == -1)
                                        {
                                            tmp1 = (uint8_t*)strcasestr((const char*)tmp0, "filename=\"");
                                            if(tmp1)
                                            {
                                                int len = strlen("filename=\"");
                                                tLen = tmp2 - tmp1 - len -1;
                                                if (tLen > 0) {
                                                    session->attachment_filename_num++;
                                                    memcpy((void*)session->attachment_list[index].attachment_filename, (const void*)(tmp1 + len), tLen < IMAP_FILE_NAME_LEN ? tLen : IMAP_FILE_NAME_LEN - 1);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if(tmp2)
                        {
                            tmp = tmp2 + 4;
                            continue;
#if 0
                            //优化:此处的匹配如果与进循环第一次匹配合并是较好的做法
                            const uint8_t *ppppp = strstr(tmp, boundary);
                            if(ppppp)
                            {
                                session->attachment_list[index].attachment_len = ppppp - tmp -10;
                                continue;
                            }
#endif
                        }
                    }
                    break;
                }
            }
        }
    }
}
#if 0
void get_receiver_name(struct imap_session *session)
{
    if(session->mail_to_ptr && session->mail_to_len > 0)
    {
        const uint8_t* p0 = strstr(session->mail_to_ptr,"<");
        const uint8_t* p2 = strstr(session->mail_to_ptr,": ");
        if(!p0 || !p2 || p0 > p2)
        {
            session->receiver_name = (uint8_t *)malloc(session->mail_to_len+1);
            memset(session->receiver_name, 0, session->mail_to_len+1);
            memcpy(session->receiver_name, session->mail_to_ptr, session->mail_to_len);
        }
        else
        {
            int arrLen = p2 - session->mail_to_ptr;
            int index = 0;
            char arrtmp[arrLen+1];
            memset(arrtmp, 0, arrLen+1);
            memcpy(arrtmp, session->mail_to_ptr, arrLen);
            p0 = strrchr(arrtmp,'\r');
            if(!p0)
                p0 = strrchr(arrtmp,' ');
            if(p0)
            {
                index = p0 - session->mail_to_ptr;
                arrtmp[index] = '\0';
                session->receiver_name = (uint8_t *)malloc(index);
                memset(session->receiver_name, 0, index);
                memcpy(session->receiver_name, arrtmp, index-1);
            }
            else
            {
                session->receiver_name = (uint8_t *)malloc(arrLen+1);
                memset(session->receiver_name, 0, arrLen+1);
                memcpy(session->receiver_name, arrtmp, arrLen);
            }
            //todo.... 需要free(session->receiver_name)
#if 0
            while(i < to_len)
            {
                len = p0 - session->mail_to_ptr - tmp - 1;
                len = len < to_len-i ? len : to_len-i;
                //过滤掉两端的 "" (如果有)
                tmp1 = strstr(session->mail_to_ptr+tmp, "\"");
                if(tmp1 && tmp1 < p0)
                {
                    tmp2 = strstr(tmp1, "\" ");
                    if(tmp2 && tmp2 < p0)
                    {
                        len = tmp2 - tmp1 - 1;
                        len = len < to_len-i ? len : to_len-i;
                        memcpy(session->receiver_name+i, tmp1+1, len);
                    }
                    else
                        memcpy(session->receiver_name+i, session->mail_to_ptr+tmp, len);
                }
                else
                    memcpy(session->receiver_name+i, session->mail_to_ptr+tmp, len);
                i += (len + 1);
                tmp = p1 - session->mail_to_ptr + 3;
                if(tmp >= session->mail_to_len)
                    break;
                p0 = strstr(session->mail_to_ptr+tmp,"<");
                p1 = strstr(session->mail_to_ptr+tmp,"\r\n");
                if(p0 && p1 && p2 > p0 && p2 > p1 && p1 > p0)
                {
                    session->receiver_name[i-1] = ',';
                    continue;
                }
                break;
            }
#endif
        }
    }
}
#endif

//获取字段值(单行或多行)的总长度,不含最后一组"\r\n"，
static int imap_find_packet_line_end(const uint8_t *data, uint16_t len)
{
    if(len>IMAP_SEARCH_MAX_LEN ){
        len=IMAP_SEARCH_MAX_LEN;
    }
    if(data)
    {
        const uint8_t* p0 = (const uint8_t*)mail_memstr((const char*)data, "\r\n",len);
        if(p0)
        {
            uint16_t left_len = len-(p0-data);
            if(left_len>len || left_len<=0){
                return -1;
            }
            const uint8_t* p1 = (const uint8_t*)mail_memstr((const char*)p0, ":",left_len);
            if(p1)
            {
                int i = p1 - data;
                if(i >= len) return len;
                for(; i > 0 && get_uint16_t(data, i) != ntohs(0x0d0a); i--);
                return i;
            }
            return p0 - data;
        }
    }
    return -1;
}


/*
* 解析Cc的地址与名字, 逗号分开
* ex: Cc: "'Antonio Pelaez'" <<EMAIL>>,
    "'Than Thi Chung'" <<EMAIL>>,
    "'Vu Thi Bich Hop'" <<EMAIL>>,
    "'Le Van Son'" <<EMAIL>>, <<EMAIL>>,
    <<EMAIL>>, "'Pham Thi Lan'" <<EMAIL>>,
    <<EMAIL>>,
    "'Pham Thuy Anh'" <<EMAIL>>,
    "'Nguyen Thi Thuy'" <<EMAIL>>
*/
static int extract_cc_infos(const uint8_t *ptr, uint16_t ptr_len, struct imap_session *session) {

    if (!ptr || !session || !ptr_len)
        return -1;

    uint16_t i = 0, start = 0;
    uint16_t j, k;
    if (!strncasecmp((const char *)ptr, "cc: ", 4))
        i += 4;

    start = i;
    do {
        if (ptr[i] == '>') {
            while (start < i && ptr[start] != '\'' && ptr[start] != '<') start++;

            for (j = start; j <= i; j++) {
                if (ptr[j] == '<') {
                    // Cc address
                    strncat(session->cc_addrs, (char *)(ptr + j + 1), i - (j + 1));
                    strcat(session->cc_addrs, ",");
                    session->cc_addrs_num++;

                    // j = i时 无 alias
                    if (j > start && j < i) {
                        // Cc alias
                        strncat(session->cc_alias, (char *)(ptr + start), j - start);

                        while (1) {

                            char ch = session->cc_alias[strlen(session->cc_alias) - 1];
                            if (ch != '\'')
                                session->cc_alias[strlen(session->cc_alias) - 1] = '\0';
                            else
                                break;
                        }

                        strcat(session->cc_alias, ",");
                        session->cc_alias_num++;
                    }

                    start = i + 1;
                    break;
                }
            }
        }

    } while (i++ < ptr_len);


    return 0;
}

static void get_address_and_alias(const uint8_t *ptr, uint16_t ptr_len, struct imap_session *session) {
    if (!ptr || !session)
        return;

    char alias[300];
    char tmp[2048 + 1];
    const char *token = "<";
    char *p_next = NULL;
    uint16_t i, k;

    memset(alias, 0, sizeof(alias));
    strncpy(alias, (const char *)ptr, ptr_len);
    p_next = strtok(alias, token);

    for (i = 0; i < strlen(alias); i++) {
        if (alias[i] == '<') {
            p_next = &alias[i];
            break;
        }
    }

    if (p_next) {
        if (strlen(p_next) < ptr_len) {
            // 别名
            strncat(session->rcv_aliases, (const char *)ptr, strlen(p_next) - 1);
            strcat(session->rcv_aliases, ",");
            session->rcv_aliases_num++;
        }

        // 地址
        strncat(session->rcv_mails, (const char *)(ptr + (int)strlen(p_next) + 1), ptr_len - ((int)strlen(p_next) + 1));
        strcat(session->rcv_mails, ",");
        session->rcv_mails_num++;

        for (k = (int)strlen(p_next) + 1; k < ptr_len; k++) {
            if (p_next[k] == '@') {
                strncat(session->rcv_doms, (const char *)&p_next[k + 1], ptr_len - k - 1);
                if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                    session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                strcat(session->rcv_doms, ",");

                session->rcv_doms_num++;
                break;
            }
        }
    }
    else {
        strncat(session->rcv_mails, (const char *)ptr, ptr_len);
        strcat(session->rcv_mails, ",");
        session->rcv_mails_num++;

        for (k = 0; k < ptr_len; k++) {
            if (ptr[k] == '@') {
                strncat(session->rcv_doms, (const char *)&ptr[k + 1], ptr_len - k - 1);
                if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                    session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                strcat(session->rcv_doms, ",");

                session->rcv_doms_num++;
                break;
            }
        }
    }
}

/*
* 提取To信息
* ex:
To: =?UTF-8?B?6YOd5LiW5Lyf?= <<EMAIL>>
*/
static int extract_to_infos(const uint8_t *ptr, uint16_t ptr_len, struct imap_session *session) {
    if (!ptr || !session || !ptr_len)
        return -1;

    int k = 0;
    int i = 0;
    char alias[300];
    char tmp[2048 + 1];
    char *p_next = NULL;
    char *p_start = NULL;

    strncpy(tmp, (const char *)ptr, ptr_len);
    p_start = tmp;
    p_next = tmp;
    while (i < ptr_len) {

        if (p_next[i] == '>') {
            get_address_and_alias((const uint8_t *)(p_start + 1), p_next + i - (p_start + 1), session);

            p_start = &tmp[i];
            if (p_start[0] != '\"') {

                // update next start
                k = 0;
                while (i + k < ptr_len && p_start[k++] != '\"')
                    ;

                p_start += (k - 1);

                i += k;
                continue;
            }
        }

        i++;
    }

    // 只有一个收件人的邮箱
    if (i == ptr_len) {
        get_address_and_alias(ptr, ptr_len, session);
    }

    return 0;
}

/*
*  根据IP查询ASN与国家
*@ip4_str ex:**************
*/
static int get_region_from_ip(ip2region_t st, const char *ip4_str, IPINFO *info, char *asn, int asn_len) {
    memset(asn, 0, asn_len);
    if (g_config.mmdb_switch == 0)
        return -1;

    if (!ip4_str || !info)
        return -1;

    int i = 0;
    uint8_t ip[4];
    char tmp[64];
    const char *token = ".";
    char *p_next = NULL;

    memset(ip, 0, sizeof(ip));
    snprintf(tmp, sizeof(tmp), "%s", ip4_str);
    p_next = strtok(tmp, token);
    while (p_next) {

        if (i >= 4)
            break;

        ip[i++] = (uint8_t)atoi(p_next);

        if (p_next[0] == '\0')
            break;

        p_next = strtok(NULL, token);
    }

    datablock_entry entry;
    char* s[6]; //国家 地区 省 市县 运营商
    if (ip2region_memory_search(st, get_uint32_ntohl(ip, 0), &entry)) {
        //printf("IP positin: %s\n", entry.region);
        if (entry.region[0] == '0') {
            strcpy(info->country, "中国");

            goto asn_extract;
        }
        int i, j = 0;
        s[j++] = entry.region;
        for (i = 0; entry.region[i]; ++i) {
            if (entry.region[i] == '|') {
                entry.region[i] = '\0';
                s[j++] = entry.region + i + 1;
            }
        }
        if (j < 4) return 0;
        strcpy(info->country, s[0]);
        strcpy(info->area,  s[1]);
        strcpy(info->state, s[2]);
        strcpy(info->city,  s[3]);
    }

asn_extract:
    get_asn_from_ip(ip4_str, asn, asn_len);

    return 0;
}

/*
* 解析Received字段中的 from domain&ip, 并统计数量
* ex: from localhost (unknown [*************])
    by esmtp10.qq.com (ESMTP) with SMTP id 0
    for <<EMAIL>>; Tue, 15 Oct 2019 11:21:47 +0800 (CST)
*/
static int extract_received_infos(const uint8_t *ptr, uint16_t ptr_len, struct imap_session *session) {

    if (!ptr || !session || !ptr_len)
        return -1;

    struct keys_info {
        uint8_t idx;
        const char *name;
    } targets[] = {
        {1,  "from"},
        {2,  "by"},
        {0,  NULL},
    };

    uint8_t offset = 0;
    uint8_t type = 0;
    uint16_t i;
    uint16_t dom_start, ip_start;
    for (i = 0; i < ptr_len; i++) {
        for (type = 0; targets[type].idx && targets[type].name; type++) {
            if (!strncasecmp((const char *)(ptr + i), targets[type].name, strlen(targets[type].name))) {
                goto recv_start;
            }
        }
    }

recv_start:
    if (i == ptr_len)
        return -1;

    i += strlen(targets[type].name) + 1;
    dom_start = i;

    // from domain
    for (; i < ptr_len; i++) {
        if (ptr[i] == ' ') {
            uint16_t len = i - dom_start;
            const uint8_t *p0 = memstr(ptr + dom_start, ")", i - dom_start);
            if (p0) {
                len = p0 - (ptr + dom_start);
            }

            if (targets[type].idx == 1) {
                strncat(session->received_from_doms, (char *)(ptr + dom_start), len);
                strcat(session->received_from_doms, ",");
                session->received_from_doms_num++;
                ip_start = i;  // update here
                break;
            }
            else if (targets[type].idx == 2) {
                strncat(session->received_by_doms, (char *)(ptr + dom_start), len);
                strcat(session->received_by_doms, ",");
                session->received_by_doms_num++;
                ip_start = i;  // update here
                break;
            }
        }
    }

    // from ip
    for (; i < ptr_len; i++) {
        if (ptr[i] == '[') {
            ip_start = i;  // found here
        }
        else if (ptr[i] == ']') {
            if (ptr[ip_start] == '[') {
                // from ip
                if (targets[type].idx == 1) {
                    strncat(session->received_from_ips, (char *)(ptr + ip_start + 1), i - ip_start - 1);
                    strcat(session->received_from_ips, ",");
                    session->received_from_ips_num++;

                    IPINFO info;
                    char ip[64];
                    memset(ip, 0, sizeof(ip));
                    strncpy(ip, (char *)(ptr + ip_start + 1), i - ip_start - 1);

                    char asn[100];
                    get_region_from_ip(&g_config.entry, ip, &info, asn, sizeof(asn));
                    strcat(session->from_countries, info.country);
                    strcat(session->from_countries, ",");
                    session->from_countries_num++;

                    if (strlen(asn)) {
                        strcat(session->from_asns, asn);
                        strcat(session->from_asns, ",");
                        session->from_asns_num++;
                    }
                }
                // by ip
                else if (targets[type].idx == 2) {
                    strncat(session->received_by_ips, (char *)(ptr + ip_start + 1), i - ip_start - 1);
                    strcat(session->received_by_ips, ",");
                    session->received_by_ips_num++;
                }
            }
            break;
        }
    }


    return 0;
}


/*
*解析邮件内容，只解析头部，邮件具体内容保存在文件中
*/
static void dissect_imap_imf(const uint8_t *payload, uint32_t payload_len, struct imap_session *session)
{
    int line_len = 0;
    int recvFlags = 0;
    int empty_line_index = 0;
    uint32_t offset = 0;
    const uint8_t *line = NULL;
    const uint8_t* pto = NULL;

    // 正文前的一些命令解析
    int cmd_end;
    int cmd_line_len;
    const uint8_t *cmd_line;
    uint32_t cmd_off = 0;

    cmd_end = _find_empty_line(payload, payload_len);
    if (cmd_end < 0)
        cmd_end = payload_len;

    cmd_line = payload;
    while (offset < (uint32_t)cmd_end)
    {
        cmd_line_len = imap_find_packet_line_end(cmd_line, cmd_end - offset);
        if (cmd_line_len <= 0)
            break;
        else
        {
            // login server
            if (session->login_svr_ptr) {
                pto = memstr(cmd_line, "Pop3 Server ", cmd_line_len);
                if (!pto) {
                    pto = memstr(cmd_line, "Imap Server ", cmd_line_len);
                }
                if (pto && pto + 12) {
                    pto = memstr(pto + 12, "(", cmd_line_len - 12);
                }
                if (pto)
                {
                    session->login_svr_ptr = pto;
                    session->login_svr_len = payload + cmd_line_len - pto;
                }
            }

            int i_set = 0;
            const char *p_set;
            while ((p_set = email_heads[i_set++])) {
                if (cmd_line_len >= (int)strlen(p_set) && strncasecmp((const char *)cmd_line, p_set, strlen(p_set)) == 0) {
                    strcat(session->head_sets, p_set);
                    strcat(session->head_sets, ",");
                    session->head_sets_num++;
                }
            }

            uint16_t space_index = 0;
            while (space_index < cmd_line_len) {
                if (cmd_line[space_index++] == ' ') {
                    int i_cmd = 0;
                    int i_line;
                    const char *p_cmd;
                    while ((p_cmd = email_cmds[i_cmd++])) {
                        if (cmd_line_len >= (int)strlen(p_cmd)) {
                            if (strncasecmp((const char *)(cmd_line + space_index), p_cmd, strlen(p_cmd)) == 0) {
                                strcat(session->commands, p_cmd);
                                strcat(session->commands, ",");
                                session->commands_num++;

                                if (session->starttls_f == 0 && !strncasecmp("starttls", p_cmd, strlen(p_cmd)) == 0)
                                    session->starttls_f = 1;

                                // HELO EHLO 后面紧跟服务器
                                if (!session->host_ptr) {
                                    if (!strcmp((const char *)(cmd_line + space_index), "HELO ")
                                        || !strcmp((const char *)(cmd_line + space_index), "EHLO ")) {
                                        session->host_ptr = cmd_line + space_index + 5;
                                        session->host_len = payload + cmd_line_len - (cmd_line + space_index + 5);
                                    }
                                }
                            }
                        }
                    }

                    break;
                }
            }

            const char *pstr = "Content-Type: ";
            int         thestrlen = strlen(pstr);
            if (strncmp((const char *)cmd_line, pstr, thestrlen) == 0) {
                session->mail_content_type_ptr = cmd_line + thestrlen;
                session->mail_content_type_len = get_imap_field(payload, payload_len, &session->mail_content_type_ptr);

                //cont_types
                if(strlen(session->cont_types) + session->mail_content_type_len +2 < sizeof(session->cont_types)) //+2 for "," + "\0"
                {
                    strncat(session->cont_types, (const char *)(session->mail_content_type_ptr), session->mail_content_type_len);
                    strcat(session->cont_types, ",");
                    session->cont_types_num++;
                }

                //head_sets
                if(strlen(session->head_sets) + session->mail_content_type_len +2 < sizeof(session->cont_types)) //+2 for "," + "\0"
                {
                    strncat(session->head_sets, (const char *)(session->mail_content_type_ptr), session->mail_content_type_len);
                    strcat(session->head_sets, ",");
                    session->head_sets_num++;
                }

                uint16_t main_len = session->mail_content_type_ptr - payload;
                uint16_t mm_len = 0;
                if (main_len < payload_len)
                    mm_len = payload_len - main_len;
                if (session->mail_content_type_ptr != NULL &&
                        ((strncasecmp((const char *)session->mail_content_type_ptr, "text/plain", 10) == 0) ||
                         (strncasecmp((const char *)session->mail_content_type_ptr, "text/html", 9) == 0))
                   ) {
                    if (session->main_content_len <= 0 && mm_len > 0 && mm_len < payload_len)
                        get_imap_main_content((const char *)session->mail_content_type_ptr, mm_len, session);
                }
            }
        }

        cmd_off += (cmd_line_len + 2);
        cmd_line = payload + cmd_off;
        offset = cmd_off;
    }

    empty_line_index = _find_empty_line(payload, payload_len);
    if (empty_line_index < 0)
        empty_line_index = payload_len;

    /*session->mail_content_type_ptr = (uint8_t*)strcasestr((const char *)payload, "Content-Type: ");
    session->mail_content_type_ptr = (NULL == session->mail_content_type_ptr ? NULL : session->mail_content_type_ptr + strlen("Content-Type: "));
    if(session->mail_content_type_ptr!=NULL){
        session->mail_content_type_len = get_imap_field(payload, payload_len, &session->mail_content_type_ptr);

        strncat(session->cont_types, (const char *)(pto + 14), session->mail_content_type_len);
        strcat(session->cont_types, ",");
        session->cont_types_num++;

        strncat(session->head_sets, (const char *)(pto + 14), session->mail_content_type_len);
        strcat(session->head_sets, ",");
        session->head_sets_num++;
        uint16_t main_len=session->mail_content_type_ptr-payload;
        uint16_t mm_len=0;
        if(main_len<payload_len)
            mm_len=payload_len-main_len;
        if(session->mail_content_type_ptr!=NULL &&
            ((strncasecmp((const char *)session->mail_content_type_ptr,"text/plain",10) == 0) ||
             (strncasecmp((const char *)session->mail_content_type_ptr,"text/html",9) == 0))
        ){
            if(session->main_content_len<=0 && mm_len>0 && mm_len<payload_len)
                get_imap_main_content((const char *)session->mail_content_type_ptr, mm_len, session);
        }
    }
    else
        session->mail_content_type_len = 0;*/

    session->mail_mime_version_ptr = (uint8_t*)strcasestr((const char *)payload, "Mime-Version: ");
    session->mail_mime_version_ptr = (NULL == session->mail_mime_version_ptr ? NULL : session->mail_mime_version_ptr + strlen("Mime-Version: "));
    if(session->mail_mime_version_ptr)
        session->mail_mime_version_len = imap_find_packet_line_end(session->mail_mime_version_ptr, empty_line_index - offset);
    else
        session->mail_mime_version_len = 0;

    pto = (uint8_t*)strcasestr((const char*)payload, "user-agent: ");
    if(pto && pto + 12)
    {
        session->mail_user_agent_ptr = pto + 12;
        const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_user_agent_ptr,": ");
        if(p1)
        {
            unsigned int len = p1 - session->mail_user_agent_ptr - 1;
            len = len < payload_len ? len : 0;
            for(;len > 0 && session->mail_user_agent_ptr[len] != '\r'; len--);
            session->mail_user_agent_len = len;
        }
    }

    line = payload;
    while (offset < (uint32_t)empty_line_index)
    {
        line_len = imap_find_packet_line_end(line, empty_line_index - offset);
        if (line_len <= 0) break;
        else
        {
            if (line_len > 6 && strncasecmp((const char *)line, "Date: ", 6) == 0)
            {

                session->mail_date_ptr = line + 6;
                session->mail_date_len = line_len - 6;
                uint16_t left_len=payload_len-(session->mail_date_ptr-line);
                if(left_len<line_len){
                    session->mail_date_len=left_len-6;
                }
                //if(session->mail_date_ptr)
                    //session->mail_date_len = imap_find_packet_line_end(session->mail_date_ptr, empty_line_index - offset);
            }
            else if (line_len > 6 && strncasecmp((const char *)line, "From: ", 6) == 0)
            {
                session->mail_from_ptr = line + 6;
                session->mail_from_len = line_len - 6;
                if(session->mail_from_ptr)
                {
                    const uint8_t* p0 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"\r\n");
                    if(*(p0 - 1) == '>')
                        p0 = p0 - 1;
                    const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"<");
                    if(p1 && p0 && p0 > p1)
                    {
                        session->sender_email_ptr = p1 + 1;
                        session->sender_email_len = p0 - p1 - 1;
/*                        const uint8_t *tmp1 = (uint8_t*)strstr((const char*)session->mail_from_ptr," ");
                        if(tmp1 && *(tmp1+1)=='<' && tmp1 < p1)
                        {
                            session->mail_sender_name_ptr = session->mail_from_ptr;
                            session->mail_sender_name_len = tmp1 - session->mail_from_ptr;
                        }
*/
                        if (*(p1 - 1) == ' ' && (p1 -1) > session->mail_from_ptr) {
                            p1 = p1 - 1;
                        }
                        session->mail_sender_name_ptr = session->mail_from_ptr;
                        session->mail_sender_name_len = p1 - session->mail_from_ptr;

                        p1 = (uint8_t*)strstr((const char*)p1,"@");
                    }
                    else
                        p1 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"@");
                    if(p0 && p1 && p0 > p1)
                    {
                        session->mail_sender_domain_ptr = p1 + 1;
                        session->mail_sender_domain_len = p0 - p1 - 1;
                    }
                }
            }
            else if (line_len > 4 && strncasecmp((const char *)line, "To: ", 4) == 0)
            {
                session->mail_to_ptr = line + 4;
                session->mail_to_len = line_len - 4;
                if(session->mail_to_ptr)
                {
                    // 获取收件人别名与地址
                    extract_to_infos(session->mail_to_ptr, session->mail_to_len, session);

                    const uint8_t* p0 = (uint8_t*)strstr((const char*)session->mail_to_ptr,"\r\n");
                    if(*(p0 - 1) == '<')
                        p0 = p0 - 1;
                    const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_to_ptr,"<");
                    if(p1 && p0 && p0 > p1)
                    {
                        session->receiver_email_ptr = p1 + 1;
                        session->receiver_email_len = p0 - p1 - 1;
                        const uint8_t *tmp1 = (uint8_t*)strstr((const char*)session->mail_to_ptr," ");
                        if(tmp1 && *(tmp1+1)=='<' && tmp1 < p1)
                        {
                            session->mail_receiver_name_ptr = session->mail_to_ptr;
                            session->mail_receiver_name_len = tmp1 - session->mail_to_ptr;
                        }
                        p1 = (uint8_t*)strstr((const char*)p1,"@");
                    }
                    else
                    {
                        session->receiver_email_ptr = session->mail_to_ptr;
                        session->receiver_email_len = p0 - session->mail_to_ptr;
                        p1 = (uint8_t*)strstr((const char*)session->mail_to_ptr,"@");

                    }
                    if(p0 && p1 && p0 > p1)
                    {
                        session->mail_receiver_domain_ptr = p1 + 1;
                        session->mail_receiver_domain_len = p0 - p1 - 1;
                    }
                }
            }
            else if (line_len > 4 && strncasecmp((const char *)line, "Cc: ", 4) == 0)
            {
                session->mail_cc_ptr = line + 4;
                session->mail_cc_len = line_len - 4;

                /*
                session->mail_cc_ptr = line + 4;
                const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_cc_ptr,": ");
                if(p1)
                {
                    int len = p1 - session->mail_cc_ptr - 1;
                    len = (unsigned)len < payload_len ? len : 0;
                    for(;len > 0 && session->mail_cc_ptr[len] != '\r'; len--);
                    session->mail_cc_len = len;
                }else{
                    session->mail_cc_len = line_len - 4;
                }*/

                extract_cc_infos(line + 4, line_len - 4, session);
            }
            else if (line_len > 5 && strncasecmp((const char *)line, "Bcc: ", 5) == 0)
            {
                session->mail_bcc_ptr = line + 5;
                session->mail_bcc_len = line_len - 5;
            }
            else if (line_len > 9 && strncasecmp((const char *)line, "Subject: ", 9) == 0)
            {
                session->mail_subject_ptr = line + 9;
                session->mail_subject_len = line_len - 9;

                strncat(session->subjects, (const char *)(line + 9), line_len - 9);
                strcat(session->subjects, ",");
                session->subjects_num++;
            }
            else if (line_len > 13 && strncasecmp((const char *)line, "Resent-Date: ", 13) == 0)
            {
                session->mail_resent_date_ptr = line + 13;
                session->mail_resent_date_len = line_len - 13;
            }
            else if(line_len > 10 && strncasecmp((const char *)line, "reply-to: ", 10) == 0)
            {
                session->mail_reply_to_ptr = line + 10;
                session->mail_reply_to_len = line_len - 10;
            }
            else if (line_len > 13 && strncasecmp((const char *)line, "In-Reply-To: ", 13) == 0)
            {
                session->mail_in_reply_to_ptr = line + 13;
                session->mail_in_reply_to_len = line_len - 13;
            }
            else if(line_len > 12 && strncasecmp((const char *)line, "references: ", 12) == 0)
            {
                session->mail_references_ptr = line + 12;
                session->mail_references_len = line_len - 12;
            }
            else if(line_len > 12 && strncasecmp((const char *)line, "x-priority: ", 12) == 0)
            {
                session->mail_x_priority_ptr = line + 12;
                session->mail_x_priority_len = line_len - 12;
            }
            else if(line_len > 10 && strncasecmp((const char *)line, "x-mailer: ", 10) == 0)
            {
                session->mail_x_mailer_ptr = line + 10;
                session->mail_x_mailer_len = line_len - 10;

                strncat(session->x_mailers, (const char *)(line + 10), line_len - 10);
                strcat(session->x_mailers, ",");
                session->x_mailers_num++;
            }
            else if(line_len > 18 && strncasecmp((const char *)line, "X-Originating-IP: ", 18) == 0)
            {
                session->mail_x_originating_ip_ptr = line + 18;
                session->mail_x_originating_ip_len = line_len - 18;
            }
            else if(line_len > 10 && strncasecmp((const char *)line, "received: ", 10) == 0)
            {
#if 0
                //过滤第二个received
                if(0 == recvFlags)
                {
                    session->mail_received1_ptr = line + 10;
                    session->mail_received1_len = get_imap_field(payload, payload_len, &session->mail_received1_ptr);
                    if(session->mail_received1_ptr)
                    {
                        const uint8_t* p0 = (const uint8_t *)strstr((const char*)session->mail_received1_ptr,"\r\n");
                        const uint8_t* p1 = (const uint8_t *)strstr((const char*)session->mail_received1_ptr,"[");
                        p1 = (NULL == p1 ? NULL : p1 + 1);
                        const uint8_t* p2 = (const uint8_t *)strstr((const char*)session->mail_received1_ptr, "]");
                        if(p0 && p1 && p2 && p1 < p0 && p2 < p0 && p1 < p2)
                        {
                            session->mail_receiver_svr_ip_ptr = p1;
                            session->mail_receiver_svr_ip_len = p2 - p1;
                            p1 = (uint8_t*)strcasestr((const char*)session->mail_received1_ptr,"from ");
                            if(p1 && p1+5 && p1 < p0)
                            {
                                session->mail_receiver_svr_ptr = p1+5;
                                p2 = (const uint8_t *)strstr((const char*)p1+5," (");
                                if(p2)
                                    session->mail_receiver_svr_len = p2 - p1 - 4;
                                else
                                    session->mail_receiver_svr_len = p0 - p1 - 4;
                            }
                            p1 = (uint8_t*)strcasestr((const char*)session->mail_received1_ptr,"by ");
                            if(p1 && p1 + 3)
                            {
                                p0 = (uint8_t*)strstr((const char*)p1, "\r\n");
                                if(p0)
                                {
                                    session->mail_send_soft_ptr = p1+3;
                                    p2 = (uint8_t*)strstr((const char*)p1+3,") ");
                                    if(p2 && p2 < p0)
                                        session->mail_send_soft_len = p2 - p1 - 2;
                                    else
                                        session->mail_send_soft_len = p0 - p1 - 2;
                                }
                            }
                        }
                    }
                    recvFlags++;
                    //此处不能用下面的偏移量,只能在这里完成偏移
                    offset += session->mail_received1_len + 2;
                    line = &payload[offset];
                    continue;
                }
                else
                {
                    session->mail_received2_ptr = line + 10;
                    session->mail_received2_len = get_imap_field(payload, payload_len, &session->mail_received2_ptr);
                    if(session->mail_received2_ptr)
                    {
                        const uint8_t* p0 = (const uint8_t *)strstr((const char*)session->mail_received2_ptr,"\r\n");
                                    const uint8_t* p1 = (const uint8_t *)strstr((const char*)session->mail_received2_ptr,"[");
                        p1 = (NULL == p1 ? NULL : p1 + 1);
                                    const uint8_t* p2 = (const uint8_t *)strstr((const char*)session->mail_received2_ptr, "]");
                        if(p0 && p1 && p2 && p1 < p0 && p2 < p0 && p1 < p2)
                        {
                            session->mail_sender_svr_ip_ptr = p1;
                            session->mail_sender_svr_ip_len = p2 - p1;
                            p1 = (uint8_t*)strcasestr((const char*)session->mail_received2_ptr,"from ");
                            if(p1 && p1+5 && p1 < p0)
                            {
                                session->mail_sender_svr_ptr = p1+5;
                                p2 = (uint8_t*)strstr((const char*)p1+5," (");
                                if(p2)
                                    session->mail_sender_svr_len = p2 - p1 - 4;
                                else
                                    session->mail_sender_svr_len = p0 - p1 - 4;
                            }
                            p1 = (uint8_t*)strcasestr((const char*)session->mail_received2_ptr,"by ");
                            if(p1 && p1+3)
                            {
                                p0 = (uint8_t*)strstr((const char*)p1, "\r\n");
                                if(p0)
                                {
                                    session->mail_send_server_ptr = p1+3;
                                    p2 = (uint8_t*)strstr((const char*)p1+3," (");
                                    if(p2 && p2 < p0)
                                        session->mail_send_server_len = p2 - p1 - 2;
                                    else
                                        session->mail_send_server_len = p0 - p1 - 2;
                                }
                            }
                        }
                        //此处不能用下面的偏移量,只能在这里完成偏移
                        offset += session->mail_received2_len + 2;
                        line = &payload[offset];
                        continue;
                    }
                }
#endif

                session->mail_received_ptr = line + 10;
                session->mail_received_len = get_imap_field(payload, payload_len, &session->mail_received_ptr);
                //此处不能用下面的偏移量,只能在这里完成偏移
                offset += session->mail_received_len + 2;
                line = &payload[offset];

                strncat(session->receiveds, (const char *)session->mail_received_ptr, session->mail_received_len);
                strcat(session->receiveds, ",");
                session->receiveds_num++;

                extract_received_infos(session->mail_received_ptr, session->mail_received_len, session);

                continue;
            }
            else if(line_len > 12 && strncasecmp((const char *)line, "message-id: ", 12) == 0)
            {
                session->mail_message_id_ptr = line + 12;
                session->mail_message_id_len = line_len - 12;

                strncat(session->msg_ids, (const char *)(line + 12), line_len - 12);
                strcat(session->msg_ids, ",");
                session->msg_ids_num++;
            }
            else if(line_len > 12 && strncasecmp((const char *)line, "content-id: ", 12) == 0)
            {
                session->mail_content_id_ptr = line + 12;
                session->mail_content_id_len = line_len - 12;
            }
            else if(line_len > 27 && strncasecmp((const char *)line, "content-transfer-encoding: ", 27) == 0)
            {
                session->mail_content_transfer_encoding_ptr = line + 27;
                session->mail_content_transfer_encoding_len = line_len - 27;
            }
            else if(line_len > 15 && strncasecmp((const char *)line, "delivery-date: ", 15) == 0)
            {
                session->mail_delivery_date_ptr = line + 15;
                session->mail_delivery_date_len = line_len - 15;
            }
            else if(line_len > 22 && strncasecmp((const char *)line, "latest-delivery-time: ", 22) == 0)
            {
                session->mail_latest_delivery_time_ptr = line + 22;
                session->mail_latest_delivery_time_len = line_len - 22;
            }
            else if(line_len > 21 && strncasecmp((const char *)line, "content-description: ", 21) == 0)
            {
                session->mail_content_description_ptr = line + 21;
                session->mail_content_description_len = line_len - 21;
            }
            else if (line_len > 11 && strncasecmp((const char *)line, "x-mimeole: ", 11) == 0)
            {
                session->mail_x_mimeole_ptr = line + 11;
                session->mail_x_mimeole_len = line_len - 11;
            }
            else if (line_len > 14 && strncasecmp((const char *)line, "Mime-Version: ", 14) == 0) {
                session->mail_mime_version_ptr = line + 14;
                session->mail_mime_version_len = line_len - 14;

                strncat(session->mime_vers, (const char *)(line + 14), line_len - 14);
                strcat(session->mime_vers, ",");
                session->mime_vers_num++;
            }
            else if (line_len > 11 && strncasecmp((const char *)line, "x-originating-ip: ", 18) == 0)
            {
                session->x_ori_ip_ptr = line + 18;
                session->x_ori_ip_len = line_len - 18;
            }
        }
        offset += line_len + 2;
        line = &payload[offset];
    }
    //解析附件信息
    //multipart/mixed: 邮件中含有附件标识
    //multipart/alternative: 传送的是超文本内容
    //multipart/related: 传送的是邮件内嵌资源
    if(session->mail_content_type_ptr)
    {
        if(0 == strncasecmp((const char*)session->mail_content_type_ptr,"multipart/mixed", strlen("multipart/mixed")))
            dissect_imap_attachments_info(payload, payload_len, session, offset);
    }
}

/*多个邮件头部分割*/
static uint32_t get_fetch_info(const uint8_t *payload, uint32_t max_len)
{
    uint8_t line_num = 0;
    uint32_t offset = 0;
    const uint8_t *line;
    int line_len;
    int copy_len;
    char line_str[1500];

    line = payload;
    while (offset < max_len) {
        line_len = find_packet_line_end(line, max_len - offset);
        if (line_len < 0)
            break;

        if (line_num == 0) {
            line_num++;
            offset += line_len + 2;
            line = &payload[offset];
            continue;
        }
        line_num++;

        copy_len = line_len >= 1500 ? 1499 : line_len;
        memcpy(line_str, line, copy_len);
        line_str[copy_len] = 0;

        if (strcasestr(line_str, "FETCH (UID ")) {
            return 1;
        }

        offset += line_len + 2;
        line = &payload[offset];
    }

    return 0;
}


/*多个邮件分割*/
static uint32_t imap_seperate_imf(const uint8_t *payload, uint32_t max_len)
{
    uint8_t line_num = 0;
    uint32_t offset = 0;
    const uint8_t *line=NULL;
    int line_len;
    int copy_len;
    char line_str[1500];
    uint32_t tmp_len=0;

    line = payload;
    while (offset < max_len) {
        //line_len = find_packet_line_end(line, max_len - offset);
        line_len=_find_imap_fetch_end_line(line, max_len - offset);
        if (line_len < 0){
            break;
        }

        offset+=line_len+5;
        tmp_len=line_len+5;
        if(offset+1>=max_len){
            break;
        }

        if(line[tmp_len]=='*'){
            return (offset);
        }

        if(offset+64>=max_len){
            break;
        }
        const char *p=NULL;
        p=mail_memstr((const char *)&line[tmp_len], "Completed",64);
        if (p!=NULL){
            return (offset);
        }
        p=mail_memstr((const char *)&line[tmp_len], "completed",64);
        if (p!=NULL){
            return (offset);
        }

        line = &payload[offset];
        tmp_len=0;
    }

    return (max_len);
}


/*多个邮件头部分割*/
static uint32_t seperate_imf(const uint8_t *payload, uint32_t max_len)
{
    uint8_t line_num = 0;
    uint32_t offset = 0;
    const uint8_t *line;
    int line_len;
    int copy_len;
    char line_str[1500];

    line = payload;
    while (offset < max_len) {
        line_len = find_packet_line_end(line, max_len - offset);
        if (line_len < 0)
            break;

        if (line_num == 0) {
            line_num++;
            offset += line_len + 2;
            line = &payload[offset];
            continue;
        }
        line_num++;

        copy_len = line_len >= 1500 ? 1499 : line_len;
        memcpy(line_str, line, copy_len);
        line_str[copy_len] = 0;

        //const char *p=NULL ,*q=NULL;
        //p=mail_memstr(line_str, "Fetch completed",line_len);
        //p=mail_memstr(line_str, "completed.",line_len)
        if (strcasestr(line_str, "Fetch Completed")  ||
            strcasestr(line_str, "Completed.") ||
            strcasestr(line_str, "FETCH (UID")
        ) {
            return (offset);
        }

        offset += line_len + 2;
        line = &payload[offset];
    }

    return (max_len);
}

#if 0
static void dissect_imf_start(struct flow_info *flow, uint8_t direction)
{
    struct imap_session *session;
    uint32_t reassemble_result_len=0;
    uint8_t *reassemble_result=NULL;
    struct list_head *reassemble=NULL;
    struct imap_session imap_info;
    memset(&imap_info, 0, sizeof(imap_info));
    uint32_t expect_len=0;

    session = (struct imap_session *)flow->app_session;
    if (session == NULL)
        goto not_write;

    if (direction == FLOW_DIR_SRC2DST) {
        reassemble_result_len = flow->reassemble_pkt_src2dst_num * TCP_PAYLOAD_MAX_LEN;
        reassemble = &flow->reassemble_src2dst_head;
    } else {
        reassemble_result_len = flow->reassemble_pkt_dst2src_num * TCP_PAYLOAD_MAX_LEN;
        reassemble = &flow->reassemble_dst2src_head;
    }
    if (reassemble_result_len <= 0)
        goto not_write;

    reassemble_result = (uint8_t *)dpi_malloc(reassemble_result_len);
    if (reassemble_result) {
        //tcp_reassemble_do(reassemble, reassemble_result, &reassemble_result_len);
        tcp_reassemble_do_new(reassemble, reassemble_result, &reassemble_result_len,&expect_len);
        if(reassemble_result_len<=0 || get_fetch_info(reassemble_result,reassemble_result_len)==1){
        //if(reassemble_result_len<500 ){
            goto not_write;
        }
        flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].total_number++;
        dissect_imap_imf(reassemble_result, reassemble_result_len, &imap_info);
        if(imap_info.mail_from_len<=0 || imap_info.mail_to_len<=0){
            goto not_write;
        }

        if (reassemble_result_len >= 5 && memcmp(reassemble_result + reassemble_result_len - 5, "\r\n.\r\n", 5) == 0)
            reassemble_result_len -= 5;

        get_imap_filename(flow->thread_id,imap_info.mail_filename, sizeof(imap_info.mail_filename));
        FILE *fp = fopen(imap_info.mail_filename, "w");
        if (fp) {
            fwrite(reassemble_result, reassemble_result_len, 1, fp);
            fclose(fp);

            imap_info.eml_filesize=reassemble_result_len;
            flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].actual_number++;
            float percent_data=0;
            if(expect_len!=0){
                percent_data=(reassemble_result_len/(expect_len*1.0))*100;
            }
            if(percent_data>=99.999){
                flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].percent_100_number++;
            }else if(percent_data>=89.999){
                flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].percent_90_number++;
            }else if(percent_data>=79.999){
                flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].percent_80_number++;
            }else{
                flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].percent_other_number++;
            }

            write_imap_log(flow, direction, &imap_info);
        }


        #if 1
        uint8_t *imf_start = reassemble_result;
        uint32_t imf_max_len = reassemble_result_len;
        uint32_t imf_len;
        while (imf_max_len) {
            imf_len = seperate_imf(imf_start, imf_max_len);
            dissect_imap_imf(imf_start, imf_len, &imap_info);
            flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].total_number++;

            get_imap_filename(flow->thread_id,imap_info.mail_filename, sizeof(imap_info.mail_filename));
            FILE *fp = fopen(imap_info.mail_filename, "w");
            if (fp) {
                fwrite(imf_start, imf_len, 1, fp);
                fclose(fp);
                flow_thread_info[flow->thread_id].stats.mail_statics[EM_MAIL_IMAP].actual_number++;
            }

            write_imap_log(flow, direction, &imap_info);

            imf_start += imf_len;
            imf_max_len -= imf_len;
        }
        #endif

not_write:
        if(reassemble_result!=NULL)
            dpi_free(reassemble_result);

    }


    if (direction == FLOW_DIR_SRC2DST)
        tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
    else
        tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);

    return;
}

#else
static int dissect_imf_start(struct flow_info *flow, uint8_t direction)
{
    // struct imap_session *session=NULL;
  uint32_t            check_len            = 0;
  uint32_t            tmp_len              = 0;
  uint32_t            index                = 0;
    struct imap_session imap_info;
    memset(&imap_info, 0, sizeof(imap_info));
  char repalce_data[256] = {0};
  memset(repalce_data, 0x20, 256);
  int           i;
  int           ret     = 0;
  EmailSession *session = (EmailSession *)flow->app_session;


  if (session == NULL) {
        return -1;
  }

  struct Emailcache *c = session->cache + 2;

  uint8_t *imf_start = (uint8_t *)c->cache;
  uint32_t imf_max_len = c->cache_hold;
  uint32_t imf_len     = 0;


  while (imf_max_len) {
    imf_len       = imap_seperate_imf(imf_start, imf_max_len);
    const char *p = NULL, *q = NULL, *m = NULL;
    if (imf_len > IMAP_SEARCH_MAX_LEN) {
            check_len = IMAP_SEARCH_MAX_LEN;
    } else {
            check_len = imf_len;
    }
    char sub_header[IMAP_SEARCH_MAX_LEN] = {0};

    p = mail_memstr((const char *)imf_start, "BODY[]", check_len);
    q = mail_memstr((const char *)imf_start, "BODY[TEXT]", check_len);  // from&to 会出现为空的情况
    if (p != NULL || q != NULL) {
      dpi_email_t *email = dpi_email_create();
      dpi_email_imf(email, NULL, (const char *)imf_start, imf_max_len);

      dissect_imap_imf(imf_start, imf_len, &imap_info);
      if (1 == g_config.imap_content) {
        get_imap_filename(flow->thread_id, session->mail_filename, sizeof(session->mail_filename));
        FILE *fp = fopen(session->mail_filename, "w");
        if (fp) {
          fwrite(imf_start, imf_len, 1, fp);
          fclose(fp);
          imap_info.eml_filesize = imf_len;
        } else
          log_trace("can't open imap mail file");
      }
            strncpy(imap_info.login_status, "YES", COMMON_STATUS_LEN);
            // memcpy(imap_info.mail_id_name, session->mail_id_name,
            //        sizeof(imap_info.mail_id_name));
            // memcpy(imap_info.mail_id_version, session->mail_id_version,
            //        sizeof(imap_info.mail_id_version));
            // memcpy(imap_info.mail_id_os, session->mail_id_os,
            //        sizeof(imap_info.mail_id_os));
            // memcpy(imap_info.mail_id_os_version, session->mail_id_os_version,
            //        sizeof(imap_info.mail_id_os_version));
            // memcpy(imap_info.mail_id_vendor, session->mail_id_vendor,
            //        sizeof(imap_info.mail_id_vendor));
            strcpy(session->email_type, "IMAP");
            write_email_log(flow, direction, email);
            q = NULL;
            memset(&imap_info, 0, sizeof(struct imap_session));
      dpi_email_destory(email);
    }
        // circle:
        imf_start += imf_len;
        imf_max_len -= imf_len;
  }
  return 0;
}
#endif

static int identify_imap(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    int line_len=0;
    uint16_t i=0;
    uint16_t space_pos=0;

    if (g_config.protocol_switch[PROTOCOL_MAIL_IMAP] == 0)
        return PROTOCOL_UNKNOWN;

    uint16_t sport = 0;
    uint16_t dport = 0;
    sport = ntohs(flow->tuple.inner.port_src);
    dport = ntohs(flow->tuple.inner.port_dst);
    if (sport != IMAP_PORT && dport != IMAP_PORT
          && sport != 144 && dport != 144) {
        return PROTOCOL_UNKNOWN;
    }

    if (payload_len > 4 && get_uint16_ntohs(payload, payload_len - 2) == 0x0d0a) {

        if (flow->pkt_first_line.has_search == 0) {
            line_len = find_packet_line_end(payload, payload_len);
            flow->pkt_first_line.has_search = 1;
            flow->pkt_first_line.linelen = line_len;
        } else {
            line_len = flow->pkt_first_line.linelen;
        }

        if(line_len > 50)
            return PROTOCOL_UNKNOWN;

        while (i < 20 && i < payload_len) {
            if (i > 0 && payload[i] == ' ') {
                space_pos = i;
                break;
            }
            if (!((payload[i] >= 'a' && payload[i] <= 'z') ||
                    (payload[i] >= 'A' && payload[i] <= 'Z') ||
                    (payload[i] >= '0' && payload[i] <= '9') || payload[i] == '*' || payload[i] == '+')) {
                goto imap_excluded;
            }
            i++;
        }

        if (space_pos == 0 || space_pos == (payload_len - 1)) {
            goto imap_excluded;
        }

        if (payload[i-1] != '*' && payload[i-1] != '+' && !(payload[i-1] >= '0' && payload[i-1] <= '9')){
            goto imap_excluded;
        }

        flow->real_protocol_id = PROTOCOL_MAIL_IMAP;
        return PROTOCOL_MAIL_IMAP;
    }

imap_excluded:
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MAIL_SMTP);
    return PROTOCOL_UNKNOWN;
}

// 函数用于拆分括号内的内容成键值对
static void parseKV(const char* input, KeyValuePair* result, size_t maxPairs) {
    char buffer[1024];  // 用于存放拷贝的字符串
    char* token;        // 用于保存每个键值对
    size_t count = 0;   // 已经解析的键值对数量

    // 拷贝输入字符串到缓冲区
    strncpy(buffer, input, sizeof(buffer) - 1);
    buffer[sizeof(buffer) - 1] = '\0';

    // 定位到括号内的内容
    char* start = strchr(buffer, '(');
    if (start == NULL) {
        // printf("No opening parenthesis found.\n");
        return;
    }
    start++;

    // 定位到括号内的结尾
    char* end = strchr(buffer, ')');
    if (end == NULL) {
        // printf("No closing parenthesis found.\n");
        return;
    }
    *end = '\0';

    // 开始拆分字符串
    token = strtok(start, "\"");
    while (token != NULL && count / 2 < maxPairs) {
        // 去掉空格和逗号
        if (strcmp(token, " ") != 0 && strcmp(token, ",") != 0) {
            // 将奇数项作为键，偶数项作为值
            if (count % 2 == 0) {
                strncpy(result[count / 2].key, token, sizeof(result[count / 2].key) - 1);
                result[count / 2].key[sizeof(result[count / 2].key) - 1] = '\0';
            } else {
                strncpy(result[count / 2].value, token, sizeof(result[count / 2].value) - 1);
                result[count / 2].value[sizeof(result[count / 2].value) - 1] = '\0';
            }

            count++;
        }

        // 继续下一个 token
        token = strtok(NULL, "\"");
    }
}

/*
*imap协议的解析函数
        主要是根据每个数据包的命令得到imap的当前状态，并缓存一些信息在会话的app_session中
*/
static int dissect_imap_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {

  char           t = 0;
  int64_t        hl = 0;
  int64_t        offset = 0;
  const uint8_t *line;
  int            line_len;
  uint16_t       copy_len;
  int64_t        l = payload_len;
  int            space_index = 0;
  int            cmd_index = 0;
    uint32_t token_len = 0;
    const uint8_t *next_token;
    int uid_len = 0;
    const uint8_t * uid_token = NULL;

    int command_len = 0;
    const uint8_t *command_token = NULL;
    EmailSession *session = NULL;
    uint8_t       is_request = 0;

    struct Emailcache *c = NULL;
    char               _str[1024] = {0};
    int                cache_offset = direction;
    if (NULL == flow->app_session) {
      flow->app_session = dpi_malloc(sizeof(EmailSession));
      if (NULL == flow->app_session) {
              goto EMAIL_NEED_MORE_PKT;
      }
      memset(flow->app_session, 0, sizeof(EmailSession));
      session = (EmailSession *)flow->app_session;
      session->complemail = 1;
    }
    session = (EmailSession *)flow->app_session;
    c = session->cache + direction;

    if (direction == FLOW_DIR_SRC2DST) {
      is_request = 1;
    } else {
      is_request = 0;
    }

  if (is_request) {
    switch (session->state) {
            case EMAIL_STATE_READING_CMDS:
                line = payload;
                line_len = find_packet_line_end(line, payload_len);
                if (line_len <= 0) {
                    log_trace("no end line when reading imap cmd");
                    return PKT_OK;
                }
      /**
       * Extract the first token, and, if there is a first
       * token, add it as the request or reply tag.
      */
      token_len = dpi_get_token_len(payload, payload + line_len, &next_token);
      if (token_len != 0) {

        // line_len -= (token_len - offset);
        offset = next_token - payload;
      }

      /**
      * Extract second token, and, if there is a second
      * token, and it's not uid, add it as the request or reply command.
      */
      token_len = dpi_get_token_len(payload + offset, payload + line_len, &next_token);
      if (token_len != 0) {
        char command[64] = { 0 };
        if (strncasecmp((const char *)payload + offset, "UID", 3) == 0) {
            uid_len = token_len;
            offset = next_token - payload;
            token_len = dpi_get_token_len(payload + offset, payload + line_len, &next_token);
            if (token_len != 0) {
                command_len = token_len;
                command_token = payload + offset;
            }
        } else {

            command_len = token_len;
            command_token = payload + offset;
        }

        if (command_len > 0 && command_len < 64) {
          strncpy(command, (const char *)command_token, command_len);
          dpi_str_join(session->commands, command, ",");

          // if comand == ID, parse linelen
          if (strncasecmp(command, "ID", 2) == 0) {
            int max_len = sizeof(session->kv_pairs) / sizeof(session->kv_pairs[0]);
            parseKV((const char *)payload + offset + 2, session->kv_pairs, max_len);
          }
        }
      }
                space_index = _find_space(line, line_len, 1);
                if (space_index < 0) {
                    log_trace("no space when reading imap cmd");
                    return PKT_OK;
                }
                cmd_index = space_index + 1;

                if (line_len > cmd_index + 6 && memcmp(line + cmd_index, "LOGIN ", 6) == 0) {
                    int authname_index = cmd_index + 6;
                    space_index = _find_space(line + authname_index, line_len - authname_index, 1);

                    if (space_index < 0) {
                        log_trace("authname and password not pair");
                        copy_len = line_len - authname_index;
                        if (copy_len >= sizeof(session->auth_name))
                            copy_len = sizeof(session->auth_name) - 1;
                        strncpy(session->auth_name, (const char *)line + authname_index, copy_len);
                        session->auth_name[copy_len] = 0;
                    } else {
                        copy_len = space_index;
                        if (copy_len >= sizeof(session->auth_name))
                            copy_len = sizeof(session->auth_name) - 1;
                        strncpy(session->auth_name, (const char *)line + authname_index, copy_len);
                        session->auth_name[copy_len] = 0;
                        copy_len = line_len - authname_index - space_index - 1;
                        if (copy_len >= sizeof(session->auth_passwd))
                            copy_len = sizeof(session->auth_passwd) - 1;
                        strncpy(session->auth_passwd, (const char *)line + authname_index + space_index + 1, copy_len);
                        session->auth_passwd[copy_len] = 0;
                        size_t i = 0;
                        if (session->auth_passwd[0] == '\"') {
                            while (i < copy_len) {
                                session->auth_passwd[i] = session->auth_passwd[i + 1];
                                i++;
                            }
                            copy_len--;
                        }
                        if (session->auth_passwd[copy_len - 1] == '\"')
                            session->auth_passwd[copy_len - 1] = 0;
                    }
                    session->state = EMAIL_STATE_READING_STATUS;
                }
                break;
            case EMAIL_STATE_READING_DATA:
                break;
            default:
                session->state = EMAIL_STATE_READING_CMDS;
                break;
    }
  } else {
    // 解析服务器banner（第一次响应）
    if (strlen(session->banner) == 0 && payload_len > 0) {
        int banner_len = find_packet_line_end(payload, payload_len);
        if (banner_len > 0 && banner_len < sizeof(session->banner)) {
            strncpy(session->banner, (const char *)payload, banner_len);
            session->banner[banner_len] = '\0';
            // 去除末尾的换行符
            char *p = session->banner + strlen(session->banner) - 1;
            while (p >= session->banner && (*p == '\r' || *p == '\n')) {
                *p-- = '\0';
            }
        }
    }

    if (payload_len > 5) {
            int check_len = 64;
            if (payload_len < 64) {
                check_len = payload_len;
            }

            if (memmem((const char *)payload, check_len, "OK", strlen("OK")) != NULL &&
                session->state == EMAIL_STATE_READING_STATUS) {
                // 认证成功
                strncpy(session->auth_result, "SUCCESS", sizeof(session->auth_result) - 1);
                session->auth_result[sizeof(session->auth_result) - 1] = '\0';
            } else if (memmem((const char *)payload, check_len, "NO", strlen("NO")) != NULL &&
                       session->state == EMAIL_STATE_READING_STATUS) {
                // 认证失败
                strncpy(session->auth_result, "FAILED", sizeof(session->auth_result) - 1);
                session->auth_result[sizeof(session->auth_result) - 1] = '\0';
            }
    }

    if (session->state == EMAIL_STATE_READING_CMDS) {
            line = payload;
            line_len = find_packet_line_end(line, payload_len);
            if (line_len <= 0) {
                log_trace("no end line when reading imap cmd");
                return PKT_OK;
            }
            space_index = _find_space(line, line_len, 2);
            if (space_index < 0) {
                log_trace("no 2 space when reading imap cmd");
                return PKT_OK;
            }
            cmd_index = space_index + 1;
            if (line_len > cmd_index + 6 && memcmp(line + cmd_index, "FETCH ", 6) == 0) {
                const char *p = memmem((const char *)line, line_len, "BODY[]", strlen("BODY[]"));
                const char *q = memmem((const char *)line, line_len, "BODY[TEXT]", strlen("BODY[TEXT]"));
                if (p != NULL || q != NULL) {
                    session->state = EMAIL_STATE_READING_DATA;
                    flow->reassemble_flag = 1; /* for mail out-of-order */
                }
            }
    }
  }
  if (session->state == EMAIL_STATE_READING_DATA) {
    //数据未结束，新的数据到来
    cache_offset = 2;
    session->state = EMAIL_STATE_READING_DATA;
  }

  c = session->cache + cache_offset;
  if (NULL == c->cache) {
    c->cache_size = 1024 * 1000;  // 200K
    c->cache_hold = 0;
    c->cache = dpi_malloc(c->cache_size);
  }
  if (c->cache && session->state != EMAIL_STATE_READING_CMDS) {
    if ((int)payload_len >= (c->cache_size - c->cache_hold)) {
            // 缓存撑爆前重新申请内存
            // 创建临时缓冲区
            char *new_cache = (char *)realloc(c->cache, c->cache_size + l + 1000);
            if (NULL == new_cache) {
                goto EMAIL_NEED_MORE_PKT;
            }
            c->cache = new_cache;
            c->cache_size += l + 1000;
    }
    // 正常 拼装
    memcpy(c->cache + c->cache_hold, payload, payload_len);
    c->cache_hold += payload_len;
  }
EMAIL_NEED_MORE_PKT:
  return 0;
}

static void timeout_imap(struct flow_info *flow) {
  EmailSession *session = flow->app_session;
  if (session) {
    struct Emailcache *c = session->cache + 2;
    if (c->cache) {
            dissect_imf_start(flow, flow->direction);
    }
#if 0
// 无实体的不输出

        else if (g_config.write_nofile_eml){
            if (!session->record) {
                static char *proto_name = "";
                if (g_config.write_mail_log) {
                    proto_name = "email";
                } else {
                    proto_name = "imap";
                }
                session->record = ya_create_record(proto_name);
                session->NXTYPE = MAIL_IMAP;
                dpi_head_field_put(flow, flow->direction, proto_name, session->record);
            }
            write_email_log(flow, 0, EM_MAIL_IMAP, NULL);
        }
#endif

    for (int i = 0; i < EMAIL_CACHE_MAX; i++) {
            struct Emailcache *c = session->cache + i;
            if (NULL != c->cache) {
                free(c->cache);
                c->cache = NULL;
                c->cache_hold = 0;
            }
    }
    free(session);
    flow->app_session = NULL;
  }
  return;
}

extern struct decode_t decode_imap;

static int init_imap_dissector(struct decode_t *decode)
{
    dpi_register_proto_schema(imap_field_array,EM_IMAP_MAX,"imap");

    decode_on_port_tcp(IMAP_PORT, &decode_imap);
    decode_on_port_tcp(144, &decode_imap);

    register_tbl_array(TBL_LOG_MAIL_IMAP, 1, "imap", NULL);

    map_fields_info_register(imap_field_array,PROTOCOL_MAIL_IMAP, EM_IMAP_MAX,"imap");
	return 0;
}

static int imap_destroy(struct decode_t *decode) { return 0; }

struct decode_t decode_imap = {
    .name           =   "imap",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   init_imap_dissector,
    .pkt_identify   =   identify_imap,
    .pkt_dissect    =   dissect_imap_rsm,
    .pkt_miss       =   dissect_email_miss,
    .flow_finish    =   timeout_imap,
    .decode_destroy =   imap_destroy,
};
