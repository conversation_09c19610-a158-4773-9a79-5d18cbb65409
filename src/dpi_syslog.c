
/****************************************************************************************
 * 文 件 名 : dpi_syslog.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *设计: hongll  2022/05/11
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <rte_mbuf.h>

#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_common.h"
#include "dpi_proto_ids.h"
#include "dpi_conversation.h"

#define BRACKET_L		(0x3C)
#define BRACKET_R		(0x3E)

#define UDP_PORT_SYSLOG 514

#define PRIORITY_MASK 0x0007  /* 0000 0111 */
#define FACILITY_MASK 0x03f8  /* 1111 1000 */

/* The maximum number if priority digits to read in. */
#define MAX_DIGITS 3


/* Level / Priority */
#define LEVEL_EMERG     0
#define LEVEL_ALERT     1
#define LEVEL_CRIT      2
#define LEVEL_ERR       3
#define LEVEL_WARNING   4
#define LEVEL_NOTICE    5
#define LEVEL_INFO      6
#define LEVEL_DEBUG     7

/* Facility */
#define FAC_KERN        0
#define FAC_USER        1
#define FAC_MAIL        2
#define FAC_DAEMON      3
#define FAC_AUTH        4
#define FAC_SYSLOG      5
#define FAC_LPR         6
#define FAC_NEWS        7
#define FAC_UUCP        8
#define FAC_CRON        9
#define FAC_AUTHPRIV    10
#define FAC_FTP         11
#define FAC_NTP         12
#define FAC_LOGAUDIT    13
#define FAC_LOGALERT    14
#define FAC_CRON_SOL    15
#define FAC_LOCAL0      16
#define FAC_LOCAL1      17
#define FAC_LOCAL2      18
#define FAC_LOCAL3      19
#define FAC_LOCAL4      20
#define FAC_LOCAL5      21
#define FAC_LOCAL6      22
#define FAC_LOCAL7      23


extern struct rte_mempool *tbl_log_mempool;

typedef enum _syslog_enum_index {
    EM_SYSLOG_FACILITY,
    EM_SYSLOG_LEVEL,
    EM_SYSLOG_MESSAGE,
    EM_SYSLOG_SS7_MSU_PRESENT,
    EM_SYSLOG_VERSION,
    EM_SYSLOG_TIMESTAMP,
    EM_SYSLOG_HOSTNAME,
    EM_SYSLOG_APPNAME,
	EM_SYSLOG_PROCESSID,
	EM_SYSLOG_MESSAGEID,
    EM_SYSLOG_MESSAGEID_UTF8,
    EM_SYSLOG_MESSAGEID_BOM,

    EM_SYSLOG_MAX
} syslog_enum_index;

static dpi_field_table syslog_field_array[] = {
        DPI_FIELD_D(EM_SYSLOG_FACILITY,                 YA_FT_UINT8,     "facility"),
        DPI_FIELD_D(EM_SYSLOG_LEVEL,                    YA_FT_UINT8,     "level"),
        DPI_FIELD_D(EM_SYSLOG_MESSAGE,                  YA_FT_STRING,    "message"),
        DPI_FIELD_D(EM_SYSLOG_SS7_MSU_PRESENT,          YA_FT_UINT8,     "ss7_msu_present"),
        DPI_FIELD_D(EM_SYSLOG_VERSION,                  YA_FT_STRING,    "version"),
        DPI_FIELD_D(EM_SYSLOG_TIMESTAMP,                YA_FT_STRING,    "timestamp"),
        DPI_FIELD_D(EM_SYSLOG_HOSTNAME,                 YA_FT_STRING,    "hostname"),
        DPI_FIELD_D(EM_SYSLOG_APPNAME,                  YA_FT_STRING,    "appname"),
        DPI_FIELD_D(EM_SYSLOG_PROCESSID,                YA_FT_INT32,     "processid"),
		DPI_FIELD_D(EM_SYSLOG_MESSAGEID,                YA_FT_STRING,    "messageid"),
		DPI_FIELD_D(EM_SYSLOG_MESSAGEID_BOM,            YA_FT_STRING,    "messageid_bom"),
        DPI_FIELD_D(EM_SYSLOG_MESSAGEID_UTF8,           YA_FT_STRING,    "messageid_utf8"),
};

struct syslog_info {
    uint8_t     facility;
    uint8_t     level;
    uint8_t     message[4096];
    uint16_t    message_len;
    uint8_t     ss7_msu_present;
    uint8_t     version;
    char        timestamp[32];  // utc
    char        timestamp_rfc3164[16]; // ascii
    char        hostname[256]; // ascii                 syslog.hostname
    char        appname[256];  // ascii                 syslog.event
    char        processid[256]; // ascii   [processid]  syslog.pid
    char        messageid[1024]; // ascii               syslog.event_info
    uint8_t     messageid_bom[3];
    uint8_t     messageid_utf8[4096];
};


static const char* short_level_val(uint8_t level) {
    switch (level) {
        case LEVEL_EMERG:   return "EMERG";
        case LEVEL_ALERT:   return "ALERT";
        case LEVEL_CRIT:    return "CRIT";
        case LEVEL_ERR:     return "ERR";
        case LEVEL_WARNING: return "WARNING";
        case LEVEL_NOTICE:  return "NOTICE";
        case LEVEL_INFO:    return "INFO";
        case LEVEL_DEBUG:   return "DEBUG";
        default:            return "UNKNOWN";
    }
}

static const char* short_facility_val(uint8_t fac) {
    switch (fac) {
        case FAC_KERN:      return "KERN";
        case FAC_USER:      return "USER";
        case FAC_MAIL:      return "MAIL";
        case FAC_DAEMON:    return "DAEMON";
        case FAC_AUTH:      return "AUTH";
        case FAC_SYSLOG:    return "SYSLOG";
        case FAC_LPR:       return "LPR";
        case FAC_NEWS:      return "NEWS";
        case FAC_UUCP:      return "UUCP";
        case FAC_CRON:      return "CRON";       /* The BSDs, Linux, and others */
        case FAC_AUTHPRIV:  return "AUTHPRIV";
        case FAC_FTP:       return "FTP";
        case FAC_NTP:       return "NTP";
        case FAC_LOGAUDIT:  return "LOGAUDIT";
        case FAC_LOGALERT:  return "LOGALERT";
        case FAC_CRON_SOL:  return "CRON";       /* Solaris */
        case FAC_LOCAL0:    return "LOCAL0";
        case FAC_LOCAL1:    return "LOCAL1";
        case FAC_LOCAL2:    return "LOCAL2";
        case FAC_LOCAL3:    return "LOCAL3";
        case FAC_LOCAL4:    return "LOCAL4";
        case FAC_LOCAL5:    return "LOCAL5";
        case FAC_LOCAL6:    return "LOCAL6";
        case FAC_LOCAL7:    return "LOCAL7";
        default:            return "UNKNOWN";
    }
}

static const char* level_vals(uint8_t level) {
    switch (level) {
        case LEVEL_EMERG:   return "EMERG - system is unusable";
        case LEVEL_ALERT:   return "ALERT - action must be taken immediately";
        case LEVEL_CRIT:    return "CRIT - critical conditions";
        case LEVEL_ERR:     return "ERR - error conditions";
        case LEVEL_WARNING: return "WARNING - warning conditions";
        case LEVEL_NOTICE:  return "NOTICE - normal but significant condition";
        case LEVEL_INFO:    return "INFO - informational";
        case LEVEL_DEBUG:   return "DEBUG - debug-level messages";
        default:            return "UNKNOWN";
    }
};

static const char* facility_vals(uint8_t fac) {
    switch (fac) {
        case FAC_KERN:      return "KERN - kernel messages";
        case FAC_USER:      return "USER - random user-level messages";
        case FAC_MAIL:      return "MAIL - mail system";
        case FAC_DAEMON:    return "DAEMON - system daemons";
        case FAC_AUTH:      return "AUTH - security/authorization messages";
        case FAC_SYSLOG:    return "SYSLOG - messages generated internally by syslogd";
        case FAC_LPR:       return "LPR - line printer subsystem";
        case FAC_NEWS:      return "NEWS - network news subsystem";
        case FAC_UUCP:      return "UUCP - UUCP subsystem";
        case FAC_CRON:      return "CRON - clock daemon (BSD, Linux)";
        case FAC_AUTHPRIV:  return "AUTHPRIV - security/authorization messages (private)";
        case FAC_FTP:       return "FTP - ftp daemon";
        case FAC_NTP:       return "NTP - ntp subsystem";
        case FAC_LOGAUDIT:  return "LOGAUDIT - log audit";
        case FAC_LOGALERT:  return "LOGALERT - log alert";
        case FAC_CRON_SOL:  return "CRON - clock daemon (Solaris)";
        case FAC_LOCAL0:    return "LOCAL0 - reserved for local use";
        case FAC_LOCAL1:    return "LOCAL1 - reserved for local use";
        case FAC_LOCAL2:    return "LOCAL2 - reserved for local use";
        case FAC_LOCAL3:    return "LOCAL3 - reserved for local use";
        case FAC_LOCAL4:    return "LOCAL4 - reserved for local use";
        case FAC_LOCAL5:    return "LOCAL5 - reserved for local use";
        case FAC_LOCAL6:    return "LOCAL6 - reserved for local use";
        case FAC_LOCAL7:    return "LOCAL7 - reserved for local use";
        default:            return "UNKNOWN";
    }

};

static int write_syslog_log(struct flow_info *flow, int direction, struct syslog_info *sys_info) {
    int             j;
    int             idx = 0, i;
    struct tbl_log *log_ptr;
    char            str[4096];
    char           *tmp;

    memset(str, 0, sizeof(str));

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }

    init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "syslog");
    for (j = 0; j < EM_SYSLOG_MAX; j++) {
        switch (j) {
          case EM_SYSLOG_FACILITY:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)sys_info->facility);
            break;
          case EM_SYSLOG_LEVEL:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)sys_info->level);
            break;
          case EM_SYSLOG_MESSAGE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (char *)sys_info->message, sys_info->message_len);
            break;
          case EM_SYSLOG_SS7_MSU_PRESENT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)sys_info->ss7_msu_present);  // TRUE/FALSE
            break;
          case EM_SYSLOG_VERSION:
            memset(str, 0, sizeof(str));
            if (sys_info->version > 0x30)
              snprintf(str, sizeof(str), "%d", sys_info->version - 0x30);

            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
            break;
          case EM_SYSLOG_TIMESTAMP:
            if (strlen(sys_info->timestamp) != 0) {
              write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, sys_info->timestamp, strlen(sys_info->timestamp));
            } else if (strlen(sys_info->timestamp_rfc3164) != 0) {
              write_one_str_reconds(
                  log_ptr->record, &idx, TBL_LOG_MAX_LEN, sys_info->timestamp_rfc3164, strlen(sys_info->timestamp_rfc3164));
            } else {
              write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
          case EM_SYSLOG_HOSTNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, sys_info->hostname, strlen(sys_info->hostname));
            break;
          case EM_SYSLOG_APPNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, sys_info->appname, strlen(sys_info->appname));
            break;
          case EM_SYSLOG_PROCESSID:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, sys_info->processid, strlen(sys_info->processid));
            break;
          case EM_SYSLOG_MESSAGEID:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, sys_info->messageid, strlen(sys_info->messageid));
            break;
          case EM_SYSLOG_MESSAGEID_UTF8:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)sys_info->messageid_bom,
                strlen((char *)sys_info->messageid_bom));
            break;
          case EM_SYSLOG_MESSAGEID_BOM:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)sys_info->messageid_utf8,
                strlen((char *)sys_info->messageid_utf8));
            break;

          default:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_SYSLOG;
    log_ptr->log_len = idx;
    log_ptr->proto_id = PROTOCOL_SYSLOG;

    log_ptr->flow = flow;

    if (write_tbl_log(log_ptr) != 1) {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static void dpi_mtp3_msu_present(struct syslog_info *info, int facility, int level, const uint8_t *msg_str) {
    if (NULL != strstr((char *)msg_str, "msu="))
        info->ss7_msu_present = 1;
}

static bool dissect_syslog_info(uint8_t *start_str, int *offset, int ptr_len, char *info) {
    uint8_t *truncated = (uint8_t *)strchr((const char *)start_str, ' ');
    if (truncated == NULL) {
        return false;
    }
    int msg_len = truncated - start_str;
    if (msg_len > ptr_len) {
        return false;
    }
    memcpy(info, start_str, msg_len);
    *offset += msg_len + 1;
    return TRUE;
}

static bool dissect_syslog_message_info(struct syslog_info *info, uint8_t *start_str, int *offset) {
    int      pid = 0;
    uint8_t *slicer = (uint8_t *)strchr((char *)start_str, ':');
    char     appname[64] = {0};
    char     processid[16] = {0};
    if (NULL == slicer) {
        return false;
        ;
    }
    int   event_str_len = slicer - start_str;
    char *pid_start = strchr((char *)start_str, '[');
    if (pid_start != NULL) {
        char *pid_end = strchr(pid_start + 1, '[');
        if (pid_end)
            memcpy(info->processid, pid_start, pid_end - pid_start);
        memcpy(info->appname, start_str, (uint8_t *)pid_start - (uint8_t *)start_str);
    } else if (false != dissect_syslog_info(start_str, offset, event_str_len, appname)) {
        dissect_syslog_info(start_str, offset, event_str_len - strlen(appname), info->processid);
    } else {
        memcpy(info->appname, start_str, event_str_len);
    }
    strcpy(info->messageid, (char *)slicer + 1);
    return true;
}

static void dpi_dissect_syslog_message(
    struct flow_info *flow, struct syslog_info *info, int direction, const uint8_t *payload, int payload_len, uint32_t offset) {
    uint16_t i_count = 0;
    int      msg_off = offset;
    int      msg_len = 0;
    info->version = *((uint8_t *)payload + offset);
    msg_off += 2;
    if (false == dissect_syslog_info((uint8_t *)payload + msg_off, &msg_off, payload_len - msg_off, info->timestamp)) {
        return;
    }
    if (false == dissect_syslog_info((uint8_t *)payload + msg_off, &msg_off, payload_len - msg_off, info->hostname)) {
        return;
    }
    if (false == dissect_syslog_info((uint8_t *)payload + msg_off, &msg_off, payload_len - msg_off, info->appname)) {
        return;
    }
    if (false == dissect_syslog_info((uint8_t *)payload + msg_off, &msg_off, payload_len - msg_off, info->processid)) {
        return;
    }

    // BOM 0xefbbbf
    if ((get_uint32_ntohl(payload, msg_off) >> 8) == 0xefbbbf) {
        memcpy(info->messageid_bom, payload + msg_off, 3);
        msg_off += 3;
        memcpy(info->messageid_utf8, payload + msg_off, payload_len - msg_off);
    } else {
        // messageid
        memcpy(info->messageid, payload + msg_off, payload_len - msg_off);
    }
    return;
}

static void dpi_dissect_rfc3164_syslog_message(
    struct flow_info *flow, struct syslog_info *info, int direction, const uint8_t *payload, int payload_len, uint32_t offset) {
    uint16_t i_count = 0;
    int      msg_off = offset;
    int      msg_len = 0;
    uint8_t *start_str = (uint8_t *)payload + offset;
    uint8_t *truncated = NULL;

    if (payload[msg_off + 3] == ' ' && payload[msg_off + 6] == ' ' && payload[msg_off + 9] == ':' &&
        payload[msg_off + 12] == ':' && payload[msg_off + 15] == ' ') {
        memcpy(info->timestamp_rfc3164, payload + msg_off, 15);
        msg_off += 16;  // 15 for timestamp ; 1 for ' '
    } else {
        return;
    }
    if (false == dissect_syslog_info((uint8_t *)payload + msg_off, &msg_off, payload_len - msg_off, info->hostname)) {
        return;
    }
    dissect_syslog_message_info(info, (uint8_t *)payload + msg_off, &msg_off);
}

static int dissect_syslog_udp(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag) {
    if (payload_len < 3 /*|| payload_len > 1024*/)
        return PKT_DROP;

    struct syslog_info info;
    memset(&info, 0, sizeof(info));

    int      pri = -1, fac = -1, lev = -1;
    uint16_t msg_off = 0, msg_len = 0;
    uint16_t i = 0, j = 0, i_start = 0, i_end = 0, i_count = 0;

    // 检测facility & level 属性 <*[1~3digit]>
    if (payload[msg_off] == '<') {
        msg_off++;
        pri = 0;
        while (msg_off < payload_len && msg_off <= MAX_DIGITS && isdigit(payload[msg_off])) {
          pri = pri * 10 + (payload[msg_off] - '0');
          msg_off++;
        }
        if (payload[msg_off] == '>')
          msg_off++;
        fac = (pri & FACILITY_MASK) >> 3;
        lev = pri & PRIORITY_MASK;
    }
    msg_len = payload_len - msg_off;
    const uint8_t *msg_str = payload + msg_off;

    //TODO: mtp3需要探测
    dpi_mtp3_msu_present(&info, fac, lev, msg_str);

    if (pri >= 0) {
        info.facility = fac;
        info.level = lev;
    }

    //TODO: why dong this? 4096 means what?
    msg_len = (payload_len - msg_off) >= 4096 ? 4095 : (payload_len - msg_off);

    info.message_len = msg_len;
    memcpy(info.message, payload + msg_off, info.message_len);

    /* RFC5424 defines a version field which is currently defined as '1'
     * followed by a space (0x3120). Otherwise the message is probable
     * a RFC3164 message.
     */

    if (msg_len > 2 && get_uint16_ntohs(payload, msg_off) == 0x3120) {
        dpi_dissect_syslog_message(flow, &info, direction, payload, payload_len, msg_off);
    }
    // for rfc3164
    else if (msg_len > 15) {
        dpi_dissect_rfc3164_syslog_message(flow, &info, direction, payload, payload_len, msg_off);
    }

    write_syslog_log(flow, direction, &info);

    return 0;
}

static void identify_syslog(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {

    if (g_config.protocol_switch[PROTOCOL_SYSLOG] == 0)
        return;

    if (payload_len < 3 /*|| payload_len > 1024*/)
        return;

//    if (payload[0] != 0x04 || payload[0] != 0x05 )
//        return;

    if (ntohs(flow->tuple.inner.port_src) != UDP_PORT_SYSLOG && ntohs(flow->tuple.inner.port_dst) != UDP_PORT_SYSLOG)
      return;

    if (payload[0] != BRACKET_L)  
      return;

    flow->real_protocol_id = PROTOCOL_SYSLOG;

}

static void init_syslog_dissector(void) {
    dpi_register_proto_schema(syslog_field_array, EM_SYSLOG_MAX,"syslog");
    port_add_proto_head(IPPROTO_UDP, UDP_PORT_SYSLOG, PROTOCOL_SYSLOG);
    udp_detection_array[PROTOCOL_SYSLOG].proto           = PROTOCOL_SYSLOG;
    udp_detection_array[PROTOCOL_SYSLOG].identify_func   = identify_syslog;
    udp_detection_array[PROTOCOL_SYSLOG].dissect_func    = dissect_syslog_udp;
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_SYSLOG].excluded_protocol_bitmask, PROTOCOL_SYSLOG);

	map_fields_info_register(syslog_field_array, PROTOCOL_SYSLOG, EM_SYSLOG_MAX, "syslog");
}

static __attribute((constructor)) void before_init_syslog(void) {
    register_tbl_array(TBL_LOG_SYSLOG, 0, "syslog", init_syslog_dissector);
}
