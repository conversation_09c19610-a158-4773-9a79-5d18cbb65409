#ifndef _DPI_OFFLINE_H
#define _DPI_OFFLINE_H

#include <rte_mempool.h>
#include <rte_ring.h>

typedef struct dpi_offline_info_
{
  struct rte_mempool *mbuf_pool;
  struct rte_ring    **mbuf_ring;
  int ring_cnt;
  struct rte_ring    *pcap_ring;
  const char         *pcap_dir;
}DpiOfflineInfo;

typedef struct dpi_offline_pcap_t
{
  char file_name[256];
} DpiOfflinePcap;


/**
*
*/
void dpi_offline_init();
void dpi_offline_start();
void dpi_offline_stop();

#endif