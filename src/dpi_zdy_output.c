#include "dpi_zdy_output.h"

#include <sys/time.h>

#include "dpi_tbl_log.h"

extern pthread_mutex_t mutex_g_config;
extern struct global_config g_config;

void dpi_zdy_get_filename(enum tbl_log_type type, int thread_id, char *filename)
{
    char dirname[128] = {0};
    struct timeval tv;

    pthread_mutex_lock(&mutex_g_config);
    snprintf(dirname, sizeof(dirname), "%s/%s", g_config.tbl_out_dir, tbl_log_array[type].protoname);
    if (access(dirname, F_OK)) mkdir(dirname, 0666);
    pthread_mutex_unlock(&mutex_g_config);

    gettimeofday(&tv, NULL);
    snprintf(filename, 256, "%s/%s_%06ld_%s_%03d", dirname,time_to_datetime(g_config.g_now_time), tv.tv_usec, tbl_log_array[type].protoname, thread_id);
    if (g_config.show_task_id) {
        strcat(filename, "_");
        strcat(filename, g_config.task_id);
    }

    // snprintf(filename, sizeof(filename), "%s.tbl.tmp", tbl_log_array[type].filename[thread_id]);
}