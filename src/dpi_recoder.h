#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>

#ifndef DPI_RECODER_H
#define DPI_RECODER_H

#ifdef __cplusplus
extern "C" {
#endif


precord_t* sdt_precord_new_record(const char *proto_name);

#define dpi_precord_new_record(record, proto_name, proto_field_table)   \
{                                                                       \
    record = sdt_precord_new_record(proto_name);                       \
}

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif


