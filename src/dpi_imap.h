/* 
 * File:   dpi_imap.h
 * Author: xu
 *
 * Created on 2019年3月18日, 下午7:57
 */
#ifndef DPI_IMAP_H
#define DPI_IMAP_H
#include "dpi_conversation.h"

#include <yaemail/email.h>

#define FALSE (0)
#define TRUE (!FALSE)

#define IMAP_PORT                    143
#define IMAP_ATTACHMENT_NUM_MAX      10
#define IMAP_ENCODING_LEN            32
#define IMAP_FILE_NAME_LEN           64
#define IMAP_CONTENT_TYPE_LEN        128
#define IMAP_DISPOSITION_LEN         128 
#define CONTENT_MD5_LEN     (64)

#define IMAP_SEARCH_MAX_LEN          1024

typedef enum 
{
    IMAP_STATE_READING_CMDS,              /* reading commands */
    IMAP_STATE_READING_DATA,              /* reading message data */
} imap_state_t;

enum imap_index_field
{
    
    EM_IMAP_AUTH_NAME,
    EM_IMAP_AUTH_PASSWD,
    EM_IMAP_MAIL_FILENAME,
    
    EM_IMAP_DATE,
    EM_IMAP_FROM,
    EM_IMAP_SENDER,
    EM_IMAP_TO,
    EM_IMAP_RECEIVER,
    EM_IMAP_CC,
    EM_IMAP_BCC,
    EM_IMAP_MESSAGE_ID,
    EM_IMAP_REPLY_TO,
    EM_IMAP_IN_REPLY_TO,
    EM_IMAP_REFERENCES,
    EM_IMAP_SUBJECT,
    EM_IMAP_RECEIVED1,
    EM_IMAP_RECEIVED2,
    EM_IMAP_X_MAILER,
    EM_IMAP_MIME_VERSION,
    EM_IMAP_CONTENT_ID,
    EM_IMAP_CONTENT_DESCRIPTION,
    EM_IMAP_CONTENT_TRANSFER_ENCODING,
    EM_IMAP_CONTENT_TYPE,
    EM_IMAP_DELIVERY_DATE,
    EM_IMAP_LATEST_DELIVERY_TIME,
    EM_IMAP_RESENT_DATE,
    EM_IMAP_SEND_SERVER,
    EM_IMAP_SEND_SOFT,
    EM_IMAP_X_ORIGINATING_IP,
    EM_IMAP_SENDER_SVR_IP,
    EM_IMAP_SENDER_SVR,
    EM_IMAP_RECEIVER_SVR_IP,
    EM_IMAP_RECEIVER_SVR,
    EM_IMAP_X_PRIORITY,
    
    EM_IMAP_PROXY_RESEND,    //转发代理
    EM_IMAP_PROXY_USER,        //用户代理
    EM_IMAP_USER_OP_TYPE,    //用户操作类型
    EM_IMAP_USER_OP_TIME,    //用户操作时间
    EM_IMAP_USER_OP_RES,    //用户操作结果
    EM_IMAP_RECEIVER_TYPE,    //收件人类型
    EM_IMAP_SEND_DURATION,    //发送持续时间

    
    
    EM_IMAP_ATTACHMENT_FILENAME0,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE0,
    EM_IMAP_ATTACHMENT_ENCODING0,
    EM_IMAP_ATTACHMENT_DISPOSITION0,
    EM_IMAP_ATTACHMENT_LEN0,
    
    EM_IMAP_ATTACHMENT_FILENAME1,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE1,
    EM_IMAP_ATTACHMENT_ENCODING1,
    EM_IMAP_ATTACHMENT_DISPOSITION1,
    EM_IMAP_ATTACHMENT_LEN1,
    
    EM_IMAP_ATTACHMENT_FILENAME2,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE2,
    EM_IMAP_ATTACHMENT_ENCODING2,
    EM_IMAP_ATTACHMENT_DISPOSITION2,
    EM_IMAP_ATTACHMENT_LEN2,
    
    EM_IMAP_ATTACHMENT_FILENAME3,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE3,
    EM_IMAP_ATTACHMENT_ENCODING3,
    EM_IMAP_ATTACHMENT_DISPOSITION3,
    EM_IMAP_ATTACHMENT_LEN3,
    
    EM_IMAP_ATTACHMENT_FILENAME4,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE4,
    EM_IMAP_ATTACHMENT_ENCODING4,
    EM_IMAP_ATTACHMENT_DISPOSITION4,
    EM_IMAP_ATTACHMENT_LEN4,
    
    EM_IMAP_ATTACHMENT_FILENAME5,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE5,
    EM_IMAP_ATTACHMENT_ENCODING5,
    EM_IMAP_ATTACHMENT_DISPOSITION5,
    EM_IMAP_ATTACHMENT_LEN5,
    
    EM_IMAP_ATTACHMENT_FILENAME6,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE6,
    EM_IMAP_ATTACHMENT_ENCODING6,
    EM_IMAP_ATTACHMENT_DISPOSITION6,
    EM_IMAP_ATTACHMENT_LEN6,
    
    EM_IMAP_ATTACHMENT_FILENAME7,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE7,
    EM_IMAP_ATTACHMENT_ENCODING7,
    EM_IMAP_ATTACHMENT_DISPOSITION7,
    EM_IMAP_ATTACHMENT_LEN7,
    
    EM_IMAP_ATTACHMENT_FILENAME8,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE8,
    EM_IMAP_ATTACHMENT_ENCODING8,
    EM_IMAP_ATTACHMENT_DISPOSITION8,
    EM_IMAP_ATTACHMENT_LEN8,
    
    EM_IMAP_ATTACHMENT_FILENAME9,
    EM_IMAP_ATTACHMENT_CONTENT_TYPE9,
    EM_IMAP_ATTACHMENT_ENCODING9,
    EM_IMAP_ATTACHMENT_DISPOSITION9,
    EM_IMAP_ATTACHMENT_LEN9,

    /* 主体内容提取 */
    EM_IMAP_MAIL_CODE_FORMAT,
    EM_IMAP_MAIL_CODE_BASE,
    EM_IMAP_MAIL_CONTENT_DATA,
    
    EM_IMAP_PROTO_TYPE,

    EM_IMAP_EML_FILESIZE,

    EM_IMAP_LOGIN_STATUS,
    
    EM_IMAP_MAX
};

typedef struct __attachment_imap_info__
{
    //附件文件名
    uint8_t attachment_filename[IMAP_FILE_NAME_LEN];
    //附件内容类型
    uint8_t attachment_content_type[IMAP_CONTENT_TYPE_LEN];
    //附件内容编码格式
    uint8_t attachment_content_transfer_encoding[IMAP_ENCODING_LEN];
    //附件类型，附件文件名
    uint8_t attachment_content_disposition[IMAP_DISPOSITION_LEN];
    //附件的内容MD5
    uint8_t attachment_content_md5[CONTENT_MD5_LEN];
    //附件内容长度
    long long attachment_len;
} AttaImap;

struct imap_session
{
  dpi_email_t *email;
    imap_state_t state;
    char auth_name[64];
    char auth_passwd[64];
    char mail_filename[128];
	//信封
    const uint8_t *mail_mail_from_ptr;
    uint16_t mail_mail_from_len;
    const uint8_t *mail_mail_from_domain_ptr;
    uint16_t mail_mail_from_domain_len;
    const uint8_t *mail_rcpt_to_ptr;
    uint16_t mail_rcpt_to_len;
    const uint8_t *mail_rcpt_to_domain_ptr;
    uint16_t mail_rcpt_to_domain_len;

    const uint8_t *mail_date_ptr;
    uint16_t mail_date_len;
    const uint8_t *mail_from_ptr;
    uint16_t mail_from_len;
    const uint8_t *sender_email_ptr;
    uint16_t sender_email_len;

    const uint8_t *mail_sender_name_ptr;
    uint16_t mail_sender_name_len;
    //发件域 senderDomain
    const uint8_t *mail_sender_domain_ptr;
    uint16_t mail_sender_domain_len;

    //uint8_t* receiver_name;
    const uint8_t *mail_to_ptr;
    uint16_t mail_to_len;
    const uint8_t *receiver_email_ptr;
    uint16_t receiver_email_len;
    //接收人
    const uint8_t *mail_receiver_name_ptr;
    uint16_t mail_receiver_name_len;
    //发件域 receiverDomain
    const uint8_t *mail_receiver_domain_ptr;
    uint16_t mail_receiver_domain_len;
    
    const uint8_t *mail_cc_ptr;
    uint16_t mail_cc_len;
    const uint8_t *mail_bcc_ptr;
    uint16_t mail_bcc_len;
    const uint8_t *mail_subject_ptr;
    uint16_t mail_subject_len;
    const uint8_t *mail_content_type_ptr;
    uint16_t mail_content_type_len;
    const uint8_t *mail_reply_to_ptr;
    uint16_t mail_reply_to_len;
    const uint8_t *mail_in_reply_to_ptr;
    uint16_t mail_in_reply_to_len;
    const uint8_t *mail_references_ptr;
    uint16_t mail_references_len;
    const uint8_t *mail_x_mailer_ptr;
    uint16_t mail_x_mailer_len;
    const uint8_t *mail_received1_ptr;
    uint16_t mail_received1_len;
    const uint8_t *mail_received2_ptr;
    uint16_t mail_received2_len;
    const uint8_t *mail_mime_version_ptr;
    uint16_t mail_mime_version_len;
    const uint8_t *mail_message_id_ptr;
    uint16_t mail_message_id_len;
    const uint8_t *mail_content_id_ptr;
    uint16_t mail_content_id_len;
    const uint8_t *mail_content_transfer_encoding_ptr;
    uint16_t mail_content_transfer_encoding_len;
    const uint8_t *mail_delivery_date_ptr;
    uint16_t mail_delivery_date_len;
    const uint8_t *mail_latest_delivery_time_ptr;
    uint16_t mail_latest_delivery_time_len;
    const uint8_t *mail_content_description_ptr;
    uint16_t mail_content_description_len;
    
    const uint8_t *mail_send_server_ptr;
    uint16_t mail_send_server_len;
    const uint8_t *mail_send_soft_ptr;
    uint16_t mail_send_soft_len;
    const uint8_t *mail_x_originating_ip_ptr;
    uint16_t mail_x_originating_ip_len;
    const uint8_t *mail_sender_svr_ip_ptr;
    uint16_t mail_sender_svr_ip_len;
    const uint8_t *mail_sender_svr_ptr;
    uint16_t mail_sender_svr_len;
    const uint8_t *mail_receiver_svr_ip_ptr;
    uint16_t mail_receiver_svr_ip_len;
    const uint8_t *mail_receiver_svr_ptr;
    uint16_t mail_receiver_svr_len;
    
    const uint8_t *mail_resent_date_ptr;
    uint16_t mail_resent_date_len;
    const uint8_t *mail_x_priority_ptr;
    uint16_t mail_x_priority_len;
    
    const uint8_t *mail_user_agent_ptr;
    uint16_t mail_user_agent_len;

    uint8_t   main_content_print[32];
    uint8_t   main_content_code[50];
    const uint8_t   *main_content_data;
    uint16_t  main_content_len;

    char mail_id_name[128];
    char mail_id_version[128];
    char mail_id_os[128];
    char mail_id_os_version[128];
    char mail_id_vendor[128];

    char mail_server_name_ptr[32];
    char mail_server_soft_ptr[32];

    const uint8_t *mail_delivered_to_ptr;
    uint16_t mail_delivered_to_len;
    const uint8_t *mail_resent_from_ptr;
    uint16_t mail_resent_from_len;
    const uint8_t *mail_resent_to_ptr;
    uint16_t mail_resent_to_len;

    //暂定为最多支持10个附件
    AttaImap attachment_list[IMAP_ATTACHMENT_NUM_MAX+1];
    uint8_t     attachment_filename_num;
    uint8_t     attachment_conttype_num;
    uint8_t     attachent_md5_num;

    uint32_t eml_filesize;
    char     login_status[COMMON_STATUS_LEN];


    // from ip -- Received字段中的IP，如果有多个，逗号隔开， 可能还有多个Received字段，都解析
    char    received_from_ips[1024];
    uint8_t received_from_ips_num;  // from ip数量

    char    receiveds[2048];
    uint8_t receiveds_num;

    // from domain  -- Received字段中的from 域名， 用逗号分开
    char    received_from_doms[1024];
    uint8_t received_from_doms_num;

    // by ip  -- Received 字段中的by IP， 逗号分开
    char    received_by_ips[1024];
    uint8_t received_by_ips_num;

    // by dom -- Received字段中的by 域名， 用逗号分开
    char    received_by_doms[1024];
    uint8_t received_by_doms_num;

    // from asn  IP解析出来的自治域
    char    from_asns[2048];
    uint8_t from_asns_num;

    // from country IP解析出来的国家
    char    from_countries[1024];
    uint8_t from_countries_num;

    char    cc_addrs[2048];  // Cc 抄送的地址
    char    cc_alias[2048];  // Cc 抄送的名字
    uint8_t cc_addrs_num;
    uint8_t cc_alias_num;

    // content-type 逗号分开
    char    cont_types[2048];
    uint8_t cont_types_num;

    // text charset  content-type=text是的charset
    char    text_charsets[1024];
    uint8_t text_charsets_num;

    // body type 正文的content-type
    char    body_type[1024];
    uint8_t body_type_num;

    // 邮件的头部设置字段，逗号分开  如 Content-type、X- clm-senderinfo 等
    char    head_sets[2048];
    uint8_t head_sets_num;

    // Message-ID 头
    char    msg_ids[1024];
    uint8_t msg_ids_num;

    // mime-version 头
    char    mime_vers[256];
    uint8_t mime_vers_num;

    // 包含html标签的正文
    const uint8_t *content_has_html_ptr;
    uint16_t content_has_html_len;

    // 正文的charset
    const uint8_t *content_charset_ptr;
    uint16_t content_charset_len;

    // 命令集合
    char    commands[2048];
    uint8_t commands_num;

    const uint8_t *x_ori_ip_ptr;
    uint16_t x_ori_ip_len;

    uint8_t mail_num;  // 邮件份数

    // 发件人邮箱域
    char    mail_from_doms[2048];
    uint8_t mail_from_doms_num;

    // 收件人邮箱域
    char    mail_rcpt_to_doms[2048];
    uint8_t mail_rcpt_to_doms_num;

    // 内容传输编码
    char    body_tra_encs[1024];
    uint8_t body_tra_encs_num;

    // 收件人邮箱， 逗号分号
    char    rcv_mails[2048];
    uint8_t rcv_mails_num;

    // 收件人别名,逗号分开
    char    rcv_aliases[2048];
    uint8_t rcv_aliases_num;

    // 收件人域，逗号分开
    char    rcv_doms[2048];
    uint16_t rcv_doms_num;

    // Subject主题， 逗号分开
    char    subjects[2048];
    uint8_t subjects_num;

    // X-Mailer， 逗号分开
    char    x_mailers[2048];
    uint8_t x_mailers_num;

    // starttls  tls加密标识
    uint8_t starttls_f;

    // login 登录的邮件服务器
    const uint8_t   *login_svr_ptr;
    uint16_t        login_svr_len;

    // host 发件人身份标识
    const uint8_t   *host_ptr;
    uint16_t        host_len;

    // index 请求索引
    const uint8_t   *req_index_ptr;
    uint16_t        req_index_len;

    const uint8_t *mail_received_ptr;
    uint16_t mail_received_len;

    const uint8_t *mail_x_mimeole_ptr;
    uint16_t mail_x_mimeole_len;
};


#endif /* DPI_IMAP_H */
