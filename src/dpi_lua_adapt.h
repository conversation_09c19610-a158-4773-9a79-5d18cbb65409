#ifndef DPI_LUA_ADAPT_H
#define DPI_LUA_ADAPT_H

#include <stdbool.h>

#include <yaProtoRecord/precord.h>

typedef enum dpi_padapt_script_t
{
    DPI_PADAPT_LUA,                 // lua 脚本
    DPI_PADAPT_BYTECODE_LUA,        // 编码后的 lua 脚本
}PadaptScriptType;

bool dpi_padapt_init(pschema_db_t *pschema_db);

/**
* 遍历目录并加载适配脚本，目录支持递归遍历
* 脚本支持:
*     - bytecode 编码脚本
*     - lua  脚本
*/
bool dpi_padapt_load_script(const char * dir);
padapt_engine_t *dpi_padapt_engine_create();

/**************** 通联日志 *********************/
bool dpi_padapt_tll_init(pschema_db_t *pschema_db);
bool dpi_padapt_tll_load_script(const char * dir);
padapt_engine_t *dpi_padapt_tll_engine_create();

/**
* 根据 lua 脚本将 record 适配到指定的 schema
* @param to_record 适配后的 record
* @param from_record 适配前的 record
* @return 0: 成功, <0: 失败
*   若适配失败，to_record 为 NULL
*/
int dpi_padapt_record_adapt(padapt_engine_t *engine, precord_t **to_record, precord_t *from_record);

void dpi_padapt_destroy();
void dpi_padapt_engine_destroy(padapt_engine_t *engine);
void dpi_padapt_print_all_schema();
int dpi_padapt_engine_set_clibs_patch(padapt_engine_t*engine ,char* path);
int  _dpi_pschema_dump_proto_schema_of(pschema_t *schema, const char *pschema_output_dir);
void dpi_pschema_dump_adapt_proto_schemas(const char *pschema_output_dir);

#endif