#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <iostream>
#include <string>
#include "dpi_detect.h"
#include "dpi_sdt_eval_field_callback.h"

extern unsigned char ethernet_packet_B[];

//MATCHER_P2(bytesMatch, bytes, len, "stream_test")
//{
//    return memcmp(arg, bytes, len) == 0;
//}
//
//TEST(basic, test_bytes)
//{
//    uint8_t buff[] =  {0x48, 0x45, 0x4c, 0x4c, 0x4f, 0x00};
//    uint8_t buff2[] = {0x48, 0x45, 0x4c, 0x4c, 0x4e, 0x00};
//    EXPECT_THAT(buff, bytesMatch(buff2, sizeof buff2)) << "请查看输入内容:" << buff;
//}

class eval_udp : public ::testing::Test
{
public:
    void eval_udp_header()
    {
        struct value_type *var=cb_udp_header(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0xcc, *(unsigned char*)var->val);
    }
    void eval_udp_payload()
    {
        struct value_type*var=cb_udp_payload(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0xd0, *(unsigned char*)var->val);


    }
    void eval_udp_payload_len()
    {
        struct value_type*var=cb_udp_payload_length(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(32, (size_t)var->val);
    }
protected:
    void SetUp() override
    {
        memset(&rec, 0, sizeof(rec));
        memset(&pkt, 0, sizeof(pkt));
        pkt.udph = (struct dpi_udphdr*)(&ethernet_packet_B[14+20]);
        pkt.payload = &ethernet_packet_B[14+20+8];
        pkt.payload_len = 32;
        rec.pkt = &pkt;
    }
    void TearDown() override
    {
    }
private:
    struct pkt_info pkt;
    struct ProtoRecord rec;
};

TEST_F(eval_udp, udp_header)
{
    eval_udp_header();
}
TEST_F(eval_udp, udp_payload)
{
    eval_udp_payload();
}
TEST_F(eval_udp, udp_payload_len)
{
    eval_udp_payload_len();
}
