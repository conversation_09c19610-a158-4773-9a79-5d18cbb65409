cmake_minimum_required(VERSION 3.14)
set(TestName "dpisdt_test")
# project
project(${TestName} LANGUAGES C CXX VERSION 0.0.1)

#set(CMAKE_VERBOSE_MAKEFILE on)
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_BUILD_TYPE Debug)

#add_compile_options(-Wall -Wextra -Werror)

#import PkgConfig
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
find_package(PkgConfig REQUIRED)
find_package(GTest REQUIRED)
pkg_check_modules(glib2 REQUIRED IMPORTED_TARGET glib-2.0)
pkg_check_modules(YA_DPDK REQUIRED IMPORTED_TARGET libdpdk)

add_executable(${TestName}
    main.cpp
    packets.cpp
    test_ip_callback.cpp
    test_tcp_callback.cpp
    test_udp_callback.cpp
    test_link_callback.cpp
    test_common_callback.cpp
    dpi_implementation_fake.cpp
)

target_link_directories(${TestName} PRIVATE
        ${CMAKE_SOURCE_DIR}/lib
)

target_compile_options(${TestName} PUBLIC "${dpdk_cflags}")

target_include_directories(${TestName} PRIVATE
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_SOURCE_DIR}/src/input
  ${CMAKE_SOURCE_DIR}/include/libevent
  /usr/include
)


target_include_directories(${TestName}
  PUBLIC ${CMAKE_SOURCE_DIR}/src
  PUBLIC ${PROJECT_SOURCE_DIR}
)

list(APPEND pkg_libaries
  ${yv_sub_STATIC_LDFLAGS}
  ${YA_PROTO_RECORD_STATIC_LDFLAGS}
  ${YA_EMAIL_STATIC_LDFLAGS}
  PkgConfig::xml2
)

target_link_libraries(${TestName}
  -Wl,--whole-archive ${YA_DPDK_STATIC_LDFLAGS} dpisdt -Wl,-Bdynamic -Wl,--no-whole-archive
  ${pkg_libaries}
  yaBasicUtils
  #启用asan需要 启用上级CmakeList中的 EMABLE_MEMORY_CHECK.
  -static-libasan #取消此注释获得静态链接asan.保持注释则获得动态链接asan.
  icuuc
  pthread
  anl
  yasdt
  yasdtacl
  tcp_rsm
  # yarestful
  glib-2.0
  ssl
  crypto
  maxminddb
  z
  cjson
  pthread
  iniparser
  rdkafka
  sdx_rdkafka_producer_consumer
  jsoncpp
  curl
  stdc++
  microxml
  dl
  numa
  pcap
  m
  hs
  mongoose
  # PkgConfig::YA_PROTO_RECORD
  line_convert
  lua
  yaProtoRecord
  yaFtypes
  yv_sub
  muduo_net
  muduo_base
  yaSdxWatch
)
target_link_libraries(${TestName} event.a)
target_link_libraries(${TestName} event_core.a)
target_link_libraries(${TestName} event_extra.a)
target_link_libraries(${TestName} event_pthreads.a)
target_link_libraries(${TestName} glib-2.0)
target_link_libraries(${TestName} gtest)
target_link_libraries(${TestName} gmock)
#  PUBLIC gtest
#  PUBLIC gmock

#add_custom_target(Hello
#  ALL
#  COMMAND ${CMAKE_SOURCE_DIR}/run/${TestName}
#)

