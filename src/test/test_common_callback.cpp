#include <gtest/gtest.h>
#include <iostream>
#include <string>
#include "dpi_detect.h"
#include "dpi_sdt_eval_field_callback.h"
#include "dpi_high_app_protos.h"

extern unsigned char ethernet_packet_A[];
extern struct global_config g_config;

class eval_common : public ::testing::Test
{
public:
#ifdef DPI_SDT_YNAO

#else
    void eval_share_linename1()
    {
        struct value_type*var=cb_share_linename1(&rec);
        const char *p1 = "[JX-20]中俄2号-1-24-E-R-S64F@1-64VC4C";
        ASSERT_TRUE(var);
        const char *v1 = (const char*)var->val;
        int ok = strcmp(p1, v1);
        EXPECT_EQ(0, ok);
    }
    void eval_share_begin_time()
    {
        struct value_type*var=cb_share_begin_time(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1748484837, (size_t)var->val);
    }
    void eval_share_end_time()
    {
        struct value_type*var=cb_share_end_time(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1748484847, (size_t)var->val);
    }
    void eval_share_dru_time()
    {
        struct value_type*var=cb_share_dru_time(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(10, (size_t)var->val);
    }
    void eval_share_ip_ver()
    {
        struct value_type*var=cb_share_ip_ver(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(4, (size_t)var->val);
    }
    void eval_share_src_addr()
    {
        struct value_type*var=cb_share_src_addr(&rec);
        ASSERT_TRUE(var);
        uint32_t ip = (23<<24 | 166 << 16 | 88 << 8 | 114);
        EXPECT_EQ(ip, (size_t)var->val);
    }
    void eval_share_dst_addr()
    {
        struct value_type*var=cb_share_dst_addr(&rec);
        ASSERT_TRUE(var);
        uint32_t ip = (140<<24 | 82 << 16 | 121 << 8 | 3);
        EXPECT_EQ(ip, (size_t)var->val);
    }
    void eval_share_src_port()
    {
        struct value_type*var=cb_share_src_port(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(80, (size_t)var->val);
    }
    void eval_share_dst_port()
    {
        struct value_type*var=cb_share_dst_port(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(5055, (size_t)var->val);
    }
    void eval_share_porto()
    {
        struct value_type*var=cb_share_porto(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(6, (size_t)var->val);
    }
    struct ProtoRecord*ipv6_init()
    {
        static struct flow_info flow;
        static struct ProtoRecord rec;

        memset(&rec, 0, sizeof(rec));
        memset(&flow,0, sizeof(flow));

        rec.flow = &flow;
        flow.ip_version = 6;
        flow.port_src  = 55555;
        flow.port_dst  = 80;

        unsigned char src[] = { 0x24, 0x09, 0x80, 0x1e, 0x03, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x30};
        unsigned char dst[] = { 0x24, 0x09, 0x8a, 0x1e, 0x7b, 0xf0, 0x66, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02};
        flow.tuple.inner.ip_version = 6;
        memcpy(flow.tuple.inner.ip_src,         src, 16);
        memcpy(flow.tuple.inner.ip_dst,         dst, 16);
        memcpy(flow.tuple_reverse.inner.ip_src, dst, 16);
        memcpy(flow.tuple_reverse.inner.ip_dst, src, 16);

        inet_pton(AF_INET6, "2607:f8b0:4006:81d::200e", src);
        inet_pton(AF_INET6, "2001:19f0:200:4d1e::5663", dst);
        flow.tuple.outer.ip_version = 6;
        memcpy(flow.tuple.outer.ip_src,         src, 16);
        memcpy(flow.tuple.outer.ip_dst,         dst, 16);
        memcpy(flow.tuple_reverse.outer.ip_src, dst, 16);
        memcpy(flow.tuple_reverse.outer.ip_dst, src, 16);

        flow.proto_layer_cnt                     = 0;
        flow.proto_layer[flow.proto_layer_cnt++] = DPI_ETH;
        flow.proto_layer[flow.proto_layer_cnt++] = VLAN;
        flow.proto_layer[flow.proto_layer_cnt++] = ETH_P_IP;
        flow.proto_layer[flow.proto_layer_cnt++] = PROTOCOL_TCP;
        flow.proto_layer[flow.proto_layer_cnt++] = PROTOCOL_HTTP;
        flow.real_protocol_id                    = PROTOCOL_HTTP;
        flow.high_app_proto_id                   = HIGH_PROTO_WEIXIN;
        return &rec;
    }
    void eval_share_src_addr_v6()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_src_addr_v6(prec);
        ASSERT_TRUE(var);
        const char *p = "2409:801e:381::830";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_addr_v6()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_dst_addr_v6(prec);
        ASSERT_TRUE(var);
        const char *p = "2409:8a1e:7bf0:66c0::2";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_proto_info()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_proto_info(prec);
        ASSERT_TRUE(var);
        const char *p = "ETH.VLAN.IPv4.TCP.HTTP";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_proto_type()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_proto_type(prec);
        ASSERT_TRUE(var);
        const char *p = "HTTP";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_proto_name()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_proto_name(prec);
        ASSERT_TRUE(var);
        const char *p = "Weixin";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_pkt_num()
    {
        struct value_type*var=cb_share_pkt_num(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(11+22, (size_t)var->val);
    }
    void eval_share_pkt_len()
    {
        struct value_type*var=cb_share_pkt_len(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(44+55, (size_t)var->val);
    }
    void eval_share_etags()
    {
        struct value_type*var=cb_share_etags(&rec);
        ASSERT_FALSE(var);
    }
    void eval_share_ttags()
    {
        struct value_type*var=cb_share_ttags(&rec);
        ASSERT_FALSE(var);
    }
    void eval_share_atags()
    {
        struct value_type*var=cb_share_atags(&rec);
        ASSERT_FALSE(var);
    }
    void eval_share_utags()
    {
        struct value_type*var=cb_share_utags(&rec);
        ASSERT_FALSE(var);
    }
    void eval_share_mpls_lable1()
    {
        struct value_type*var=cb_share_mpls_lable1(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(120, (size_t)var->val);
    }
    void eval_share_mpls_lable2()
    {
        struct value_type*var=cb_share_mpls_lable2(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(121, (size_t)var->val);
    }
    void eval_share_mpls_lable3()
    {
        struct value_type*var=cb_share_mpls_lable3(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(122, (size_t)var->val);
    }
    void eval_share_mpls_lable4()
    {
        struct value_type*var=cb_share_mpls_lable4(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(123, (size_t)var->val);
    }
    void eval_share_vlan_id1()
    {
        struct value_type*var=cb_share_vlan_id1(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(431, (size_t)var->val);
    }
    void eval_share_vlan_id2()
    {
        struct value_type*var=cb_share_vlan_id2(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(432, (size_t)var->val);
    }
    void eval_share_src_mac()
    {
        struct value_type*var=cb_share_src_mac(&rec);
        ASSERT_TRUE(var);
        const char *p = "8c:83:e1:ff:70:fb";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_mac()
    {
        struct value_type*var=cb_share_dst_mac(&rec);
        ASSERT_TRUE(var);
        const char *p = "fc:d7:33:7d:ea:54";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_ountry()
    {
        struct value_type*var=cb_share_src_ountry(&rec);
        ASSERT_TRUE(var);
        const char *p = "China";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_state()
    {
        struct value_type*var=cb_share_src_state(&rec);
        ASSERT_TRUE(var);
        const char *p = "Shanghai";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_city()
    {
        struct value_type*var=cb_share_src_city(&rec);
        ASSERT_TRUE(var);
        const char *p = "Shanghai";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_lon()
    {
        struct value_type*var=cb_share_src_lon(&rec);
        ASSERT_TRUE(var);
        const char *p = "121.46";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_lat()
    {
        struct value_type*var=cb_share_src_lat(&rec);
        ASSERT_TRUE(var);
        const char *p = "31.22";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_isp()
    {
        struct value_type*var=cb_share_src_isp(&rec);
        ASSERT_TRUE(var);
        const char *p = "China Telecom Group";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_src_asn()
    {
        struct value_type*var=cb_share_src_asn(&rec);
        ASSERT_TRUE(var);
        const char *p = "4812";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_ountry()
    {
        struct value_type*var=cb_share_dst_ountry(&rec);
        ASSERT_TRUE(var);
        const char *p = "Germany";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_state()
    {
        struct value_type*var=cb_share_dst_state(&rec);
        ASSERT_TRUE(var);
        const char *p = "Hesse";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_city()
    {
        struct value_type*var=cb_share_dst_city(&rec);
        ASSERT_TRUE(var);
        const char *p = "Frankfurt am Main";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_lon()
    {
        struct value_type*var=cb_share_dst_lon(&rec);
        ASSERT_TRUE(var);
        const char *p = "8.68";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_lat()
    {
        struct value_type*var=cb_share_dst_lat(&rec);
        ASSERT_TRUE(var);
        const char *p = "50.12";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_isp()
    {
        struct value_type*var=cb_share_dst_isp(&rec);
        ASSERT_TRUE(var);
        const char *p = "AMAZON-02";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_asn()
    {
        struct value_type*var=cb_share_dst_asn(&rec);
        ASSERT_TRUE(var);
        const char *p = "16509";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_out_addr_type()
    {
        struct value_type*var=cb_share_out_addr_type(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(4, (size_t)var->val);
    }
    void eval_share_out_src_addr()
    {
        struct value_type*var=cb_share_out_src_addr(&rec);
        ASSERT_TRUE(var);
        uint32_t ip = (142<<24 | 251 << 16 | 40 << 8 | 110);
        EXPECT_EQ(ip, (size_t)var->val);
    }
    void eval_share_out_dst_addr()
    {
        struct value_type*var=cb_share_out_dst_addr(&rec);
        ASSERT_TRUE(var);
        uint32_t ip = (108<<24 | 61 << 16 | 13 << 8 | 174);
        EXPECT_EQ(ip, (size_t)var->val);
    }
    void eval_share_out_ipv6_src()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_out_ipv6_src(prec);
        ASSERT_TRUE(var);
        const char *p = "2607:f8b0:4006:81d::200e";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_out_ipv6_dst()
    {
        struct ProtoRecord*prec=ipv6_init();
        struct value_type*var=cb_share_out_ipv6_dst(prec);
        ASSERT_TRUE(var);
        const char *p = "2001:19f0:200:4d1e::5663";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_out_port_src()
    {
        struct value_type*var=cb_share_out_port_src(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(8344, (size_t)var->val);
    }
    void eval_share_out_port_dst()
    {
        struct value_type*var=cb_share_out_port_dst(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(3306, (size_t)var->val);
    }
    void eval_share_out_proto()
    {
        struct value_type*var=cb_share_out_proto(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(17, (size_t)var->val);
    }
    void eval_share_capture_time()
    {
        struct value_type*var=cb_share_capture_time(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1749026062, (size_t)var->val);
    }
    void eval_share_src_mac_oui()
    {
        struct value_type*var=cb_share_src_mac_oui(&rec);
        ASSERT_TRUE(var);
        const char *p = "SamsungE";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
    void eval_share_dst_mac_oui()
    {
        struct value_type*var=cb_share_dst_mac_oui(&rec);
        ASSERT_TRUE(var);
        const char *p = "Tp-LinkT";
        int ok = strcmp((const char*)var->val, p);
        EXPECT_EQ(0, ok);
    }
#endif
protected:
    void SetUp() override
    {
        g_config.ip_position_switch = 2; //2为优先查询MMDB_CITY库，若未查到值则查询ip2region库
        init_ip_location_db(g_config.ip_position_switch);
        memset(&rec, 0, sizeof(rec));
        memset(&flow,0, sizeof(flow));
        flow.create_time                                = 1748484837000;
        flow.end_time                                   = 1748484847000;
        flow.ip_version                                 = 4;

        //内层
        *(uint32_t*)flow.tuple.inner.ip_src             = (23<<24 | 166 << 16 | 88 << 8 | 114);
        *(uint32_t*)flow.tuple.inner.ip_dst             = (140<<24 | 82 << 16 | 121 << 8 | 3);
        *(uint32_t*)flow.tuple_reverse.inner.ip_src     = (140<<24 | 82 << 16 | 121 << 8 | 3);
        *(uint32_t*)flow.tuple_reverse.inner.ip_dst     = (23<<24 | 166 << 16 | 88 << 8 | 114);
        flow.tuple.inner.ip_version                     = 4;
        flow.tuple.inner.port_src                       = ntohs(80);
        flow.tuple.inner.port_dst                       = ntohs(5055);
        flow.tuple_reverse.inner.port_src               = ntohs(5055);
        flow.tuple_reverse.inner.port_dst               = ntohs(80);
        flow.tuple_reverse.inner.proto                  = 6;
        flow.tuple.inner.proto                          = 6;

        //外层
        *(uint32_t*)flow.tuple.outer.ip_src             = (142<<24 | 251 << 16 | 40 << 8 | 110);
        *(uint32_t*)flow.tuple.outer.ip_dst             = (108<<24 | 61 << 16 | 13 << 8 | 174);
        *(uint32_t*)flow.tuple_reverse.outer.ip_src     = (108<<24 | 61 << 16 | 13 << 8 | 174);
        *(uint32_t*)flow.tuple_reverse.outer.ip_dst     = (142<<24 | 251 << 16 | 40 << 8 | 110);
        flow.tuple.outer.ip_version                     = 4;
        flow.tuple.outer.port_src                       = ntohs(8344);
        flow.tuple.outer.port_dst                       = ntohs(3306);
        flow.tuple_reverse.outer.port_src               = ntohs(3306);
        flow.tuple_reverse.outer.port_dst               = ntohs(8344);
        flow.tuple_reverse.outer.proto                  = 17;
        flow.tuple.outer.proto                          = 17;

        flow.src2dst_packets                            = 11;
        flow.dst2src_packets                            = 22;
        flow.src2dst_payload_len                        = 44;
        flow.dst2src_payload_len                        = 55;
        flow.data_link_layer.mpls_label[0]              = 120;
        flow.data_link_layer.mpls_label[1]              = 121;
        flow.data_link_layer.mpls_label[2]              = 122;
        flow.data_link_layer.mpls_label[3]              = 123;
        flow.data_link_layer.vlan_id[0]                 = 431;
        flow.data_link_layer.vlan_id[1]                 = 432;
        flow.port_src                                   = 5055;
        flow.port_dst                                   = 80;
        static unsigned char packet_bytes_sdt_mac_header[] = {
            0x14, 0xa0, 0xf2, 0xdc, 0xbc, 0xdf, 0x72, 0x10,
            0x06, 0x00, 0x00, 0x46, 0x08, 0x00, 0xbe, 0xc0,
            0x00, 0x8f, 0x02, 0x08, 0x00, 0x00, 0xfa, 0x08,
            0xc0, 0x05, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x31,
            0x00, 0x67, 0x6a, 0x7b, 0x57, 0x00, 0x07, 0x11,
            0x0f, 0x06, 0x0e
        };
        unsigned char packet_bytes_A[] = { 0x8c, 0x83, 0xe1, 0xff, 0x70, 0xfb, 0xFC, 0xD7, 0x33, 0x7d, 0xea, 0x54, 0x08, 0x00};
        unsigned char packet_bytes_B[] = { 0xFC, 0xD7, 0x33, 0x7d, 0xea, 0x54, 0x8c, 0x83, 0xe1, 0xff, 0x70, 0xfb, 0x08, 0x00};
        memcpy(&flow.session_ethhdr[0], packet_bytes_A, sizeof(packet_bytes_A));
        memcpy(&flow.session_ethhdr[1], packet_bytes_B, sizeof(packet_bytes_B));
        flow.pSDTMacHeader[0] = packet_bytes_sdt_mac_header;
        flow.pSDTMacHeader[1] = packet_bytes_sdt_mac_header;
        rec.flow = &flow;
    }
    void TearDown() override
    {
    }
private:
    struct flow_info flow;
    struct ProtoRecord rec;
};

#ifdef DPI_SDT_YNAO

#else
TEST_F(eval_common, eval_share_linename1)
{
    eval_share_linename1();
}
TEST_F(eval_common, eval_share_begin_time)
{
    eval_share_begin_time();
}
TEST_F(eval_common, eval_share_end_time)
{
    eval_share_end_time();
}
TEST_F(eval_common, eval_share_dru_time)
{
    eval_share_dru_time();
}
TEST_F(eval_common, eval_share_ip_ver)
{
    eval_share_ip_ver();
}
TEST_F(eval_common, eval_share_src_addr)
{
    eval_share_src_addr();
}
TEST_F(eval_common, eval_share_dst_addr)
{
    eval_share_dst_addr();
}
TEST_F(eval_common, eval_share_src_port)
{
    eval_share_src_port();
}
TEST_F(eval_common, eval_share_dst_port)
{
    eval_share_dst_port();
}
TEST_F(eval_common, eval_share_porto)
{
    eval_share_porto();
}
TEST_F(eval_common, eval_share_src_addr_v6)
{
    eval_share_src_addr_v6();
}
TEST_F(eval_common, eval_share_dst_addr_v6)
{
    eval_share_dst_addr_v6();
}
TEST_F(eval_common, eval_share_proto_info)
{
    eval_share_proto_info();
}
TEST_F(eval_common, eval_share_proto_type)
{
    eval_share_proto_type();
}
TEST_F(eval_common, eval_share_proto_name)
{
    eval_share_proto_name();
}
TEST_F(eval_common, eval_share_pkt_num)
{
    eval_share_pkt_num();
}
TEST_F(eval_common, eval_share_pkt_len)
{
    eval_share_pkt_len();
}
TEST_F(eval_common, eval_share_etags)
{
    eval_share_etags();
}
TEST_F(eval_common, eval_share_ttags)
{
    eval_share_ttags();
}
TEST_F(eval_common, eval_share_atags)
{
    eval_share_atags();
}
TEST_F(eval_common, eval_share_utags)
{
    eval_share_utags();
}
TEST_F(eval_common, eval_share_mpls_lable1)
{
    eval_share_mpls_lable1();
}
TEST_F(eval_common, eval_share_mpls_lable2)
{
    eval_share_mpls_lable2();
}
TEST_F(eval_common, eval_share_mpls_lable3)
{
    eval_share_mpls_lable3();
}
TEST_F(eval_common, eval_share_mpls_lable4)
{
    eval_share_mpls_lable4();
}
TEST_F(eval_common, eval_share_vlan_id1)
{
    eval_share_vlan_id1();
}
TEST_F(eval_common, eval_share_vlan_id2)
{
    eval_share_vlan_id2();
}
TEST_F(eval_common, eval_share_src_mac)
{
    eval_share_src_mac();
}
TEST_F(eval_common, eval_share_dst_mac)
{
    eval_share_dst_mac();
}
TEST_F(eval_common, eval_share_src_ountry)
{
    eval_share_src_ountry();
}
TEST_F(eval_common, eval_share_src_state)
{
    eval_share_src_state();
}
TEST_F(eval_common, eval_share_src_city)
{
    eval_share_src_city();
}
TEST_F(eval_common, eval_share_src_lon)
{
    eval_share_src_lon();
}
TEST_F(eval_common, eval_share_src_lat)
{
    eval_share_src_lat();
}
TEST_F(eval_common, eval_share_src_isp)
{
    eval_share_src_isp();
}
TEST_F(eval_common, eval_share_src_asn)
{
    eval_share_src_asn();
}
TEST_F(eval_common, eval_share_dst_ountry)
{
    eval_share_dst_ountry();
}
TEST_F(eval_common, eval_share_dst_state)
{
    eval_share_dst_state();
}
TEST_F(eval_common, eval_share_dst_city)
{
    eval_share_dst_city();
}
TEST_F(eval_common, eval_share_dst_lon)
{
    eval_share_dst_lon();
}
TEST_F(eval_common, eval_share_dst_lat)
{
    eval_share_dst_lat();
}
TEST_F(eval_common, eval_share_dst_isp)
{
    eval_share_dst_isp();
}
TEST_F(eval_common, eval_share_dst_asn)
{
    eval_share_dst_asn();
}
TEST_F(eval_common, eval_share_out_addr_type)
{
    eval_share_out_addr_type();
}
TEST_F(eval_common, eval_share_out_src_addr)
{
    eval_share_out_src_addr();
}
TEST_F(eval_common, eval_share_out_dst_addr)
{
    eval_share_out_dst_addr();
}
TEST_F(eval_common, eval_share_out_ipv6_src)
{
    eval_share_out_ipv6_src();
}
TEST_F(eval_common, eval_share_out_ipv6_dst)
{
    eval_share_out_ipv6_dst();
}
TEST_F(eval_common, eval_share_out_port_src)
{
    eval_share_out_port_src();
}
TEST_F(eval_common, eval_share_out_port_dst)
{
    eval_share_out_port_dst();
}
TEST_F(eval_common, eval_share_out_proto)
{
    eval_share_out_proto();
}
TEST_F(eval_common, eval_share_capture_time)
{
    eval_share_capture_time();
}
TEST_F(eval_common, eval_share_src_mac_oui)
{
    eval_share_src_mac_oui();
}
TEST_F(eval_common, eval_share_dst_mac_oui)
{
    eval_share_dst_mac_oui();
}
#endif
