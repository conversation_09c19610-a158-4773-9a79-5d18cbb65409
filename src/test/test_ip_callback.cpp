#include <gtest/gtest.h>
#include <iostream>
#include <string>
#include "dpi_detect.h"
#include "dpi_sdt_eval_field_callback.h"

extern unsigned char ethernet_packet_A[];

class eval_ip : public ::testing::Test
{
public:
    void eval_ip_flag()
    {
        struct value_type *var=cb_ip_flag(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0x02, (size_t)var->val);
    }
    void eval_ip_len()
    {
        struct value_type *var=cb_ip_len(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1317, (size_t)var->val);
    }
    void eval_ip_header()
    {
        struct value_type *var=cb_ip_header(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0x45, *(unsigned char*)var->val);
    }
    void eval_ip_ttl()
    {
        struct value_type *var=cb_ip_ttl(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(128, (size_t)var->val);
    }
    void eval_ip_payload()
    {
        struct value_type *var=cb_ip_payload(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(0x15, *(unsigned char*)var->val);
    }
    void eval_ip_payload_len()
    {
        struct value_type *var=cb_ip_payload_len(&rec);
        ASSERT_TRUE(var);
        EXPECT_EQ(1297, (size_t)var->val);
    }
protected:
    void SetUp() override
    {
        memset(&rec, 0, sizeof(rec));
        memset(&pkt, 0, sizeof(pkt));
        pkt.iph4 = (struct dpi_iphdr*)(&ethernet_packet_A[14]);
        rec.pkt = &pkt;
    }
    void TearDown() override
    {
    }
private:
    struct pkt_info pkt;
    struct ProtoRecord rec;
};

TEST_F(eval_ip, ip_flag)
{
    eval_ip_flag();
}
TEST_F(eval_ip, ip_len)
{
    eval_ip_len();
}
TEST_F(eval_ip, ip_header)
{
    eval_ip_header();
}
TEST_F(eval_ip, ip_ttl)
{
    eval_ip_ttl();
}
TEST_F(eval_ip, ip_payload)
{
    eval_ip_payload();
}
TEST_F(eval_ip, ip_payload_len)
{
    eval_ip_payload_len();
}
