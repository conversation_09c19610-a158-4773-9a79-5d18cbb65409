#ifndef _DPI_SDX_COMMON_
#define _DPI_SDX_COMMON_

#include "iniparser/dictionary.h"
#include "iniparser/iniparser.h"

#include "dpi_common.h"

#define   SDX_MAX_PORT_NUM    10
#define   SDX_IP_ADDR_SZIE    32


struct sdx_config_variable
{
    uint8_t      sdx_mac_packet_header_flag;
    uint8_t      sdx_server_switch;
    uint8_t      sdx_share_header_switch;
    uint8_t      sdx_match_algorithm;

    uint8_t      sdx_stat_program_mode;
    uint8_t      sdx_reflect_rule;
    int          sdx_stat_report_time;
    char         sdx_stat_web_addr[64];
    char         sdx_stat_case_name[64];

    uint8_t      sdx_rx_port_num;
    uint8_t      sdx_tx_port_num;
    uint8_t      sdx_rx_port_list[SDX_MAX_PORT_NUM];
    uint8_t      sdx_tx_port_list[SDX_MAX_PORT_NUM];

    uint16_t     sdx_rule_listen_port;
    uint8_t      sdx_rule_web_addr[64];

    uint16_t     sdx_web_config_listen_port;
    char         sdx_web_config_addr[64];
    char         sdx_web_config_topic_name[64];
    char         sdx_ip_interface[64];         /* 设备通信接口，用来通过此接口获取 ip 来标识该机器 */
    char         sdx_ip_str[64];
    uint32_t     sdx_ip_number;

    uint8_t      dump_pcap_with_sdx_mac;
};


struct web_protocol_config{
    uint8_t  outputtype;
    uint8_t  sample;
    uint16_t flowtimeout;
    uint16_t flowtimeout_total;
};


struct web_config_variable
{
    uint8_t print_switch;
    struct web_protocol_config protocal_list[PROTOCOL_MAX];

    
    uint32_t cycle;

    char     file_path[COMMON_FILE_PATH];
    uint32_t file_size;
    uint32_t file_trunction;
    
    char kafka_service_ip[SDX_IP_ADDR_SZIE];
    char nfs_service_ip[SDX_IP_ADDR_SZIE];

    char mac_addr_dst[SDX_IP_ADDR_SZIE];
    char mac_addr_src[SDX_IP_ADDR_SZIE];

    uint32_t json_size;
    uint32_t json_truncation;

    uint32_t pcap_size;
    uint32_t pcap_truncation;

    char data_from[COMMON_SOME_TYPE];
    char sig_type[COMMON_SOME_TYPE];
    char sys_from[COMMON_SOME_TYPE];



    uint32_t duration;
    uint32_t traffic;
    uint32_t dynamic_old_time;
};



int dpi_sdx_init_config(dictionary *ini, struct sdx_config_variable *sdx_config);
int dpi_sdx_config_after_init_nic(dictionary *ini, struct sdx_config_variable *sdx_config);


int init_stat_restful_server(struct sdx_config_variable sdx_config);

int dpi_web_init_config(struct web_config_variable  *web_config);

void dpi_web_config_printf(struct web_config_variable  *web_config);


int sdx_convert_linename(const uint8_t line_no[], char *line_name, uint32_t HW[4]);

#endif
