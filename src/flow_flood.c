#ifndef _GNU_SOURCE
#define _GNU_SOURCE         /* See feature_test_macros(7) */
#endif

#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <libgen.h>
#include <arpa/inet.h>
#include <net/ethernet.h>
#include <netinet/ip.h>
#include <netinet/ip6.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>

#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <libgen.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <string.h>

#include <pcap/pcap.h>
#include <glib.h>
#include "jhash.h"
#include "flow_flood.h"


////////// STRUCT //////////////////
struct session_key_t;
struct flood_context_t;
struct pkt_ctx_t;
struct session_ctx_t;

struct flood_context_t
{
    GList *list;
    GList *iter;
    int total_pkts;
    int loop;
    int (*cb_pkt)(unsigned char *p, int l, void *user);
    void *user;
};

struct pkt_ctx_t
{
    struct flood_pkt_t      pkt;
    int                     C2S;
    int                     af;
    uint32_t               *client_ip;
    uint32_t               *server_ip;
    struct flood_context_t *ctx;
};
////////// STRUCT //////////////////

int tcp_pkt_enqueue(struct pkt_ctx_t *pkt_ctx, const unsigned char *p, int l)
{
    const struct tcphdr   *tcphdr = (const struct tcphdr*)p;
    uint32_t src    =   ntohs(tcphdr->source);
    uint32_t dst    =   ntohs(tcphdr->dest);
    pkt_ctx->C2S    = src > dst;
    return 0;
}

int udp_pkt_enqueue(struct pkt_ctx_t *pkt_ctx, const unsigned char *p, int l)
{
    const struct udphdr   *udphdr = (const struct udphdr*)p;
    uint32_t src    =   ntohs(udphdr->source);
    uint32_t dst    =   ntohs(udphdr->dest);
    pkt_ctx->C2S    = src > dst;
    return 0;
}

int pkt_ipv6(struct pkt_ctx_t *pkt_ctx, unsigned char *p, int l)
{
    struct ip6_hdr *ip6 = (struct ip6_hdr*)p;
    int  ip6_hdr_size    = sizeof(struct ip6_hdr);
    int ipv6_payload_len = ip6->ip6_ctlun.ip6_un1.ip6_un1_plen ? ntohs(ip6->ip6_ctlun.ip6_un1.ip6_un1_plen) - ip6_hdr_size : l - ip6_hdr_size;
    pkt_ctx->af = AF_INET6;
    switch(ip6->ip6_ctlun.ip6_un1.ip6_un1_nxt)
    {
        case IPPROTO_TCP:
            {
                unsigned char *tcphdr   = p + ip6_hdr_size;
                tcp_pkt_enqueue(pkt_ctx, tcphdr, ipv6_payload_len);
             }
             break;

        case IPPROTO_UDP:
            {
                unsigned char *tcphdr   = p + ip6_hdr_size;
                udp_pkt_enqueue(pkt_ctx, tcphdr, ipv6_payload_len);
            }
             break;
        case IPPROTO_ICMP:
        case IPPROTO_ICMPV6:
        case IPPROTO_IGMP:
        case IPPROTO_HOPOPTS:
            return l;
            break;

        default:
            printf("WARN: 未知的协议类型 in ipv6 on pkt %u len %u\n", pkt_ctx->ctx->total_pkts, l);
    }
    pkt_ctx->client_ip = pkt_ctx->C2S ? (uint32_t*)&ip6->ip6_src : (uint32_t*)&ip6->ip6_dst;
    pkt_ctx->server_ip = pkt_ctx->C2S ? (uint32_t*)&ip6->ip6_dst : (uint32_t*)&ip6->ip6_src;
    return ipv6_payload_len;
}

int pkt_ipv4(struct pkt_ctx_t *pkt_ctx, unsigned char *p, int l)
{
    const struct iphdr *ip4 = (const struct iphdr*)p;
    int ip_hdr_size      = ip4->ihl*4;
    int ipv4_payload_len = ip4->tot_len ? (int)(ntohs(ip4->tot_len) - ip_hdr_size) : (int)(l - ip_hdr_size);
    pkt_ctx->af = AF_INET;
    switch(ip4->protocol)
    {
        case IPPROTO_TCP:
            {
                const unsigned char *tcphdr  = p + ip_hdr_size;
                tcp_pkt_enqueue(pkt_ctx, tcphdr, ipv4_payload_len);
            }
            break;

        case IPPROTO_UDP:
            {
                const unsigned char *tcphdr  = p + ip_hdr_size;
                udp_pkt_enqueue(pkt_ctx, tcphdr, ipv4_payload_len);
            }
            break;
        case IPPROTO_ICMP:
        case IPPROTO_IGMP:
            return l;
            break;

        default:
            printf("WARN: 未知的协议类型 in ipv4 on pkt %u len %u\n", pkt_ctx->ctx->total_pkts, l);
    }

    pkt_ctx->client_ip = pkt_ctx->C2S ? (uint32_t*)&ip4->saddr : (uint32_t*)&ip4->daddr;
    pkt_ctx->server_ip = pkt_ctx->C2S ? (uint32_t*)&ip4->daddr : (uint32_t*)&ip4->saddr;
    return ipv4_payload_len;
}

int pkt_trailer(struct pkt_ctx_t *pkt_ctx, const unsigned char *p, int l)
{
    //printf("pkt_trailer len=%u\n", l);
    return 0;
}

int pkt_raw(struct pkt_ctx_t *pkt_ctx, unsigned char *p, int l)
{
    unsigned char version = *p;
    int     offset                    = 0;
    version >>=4;

    switch(version)
    {
        case 4:
            offset += pkt_ipv4(pkt_ctx, p, l);
            break;

        case 6:
            offset += pkt_ipv6(pkt_ctx, p, l);
            break;

        default:
            printf("这是什么东西\n");
            abort();
    }
    return offset;
}

int pkt_ether(struct pkt_ctx_t *pkt_ctx, unsigned char *p, int l)
{
    struct  ether_header*ether_header = NULL;
    int     packet_type               = 0;
    int     offset                    = 0;

    if(l < (int)sizeof(struct ether_header))
    {
        printf("ERROR: pkt_ether length\n");
        return -1;
    }

    ether_header = (struct ether_header *) p;
    packet_type = ntohs(ether_header->ether_type);

    if(packet_type < 1500)
    {
        return 0;
    }

    switch(packet_type)
    {
        case 0x0800:
            {
                unsigned char *iphdr = p + sizeof(struct ether_header);
                int   iplen = l - sizeof(struct ether_header);
                offset += pkt_ipv4(pkt_ctx, iphdr, iplen);
            }
            break;

        case 0x86dd:
            {
                unsigned char *iphdr = p + sizeof(struct ether_header);
                int   iplen = l - sizeof(struct ether_header);
                offset += pkt_ipv6(pkt_ctx, iphdr, iplen);
            }
            break;

        case 0x8100:    //VLAN
            printf("暂不支持VLAN\n");
            break;

        case 0x0806:    //ARP
        case 0x88CC:    //LLDP
        case 0x8847:    //MPLS
        case 0x9998:    //HuaWei private
            break;


        default:
            printf("WARN: ETH type=%04X\n", packet_type);
            return -1;
    }

    offset += sizeof(struct ether_header);
    if(offset < l)
    {
        const unsigned char *trailerhdr = p + offset;
        int         trailerlen = l - offset;
        pkt_trailer(pkt_ctx, trailerhdr, trailerlen);
    }
    return 0;
}

int cb_pkt_pcap(struct pkt_ctx_t *pkt_ctx, int frame_nm, unsigned char *p, int l)
{
    pkt_ctx->ctx->cb_pkt(p, l, pkt_ctx->ctx->user);
    return 0;
}

int cb_pkt_arrived(struct pkt_ctx_t *pkt_ctx, int frame_nm, unsigned char *p, int l)
{
    int    ret                     = 0;
    struct flood_context_t   *ctx   = pkt_ctx->ctx;
    ctx->total_pkts++;
    switch(pkt_ctx->pkt.datalink)
    {
        case DLT_EN10MB:
            ret = pkt_ether(pkt_ctx, p, l);
            break;
        case DLT_RAW:
            ret = pkt_raw(pkt_ctx, p, l);
            break;
        default:
            printf("你的PCAP 不是 DLT_EN10MB\n");
            printf("你的PCAP 不是 DLT_RAW\n");
            printf("支持的类型受限\n");
            abort();
            break;
    }
    if(ret < 0)
    {
        printf("ERROR: in pkt_ether\n");
        return -1;
    }
    pkt_ctx->ctx->list        = g_list_append(pkt_ctx->ctx->list, pkt_ctx);
    return 0;
}

int open_pcap_file(const char *filename, int (*func)(struct pkt_ctx_t *pkt, int frame, unsigned char *ptr, int len), struct flood_context_t *ctx)
{
    char errbuf[PCAP_ERRBUF_SIZE];
    uint32_t frame_num = 0;
    printf("open file %s\n", filename);
    pcap_t *file_pcap = pcap_open_offline(filename, errbuf);
    if(NULL == file_pcap)
    {
        printf("error pcap:[%s]\n", errbuf);
        return -1;
    }

    const unsigned char *pkt = NULL;
    struct pcap_pkthdr pkthdr;
    while((pkt = pcap_next(file_pcap, &pkthdr)))
    {
        unsigned char *ptr = malloc(pkthdr.len);
        memcpy(ptr, pkt, pkthdr.len);

        struct pkt_ctx_t *p = malloc(sizeof(struct pkt_ctx_t));
        memset(p, 0, sizeof(struct pkt_ctx_t));

        p->ctx              = ctx;
        p->pkt.pkt_ptr      = ptr;
        p->pkt.pkt_len      = pkthdr.len;
        p->pkt.datalink     = pcap_datalink(file_pcap);
        func(p, ++frame_num, p->pkt.pkt_ptr, p->pkt.pkt_len);
    }
    pcap_close(file_pcap);
    return 0;
}

int cb_filenum(const char *filename, void *user)
{
    return (*(int*)user)++;
}

int cb_filelist(const char *filename, void *user)
{
    int i = 0;
    const char**filelist = user;
    while(filelist[i++]);
    filelist[i-1] = strdup(filename);
    return 0;
}

static int listdir(const char *dirname, int (*cb)(const char *filename, void *user), void *user)
{
    DIR *dir = NULL;
    struct dirent *entry = NULL;
    char   path[1024] = {0};

    if (NULL == dirname || !(dir = opendir(dirname)))
    {
        perror(dirname);
        return -1;
    }

    while ((entry = readdir(dir)) != NULL)
    {
        memset(path, 0, sizeof(path));
        //strip filepath with //
        if('/' == *(dirname + strlen(dirname) -1))
        {
            snprintf(path, sizeof(path), "%s%s", dirname, entry->d_name);
        }
        else
        {
            snprintf(path, sizeof(path), "%s/%s", dirname, entry->d_name);
        }
        if (entry->d_type == DT_DIR)
        {
            if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
            {
                continue;
            }
            //目录
            listdir(path, cb, user);
        }
        else
        {
            //文件
            cb(path,  user);
        }
    }
    closedir(dir);
    return 0;
}

/*对文件排序 */
static int filelist_sort(const char **filename, int num)
{
    int i = 0;
    int j = 0;
    for(i = 0; i < num; i++)
    {
        for(j = 0; j < num; j++)
        {
            if(strverscmp(filename[i], filename[j]) < 0)
            {
                const char *name    =   filename[i];
                filename[i]         =   filename[j];
                filename[j]         =   name;
            }
        }
    }
    return 0;
}

static int file_ISREG(const char *path)
{
    struct stat path_stat;
    stat(path, &path_stat);
    return S_ISREG(path_stat.st_mode);
}

static int file_ISDIR(const char *path)
{
    struct stat path_stat;
    stat(path, &path_stat);
    return S_ISDIR(path_stat.st_mode);
}

int free_filelist(char **filelist)
{
    int i = 0;
    while(filelist[i])
    {
        free(filelist[i]);
        i++;
    }
    free(filelist);
    return 0;
}

int recurrence_dir(const char *filename, int (*func)(struct pkt_ctx_t *pkt_ctx, int frame, unsigned char *pkt, int len), struct flood_context_t *ctx)
{
    //如果是文件 直接打开
    if(file_ISREG(filename))
    {
        open_pcap_file(filename, func, ctx);
    }
    else
    //如果是目录 先排序再打开
    if(file_ISDIR(filename))
    {
        int   filenum = 0;
        int   i = 0;

        //获取文件数量
        listdir(filename, cb_filenum,  &filenum);

        char **filelist = malloc(sizeof(char*) * (filenum +1)); // +1 for NULL
        memset(filelist, 0, sizeof(char*) * (filenum +1));

        //获取文件名
        listdir(filename, cb_filelist, filelist);

        //开始排序
        filelist_sort((const char**)filelist, filenum);

        //顺次解析
        for(i = 0; filelist[i]; i++)
        {
            filename = filelist[i];
            open_pcap_file(filename, func, ctx);
        }
        free_filelist(filelist);
    }
    return 0;
}

int read_pcap(const char *dirname, int cb_pkt(unsigned char *p, int l, void *user), void *user)
{
    struct flood_context_t *ctx = malloc(sizeof(struct flood_context_t));
    memset(ctx, 0, sizeof(struct flood_context_t));
    ctx->cb_pkt = cb_pkt;
    ctx->user   = user;
    recurrence_dir(dirname, cb_pkt_pcap, ctx);
    flow_flood_free(ctx);
    return 0;
}

struct flood_context_t*
flow_flood_init(const char *filename)
{
    struct flood_context_t *ctx = malloc(sizeof(struct flood_context_t));
    memset(ctx, 0, sizeof(struct flood_context_t));
    recurrence_dir(filename, cb_pkt_arrived, ctx);
    printf("报文总数 %u\n", g_list_length(ctx->list));
    return ctx;
}

struct flood_pkt_t*
flow_flood_next(struct flood_context_t *ctx)
{
    if(0 == ctx->total_pkts)
    {
        printf("ERROR: 无效的PCAP目录\n");
        abort();
    }
    if(NULL == ctx->iter)
    {
        //printf("总包数%u, 第%u轮\n", ctx->total_pkts, ctx->loop++);
        ctx->iter = ctx->list;
    }
    struct pkt_ctx_t *p = ctx->iter->data;
    //存在某些协议 不是TCP/UDP 没有会话
    if(p->client_ip)
    {
        p->client_ip[0] = ntohl(ntohl(p->client_ip[0])+1);
    }
    ctx->iter = ctx->iter->next;
    if(p->client_ip && 0)
    {
        char buff_C[128];
        char buff_S[128];
        inet_ntop(p->af, p->client_ip, buff_C, sizeof(buff_C));
        inet_ntop(p->af, p->server_ip, buff_S, sizeof(buff_S));
        printf("server_ip %s server_ip %s\n", buff_S, buff_C);
    }
    return &p->pkt;
}

void pkt_free(gpointer data)
{
    struct pkt_ctx_t *pkt = (struct pkt_ctx_t*)data;
    free(pkt->pkt.pkt_ptr);
    free(pkt);
}

void
flow_flood_free(struct flood_context_t *ctx)
{
    g_list_free_full(ctx->list, pkt_free);
}

//int main(int a, const char *argv[])
//{
//    struct flood_context_t *ctx = flow_flood_init(argv[1]);
//    struct flood_pkt_t *pkt = NULL;
//    int loop = 1000;
//    while((pkt = flow_flood_next(ctx)) && loop--)
//    {
//        char buff[128];
//        inet_ntop(pkt->af, pkt->client_ip, buff, sizeof(buff));
//        printf("client_ip %s\n", buff);
//    }
//    flow_flood_free(ctx);
//    return 0;
//}
