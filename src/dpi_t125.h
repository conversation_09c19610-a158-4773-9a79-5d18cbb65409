/****************************************************************************************
 * 文 件 名 : dpi_t125.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/11/22
编码: wangy                2018/11/22
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_T125_H_
#define  _DPI_T125_H_

#include <stdint.h>
#include "dpi_common.h"
#include "dpi_tpkt.h"

struct t125_info{
    int noused;    
};

int dissect_t125(struct dpi_pkt_st* pkt, uint32_t offset, struct t125_info* local_info, rdp_info* info);
int dissect_t125_heur(struct dpi_pkt_st* pkt, uint32_t offset, struct t125_info* local_info, rdp_info* info);


#endif // !_DPI_T125_H_