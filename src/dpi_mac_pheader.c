
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "dpi_detect.h"

#include "sdtapp_interface.h"
#include "dpi_mac_pheader.h"


extern struct rte_mempool *tbl_log_mempool;


dpi_field_table  mac_pheader_field_array[] = {

    DPI_FIELD_D(EM_MAC_PHEADER_GLOBAL_LINE_NO,              YA_FT_STRING,       "global_lineNo"),
    DPI_FIELD_D(EM_MAC_PHEADER_LINE_TYPE,                   YA_FT_STRING,       "line_type"),
    DPI_FIELD_D(EM_MAC_PHEADER_POS_TYPE,                    YA_FT_STRING,       "pos_type"),
    DPI_FIELD_D(EM_MAC_PHEADER_SYSNO_DEVICE_TYPE,           YA_FT_STRING,       "sysNo_device_type"),
    DPI_FIELD_D(EM_MAC_PHEADER_DEVICE_SEQ,                  YA_FT_STRING,       "device_seq"),
    DPI_FIELD_D(EM_MAC_PHEADER_BOARD_TYPE_SEQ,              YA_FT_STRING,       "board_type_seq"),
    DPI_FIELD_D(EM_MAC_PHEADER_SENDPORT_RESERVE,            YA_FT_STRING,       "sendport_reserve"),
    DPI_FIELD_D(EM_MAC_PHEADER_DSTMAC,                      YA_FT_STRING,       "dstmac"),
    DPI_FIELD_D(EM_MAC_PHEADER_SRCMAC,                      YA_FT_STRING,       "srcmac"),
    DPI_FIELD_D(EM_MAC_PHEADER_WPROTOCOL,                   YA_FT_STRING,       "wprotocol"),
    DPI_FIELD_D(EM_MAC_PHEADER_MAC_SEQ,                     YA_FT_STRING,       "mac_seq"),
    DPI_FIELD_D(EM_MAC_PHEADER_DATATYPE,                    YA_FT_STRING,       "datatype"),
    DPI_FIELD_D(EM_MAC_PHEADER_WDATALEN,                    YA_FT_STRING,       "wdatalen"),
    DPI_FIELD_D(EM_MAC_PHEADER_TIMESTAMP,                   YA_FT_STRING,       "timestamp"),
    DPI_FIELD_D(EM_MAC_PHEADER_BLINKTYPE,                   YA_FT_STRING,       "blinktype"),
    DPI_FIELD_D(EM_MAC_PHEADER_BLINKLEN,                    YA_FT_STRING,       "blinklen"),
};



static int *mac_pheader_reflect_array=NULL; /* 数组顺序表示客户字段顺序，数组中存储的索引位置，表示原有字段顺序*/
static int mac_pheader_reflect_array_num=0;

static int mac_pheader_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct mac_packet_header *info, int *idx, int i)
{
    char _str[64]={0};
    switch(i){
    case EM_MAC_PHEADER_GLOBAL_LINE_NO:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, info->Datasrc.Global_LineNO, sizeof(info->Datasrc.Global_LineNO));
        break;
    case EM_MAC_PHEADER_LINE_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT16, NULL, info->Datasrc.LineType);
        break;
    case EM_MAC_PHEADER_POS_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->Datasrc.PosType);
        break;
    case EM_MAC_PHEADER_SYSNO_DEVICE_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->Datasrc.SysNO_DeviceType);
        break;
    case EM_MAC_PHEADER_DEVICE_SEQ:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->Datasrc.DeviceSeq);
        break;
    case EM_MAC_PHEADER_BOARD_TYPE_SEQ:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->Datasrc.Board_Type_Seq);
        break;
    case EM_MAC_PHEADER_SENDPORT_RESERVE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->Datasrc.SendPort_Reserve);
        break;

    case EM_MAC_PHEADER_DSTMAC:
        snprintf(_str, 64, "%02x:%02x:%02x:%02x:%02x:%02x",info->dstMac[0],info->dstMac[1],info->dstMac[2],
                                                           info->dstMac[3],info->dstMac[4],info->dstMac[5]);
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (uint8_t *)_str, strlen(_str));
        break;
    case EM_MAC_PHEADER_SRCMAC:
        snprintf(_str, 64, "%02x:%02x:%02x:%02x:%02x:%02x",info->srcMac[0],info->srcMac[1],info->srcMac[2],
                                                           info->srcMac[3],info->srcMac[4],info->srcMac[5]);
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (uint8_t *)_str, strlen(_str));
        break;
    case EM_MAC_PHEADER_WPROTOCOL:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT16, NULL, info->wProtocol);
        break;
    case EM_MAC_PHEADER_MAC_SEQ:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->mac_Seq);
        break;
    case EM_MAC_PHEADER_DATATYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->DataType);
        break;


    case EM_MAC_PHEADER_WDATALEN:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT16, NULL, info->wDataLen);
        break;


    case EM_MAC_PHEADER_TIMESTAMP:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, info->TimeStamp, sizeof(info->TimeStamp));
        break;
    case EM_MAC_PHEADER_BLINKTYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->bLinkType);
        break;
    case EM_MAC_PHEADER_BLINKLEN:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT8, NULL, info->bLinkLen);
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }
    
    return 0;
}


static int write_mac_pheader_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int idx = 0;
    struct tbl_log *log_ptr;

    struct mac_packet_header *info=(struct mac_packet_header *)field_info;
    if(!info){

        return 0;
    }
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    int i;
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "mac_pheader");

    for(i=0;i<EM_MAC_PHEADER_MAX;i++){
        mac_pheader_field_element(log_ptr,flow, direction, info, &idx, i);
    }
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_MAC_PHEADER;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}

static void init_mac_pheader_dissector(void)
{
    //dpi_register_proto_schema(mac_pheader_field_array,EM_MAC_PHEADER_MAX,"mac_pheader");
    map_fields_info_register(mac_pheader_field_array, PROTOCOL_LINK, EM_MAC_PHEADER_MAX,"mac_pheader");
    return;
}

static __attribute((constructor)) void     before_init_mac_pheader(void){
    register_tbl_array(TBL_LOG_MAC_PHEADER, 0, "mac_pheader", init_mac_pheader_dissector);
}
