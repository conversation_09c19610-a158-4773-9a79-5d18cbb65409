﻿/****************************************************************************************
 * 文 件 名 : dpi_tll.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *修改: hongll  2022/06/17
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <yaFtypes/ftypes.h>
#include <yaFtypes/fvalue.h>

#include "dpi_log.h"
#include "dpi_pschema.h"
#include "dpi_tbl_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tll.h"
#include "dpi_sdt_link.h"

extern struct rte_mempool *tbl_log_mempool;
extern dpi_field_table  link_field_array[EM_LINK_MAX];

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];


#define _BIG_ENDIAN			(0x00000000)
#define _LIT_ENDIAN			(0x80000000)

#define HAS_TLL			(1 << 0)


typedef enum _tll_enum_index {
	EM_TLL_PROTOCOL,
	EM_TLL_NETWORKID,
	EM_TLL_STARTTIME,
	EM_TLL_ENDTIME,
	EM_TLL_PKTALL_C2S,
	EM_TLL_PKTALL_S2C,
	EM_TLL_OCTETSALL_C2S,
	EM_TLL_OCTETSALL_S2C,
	EM_TLL_LINKNO_COUNT,
	EM_TLL_LINKNO1_C2S,
	EM_TLL_LINKNO2_C2S,
	EM_TLL_LINKNO3_C2S,
	EM_TLL_LINKNO4_C2S,
	EM_TLL_LINKNO1_S2C,
	EM_TLL_LINKNO2_S2C,
	EM_TLL_LINKNO3_S2C,
	EM_TLL_LINKNO4_S2C,
	EM_TLL_LINKNO_PKTSLINE_C2S,
	EM_TLL_LINKNO_PKTSLINE_S2C,
	EM_TLL_LINKNO_OCTETSLINE_C2S,
	EM_TLL_LINKNO_OCTETSLINE_S2C,
  EM_TTL_DIRECTION,

	EM_TLL_MAX
} tll_enum_index;

static dpi_field_table tll_field_array[] = {
	DPI_FIELD_D(EM_TLL_PROTOCOL,						EM_F_TYPE_NULL,		"protocol"),
	DPI_FIELD_D(EM_TLL_NETWORKID,						EM_F_TYPE_NULL,		"networkid"),
	DPI_FIELD_D(EM_TLL_STARTTIME,						EM_F_TYPE_NULL,		"starttime_s"),
	DPI_FIELD_D(EM_TLL_STARTTIME,						EM_F_TYPE_NULL,		"starttime_ms"),
	DPI_FIELD_D(EM_TLL_ENDTIME,						    EM_F_TYPE_NULL,		"endtime_s"),
	DPI_FIELD_D(EM_TLL_ENDTIME,						    EM_F_TYPE_NULL,		"endtime_ms"),
	DPI_FIELD_D(EM_TLL_PKTALL_C2S,						EM_F_TYPE_NULL,		"pktall_c2s"),
	DPI_FIELD_D(EM_TLL_PKTALL_S2C,						EM_F_TYPE_NULL,		"pktall_s2c"),
	DPI_FIELD_D(EM_TLL_OCTETSALL_C2S,					EM_F_TYPE_NULL,		"octetsall_c2s"),
	DPI_FIELD_D(EM_TLL_OCTETSALL_S2C,					EM_F_TYPE_NULL,		"octetsall_s2c"),
	DPI_FIELD_D(EM_TLL_LINKNO_COUNT,					EM_F_TYPE_NULL,		"linkno_count"),
	DPI_FIELD_D(EM_TLL_LINKNO1_C2S,						EM_F_TYPE_NULL,		"linkno1_c2s"),
	DPI_FIELD_D(EM_TLL_LINKNO2_C2S,						EM_F_TYPE_NULL,		"linkno2_c2s"),
	DPI_FIELD_D(EM_TLL_LINKNO3_C2S,						EM_F_TYPE_NULL,		"linkno3_c2s"),
	DPI_FIELD_D(EM_TLL_LINKNO4_C2S,						EM_F_TYPE_NULL,		"linkno4_c2s"),
	DPI_FIELD_D(EM_TLL_LINKNO1_S2C,						EM_F_TYPE_NULL,		"linkno1_s2c"),
	DPI_FIELD_D(EM_TLL_LINKNO2_S2C,						EM_F_TYPE_NULL,		"linkno2_s2c"),
	DPI_FIELD_D(EM_TLL_LINKNO3_S2C,						EM_F_TYPE_NULL,		"linkno3_s2c"),
	DPI_FIELD_D(EM_TLL_LINKNO4_S2C,						EM_F_TYPE_NULL,		"linkno4_s2c"),
	DPI_FIELD_D(EM_TLL_LINKNO_PKTSLINE_C2S,				EM_F_TYPE_NULL,		"pktline_c2s"),
	DPI_FIELD_D(EM_TLL_LINKNO_PKTSLINE_S2C,				EM_F_TYPE_NULL,		"pktline_s2c"),
	DPI_FIELD_D(EM_TLL_LINKNO_OCTETSLINE_C2S,			EM_F_TYPE_NULL,		"octetsline_c2s"),
	DPI_FIELD_D(EM_TLL_LINKNO_OCTETSLINE_S2C,			EM_F_TYPE_NULL,		"octetsline_s2c"),
	DPI_FIELD_D(EM_TTL_DIRECTION,			        	EM_F_TYPE_NULL,		"direction"),
};

static uint64_t get_uint64(const uint8_t *x, uint32_t offset) {

	int8_t i;
	uint64_t u64_v = 0;

	for (i = 7; i >= 0; i--) {

		u64_v <<= 8;
		u64_v |= x[offset + i];
	}

	return u64_v;
}

static uint64_t get_uint64_2(const uint8_t *x, uint32_t offset) {

	int8_t i;
	uint64_t u64_v = 0;

	for (i = 0; i < 8; i++) {
		u64_v <<= 8;
		u64_v |= x[offset + i];
	}

	return u64_v;
}

#if 0
static
void record_tll_log(struct flow_info *flow, int direction, DATA_STATE_T *info)
{

	// 这里只记录协议字段，公共字段接口需要 log 内存池，现在未创建
	ya_fvalue_t *val = NULL;
	val = ya_fvalue_new_uinteger(YA_FT_UINT16, info->protocol);
	precord_fvalue_put(flow->tll_record, "protocol", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT32, info->networkid);
	precord_fvalue_put(flow->tll_record, "networkid", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT32, info->startTime_s);
	precord_fvalue_put(flow->tll_record, "starttime_s", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT16, info->startTime_ms);
	precord_fvalue_put(flow->tll_record, "starttime_ms", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT32, info->endTime_s);
	precord_fvalue_put(flow->tll_record, "endtime_s", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT16, info->endTime_ms);
	precord_fvalue_put(flow->tll_record, "endtime_ms", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT32, info->packetAll[0]);
	precord_fvalue_put(flow->tll_record, "pktall_c2s", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT32, info->packetAll[1]);
	precord_fvalue_put(flow->tll_record, "pktall_s2c", val);
	val = ya_fvalue_new_uinteger64(YA_FT_UINT64, info->octetsAll[0]);
	precord_fvalue_put(flow->tll_record, "octetsall_c2s", val);
	val = ya_fvalue_new_uinteger64(YA_FT_UINT64, info->octetsAll[1]);
	precord_fvalue_put(flow->tll_record, "octetsall_s2c", val);
	val = ya_fvalue_new_uinteger(YA_FT_UINT16, info->linkNOcount);
	precord_fvalue_put(flow->tll_record, "linkno_count", val);
}
#endif

// 通联信息数据回填到流表
int dpi_tll_record_backfill(precord_t *precord, DATA_STATE_T *tll_msg)
{
	precord_update_to_layer(precord, "link", "upLinkPktNum",   uinteger,   tll_msg->packetAll[0]);
	precord_update_to_layer(precord, "link", "upSesBytes", 	   uinteger64, tll_msg->octetsAll[0]);
	precord_update_to_layer(precord, "link", "downLinkPktNum", uinteger,   tll_msg->packetAll[1]);
	precord_update_to_layer(precord, "link", "downSesBytes",   uinteger64, tll_msg->octetsAll[1]);

	return 0;
}


static void update_flow_statistics(struct flow_info *flow, DATA_STATE_T *msg) {
    if (!flow || !msg)
        return;

    uint32_t pkts_inc = 0;
    uint64_t bytes_inc = 0;

    struct work_process_data *process = &flow_thread_info[flow->thread_id];
    struct flow_info *orig_flow = NULL;
    struct flow_key key;
    memset(&key, 0, sizeof(key));
/*
    if (msg->protocol == flow->tuple.inner.proto) {
        orig_flow = flow;
    }
    else
*/    {
        if (flow->pkt) {
            key.inner.ip_version = flow->ip_version;
            key.inner.port_src = htons(flow->port_src);
            key.inner.port_dst = htons(flow->port_dst);
            if (flow->ip_version == 4) {
                memcpy(key.inner.ip_src, flow->pkt->iph4->saddr, sizeof(flow->pkt->iph4->saddr));
                memcpy(key.inner.ip_dst, flow->pkt->iph4->daddr, sizeof(flow->pkt->iph4->daddr));
            }
            else if (flow->ip_version == 6) {
                memcpy(key.inner.ip_src, flow->pkt->iph6->ip6_src, sizeof(flow->pkt->iph6->ip6_src));
                memcpy(key.inner.ip_dst, flow->pkt->iph6->ip6_dst, sizeof(flow->pkt->iph6->ip6_dst));
            }
        }

//        memcpy(&key, &flow->tuple, sizeof(flow->tuple));
        key.inner.proto = msg->protocol;

        int pos = rte_hash_lookup_data(process->hash, &key, (void **)&orig_flow);
    }

    if (orig_flow) {
        /* 更新流统计信息 */
        if (FLOW_DIR_SRC2DST == orig_flow->direction) {
            orig_flow->src2dst_packets = msg->packetAll[0];
            orig_flow->src2dst_bytes = msg->octetsAll[0];

            pkts_inc = msg->packetAll[0];
            bytes_inc = msg->octetsAll[0];
            orig_flow->direction = 0;
        }
        else {
            orig_flow->dst2src_packets = msg->packetAll[1];
            orig_flow->dst2src_bytes = msg->octetsAll[1];

            pkts_inc = msg->packetAll[1];
            bytes_inc = msg->octetsAll[1];
            orig_flow->direction = 1;
        }
        /* 更新线路统计信息 */
//这是啥? 没实现? 先注释掉,去除编译警告
//        struct mac_packet_header * mac_hdr = orig_flow->pSDTMacHeader;
//        if (mac_hdr) {
//            mac_hdr->Datasrc
//        }

        /* 更新workflow的统计信息 */
/*        process->stats.ip_packet_count++;
        process->stats.total_ip_bytes += rawsize;
        process->stats.flow_stats_total_pkts[msg->protocol]++;
        process->stats.flow_stats_total_bytes[msg->protocol] += rawsize;
*/
    }
}


int dissect_tll(struct flow_info *flow, int direction, uint32_t seq,
                const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if (g_config.protocol_switch[PROTOCOL_TLL] == 0) {
        return -1;
	}

	flow_thread_info[flow->thread_id].stats.tll_count++;

	// 每条流只解第一个通联帧
	if (flow->tll_flag == 1) {
		return 0;
	}

	int offset = 0;
	int i, j;
	DATA_STATE_T *msg = &flow->tll_msg;

	memset(msg, 0, sizeof(*msg));

	while (payload_len - offset >= (int32_t)sizeof(DATA_STATE_T)) {

		msg->protocol = get_uint16_ntohs(payload, offset);
		offset += 2;

		msg->networkid = get_uint32_ntohl(payload, offset);
		offset += 4;

		msg->startTime_s = get_uint32_ntohl(payload, offset);
		offset += 4;

		msg->startTime_ms = get_uint16_ntohs(payload, offset);
		offset += 2;

		msg->endTime_s = get_uint32_ntohl(payload, offset);
		offset += 4;

		msg->endTime_ms = get_uint16_ntohs(payload, offset);
		offset += 2;

		msg->packetAll[0] = get_uint32_ntohl(payload, offset);
		offset += 4;

		msg->packetAll[1] = get_uint32_ntohl(payload, offset);
		offset += 4;

		msg->octetsAll[0] = get_uint64_2(payload, offset);
		offset += 8;

		msg->octetsAll[1] = get_uint64_2(payload, offset);
		offset += 8;

		msg->linkNOcount = get_uint16_ntohs(payload, offset);
		offset += 2;

		for (i = 0; i < 2; i++) {
			// lineNO
			for (j = 0; j < 4; j++) {
				msg->lineStatContent[i].lineNO[j] = get_uint32_ntohl(payload, offset);
				offset += 4;
			}
			// packetLine
			for (j = 0; j < 2; j++) {
				msg->lineStatContent[i].packetLine[j] = get_uint32_ntohl(payload, offset);
				offset += 4;
			}
			// octetsLine
			for (j = 0; j < 2; j++) {
				msg->lineStatContent[i].octetsLine[j] = get_uint64_2(payload, offset);
				offset += 8;
			}
		}

        /* 更新流统计信息 */
    	// update_flow_statistics(flow, &flow->tll_msg);
	}

	flow->tll_flag = 1;

	return 0;
}

#if 0
static void
identify_tll(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {
	//printf("data_state_t size: %d\n", sizeof(DATA_STATE_T));

	if (1 != g_config.sdx_config.sdx_mac_packet_header_flag || !flow || !flow->pSDTMacHeader)
		return;

	struct mac_packet_header *mh = (struct mac_packet_header *)flow->pSDTMacHeader;
	if (mh && mh->DataType == 0x47)
		flow->real_protocol_id = PROTOCOL_TLL;

}


static void init_tll_dissector(void) {
	// dpi_register_proto_schema(tll_field_array, EM_TLL_MAX, "tll");
	dpi_register_proto_schema_ex(link_field_array, EM_LINK_MAX, "tll", SCHEMA_COMMON_FIELD_NONE);

// 共性字段还没有单独分离出来，暂时还是用 json 表注册的方式
#if 0
	const char * proto_name = "tll";

	dpi_register_proto(SCHEMA_COMMON_FIELD_NONE, proto_name, "Communication log");

	pschema_t *schema = dpi_pschema_get_proto(proto_name);

	// ! 标准类型
	// pschema_register_field(schema, "protocol", YA_FT_UINT16, "desc");
	// pschema_register_field(schema, "networkid", YA_FT_UINT32, "desc");
	// pschema_register_field(schema, "starttime_s", YA_FT_UINT32, "desc");
	// pschema_register_field(schema, "starttime_ms", YA_FT_UINT16, "desc");
	// pschema_register_field(schema, "endtime_s", YA_FT_UINT32, "desc");
	// pschema_register_field(schema, "endtime_ms", YA_FT_UINT16, "desc");
	// pschema_register_field(schema, "pktall_c2s", YA_FT_UINT32, "desc");
	// pschema_register_field(schema, "pktall_s2c", YA_FT_UINT32, "desc");
	// pschema_register_field(schema, "octetsall_c2s", YA_FT_UINT64, "desc");
	// pschema_register_field(schema, "octetsall_s2c", YA_FT_UINT64, "desc");
	// pschema_register_field(schema, "linkno_count", YA_FT_UINT16, "desc");
	//! 全是 string 类型
	pschema_register_field(schema, "protocol", YA_FT_STRING, "desc");
	pschema_register_field(schema, "networkid", YA_FT_STRING, "desc");
	pschema_register_field(schema, "starttime_s", YA_FT_STRING, "desc");
	pschema_register_field(schema, "starttime_ms", YA_FT_STRING, "desc");
	pschema_register_field(schema, "endtime_s", YA_FT_STRING, "desc");
	pschema_register_field(schema, "endtime_ms", YA_FT_STRING, "desc");
	pschema_register_field(schema, "pktall_c2s", YA_FT_STRING, "desc");
	pschema_register_field(schema, "pktall_s2c", YA_FT_STRING, "desc");
	pschema_register_field(schema, "octetsall_c2s", YA_FT_STRING, "desc");
	pschema_register_field(schema, "octetsall_s2c", YA_FT_STRING, "desc");
	pschema_register_field(schema, "linkno_count", YA_FT_STRING, "desc");
#endif
	// proto_tll_field_register();

	// udp_detection_array[PROTOCOL_TLL].proto			= PROTOCOL_TLL;
	// udp_detection_array[PROTOCOL_TLL].dissect_func	= dissect_tll;
	// udp_detection_array[PROTOCOL_TLL].identify_func = identify_tll;
}

static __attribute((constructor)) void before_init_tll(void) {
	register_tbl_array( 0, "tll", init_tll_dissector);
}
#endif
