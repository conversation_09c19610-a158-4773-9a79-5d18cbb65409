/****************************************************************************************
 * 文 件 名 : dpi_ssh.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/08/06
编码: wangy            2018/08/06
修改: chunli        2019/03/13
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <glib.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include <assert.h>
#include "dpi_utils.h"

extern struct global_config g_config;
extern GAsyncQueue *sslh_encrypted_pcap;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;

#define SSH_PORT 22

#define SSH_VERSION_UNKNOWN     0
#define SSH_VERSION_1           1
#define SSH_VERSION_2           2

/* Message Numbers (from RFC 4250) (1-255) */

/* Transport layer protocol: generic (1-19) */
#define SSH_MSG_DISCONNECT          1
#define SSH_MSG_IGNORE              2
#define SSH_MSG_UNIMPLEMENTED       3
#define SSH_MSG_DEBUG               4
#define SSH_MSG_SERVICE_REQUEST     5
#define SSH_MSG_SERVICE_ACCEPT      6

/* Transport layer protocol: Algorithm negotiation (20-29) */
#define SSH_MSG_KEXINIT             20
#define SSH_MSG_NEWKEYS             21

/* Transport layer: Key exchange method specific (reusable) (30-49) */
#define SSH_MSG_KEXDH_INIT          30
#define SSH_MSG_KEXDH_REPLY         31

#define SSH_MSG_KEX_DH_GEX_REQUEST_OLD  30
#define SSH_MSG_KEX_DH_GEX_GROUP        31
#define SSH_MSG_KEX_DH_GEX_INIT         32
#define SSH_MSG_KEX_DH_GEX_REPLY        33
#define SSH_MSG_KEX_DH_GEX_REQUEST      34

#define SSH_MSG_KEX_ECDH_INIT       30
#define SSH_MSG_KEX_ECDH_REPLY      31

/* User authentication protocol: generic (50-59) */
#define SSH_MSG_USERAUTH_REQUEST    50
#define SSH_MSG_USERAUTH_FAILURE    51
#define SSH_MSG_USERAUTH_SUCCESS    52
#define SSH_MSG_USERAUTH_BANNER     53

/* User authentication protocol: method specific (reusable) (50-79) */

/* Connection protocol: generic (80-89) */
#define SSH_MSG_GLOBAL_REQUEST          80
#define SSH_MSG_REQUEST_SUCCESS         81
#define SSH_MSG_REQUEST_FAILURE         82

/* Connection protocol: channel related messages (90-127) */
#define SSH_MSG_CHANNEL_OPEN                90
#define SSH_MSG_CHANNEL_OPEN_CONFIRMATION   91
#define SSH_MSG_CHANNEL_OPEN_FAILURE        92
#define SSH_MSG_CHANNEL_WINDOW_ADJUST       93
#define SSH_MSG_CHANNEL_DATA                94
#define SSH_MSG_CHANNEL_EXTENDED_DATA       95
#define SSH_MSG_CHANNEL_EOF                 96
#define SSH_MSG_CHANNEL_CLOSE               97
#define SSH_MSG_CHANNEL_REQUEST             98
#define SSH_MSG_CHANNEL_SUCCESS             99
#define SSH_MSG_CHANNEL_FAILURE             100

/*****************************************************************
*ENUM        :ssh_index_em
*Description :设定 TBL 字段写入的顺序
*Input       :none
*Output      :none
*Return      :none
*Others      :none
*****************************************************************/
enum  ssh_index_em{


    EM_SSH_CLIENT_VERSION,
    EM_SSH_SERVER_VERSION,
    EM_SSH_CLIENTKEXINIT_PACKETLENGTH,
    EM_SSH_CLIENTKEXINIT_PADINGLENGTH,
    EM_SSH_CLIENTKEXINIT_MESSAGECODE,
    EM_SSH_CLIENTKEXINIT_COOKIE,
    EM_SSH_CLTKEXINIT_KEX_ALGORITHMSLEN,
    EM_SSH_CLIENTKEXINIT_KEX_ALGORITHMS,
    EM_SSH_CLTKEXINIT_SVRHOSTKEY_ALGLEN,
    EM_SSH_CLTKEXINIT_SVRHOSTKEY_ALG,
    EM_SSH_CLTKEXINIT_ENCRYPTALG_C2SLEN,
    EM_SSH_CLTKEXINIT_ENCRYPTALGC2S,
    EM_SSH_CLTKEXINIT_ENCRYPTALGS2CLEN,
    EM_SSH_CLTKEXINIT_ENCRYPTALGS2C,
    EM_SSH_CLTKEXINIT_MACALGC2SLEN,
    EM_SSH_CLTKEXINIT_MACALGC2S,
    EM_SSH_CLTKEXINIT_MACALGS2CLEN,
    EM_SSH_CLTKEXINIT_MACALGS2C,
    EM_SSH_CLTKEXINIT_COMPALGC2SLEN,
    EM_SSH_CLTKEXINIT_COMPALGC2S,
    EM_SSH_CLTKEXINIT_COMPALGS2CLEN,
    EM_SSH_CLTKEXINIT_COMPALGS2C,
    EM_SSH_CLTKEXINIT_LANGUAGEC2SLEN,
    EM_SSH_CLTKEXINIT_LANGUAGEC2S,
    EM_SSH_CLTKEXINIT_LANGUAGES2CLEN,
    EM_SSH_CLTKEXINIT_LANGUAGES2C,
    EM_SSH_CLTKEXINIT_KEX_FIRSTPKT_FW,
    EM_SSH_CLIENTKEXINIT_RESERVED,
    EM_SSH_CLIENTKEXINIT_PADING,
	EN_SSH_CLIENTKEXINIT_HASSH,
	EM_SSH_CLIENTKEXINIT_DHPUBKEY,
    EM_SSH_SERVERKEXINIT_PACKETLENGTH,
    EM_SSH_SERVERKEXINIT_PADINGLENGTH,
    EM_SSH_SERVERKEXINIT_MESSAGECODE,
    EM_SSH_SERVERKEXINIT_COOKIE,
    EM_SSH_SVRKEXINIT_KEX_ALGLEN,
    EM_SSH_SERVERKEXINIT_KEX_ALGORITHMS,
    EM_SSH_SVRKEXINIT_SVR_HOSTKEYALGLEN,
    EM_SSH_SVRKEXINIT_SVR_HOSTKEYALG,
    EM_SSH_SVRKEXINIT_ENCRYPTALGC2SLEN,
    EM_SSH_SVRKEXINIT_ENCRYPTALGC2S,
    EM_SSH_SVRKEXINIT_ENCRYPTALGS2CLEN,
    EM_SSH_SVRKEXINIT_ENCRYPTALGS2C,
    EM_SSH_SVRKEXINIT_MACALGC2SLEN,
    EM_SSH_SVRKEXINIT_MACALGC2S,
    EM_SSH_SVRKEXINIT_MACALGS2CLEN,
    EM_SSH_SVRKEXINIT_MACALGS2C,
    EM_SSH_SVRKEXINIT_COMPRESSALGC2SLEN,
    EM_SSH_SVRKEXINIT_COMPRESSALGC2S,
    EM_SSH_SVRKEXINIT_COMPRESSALGS2CLEN,
    EM_SSH_SVRKEXINIT_COMPRESSALGS2C,
    EM_SSH_SVRKEXINIT_LANGUAGES_C2SLEN,
    EM_SSH_SVRKEXINIT_LANGUAGES_C2S,
    EM_SSH_SVRKEXINIT_LANGUAGES_S2CLEN,
    EM_SSH_SVRKEXINIT_LANGUAGES_S2C,
    EM_SSH_SVRKEXINIT_KEX_FIRST_PKT_FW,
    EM_SSH_SERVERKEXINIT_RESERVED,
    EM_SSH_SERVERKEXINIT_PADING,
	EN_SSH_SERVERKEXINIT_HASSH,
    EM_SSH_CLIENTKEXDHINIT_PACKETLENGTH,
    EM_SSH_CLIENTKEXDHINIT_PADINGLENGTH,
    EM_SSH_CLIENTKEXDHINIT_MESSAGECODE,
    EM_SSH_CLIENTKEXDHINIT_E_LENGTH,
    EM_SSH_CLIENTKEXDHINIT_E,
    EM_SSH_CLIENTKEXDHINIT_PADING,
    EM_SSH_CLIENTKEXGEX_REQ_PACKETLEN,
    EM_SSH_CLIENTKEXGEX_REQ_PADINGLEN,
    EM_SSH_CLIENTKEXGEX_REQ_MSGCODE,
    EM_SSH_CLIENTKEXGEX_REQUEST_MIN,
    EM_SSH_CLIENTKEXGEX_REQUEST_NBITS,
    EM_SSH_CLIENTKEXGEX_REQUEST_MAX,
    EM_SSH_CLIENTKEXGEX_REQUEST_PADING,
    EM_SSH_SERVERKEXGEX_GROUP_PACKETLEN,
    EM_SSH_SERVERKEXGEX_GROUP_PADINGLEN,
    EM_SSH_SERVERKEXGEX_GROUP_MSGCODE,
    EM_SSH_SERVERKEXGEX_GROUP_P_LENGTH,
    EM_SSH_SERVERKEXGEX_GROUP_P,
    EM_SSH_SERVERKEXGEX_GROUP_G_LENGTH,
    EM_SSH_SERVERKEXGEX_GROUP_G,
    EM_SSH_SERVERKEXGEX_GROUP_PADING,
    EM_SSH_SERVERKEXDHREPLY_PACKETLEN,
    EM_SSH_SERVERKEXDHREPLY_PADINGLEN,
    EM_SSH_SERVERKEXDHREPLY_MESSAGECODE,
    EM_SSH_SERVERKEXDHREPLY_PUBKEYLEN,
    EM_SSH_SERVERKEXDHREPLY_PUBKEY,
    EM_SSH_SERVERKEXDHREPLY_F_LENGTH,
    EM_SSH_SERVERKEXDHREPLY_F,

    EM_SSH_SERVER_RSA_DS,
    EM_SSH_SERVER_RSA_MS,
    EM_SSH_SERVERKEXGEX_GROUP_Q,
    EM_SSH_SERVERKEXGEX_GROUP_Y,
    EM_SSH_SERVER_KEY_ZS,
    EM_SSH_SERVER_KEY_MS,
    EM_SSH_SERVER_KEY,
    EM_SSH_SERVER_KEYEX_SCY,
    EM_SSH_SERVER_KEYEX_MS,

    EM_SSH_SERVERKEXDHREPLY_SIG_LEN,
    EM_SSH_SERVERKEXDHREPLY_SIGNATURE,
    EM_SSH_SERVERKEXDHREPLY_PADING,
    EM_SSH_MSGNEWKEYS,

    EM_SSH_PCAP_FILENAME,
    EM_SSH_LOGIN_STATUS,
    EM_SSH_HOSTNAME,
    EM_SSH_OPERATION_TYPE,

    EM_SSH_MAX
};


static dpi_field_table  ssh_field_array[] = {
    DPI_FIELD_D(EM_SSH_CLIENT_VERSION,                       YA_FT_STRING,      "SSHClientVersion"),
    DPI_FIELD_D(EM_SSH_SERVER_VERSION,                       YA_FT_STRING,      "SSHServerVersion"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_PACKETLENGTH,           YA_FT_UINT32,      "ClientKexInit_PacketLength"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_PADINGLENGTH,           YA_FT_UINT32,      "ClientKexInit_PadingLength"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_MESSAGECODE,            YA_FT_UINT32,      "ClientKexInit_MessageCode"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_COOKIE,                 YA_FT_BYTES,       "ClientKexInit_Cookie"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_KEX_ALGORITHMSLEN,         YA_FT_UINT32,      "CltKexInit_kex_algorithmsLen"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_KEX_ALGORITHMS,         YA_FT_STRING,      "ClientKexInit_kex_algorithms"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_SVRHOSTKEY_ALGLEN,         YA_FT_UINT32,      "CltKexInit_svrhostkey_algLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_SVRHOSTKEY_ALG,            YA_FT_STRING,      "CltKexInit_svrhostkey_alg"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_ENCRYPTALG_C2SLEN,         YA_FT_UINT32,      "CltKexInit_EncryptAlg_C2SLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_ENCRYPTALGC2S,             YA_FT_STRING,      "CltKexInit_EncryptAlgC2S"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_ENCRYPTALGS2CLEN,          YA_FT_UINT32,      "CltKexInit_EncryptAlgS2CLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_ENCRYPTALGS2C,             YA_FT_STRING,      "CltKexInit_EncryptAlgS2C"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_MACALGC2SLEN,              YA_FT_UINT32,      "CltKexInit_MacAlgC2SLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_MACALGC2S,                 YA_FT_STRING,      "CltKexInit_MacAlgC2S"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_MACALGS2CLEN,              YA_FT_UINT32,      "CltKexInit_MacAlgS2CLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_MACALGS2C,                 YA_FT_STRING,      "CltKexInit_MacAlgS2C"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_COMPALGC2SLEN,             YA_FT_UINT32,      "CltKexInit_CompAlgC2SLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_COMPALGC2S,                YA_FT_STRING,      "CltKexInit_CompAlgC2S"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_COMPALGS2CLEN,             YA_FT_UINT32,      "CltKexInit_CompAlgS2CLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_COMPALGS2C,                YA_FT_STRING,      "CltKexInit_CompAlgS2C"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_LANGUAGEC2SLEN,            YA_FT_UINT32,      "CltKexInit_languageC2SLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_LANGUAGEC2S,               YA_FT_STRING,      "CltKexInit_languageC2S"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_LANGUAGES2CLEN,            YA_FT_UINT32,      "CltKexInit_languageS2CLen"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_LANGUAGES2C,               YA_FT_STRING,      "CltKexInit_languageS2C"),
    DPI_FIELD_D(EM_SSH_CLTKEXINIT_KEX_FIRSTPKT_FW,           YA_FT_UINT32,      "CltKexInit_kex_FirstPkt_fw"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_RESERVED,               YA_FT_STRING,      "ClientKexInit_reserved"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_PADING,                 YA_FT_STRING,      "ClientKexInit_Pading"),
	DPI_FIELD_D(EN_SSH_CLIENTKEXINIT_HASSH,                  YA_FT_STRING,      "ClientKexInit_HASSH"),
	DPI_FIELD_D(EM_SSH_CLIENTKEXINIT_DHPUBKEY,               YA_FT_BYTES,       "ClientKexInit_DHPubKey"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_PACKETLENGTH,           YA_FT_UINT32,      "ServerKexInit_PacketLength"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_PADINGLENGTH,           YA_FT_UINT32,      "ServerKexInit_PadingLength"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_MESSAGECODE,            YA_FT_UINT32,      "ServerKexInit_MessageCode"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_COOKIE,                 YA_FT_BYTES,       "ServerKexInit_Cookie"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_KEX_ALGLEN,                YA_FT_UINT32,      "SvrKexInit_kex_AlgLen"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_KEX_ALGORITHMS,         YA_FT_STRING,      "ServerKexInit_kex_algorithms"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_SVR_HOSTKEYALGLEN,         YA_FT_UINT32,      "SvrKexInit_svr_HostKeyAlgLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_SVR_HOSTKEYALG,            YA_FT_STRING,      "SvrKexInit_svr_HostKeyAlg"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_ENCRYPTALGC2SLEN,          YA_FT_UINT32,      "SvrKexInit_encryptAlgC2SLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_ENCRYPTALGC2S,             YA_FT_STRING,      "SvrKexInit_encryptAlgC2S"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_ENCRYPTALGS2CLEN,          YA_FT_UINT32,      "SvrKexInit_encryptAlgS2CLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_ENCRYPTALGS2C,             YA_FT_STRING,      "SvrKexInit_encryptAlgS2C"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_MACALGC2SLEN,              YA_FT_UINT32,      "SvrKexInit_macAlgC2SLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_MACALGC2S,                 YA_FT_STRING,      "SvrKexInit_macAlgC2S"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_MACALGS2CLEN,              YA_FT_UINT32,      "SvrKexInit_macAlgS2CLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_MACALGS2C,                 YA_FT_STRING,      "SvrKexInit_macAlgS2C"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_COMPRESSALGC2SLEN,         YA_FT_UINT32,      "SvrKexInit_compressAlgC2SLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_COMPRESSALGC2S,            YA_FT_STRING,      "SvrKexInit_compressAlgC2S"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_COMPRESSALGS2CLEN,         YA_FT_UINT32,      "SvrKexInit_compressAlgS2CLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_COMPRESSALGS2C,            YA_FT_STRING,      "SvrKexInit_compressAlgS2C"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_LANGUAGES_C2SLEN,          YA_FT_UINT32,      "SvrKexInit_languages_C2SLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_LANGUAGES_C2S,             YA_FT_STRING,      "SvrKexInit_languages_C2S"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_LANGUAGES_S2CLEN,          YA_FT_UINT32,      "SvrKexInit_languages_S2CLen"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_LANGUAGES_S2C,             YA_FT_STRING,      "SvrKexInit_languages_S2C"),
    DPI_FIELD_D(EM_SSH_SVRKEXINIT_KEX_FIRST_PKT_FW,          YA_FT_UINT32,      "SvrKexInit_kex_First_Pkt_fw"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_RESERVED,               YA_FT_STRING,      "ServerKexInit_reserved"),
    DPI_FIELD_D(EM_SSH_SERVERKEXINIT_PADING,                 YA_FT_STRING,      "ServerKexInit_Pading"),
    DPI_FIELD_D(EN_SSH_SERVERKEXINIT_HASSH,                  YA_FT_STRING,      "ServerKexInit_HASSH"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXDHINIT_PACKETLENGTH,         YA_FT_UINT32,      "ClientKexDHINIT_PacketLength"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXDHINIT_PADINGLENGTH,         YA_FT_UINT32,      "ClientKexDHINIT_PadingLength"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXDHINIT_MESSAGECODE,          YA_FT_UINT32,      "ClientKexDHINIT_MessageCode"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXDHINIT_E_LENGTH,             YA_FT_UINT32,      "ClientKexDHINIT_e_Length"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXDHINIT_E,                    YA_FT_BYTES,       "ClientKexDHINIT_e"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXDHINIT_PADING,               YA_FT_STRING,      "ClientKexDHINIT_Pading"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQ_PACKETLEN,           YA_FT_UINT32,      "ClientKexGEX_REQ_PacketLen"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQ_PADINGLEN,           YA_FT_UINT32,      "ClientKexGEX_REQ_PadingLen"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQ_MSGCODE,             YA_FT_UINT32,      "ClientKexGEX_REQ_MsgCode"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQUEST_MIN,             YA_FT_UINT32,      "ClientKexGEX_REQUEST_min"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQUEST_NBITS,           YA_FT_UINT32,      "ClientKexGEX_REQUEST_nbits"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQUEST_MAX,             YA_FT_UINT32,      "ClientKexGEX_REQUEST_max"),
    DPI_FIELD_D(EM_SSH_CLIENTKEXGEX_REQUEST_PADING,          YA_FT_STRING,      "ClientKexGEX_REQUEST_Pading"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_PACKETLEN,         YA_FT_UINT32,      "ServerKexGEX_GROUP_PacketLen"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_PADINGLEN,         YA_FT_UINT32,      "ServerKexGEX_GROUP_PadingLen"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_MSGCODE,           YA_FT_UINT32,      "ServerKexGEX_GROUP_MsgCode"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_P_LENGTH,          YA_FT_UINT32,      "ServerKexGEX_GROUP_p_Length"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_P,                 YA_FT_STRING,      "ServerKexGEX_GROUP_p"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_G_LENGTH,          YA_FT_UINT32,      "ServerKexGEX_GROUP_g_Length"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_G,                 YA_FT_STRING,      "ServerKexGEX_GROUP_g"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_PADING,            YA_FT_STRING,      "ServerKexGEX_GROUP_Pading"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_PACKETLEN,           YA_FT_UINT32,      "ServerKexDHReply_PacketLen"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_PADINGLEN,           YA_FT_UINT32,      "ServerKexDHReply_PadingLen"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_MESSAGECODE,         YA_FT_UINT32,      "ServerKexDHReply_MessageCode"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_PUBKEYLEN,           YA_FT_UINT32,      "ServerKexDHReply_PubKeyLen"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_PUBKEY,              YA_FT_BYTES,       "ServerKexDHReply_PubKey"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_F_LENGTH,            YA_FT_UINT32,      "ServerKexDHReply_f_Length"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_F,                   YA_FT_BYTES,       "ServerKexDHReply_f"),

    DPI_FIELD_D(EM_SSH_SERVER_RSA_DS,                        YA_FT_STRING,      "Server_RSA_ds"),
    DPI_FIELD_D(EM_SSH_SERVER_RSA_MS,                        YA_FT_STRING,      "Server_RSA_ms"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_Q,                 YA_FT_STRING,      "ServerKexGEX_GROUP_q"),
    DPI_FIELD_D(EM_SSH_SERVERKEXGEX_GROUP_Y,                 YA_FT_STRING,      "ServerKexGEX_GROUP_y"),
    DPI_FIELD_D(EM_SSH_SERVER_KEY_ZS,                        YA_FT_UINT32,      "Server_Key_zs"),
    DPI_FIELD_D(EM_SSH_SERVER_KEY_MS,                        YA_FT_STRING,      "Server_Key_ms"),
    DPI_FIELD_D(EM_SSH_SERVER_KEY,                           YA_FT_STRING,      "Server_Key"),
    DPI_FIELD_D(EM_SSH_SERVER_KEYEX_SCY,                     YA_FT_STRING,      "Server_keyex_scy"),
    DPI_FIELD_D(EM_SSH_SERVER_KEYEX_MS,                      YA_FT_STRING,      "Server_keyex_ms"),

    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_SIG_LEN,             YA_FT_UINT32,      "ServerKexDHReply_Sig_Len"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_SIGNATURE,           YA_FT_BYTES,       "ServerKexDHReply_Signature"),
    DPI_FIELD_D(EM_SSH_SERVERKEXDHREPLY_PADING,              YA_FT_BYTES,       "ServerKexDHReply_Pading"),
    DPI_FIELD_D(EM_SSH_MSGNEWKEYS,                           YA_FT_UINT32,      "MsgNewKeys"),

    DPI_FIELD_D(EM_SSH_PCAP_FILENAME,                        YA_FT_STRING,      "PcapFilename"),
    DPI_FIELD_D(EM_SSH_LOGIN_STATUS,                         YA_FT_NONE,        "LoginStatus"),
    DPI_FIELD_D(EM_SSH_HOSTNAME,                             YA_FT_STRING,      "HostName"),
    DPI_FIELD_D(EM_SSH_OPERATION_TYPE,                       YA_FT_STRING,      "OperationType"),
};

#define SSH_NEED_MORE  0
#define SSH_ERROR     -1

#define PROTOCOL_HEAD_STRDEF(head_name) char *head_name##_val_ptr; uint32_t head_name##_val_len;

struct ssh_info
{
    uint8_t operation_type;

    char     client_version_ptr[64];
    uint16_t client_version_len;
    char     server_version_ptr[64];
    uint16_t server_version_len;

	char	ClientHassh[1024];
	char	ServerHassh[1024];

    uint16_t ClientKexInit_PacketLength;
    uint16_t ClientKexInit_PadingLength;
    uint16_t ClientKexInit_MessageCode;
    uint8_t  ClientKexInit_Cookie[16];
    PROTOCOL_HEAD_STRDEF(ClientKexInit_kex_algorithms)
    PROTOCOL_HEAD_STRDEF(CltKexInit_svrhostkey_alg)
    PROTOCOL_HEAD_STRDEF(CltKexInit_EncryptAlgC2S)
    PROTOCOL_HEAD_STRDEF(CltKexInit_EncryptAlgS2C)
    PROTOCOL_HEAD_STRDEF(CltKexInit_MacAlgC2S)
    PROTOCOL_HEAD_STRDEF(CltKexInit_MacAlgS2C)
    PROTOCOL_HEAD_STRDEF(CltKexInit_CompAlgC2S)
    PROTOCOL_HEAD_STRDEF(CltKexInit_CompAlgS2C)
    PROTOCOL_HEAD_STRDEF(CltKexInit_languageC2S)
    PROTOCOL_HEAD_STRDEF(CltKexInit_languageS2C)

    uint16_t CltKexInit_kex_FirstPkt_fw;
    char ClientKexInit_reserved[16];
    char ClientKexInit_Pading[64];

    uint16_t ServerKexInit_PacketLength;
    uint16_t ServerKexInit_PadingLength;
    uint16_t ServerKexInit_MessageCode;
    uint8_t  ServerKexInit_Cookie[16];
    PROTOCOL_HEAD_STRDEF(ServerKexInit_kex_algorithms)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_svr_HostKeyAlg)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_encryptAlgC2S)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_encryptAlgS2C)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_macAlgC2S)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_macAlgS2C)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_compressAlgC2S)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_compressAlgS2C)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_languages_C2S)
    PROTOCOL_HEAD_STRDEF(SvrKexInit_languages_S2C)
    uint16_t SvrKexInit_kex_First_Pkt_fw;
    char ServerKexInit_reserved[16];
    char ServerKexInit_Pading[64];
	// e (client ephemeral key public part)
	uint8_t ClientDHPubKey[256];
	uint32_t ClientDHPubKeyLength;

    uint16_t ClientKexDHINIT_PacketLength;
    uint16_t ClientKexDHINIT_PadingLength;
    uint16_t ClientKexDHINIT_MessageCode;
    uint8_t  ClientKexDHINIT_e[256];
    uint16_t ClientKexDHINIT_eLen;
    char ClientKexDHINIT_Pading[64];

    uint16_t ClientKexGEX_REQ_PacketLen;
    uint16_t ClientKexGEX_REQ_PadingLen;
    uint16_t ClientKexGEX_REQ_MsgCode;
    uint32_t ClientKexGEX_REQUEST_min;
    uint32_t ClientKexGEX_REQUEST_nbits;
    uint32_t ClientKexGEX_REQUEST_max;
    char ClientKexGEX_REQUEST_Pading[64];

    uint16_t ServerKexGEX_GROUP_PacketLen;
    uint16_t ServerKexGEX_GROUP_PadingLen;
    uint16_t ServerKexGEX_GROUP_MsgCode;
    char ServerKexGEX_GROUP_Pading[64];

    uint16_t ServerKexDHReply_PacketLen;
    uint16_t ServerKexDHReply_MessageCode;

    uint8_t     ServerKexDHReply_PubKey[256];
    uint16_t    ServerKexDHReply_PubKeyLen;
    uint8_t     ServerKexDHReply_f[256];
    uint16_t    ServerKexDHReply_fLen;
    uint8_t     ServerKexDHReply_Signature[1024];
    uint16_t    ServerKexDHReply_SignatureLen;
    uint8_t     ServerKexDHReply_Pading[64];
    uint16_t    ServerKexDHReply_PadingLen;

    uint8_t MsgNewKeys;

    char     Server_RSA_ds[128]       ;  // -> ssh.host_key.rsa.e  采用RSA签名的服务器主机密钥底数(底数应该指的是RSA签名中的消息体，就是签名的数据)
    int      Server_RSA_ds_len;
    char     Server_RSA_ms[512]       ;  // -> ssh.host_key.rsa.n  采用RSA签名的服务器主机密钥模数(模数指的应该是公钥中的N)
    int      Server_RSA_ms_len;
    char     ServerKexGEX_GROUP_p[65] ;  // -> ssh.host_key.dsa.p  采用DSA签名的服务器密钥素数p
    int      ServerKexGEX_GROUP_p_len;
    char     ServerKexGEX_GROUP_q[65] ;  // -> ssh.host_key.dsa.q  采用DSA签名的服务器密钥素数q
    int      ServerKexGEX_GROUP_q_len;
    char     ServerKexGEX_GROUP_g[65] ;  // -> ssh.host_key.dsa.g  采用DSA签名的服务器密钥素数g
    int      ServerKexGEX_GROUP_g_len;
    char     ServerKexGEX_GROUP_y[65] ;  // -> ssh.host_key.dsa.y  采用DSA签名的服务器密钥素数y
    int      ServerKexGEX_GROUP_y_len;

    uint32_t Server_Key_zs            ;  // ->                     服务器主机密钥指数     (默认都是65537)
    char     Server_key_ms[1024]      ;  // ->                     服务器主机密钥模数     (模数指的应该是公钥中的N)
    int      Server_key_ms_len;
    char     Server_key_sig[1024]     ;  // -> ssh.kex.h_sig       服务器密钥签名值       (指的是ssh.host_key.rsa.e)
    int      Server_key_sig_len;
    char     Server_keyex_g[1024]     ;  // -> ssh.dh_gex.g        DH密钥交换算法的生成元 (指的是DH中P的生成元数)
    int      Server_keyex_g_len;
    char     Server_keyex_p[1024]     ;  // -> ssh.dh_gex.p        DH密钥交换算法的模数   (指的是DH中的 mod P)
    int      Server_keyex_p_len;

};

struct ssh_cache
{
	char     *cache;
	int       cache_size;
	int       cache_hold;
};

struct ssh_session
{
    uint8_t written;
    uint8_t version;
    uint8_t client_new_keys_appear;
    uint8_t server_new_keys_appear;
    uint8_t client_init_flag;
    uint8_t server_init_flag;

    char client_kex_proposal[256];
    char server_kex_proposal[256];
    char key[256];

    int (*kex_specific_dissector)(uint8_t msg_code, struct dpi_pkt_st *pkt, uint32_t offset, struct ssh_info *info);
    struct ssh_info info;

    struct ssh_cache    cache[FLOW_DIR_MAX];
};

struct value_string{
    uint8_t     value;
    const char* string;
};

static struct  value_string ssh_op_type[] = {
    {20, "key exchange init"},
    {21, "new keys"},
    {30, "ECDH key exchange init"},
    {31, "ECDH key exchange replay"},
    {0,  NULL}
};

static const char* value_to_string(const struct value_string *v_s, uint8_t  v){
    int i;
    for(i=0; v_s[i].string; i++){
        if(v_s[i].value == v)
            return v_s[i].string;
    }
    return NULL;
}

static gint
ssh_gslist_compare_strings(gconstpointer a, gconstpointer b)
{
    if (a == NULL && b == NULL)
        return 0;
    if (a == NULL)
        return -1;
    if (b == NULL)
        return 1;
    return strcmp((const char*)a, (const char*)b);
}

/* expects that *result is NULL */
static void
ssh_choose_algo(gchar *client, gchar *server, gchar *result, int len)
{
    gchar **server_strings=NULL;
    gchar **client_strings=NULL;
    gchar **step;
    GSList *server_list = NULL;

    if (!client || !server || !result || *result)
        return;

    server_strings = g_strsplit(server, ",", 0);
    for (step = server_strings; *step; step++) {
        server_list = g_slist_append(server_list, *step);
    }

    client_strings = g_strsplit(client, ",", 0);
    for (step = client_strings; *step; step++) {
        GSList *agreed;
        if ((agreed=g_slist_find_custom(server_list, *step, ssh_gslist_compare_strings))) {
            snprintf(result, len, "%s", (const gchar *)agreed->data);
            break;
        }
    }

    g_strfreev(client_strings);
    g_slist_free(server_list);
    g_strfreev(server_strings);
}

static int ssh_dissect_proposal(struct dpi_pkt_st *pkt, uint32_t offset, char  **ptr, uint32_t *len)
{
    if (offset >= pkt->payload_len)
        return offset;

    if (dpi_get_be32(pkt, offset, len) == -1)
        return pkt->payload_len;
    offset += 4;
    if (offset + *len > pkt->payload_len) {
        *len = 0;
        return pkt->payload_len;
    }

    //*ptr = pkt->payload + offset;
    if( (*ptr = malloc(*len)) ){
        memcpy(*ptr, pkt->payload + offset, *len);
    }

    return (offset + *len);
}

/**
 *  @return
 *      hostkey length
 */
static int ssh_dissect_hostkey(struct dpi_pkt_st *pkt, uint32_t offset, struct ssh_info *info)
{
    int sp_len;
    int hostkey_len;
    uint32_t len = 0;
    const char* key_type;

    if (-1 == dpi_get_be32(pkt, offset, &len))
        return SSH_ERROR;

    hostkey_len = (int)len + 4;
    offset += 4;
    // Skip Host key type
    if (offset + len > pkt->payload_len)
        return SSH_NEED_MORE;

    // GET Host key Type length
    if (-1 == dpi_get_be32(pkt, offset, &len))
        return SSH_ERROR;

    offset += 4;

    key_type = (const char*)(pkt->payload + offset);
    offset += len;

    if (0 == strcmp(key_type, "ssh-rsa"))
    {
        // Skip Multi Precision Integer Length
        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;

        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;

        // RSA public exponent (e)
        // RSA 公钥指数
        sp_len= DPI_MIN(len, array_length(info->Server_RSA_ds));
        memcpy(info->Server_RSA_ds, pkt->payload + offset, sp_len);
        info->Server_RSA_ds_len  = sp_len;

        offset += len;

        // Multi Precision Integer Length:
        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;

        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;

        // RSA modulus (N)
        info->Server_RSA_ms_len   =  DPI_MIN(len, array_length(info->Server_RSA_ms));
        memcpy(info->Server_RSA_ms, pkt->payload + offset, info->Server_RSA_ms_len);

        offset += len;
       
    }
    else
    if(0 == strcmp(key_type, "ssh-dss"))
    {
        unsigned int Arrsize = sizeof(info->ServerKexGEX_GROUP_p)/sizeof(info->ServerKexGEX_GROUP_p[0]);

        assert(-1 != dpi_get_be32(pkt, offset,  &len));
        offset += 4;
        memcpy(info->ServerKexGEX_GROUP_p, pkt->payload + offset, len < Arrsize ? len : Arrsize);
        info->ServerKexGEX_GROUP_p_len = len < Arrsize ? len : Arrsize;
        offset += len;

        assert(-1 != dpi_get_be32(pkt, offset,  &len));
        offset += 4;
        memcpy(info->ServerKexGEX_GROUP_q, pkt->payload + offset, len < Arrsize ? len : Arrsize);
        info->ServerKexGEX_GROUP_q_len = len < Arrsize ? len : Arrsize;
        offset += len;

        assert(-1 != dpi_get_be32(pkt, offset,  &len));
        offset += 4;
        memcpy(info->ServerKexGEX_GROUP_g, pkt->payload + offset, len < Arrsize ? len : Arrsize);
        info->ServerKexGEX_GROUP_g_len = len < Arrsize ? len : Arrsize;
        offset += len;

        assert(-1 != dpi_get_be32(pkt, offset,  &len));
        offset += 4;
        memcpy(info->ServerKexGEX_GROUP_y, pkt->payload + offset, len < Arrsize ? len : Arrsize);
        info->ServerKexGEX_GROUP_y_len = len < Arrsize ? len : Arrsize;
        offset += len;

    }
    else
    if(0 == strcmp(key_type, "ecdsa-sha2-"))
    {

    }

    return hostkey_len;
}

static int ssh_dissect_kex_dh(uint8_t msg_code, struct dpi_pkt_st *pkt, uint32_t offset, struct ssh_info *info)
{
    uint32_t sp_len;
    uint32_t len;
    int      ret;

    switch (msg_code) {
        case SSH_MSG_KEXDH_INIT:
        {
            if (-1 == dpi_get_be32(pkt, offset, &len))
                return SSH_ERROR;

            info->ClientKexDHINIT_MessageCode = SSH_MSG_KEXDH_INIT;
            info->ClientKexDHINIT_PacketLength = len;

            offset += 4;

            sp_len = DPI_MIN(len, sizeof(info->ClientKexDHINIT_e));
            memcpy(info->ClientKexDHINIT_e, pkt->payload + offset, sp_len);
            info->ClientKexDHINIT_eLen = sp_len;

            offset += len;
            break;
        }
        case SSH_MSG_KEXDH_REPLY:
        {

            info->ServerKexDHReply_MessageCode = SSH_MSG_KEXDH_REPLY;
            ret = ssh_dissect_hostkey(pkt, offset, info);
            if (ret <= 0)
                return ret;

            offset += ret;
            // DH Server f
            if (-1 == dpi_get_be32(pkt, offset, &len))
                return SSH_ERROR;

            offset += 4;
            if (offset + len > pkt->payload_len)
                return SSH_NEED_MORE;

            sp_len = DPI_MIN(len, sizeof(info->ServerKexDHReply_f));
            memcpy(info->ServerKexDHReply_f, pkt->payload + offset, sp_len);
            info->ServerKexDHReply_fLen = sp_len;

            offset += len;

            // 提取 KEX H signature
            if (-1 == dpi_get_be32(pkt, offset, &len))
                return SSH_ERROR;

            offset += 4;
            if (offset + len > pkt->payload_len)
                return SSH_NEED_MORE;

            sp_len = DPI_MIN(len, sizeof(info->ServerKexDHReply_Signature));
            memcpy(info->ServerKexDHReply_Signature,  pkt->payload + offset, sp_len);
            info->ServerKexDHReply_SignatureLen = sp_len;

            offset += len;
            break;
        }
    }

    return offset;
}

static int ssh_dissect_kex_dh_gex(uint8_t msg_code, struct dpi_pkt_st *pkt, uint32_t offset, struct ssh_info *info)
{
    uint32_t i;
    uint32_t len;
    uint32_t sp_len;

    switch (msg_code) {
    case SSH_MSG_KEX_DH_GEX_REQUEST_OLD:
        if (-1 == dpi_get_be32(pkt, offset, &info->ClientKexGEX_REQUEST_nbits))
            return SSH_ERROR;
        offset += 4;
        break;

    case SSH_MSG_KEX_DH_GEX_GROUP:
    {
        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;
    
        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;

        if (len >= sizeof(info->ServerKexGEX_GROUP_p) / 2 - 1)
            sp_len = sizeof(info->ServerKexGEX_GROUP_p) / 2 - 1;
        else
            sp_len = len;
        for (i = 0; i < sp_len; i++) {
            if (i * 2 >= sizeof(info->ServerKexGEX_GROUP_p))
                break;
            snprintf(info->ServerKexGEX_GROUP_p + 2 * i, sizeof(info->ServerKexGEX_GROUP_p) - 2 * i, "%02x", *(pkt->payload + offset + i));
        }
        {
            // GET DH 模数
            int copylen = len < sizeof(info->Server_keyex_p)/sizeof(info->Server_keyex_p[0]) ? len : sizeof(info->Server_keyex_p)/sizeof(info->Server_keyex_p[0]);
            memcpy(info->Server_keyex_p, pkt->payload + offset, copylen);
            info->Server_keyex_p_len = copylen;
        }
        offset += len;

        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;
        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;

        if (len >= sizeof(info->ServerKexGEX_GROUP_g) / 2 - 1)
            sp_len = sizeof(info->ServerKexGEX_GROUP_g) / 2 - 1;
        else
            sp_len = len;
        for (i = 0; i < sp_len; i++) {
            if (i * 2 >= sizeof(info->ServerKexGEX_GROUP_g))
                break;
            snprintf(info->ServerKexGEX_GROUP_g + 2 * i, sizeof(info->ServerKexGEX_GROUP_g) - 2 * i, "%02x", *(pkt->payload + offset + i));
        }
        {
            // GET DH 生成元
            int copylen = len < sizeof(info->Server_keyex_g)/sizeof(info->Server_keyex_g[0]) ? len : sizeof(info->Server_keyex_g)/sizeof(info->Server_keyex_g[0]);
            memcpy(info->Server_keyex_g, pkt->payload + offset, copylen);
            info->Server_keyex_g_len = copylen;
        }
        offset += len;
        break;
    }
    case SSH_MSG_KEX_DH_GEX_INIT:
        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;
        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;

        sp_len = DPI_MIN(len, sizeof(info->ClientKexDHINIT_e));
        memcpy(info->ClientKexDHINIT_e , pkt->payload + offset, sp_len);
        info->ClientKexDHINIT_eLen = sp_len;

        offset += len;
        break;

    case SSH_MSG_KEX_DH_GEX_REPLY:
        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;
        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;
        offset += len;

        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;
        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;
        offset += len;

        if (-1 == dpi_get_be32(pkt, offset, &len))
            return SSH_ERROR;
        offset += 4;
        if (offset + len > pkt->payload_len)
            return SSH_NEED_MORE;
        offset += len;
    /*
        offset += ssh_tree_add_hostkey(tvb, offset, tree, "KEX host key", ett_key_exchange_host_key);
        offset += ssh_tree_add_mpint(tvb, offset, tree, hf_ssh_dh_f);
        offset += ssh_tree_add_string(tvb, offset, tree, hf_ssh_kex_h_sig, hf_ssh_kex_h_sig_length);*/
        break;

    case SSH_MSG_KEX_DH_GEX_REQUEST:
        if (-1 == dpi_get_be32(pkt, offset, &info->ClientKexGEX_REQUEST_min))
            return SSH_ERROR;
        offset += 4;
        if (offset >= pkt->payload_len)
            return SSH_NEED_MORE;

        if (-1 == dpi_get_be32(pkt, offset, &info->ClientKexGEX_REQUEST_nbits))
            return SSH_ERROR;
        offset += 4;
        if (offset >= pkt->payload_len)
            return SSH_NEED_MORE;

        if (-1 == dpi_get_be32(pkt, offset, &info->ClientKexGEX_REQUEST_max))
            return SSH_ERROR;
        offset += 4;
        if (offset >= pkt->payload_len)
            return SSH_NEED_MORE;

        break;
    }

    return offset;
}

static int
ssh_dissect_kex_ecdh(uint8_t msg_code, struct dpi_pkt_st *pkt, uint32_t offset, struct ssh_info *info)
{
    uint32_t sp_len;
    uint32_t len;
    int ret;

    switch (msg_code) {
        case SSH_MSG_KEX_ECDH_INIT:
        {
            if (-1 == dpi_get_be32(pkt, offset, &len))
                return SSH_ERROR;

            offset += 4;
            if (offset + len > pkt->payload_len)
                return SSH_NEED_MORE;

            info->ClientKexDHINIT_MessageCode = SSH_MSG_KEXDH_INIT;
            info->ClientKexDHINIT_PacketLength = len;

            sp_len = DPI_MIN(len, sizeof(info->ClientDHPubKey));
            memcpy(info->ClientDHPubKey, pkt->payload + offset, sp_len);
            info->ClientDHPubKeyLength = sp_len;

            offset += len;
            break;
        }
        case SSH_MSG_KEX_ECDH_REPLY:
        {
             // GET Host key
            ret = ssh_dissect_hostkey(pkt, offset, info);
            if (ret <= 0)
                return ret;

            info->ServerKexDHReply_MessageCode = SSH_MSG_KEXDH_REPLY;
            offset += ret;

            {
                // 提取 ECDH server's ephemeral public key
                if(-1 == dpi_get_be32(pkt, offset,  &len)) //提取出错
                {
                    return SSH_ERROR;
                }

                offset += 4;
                
                sp_len = DPI_MIN(len, sizeof(info->ServerKexDHReply_PubKey));
                memcpy(info->ServerKexDHReply_PubKey, pkt->payload + offset, sp_len);
                info->ServerKexDHReply_PubKeyLen = sp_len;

                offset += len;
            }
            {
                // 提取 KEX H signature
                if (-1 == dpi_get_be32(pkt, offset, &len))
                    return SSH_ERROR;

                offset += 4;
                sp_len = DPI_MIN(len, sizeof(info->ServerKexDHReply_Signature));
                memcpy(info->ServerKexDHReply_Signature, pkt->payload + offset, sp_len);
                info->ServerKexDHReply_SignatureLen = sp_len;

                offset += len;
            }

            break;
        }
    }

    return offset;
}

static void ssh_set_kex_specific_dissector(struct ssh_session *session_data)
{
    const char *kex_name = session_data->key;

    if (!kex_name) return;

    if (strcmp(kex_name, "diffie-hellman-group-exchange-sha1") == 0 ||
        strcmp(kex_name, "diffie-hellman-group-exchange-sha256") == 0)
    {
        session_data->kex_specific_dissector = ssh_dissect_kex_dh_gex;
    }
    else if (g_str_has_prefix(kex_name, "ecdh-sha2-")
         || strcmp(kex_name, "<EMAIL>") == 0
         || strcmp(kex_name, "curve25519-sha256") == 0
         || strcmp(kex_name, "curve448-sha512") == 0)
    {
        session_data->kex_specific_dissector = ssh_dissect_kex_ecdh;
    }
}

static struct ssh_session *ssh_session_init(void)
{
    int                 i;
    struct ssh_session *session;
    struct ssh_cache   *c;

    session = (struct ssh_session *)dpi_malloc(sizeof(struct ssh_session));
    memset(session, 0, sizeof(struct ssh_session));

    session->version                = SSH_VERSION_UNKNOWN;
    session->kex_specific_dissector = ssh_dissect_kex_dh;

    for (i = 0; i < FLOW_DIR_MAX; i++)
    {
        c             = &session->cache[i];
        c->cache_size = g_config.http.http_strip_cache_size;
        c->cache_hold = 0;
        c->cache      = dpi_malloc(c->cache_size);
    }

    return session;
}


static int ssh_dissect_key_init(struct dpi_pkt_st *pkt, uint32_t offset, int is_request, struct ssh_info *info, struct ssh_session *session)
{
    uint32_t start_offset = offset;
    uint32_t copy_len;
	gchar *hassh;
	char tmp[2048];

	memset(tmp, 0, sizeof(tmp));

    if (is_request) {
        if (session->client_init_flag || offset + 16 > pkt->payload_len)
            return pkt->payload_len;
        //info->PROTOCOL_VAL_PTR(ClientKexInit_Cookie) = pkt->payload + start_offset;
        //info->PROTOCOL_VAL_LEN(ClientKexInit_Cookie) = 16;
        memcpy(info->ClientKexInit_Cookie, pkt->payload + start_offset, 16);

        start_offset += 16;

        session->client_init_flag = 1;

        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(ClientKexInit_kex_algorithms),
                    &info->PROTOCOL_VAL_LEN(ClientKexInit_kex_algorithms));
        copy_len = info->PROTOCOL_VAL_LEN(ClientKexInit_kex_algorithms);
        if (copy_len >= sizeof(session->client_kex_proposal))
            copy_len = sizeof(session->client_kex_proposal) - 1;
        strncpy(session->client_kex_proposal, (const char *)info->PROTOCOL_VAL_PTR(ClientKexInit_kex_algorithms), copy_len);
        session->client_kex_proposal[copy_len] = 0;

        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_svrhostkey_alg),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_svrhostkey_alg));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_EncryptAlgC2S),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_EncryptAlgC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_EncryptAlgS2C),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_EncryptAlgS2C));

        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_MacAlgC2S),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_MacAlgC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_MacAlgS2C),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_MacAlgS2C));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_CompAlgC2S),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_CompAlgC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_CompAlgS2C),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_CompAlgS2C));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_languageC2S),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_languageC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(CltKexInit_languageS2C),
                    &info->PROTOCOL_VAL_LEN(CltKexInit_languageS2C));

        if (start_offset + 1 <= pkt->payload_len) {
            info->CltKexInit_kex_FirstPkt_fw = *(pkt->payload + start_offset);
            start_offset += 1;
        }

        if (start_offset + 4 <= pkt->payload_len) {
            snprintf(info->ClientKexInit_reserved, sizeof(info->ClientKexInit_reserved), "%02x%02x%02x%02x",
                    *(pkt->payload + start_offset), *(pkt->payload + start_offset + 1), *(pkt->payload + start_offset + 2), *(pkt->payload + start_offset + 3));
            start_offset += 4;
        }

		memset(info->ClientHassh, 0, sizeof(info->ClientHassh));

//		snprintf(tmp, sizeof(tmp), "%s;%s;%s;%s"
//								, info->ClientKexInit_kex_algorithms_val_ptr
//								, info->CltKexInit_EncryptAlgC2S_val_ptr
//								, info->CltKexInit_MacAlgC2S_val_ptr
//								, info->CltKexInit_CompAlgC2S_val_ptr);

		strncat(tmp, info->ClientKexInit_kex_algorithms_val_ptr, info->ClientKexInit_kex_algorithms_val_len);
		strcat(tmp, ";");
		strncat(tmp, info->CltKexInit_EncryptAlgC2S_val_ptr, info->CltKexInit_EncryptAlgC2S_val_len);
		strcat(tmp, ";");
		strncat(tmp, info->CltKexInit_MacAlgC2S_val_ptr, info->CltKexInit_MacAlgC2S_val_len);
		strcat(tmp, ";");
		strncat(tmp, info->CltKexInit_CompAlgC2S_val_ptr, info->CltKexInit_CompAlgC2S_val_len);

		hassh = g_compute_checksum_for_string(G_CHECKSUM_MD5, tmp, strlen(tmp));
		memcpy(info->ClientHassh, hassh, DPI_MIN(strlen(hassh), sizeof(info->ClientHassh)));
		g_free(hassh);
//		printf("cli:%s\n", info->ClientHassh);

    }else {
        if (session->server_init_flag || offset + 16 > pkt->payload_len)
            return pkt->payload_len;
        //info->PROTOCOL_VAL_PTR(ServerKexInit_Cookie) = pkt->payload + start_offset;
        //info->PROTOCOL_VAL_LEN(ServerKexInit_Cookie) = 16;
        memcpy(info->ServerKexInit_Cookie, pkt->payload + start_offset, 16);
        start_offset += 16;

        session->server_init_flag = 1;

        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(ServerKexInit_kex_algorithms),
                    &info->PROTOCOL_VAL_LEN(ServerKexInit_kex_algorithms));
        copy_len = info->PROTOCOL_VAL_LEN(ServerKexInit_kex_algorithms);
        if (copy_len >= sizeof(session->server_kex_proposal))
            copy_len = sizeof(session->server_kex_proposal) - 1;
        strncpy(session->server_kex_proposal, (const char *)info->PROTOCOL_VAL_PTR(ServerKexInit_kex_algorithms), copy_len);
        session->server_kex_proposal[copy_len] = 0;

        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_svr_HostKeyAlg),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_svr_HostKeyAlg));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_encryptAlgC2S),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_encryptAlgC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_encryptAlgS2C),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_encryptAlgS2C));

        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_macAlgC2S),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_macAlgC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_macAlgS2C),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_macAlgS2C));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_compressAlgC2S),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_compressAlgC2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_compressAlgS2C),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_compressAlgS2C));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_languages_C2S),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_languages_C2S));
        start_offset = ssh_dissect_proposal(pkt, start_offset, &info->PROTOCOL_VAL_PTR(SvrKexInit_languages_S2C),
                    &info->PROTOCOL_VAL_LEN(SvrKexInit_languages_S2C));

        if (start_offset + 1 <= pkt->payload_len) {
            info->SvrKexInit_kex_First_Pkt_fw = *(pkt->payload + start_offset);
            start_offset += 1;
        }
        if (start_offset + 4 <= pkt->payload_len) {
            snprintf(info->ServerKexInit_reserved, sizeof(info->ServerKexInit_reserved), "%02x%02x%02x%02x",
                    *(pkt->payload + start_offset), *(pkt->payload + start_offset + 1), *(pkt->payload + start_offset + 2), *(pkt->payload + start_offset + 3));
            start_offset += 4;
        }

		memset(info->ServerHassh, 0, sizeof(info->ServerHassh));

//		snprintf(tmp, sizeof(tmp), "%s;%s;%s;%s"
//								, info->ServerKexInit_kex_algorithms_val_ptr
//								, info->CltKexInit_EncryptAlgS2C_val_ptr
//								, info->CltKexInit_MacAlgS2C_val_ptr
//								, info->CltKexInit_CompAlgS2C_val_ptr);

		strncat(tmp, info->ServerKexInit_kex_algorithms_val_ptr, info->ServerKexInit_kex_algorithms_val_len);
		strcat(tmp, ";");
		strncat(tmp, info->SvrKexInit_encryptAlgS2C_val_ptr, info->SvrKexInit_encryptAlgS2C_val_len);
		strcat(tmp, ";");
		strncat(tmp, info->SvrKexInit_macAlgS2C_val_ptr, info->SvrKexInit_macAlgS2C_val_len);
		strcat(tmp, ";");
		strncat(tmp, info->SvrKexInit_compressAlgS2C_val_ptr, info->SvrKexInit_compressAlgS2C_val_len);

		hassh = g_compute_checksum_for_string(G_CHECKSUM_MD5, tmp, strlen(tmp));
		memcpy(info->ServerHassh, hassh, DPI_MIN(strlen(hassh), sizeof(info->ServerHassh)));
		g_free(hassh);

//		printf("srv:%s\n", info->ServerHassh);
    }

    return start_offset;
}

static int identify_ssh(struct flow_info *flow, uint8_t direction, const uint8_t *payload, const uint32_t payload_len)
{
    UNUSED(direction);

    if (g_config.protocol_switch[PROTOCOL_SSH] == 0)
        return PROTOCOL_UNKNOWN;

    if (payload_len > 7 && payload_len < 100 && memcmp(payload, "SSH-", 4) == 0) {
        flow->real_protocol_id = PROTOCOL_SSH;
        return PROTOCOL_SSH;
    }

    return PROTOCOL_UNKNOWN;
}

#define WRITE_HEAD_DEF_LINUX_STR(name)                                                                        \
if(info->PROTOCOL_VAL_PTR(name))                                                                              \
{                                                                                                             \
    write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,                                        \
                 (const char *)info->PROTOCOL_VAL_PTR(name), info->PROTOCOL_VAL_LEN(name));                   \
    free(info->PROTOCOL_VAL_PTR(name));                                                                       \
    info->PROTOCOL_VAL_PTR(name) = 0;                                                                         \
}                                                                                                             \
else                                                                                                          \
{                                                                                                             \
    write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);                                    \
}

#define FREE_VAL(name)                      \
if (info->PROTOCOL_VAL_PTR(name)) {         \
    free(info->PROTOCOL_VAL_PTR(name));     \
    info->PROTOCOL_VAL_PTR(name) = NULL;    \
}                                           \


static void ssh_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction _U_, struct ssh_info *info, int *idx, int i)
{
    //int local_idx=*idx;
    switch(i){

    case EM_SSH_CLIENT_VERSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->client_version_ptr, info->client_version_len);
        break;
    case EM_SSH_CLIENTKEXINIT_PACKETLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexInit_PacketLength);
        break;
    case EM_SSH_CLIENTKEXINIT_PADINGLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexInit_PadingLength);
        break;
    case EM_SSH_CLIENTKEXINIT_MESSAGECODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexInit_MessageCode);
        break;
    case EM_SSH_CLIENTKEXINIT_COOKIE:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexInit_Cookie, 16);
        break;
    case EM_SSH_CLTKEXINIT_KEX_ALGORITHMSLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(ClientKexInit_kex_algorithms));
        break;
    case EM_SSH_CLIENTKEXINIT_KEX_ALGORITHMS:
        WRITE_HEAD_DEF_LINUX_STR(ClientKexInit_kex_algorithms);
        break;
    case EM_SSH_CLTKEXINIT_SVRHOSTKEY_ALGLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_svrhostkey_alg));
        break;
    case EM_SSH_CLTKEXINIT_SVRHOSTKEY_ALG:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_svrhostkey_alg);
        break;
    case EM_SSH_CLTKEXINIT_ENCRYPTALG_C2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_EncryptAlgC2S));
        break;
    case EM_SSH_CLTKEXINIT_ENCRYPTALGC2S:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_EncryptAlgC2S);
        break;
    case EM_SSH_CLTKEXINIT_ENCRYPTALGS2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_EncryptAlgS2C));
        break;
    case EM_SSH_CLTKEXINIT_ENCRYPTALGS2C:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_EncryptAlgS2C);
        break;
    case EM_SSH_CLTKEXINIT_MACALGC2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_MacAlgC2S));
        break;
    case EM_SSH_CLTKEXINIT_MACALGC2S:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_MacAlgC2S);
        break;
    case EM_SSH_CLTKEXINIT_MACALGS2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_MacAlgS2C));
        break;
    case EM_SSH_CLTKEXINIT_MACALGS2C:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_MacAlgS2C);
        break;
    case EM_SSH_CLTKEXINIT_COMPALGC2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_CompAlgC2S));
        break;
    case EM_SSH_CLTKEXINIT_COMPALGC2S:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_CompAlgC2S);
        break;
    case EM_SSH_CLTKEXINIT_COMPALGS2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_CompAlgS2C));
        break;
    case EM_SSH_CLTKEXINIT_COMPALGS2C:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_CompAlgS2C);
        break;
    case EM_SSH_CLTKEXINIT_LANGUAGEC2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_languageC2S));
        break;
    case EM_SSH_CLTKEXINIT_LANGUAGEC2S:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_languageC2S);
        break;
    case EM_SSH_CLTKEXINIT_LANGUAGES2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(CltKexInit_languageS2C));
        break;
    case EM_SSH_CLTKEXINIT_LANGUAGES2C:
        WRITE_HEAD_DEF_LINUX_STR(CltKexInit_languageS2C);
        break;
    case EM_SSH_CLTKEXINIT_KEX_FIRSTPKT_FW:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->CltKexInit_kex_FirstPkt_fw);
        break;
    case EM_SSH_CLIENTKEXINIT_RESERVED:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ClientKexInit_reserved, strlen(info->ClientKexInit_reserved));
        break;
    case EM_SSH_CLIENTKEXINIT_PADING:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ClientKexInit_Pading, strlen(info->ClientKexInit_Pading));

        break;
	case EN_SSH_CLIENTKEXINIT_HASSH:
		write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientHassh, strlen(info->ClientHassh));
           break;
	case EM_SSH_CLIENTKEXINIT_DHPUBKEY:
		write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientDHPubKey, info->ClientDHPubKeyLength);
		break;
    case EM_SSH_SERVERKEXINIT_PACKETLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexInit_PacketLength);
        break;
    case EM_SSH_SERVERKEXINIT_PADINGLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexInit_PadingLength);
        break;
    case EM_SSH_SERVERKEXINIT_MESSAGECODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexInit_MessageCode);
        break;
    case EM_SSH_SERVERKEXINIT_COOKIE:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexInit_Cookie, 16);
        break;
    case EM_SSH_SVRKEXINIT_KEX_ALGLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(ServerKexInit_kex_algorithms));
        break;
    case EM_SSH_SERVERKEXINIT_KEX_ALGORITHMS:
        WRITE_HEAD_DEF_LINUX_STR(ServerKexInit_kex_algorithms);
        break;
    case EM_SSH_SVRKEXINIT_SVR_HOSTKEYALGLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_svr_HostKeyAlg));
        break;
    case EM_SSH_SVRKEXINIT_SVR_HOSTKEYALG:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_svr_HostKeyAlg);
        break;
    case EM_SSH_SVRKEXINIT_ENCRYPTALGC2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_encryptAlgC2S));
        break;
    case EM_SSH_SVRKEXINIT_ENCRYPTALGC2S:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_encryptAlgC2S);
        break;
    case EM_SSH_SVRKEXINIT_ENCRYPTALGS2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_encryptAlgS2C));
        break;
    case EM_SSH_SVRKEXINIT_ENCRYPTALGS2C:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_encryptAlgS2C);
        break;
    case EM_SSH_SVRKEXINIT_MACALGC2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_macAlgC2S));
        break;
    case EM_SSH_SVRKEXINIT_MACALGC2S:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_macAlgC2S);
        break;
    case EM_SSH_SVRKEXINIT_MACALGS2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_macAlgS2C));
        break;
    case EM_SSH_SVRKEXINIT_MACALGS2C:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_macAlgS2C);
        break;
    case EM_SSH_SVRKEXINIT_COMPRESSALGC2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_compressAlgC2S));
        break;
    case EM_SSH_SVRKEXINIT_COMPRESSALGC2S:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_compressAlgC2S);
        break;
    case EM_SSH_SVRKEXINIT_COMPRESSALGS2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_compressAlgS2C));
        break;
    case EM_SSH_SVRKEXINIT_COMPRESSALGS2C:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_compressAlgS2C);
        break;
    case EM_SSH_SVRKEXINIT_LANGUAGES_C2SLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_languages_C2S));
        break;
    case EM_SSH_SVRKEXINIT_LANGUAGES_C2S:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_languages_C2S);
        break;
    case EM_SSH_SVRKEXINIT_LANGUAGES_S2CLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PROTOCOL_VAL_LEN(SvrKexInit_languages_S2C));
        break;
    case EM_SSH_SVRKEXINIT_LANGUAGES_S2C:
        WRITE_HEAD_DEF_LINUX_STR(SvrKexInit_languages_S2C);
        break;
    case EM_SSH_SVRKEXINIT_KEX_FIRST_PKT_FW:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->SvrKexInit_kex_First_Pkt_fw);
        break;
    case EM_SSH_SERVERKEXINIT_RESERVED:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ServerKexInit_reserved, strlen(info->ServerKexInit_reserved));
        break;
    case EM_SSH_SERVERKEXINIT_PADING:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ServerKexInit_Pading, strlen(info->ServerKexInit_Pading));

        break;
	case EN_SSH_SERVERKEXINIT_HASSH:
		write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerHassh, strlen(info->ServerHassh));
		break;
    case EM_SSH_CLIENTKEXDHINIT_PACKETLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexDHINIT_PacketLength);
        break;
    case EM_SSH_CLIENTKEXDHINIT_PADINGLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexDHINIT_PadingLength);
        break;
    case EM_SSH_CLIENTKEXDHINIT_MESSAGECODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexDHINIT_MessageCode);
        break;
    case EM_SSH_CLIENTKEXDHINIT_E_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexDHINIT_eLen);
        break;
    case EM_SSH_CLIENTKEXDHINIT_E:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                                info->ClientKexDHINIT_e, info->ClientKexDHINIT_eLen);
        break;
    case EM_SSH_CLIENTKEXDHINIT_PADING:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ClientKexDHINIT_Pading, strlen(info->ClientKexDHINIT_Pading));

        break;
    case EM_SSH_CLIENTKEXGEX_REQ_PACKETLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexGEX_REQ_PacketLen);
        break;
    case EM_SSH_CLIENTKEXGEX_REQ_PADINGLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexGEX_REQ_PadingLen);
        break;
    case EM_SSH_CLIENTKEXGEX_REQ_MSGCODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexGEX_REQ_MsgCode);
        break;
    case EM_SSH_CLIENTKEXGEX_REQUEST_MIN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexGEX_REQUEST_min);
        break;
    case EM_SSH_CLIENTKEXGEX_REQUEST_NBITS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexGEX_REQUEST_nbits);
        break;
    case EM_SSH_CLIENTKEXGEX_REQUEST_MAX:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientKexGEX_REQUEST_max);
        break;
    case EM_SSH_CLIENTKEXGEX_REQUEST_PADING:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ClientKexGEX_REQUEST_Pading, strlen(info->ClientKexGEX_REQUEST_Pading));

        break;
    case EM_SSH_SERVERKEXGEX_GROUP_PACKETLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexGEX_GROUP_PacketLen);
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_PADINGLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexGEX_GROUP_PadingLen);
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_MSGCODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexGEX_GROUP_MsgCode);
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_P_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, strlen(info->ServerKexGEX_GROUP_p));
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_P:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ServerKexGEX_GROUP_p, strlen(info->ServerKexGEX_GROUP_p));
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_G_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, strlen(info->ServerKexGEX_GROUP_g));
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_G:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ServerKexGEX_GROUP_g, strlen(info->ServerKexGEX_GROUP_g));
        break;
    case EM_SSH_SERVERKEXGEX_GROUP_PADING:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                info->ServerKexGEX_GROUP_Pading, strlen(info->ServerKexGEX_GROUP_Pading));

        break;
    case EM_SSH_SERVERKEXDHREPLY_PACKETLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexDHReply_PacketLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_PADINGLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexDHReply_PadingLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_MESSAGECODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexDHReply_MessageCode);

        break;
    case EM_SSH_SERVERKEXDHREPLY_PUBKEYLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexDHReply_PubKeyLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_PUBKEY:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                            info->ServerKexDHReply_PubKey, info->ServerKexDHReply_PubKeyLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_F_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexDHReply_fLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_F:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                                info->ServerKexDHReply_f, info->ServerKexDHReply_fLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_SIG_LEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerKexDHReply_SignatureLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_SIGNATURE:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                        info->ServerKexDHReply_Signature, info->ServerKexDHReply_SignatureLen);
        break;
    case EM_SSH_SERVERKEXDHREPLY_PADING:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN,
                                        info->ServerKexDHReply_Pading, info->ServerKexDHReply_PadingLen);
        break;
    case EM_SSH_MSGNEWKEYS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->MsgNewKeys);
        break;

    case EM_SSH_SERVER_RSA_DS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->Server_RSA_ds, info->Server_RSA_ds_len);
        break;

    case EM_SSH_SERVER_RSA_MS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->Server_RSA_ms, info->Server_RSA_ms_len);
        break;

    case EM_SSH_SERVERKEXGEX_GROUP_Q:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->ServerKexGEX_GROUP_q, info->ServerKexGEX_GROUP_q_len);
        break;

    case EM_SSH_SERVERKEXGEX_GROUP_Y:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->ServerKexGEX_GROUP_y, info->ServerKexGEX_GROUP_y_len);
        break;

    case EM_SSH_SERVER_KEY_ZS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_UINT64, NULL, 65537);
        break;

    case EM_SSH_SERVER_KEY_MS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->Server_key_ms, info->Server_key_ms_len);
        break;

    case EM_SSH_SERVER_KEY:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->Server_key_sig, info->Server_key_sig_len);
        break;

    case EM_SSH_SERVER_KEYEX_SCY:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->Server_keyex_g, info->Server_keyex_g_len);
        break;

    case EM_SSH_SERVER_KEYEX_MS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_HEX, (const uint8_t*)info->Server_keyex_p, info->Server_keyex_p_len);
        break;

    case EM_SSH_SERVER_VERSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->server_version_ptr, info->server_version_len);
        break;

    case EM_SSH_PCAP_FILENAME:
        if(g_config.ssh_pcap){
            char filename[64];
            snprintf(filename, 64, "ssh_%u%u%lu.pcap", g_config.mac, flow->thread_id, flow->flow_id);
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, filename, strlen(filename));
        }
        else{
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1); 
        }
        break;
    case EM_SSH_HOSTNAME:
    {
        char __str[64];
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
        else
            get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
    }
        break;
    case EM_SSH_LOGIN_STATUS:
        // if(session_flag){
        //     //if(flow->src2dst_packets + flow->dst2src_packets >= g_config.ssh_login_valid_num && (flow->timestamp - flow->create_time) / 1e6 > g_config.login_success_time)
        //     if(flow->src2dst_packets + flow->dst2src_packets >= g_config.ssh_login_valid_num)
        //         write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "YES", 3);
        //     else
        //         write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "NO",  2);
        // }
        // else{
        //     write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);  
        // }
		write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SSH_OPERATION_TYPE:
        write_string_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, value_to_string(ssh_op_type, info->operation_type));
        break;
    default:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);  
        break;
    }

    return;
}



static int write_ssh_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    struct ssh_info *info=(struct ssh_info *)field_info;
    if(!info){
        return 0;
    }
    
    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ssh");

	for (i = 0; i < EM_SSH_MAX; i++) {
		ssh_field_element(log_ptr, flow, direction, info, &idx, i);
	}
    
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_SSH;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}


static int dissect_ssh_v2(struct flow_info *flow, int direction _U_, uint8_t is_request,
        const uint8_t *payload, uint32_t payload_len, uint32_t offset, struct ssh_info *info)
{
    int ret;
    uint32_t plen;
    uint8_t padding_length;
    uint8_t msg_code;
    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;

    struct ssh_session *session = (struct ssh_session *)flow->app_session;
    if (!session)
        return SSH_ERROR;

    //dissect ssh
    if(session->client_new_keys_appear && session->server_new_keys_appear)
    {
        // 已进入密文部分
        return payload_len;
    }

    if (payload_len < 7)
        return SSH_ERROR;

    // GET packet len
    if (-1 == dpi_get_be32(&pkt, offset, &plen))
        return SSH_ERROR;

    offset += 4;

    // GET Padding Length
    if (-1 == dpi_get_uint8(&pkt, offset, &padding_length))
        return SSH_ERROR;

    if (offset + plen > payload_len)
        return SSH_NEED_MORE;

    offset++;

    // GET Message Code
    if (-1 == dpi_get_uint8(&pkt, offset, &msg_code))
        return SSH_ERROR;

    offset++;
    info->operation_type = msg_code;


    if (msg_code >= 30 && msg_code < 40)
    {
        ret = session->kex_specific_dissector(msg_code, &pkt, offset, info);
        if (ret <= 0)
            return SSH_ERROR;

        offset = ret + padding_length;
    }
    else
    if (msg_code == SSH_MSG_KEXINIT)
    {
        offset = ssh_dissect_key_init(&pkt, offset, is_request, info, session);
        if (session->client_kex_proposal && session->server_kex_proposal) {
            /* Note: we're ignoring first_kex_packet_follows. */
            ssh_choose_algo(session->client_kex_proposal, session->server_kex_proposal, session->key, sizeof(session->key));
            ssh_set_kex_specific_dissector(session);
        }


        int i;
        for (i = 0; i < padding_length && offset + i < payload_len; i++) {
            if (is_request) {
                if (i * 2 >= (int)sizeof(info->ClientKexInit_Pading))
                    break;
                snprintf(info->ClientKexInit_Pading + 2 * i, sizeof(info->ClientKexInit_Pading) - 2 * i, "%02x", *(payload + offset + i));
            } else {
                if (i * 2 >= (int)sizeof(info->ServerKexInit_Pading))
                    break;
                snprintf(info->ServerKexInit_Pading + 2 * i, sizeof(info->ServerKexInit_Pading) - 2 * i, "%02x", *(payload + offset + i));
            }
        }

        offset += padding_length;
    }
    else
    if (msg_code == SSH_MSG_NEWKEYS)
    {
        if (is_request && !session->client_new_keys_appear) {
            session->client_new_keys_appear = 1;
        } else if (!is_request && !session->server_new_keys_appear) {
            session->server_new_keys_appear = 1;
        }
        info->MsgNewKeys = 1;

        offset += padding_length;
    }
    else
    {
        offset += plen;
    }

    if(g_config.ssh_packet_mode)
        write_ssh_log(flow, flow->direction, info,NULL);

    return offset;
}




static int is_encrypted(const uint8_t *payload, const uint32_t payload_len)
{
    if(NULL == payload && payload_len <6)
        return 0;

    int ret = memcmp(payload, "SSH-", 4);
    if(0 == ret)
        return 0;
    else if(payload[5] == 20 || payload[5] == 21 || payload[5] == 30 || payload[5] == 31)
        return 0;
    else
        return 1;
}


/*
*ssh的解析入口函数
*/
static int dissect_ssh(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len)
{
    int         i;
    int         sp_len;
    uint8_t     is_request;
    uint32_t    offset = 0;

    struct ssh_info     *info;
    struct ssh_session  *session;

    flow->match_data_len = payload_len;

    session = (struct ssh_session *)flow->app_session;
    info    = &session->info;

    if (direction == FLOW_DIR_SRC2DST)
        is_request = 1;
    else
        is_request = 0;

    if(session->written)
        return payload_len;

    while (offset < payload_len)
    {
        // 1, SSH 版本报文
        if (payload_len > 7 && memcmp(payload, "SSH-", 4) == 0)
        {

            // 是不是 客户端 行为 呀？
            if(FLOW_DIR_SRC2DST == direction)
            {
                sp_len = DPI_MIN(payload_len, 64 - 1);
                memcpy(info->client_version_ptr, payload, sp_len);
                dpi_utils_rstrip(info->client_version_ptr, sp_len, NULL);
                info->client_version_len = strlen(info->client_version_ptr);
            }
            else
            {
                sp_len = DPI_MIN(payload_len, 64 - 1);
                memcpy(info->server_version_ptr, payload, sp_len);
                dpi_utils_rstrip(info->server_version_ptr, sp_len, NULL);
                info->server_version_len = strlen(info->server_version_ptr);
            }

            if (is_request)
            {
                if (payload_len > 6 && memcmp(payload, "SSH-2.", 6) == 0)
                {
                    session->version = SSH_VERSION_2;
                }
                else
                if (payload_len > 9 && memcmp(payload, "SSH-1.99-", 9) == 0)
                {                    session->version = SSH_VERSION_2;
                }
                else if (payload_len > 6 && memcmp(payload, "SSH-1.", 6) == 0)
                {
                    session->version = SSH_VERSION_1;
                }
            }

            offset += payload_len;
        }
        else
        // 2, SSH 协商报文
        {
            switch (session->version)
            {
                case SSH_VERSION_UNKNOWN:
                    break;
                case SSH_VERSION_1:
                    break;
                case SSH_VERSION_2:
                    offset = dissect_ssh_v2(flow, direction, is_request, payload, payload_len, offset, info);
                    break;
                default:
                    break;
            }
        }

        if ((int)offset <= 0)
            break;
    }

    //SSH 改为 超时 才输出. 解决BUG 2258

    return (int)offset;
}

// 专业切割
// 解决 一条消息分为多帧, 需要完成的消息才能解析。
static int dissect_ssh_pipeline(struct flow_info *flow, uint8_t C2S, const uint8_t *p, uint32_t  pl)
{
    int64_t              hl    = 0;
    int64_t              offset= 0;
    int64_t              l     = pl;
    struct flow_info    *f     = flow;
    struct ssh_session  *s     = NULL;
    struct ssh_cache    *c     = NULL;

    if (NULL == f->app_session)
    {
        f->app_session = ssh_session_init();
        if (NULL == f->app_session)
        {
            goto SSH_NEED_MORE_PKT;
        }
    }
    s = (struct ssh_session *)f->app_session;
    c = s->cache + C2S;

    // 是否有缓存数据
    if(c->cache_hold > 0)
    {
        if(l > (c->cache_size - c->cache_hold))
        {
            // 缓存撑爆前,  解析数据, 释放
            dissect_ssh(f, C2S, (const uint8_t*)c->cache, (uint32_t)c->cache_hold);
            memset(c->cache, 0, c->cache_size);
            c->cache_hold = 0;
        }
 
        //正常 拼装
        memcpy(c->cache + c->cache_hold, p, l);
        c->cache_hold  += l;
        p = (const uint8_t*)c->cache;
        l = c->cache_hold;
    }

    // 专业切割机
    while(offset < l)
    {
        hl = dissect_ssh(f, C2S, (const uint8_t*)p + offset, (uint32_t)l - offset);
        if(hl > 0)
        { 
            offset += hl;
        }
        else if(hl == SSH_ERROR)
        {
            goto SSH_DROP;
        }
        else if(hl == SSH_NEED_MORE)
        {
            break;
        }
        else if(hl > l - offset)
        {
            break;
        }
    }

    // 有没有剩料?
    if(offset>=0 && offset < l)
    {
        if(c->cache_hold > 0)  //已开启缓存, 将剩料挪到前面
        {
            memmove(c->cache, c->cache + offset, c->cache_hold - offset);
            c->cache_hold  -= offset;
        }
        else if(c->cache_hold == 0)          //未开启缓存, 直接把剩料放在前面
        {
            c->cache_hold = l - offset;
            memcpy(c->cache, p + offset, l - offset);
        }
        goto SSH_NEED_MORE_PKT;
    }

SSH_DROP:
    if(c->cache_hold > 0)
    {
        memset(c->cache, 0, c->cache_size);
        c->cache_hold = 0;
    }

// SSH 解析,需要更多报文
SSH_NEED_MORE_PKT:
    return 0;
}


extern struct decode_t decode_ssh;
static int init_ssh_dissector(struct decode_t *decode _U_)
{
    decode_on_port_tcp(SSH_PORT,   &decode_ssh);

    dpi_register_proto_schema(ssh_field_array, EM_SSH_MAX, "ssh");
    map_fields_info_register(ssh_field_array, PROTOCOL_SSH, EM_SSH_MAX, "ssh");
    register_tbl_array(TBL_LOG_SSH, 0, "ssh", NULL);

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_SSH].excluded_protocol_bitmask, PROTOCOL_SSH);
    return 0;
}


static int ssh_miss(struct flow_info *flow, uint8_t C2S, uint32_t len)
{
    UNUSED(flow);
    UNUSED(C2S);
    UNUSED(len);
    return 0;
}

static void flow_ssh_finish(struct flow_info *flow)
{
    if(flow->app_session)
    {
        struct ssh_session *session = (struct ssh_session *)flow->app_session;

        //输出 容忍单向数据流
        if(session->client_new_keys_appear || session->server_new_keys_appear
          || (session->info.client_version_len || session->info.server_version_len))
        {
            write_ssh_log(flow, flow->direction, &session->info, NULL);
        }

        dpi_free(session->cache[0].cache);
        dpi_free(session->cache[1].cache);

        dpi_free(flow->app_session);
        flow->app_session = NULL;
    }
}

static int ssh_destroy(struct decode_t *decode)
{
    UNUSED(decode);
    return 0;
}


struct decode_t decode_ssh = {
    .name           = "ssh",
    .decode_initial = init_ssh_dissector,
    .identify_type  = DPI_IDENTIFY_PORT_CONTENT,
    .pkt_identify   = identify_ssh,
    .pkt_dissect    = dissect_ssh_pipeline,
    .pkt_miss       = ssh_miss,
    .flow_finish    = flow_ssh_finish,
    .decode_destroy = ssh_destroy,
};
