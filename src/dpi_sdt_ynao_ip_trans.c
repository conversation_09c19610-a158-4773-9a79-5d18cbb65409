#ifdef DPI_SDT_YNAO

#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <glib.h>

#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"

#include "dpi_high_app_protos.h"
#include "sdt_ip_protocols.h"
#include "dpi_sdt_link.h"
#include "dpi_utils.h"

#define LOCAL_BUFFER_SIZE       64
#define LOCAL_STREAM_BUFF_SIZE  8192

extern struct rte_mempool *tbl_log_mempool;
static int tcp_operator_ip_data(struct flow_info *flow, char *buff, uint32_t buff_len,const uint8_t *data, uint32_t data_len);
static int get_payload_len_sequence(uint16_t *set, int cnt, char *buff, int size);

dpi_field_table ip_field_array[] = {
    DPI_FIELD_D(EM_YNAO_IP_IPVER,                    YA_FT_UINT32,       "ipVer"),
    DPI_FIELD_D(EM_YNAO_IP_SRCADDR,                  YA_FT_STRING,       "srcAddr"),
    DPI_FIELD_D(EM_YNAO_IP_SRCADDRV6,                YA_FT_STRING,       "srcAddrV6"),
    DPI_FIELD_D(EM_YNAO_IP_DSTADDR,                  YA_FT_STRING,       "dstAddr"),
    DPI_FIELD_D(EM_YNAO_IP_DSTADDRV6,                YA_FT_STRING,       "dstAddrV6"),
    DPI_FIELD_D(EM_YNAO_IP_SRCPORT,                  YA_FT_UINT16,       "srcPort"),
    DPI_FIELD_D(EM_YNAO_IP_DSTPORT,                  YA_FT_UINT16,       "dstPort"),
    DPI_FIELD_D(EM_YNAO_IP_PROTNUM,                  YA_FT_UINT32,       "protNum"),
    DPI_FIELD_D(EM_YNAO_IP_SRCCOUNTRY,               YA_FT_STRING,       "srcCountry"),
    DPI_FIELD_D(EM_YNAO_IP_SRCSTATE,                 YA_FT_STRING,       "srcState"),
    DPI_FIELD_D(EM_YNAO_IP_SRCCITY,                  YA_FT_STRING,       "srcCity"),
    DPI_FIELD_D(EM_YNAO_IP_SRCLONGITUDE,             YA_FT_STRING,       "srcLongitude"),
    DPI_FIELD_D(EM_YNAO_IP_SRCLATITUDE,              YA_FT_STRING,       "srcLatitude"),
    DPI_FIELD_D(EM_YNAO_IP_SRCISP,                   YA_FT_STRING,       "srcISP"),
    DPI_FIELD_D(EM_YNAO_IP_SRCASN,                   YA_FT_UINT32,       "srcASN"),
    DPI_FIELD_D(EM_YNAO_IP_DSTCOUNTRY,               YA_FT_STRING,       "dstCountry"),
    DPI_FIELD_D(EM_YNAO_IP_DSTSTATE,                 YA_FT_STRING,       "dstState"),
    DPI_FIELD_D(EM_YNAO_IP_DSTCITY,                  YA_FT_STRING,       "dstCity"),
    DPI_FIELD_D(EM_YNAO_IP_DSTLONGITUDE,             YA_FT_STRING,       "dstLongitude"),
    DPI_FIELD_D(EM_YNAO_IP_DSTLATITUDE,              YA_FT_STRING,       "dstLatitude"),
    DPI_FIELD_D(EM_YNAO_IP_DSTISP,                   YA_FT_STRING,       "dstISP"),
    DPI_FIELD_D(EM_YNAO_IP_DSTASN,                   YA_FT_UINT32,       "dstASN"),
    DPI_FIELD_D(EM_YNAO_IP_PROTINFO,                 YA_FT_STRING,       "protInfo"),
    DPI_FIELD_D(EM_YNAO_IP_PROTTYPE,                 YA_FT_STRING,       "protType"),
    DPI_FIELD_D(EM_YNAO_IP_PROTNAME,                 YA_FT_STRING,       "protName"),

    DPI_FIELD_D(EM_YNAO_IP_BEGTIME,                  YA_FT_UINT64,       "begTime"),
    DPI_FIELD_D(EM_YNAO_IP_ENDTIME,                  YA_FT_UINT64,       "endTime"),
    DPI_FIELD_D(EM_YNAO_IP_COMDUR,                   YA_FT_UINT32,       "comDur"),

    DPI_FIELD_D(EM_YNAO_IP_FIRTTLBYCLI,              YA_FT_UINT32,       "firTtlByCli"),
    DPI_FIELD_D(EM_YNAO_IP_FIRTTLBYSRV,              YA_FT_UINT32,       "firTtlBySrv"),
    DPI_FIELD_D(EM_YNAO_IP_APPDIREC,                 YA_FT_UINT32,       "appDirec"),
    DPI_FIELD_D(EM_YNAO_IP_PKTNUM,                   YA_FT_UINT32,       "pktNum"),
    DPI_FIELD_D(EM_YNAO_IP_UPLINKPKTNUM,             YA_FT_UINT32,       "upLinkPktNum"),
    DPI_FIELD_D(EM_YNAO_IP_DOWNLINKPKTNUM,           YA_FT_UINT32,       "downLinkPktNum"),
    DPI_FIELD_D(EM_YNAO_IP_SESBYTES,                 YA_FT_UINT32,       "sesBytes"),
    DPI_FIELD_D(EM_YNAO_IP_UPSESBYTES,               YA_FT_UINT64,       "upSesBytes"),
    DPI_FIELD_D(EM_YNAO_IP_DOWNSESBYTES,             YA_FT_UINT64,       "downSesBytes"),
    DPI_FIELD_D(EM_YNAO_IP_SESBYTESRATIO,            YA_FT_UINT32,       "sesBytesRatio"),
    DPI_FIELD_D(EM_YNAO_IP_PAYLEN,                   YA_FT_UINT32,       "payLen"),
    DPI_FIELD_D(EM_YNAO_IP_UPPAYLEN,                 YA_FT_UINT32,       "upPayLen"),
    DPI_FIELD_D(EM_YNAO_IP_DOWNPAYLEN,               YA_FT_UINT32,       "downPayLen"),
    DPI_FIELD_D(EM_YNAO_IP_PAYLENRATIO,              YA_FT_UINT32,       "payLenRatio"),
    DPI_FIELD_D(EM_YNAO_IP_DESBYTES,                 YA_FT_UINT64,       "desBytes"),
    DPI_FIELD_D(EM_YNAO_IP_UPLINKDESBYTES,           YA_FT_UINT64,       "upLinkDesBytes"),
    DPI_FIELD_D(EM_YNAO_IP_DOWNLINKDESBYTES,         YA_FT_UINT64,       "downLinkDesBytes"),
    DPI_FIELD_D(EM_YNAO_IP_STREAM,                   YA_FT_STRING,       "stream"),
    DPI_FIELD_D(EM_YNAO_IP_UPLINKSTREAM,             YA_FT_STRING,       "upLinkStream"),
    DPI_FIELD_D(EM_YNAO_IP_DOWNLINKSTREAM,           YA_FT_STRING,       "downLinkStream"),
    DPI_FIELD_D(EM_YNAO_IP_AVGPKTLEN,                YA_FT_UINT32,       "avgPktLen"),
    DPI_FIELD_D(EM_YNAO_IP_AVGPKTINT,                YA_FT_UINT32,       "avgPktInt"),
};

dpi_field_table link_field_array[] = {
    DPI_FIELD_D(EM_LINK_UPLINKTRANSPAYHEX,        YA_FT_STRING,       "upLinkTransPayHex"),
    DPI_FIELD_D(EM_LINK_DOWNLINKTRANSPAYHEX,      YA_FT_STRING,       "downLinkTransPayHex"),
    DPI_FIELD_D(EM_LINK_UPLINKPAYLENSET,          YA_FT_STRING,       "upLinkPayLenSet"),
    DPI_FIELD_D(EM_LINK_DOWNLINKPAYLENSET,        YA_FT_STRING,       "downLinkPayLenSet"),
    DPI_FIELD_D(EM_LINK_UPLINKBIGPKTLEN,          YA_FT_UINT32,       "upLinkBigPktLen"),
    DPI_FIELD_D(EM_LINK_DOWNLINKBIGPKTLEN,        YA_FT_UINT32,       "downLinkBigPktLen"),
    DPI_FIELD_D(EM_LINK_UPLINKSMAPKTLEN,          YA_FT_UINT32,       "upLinkSmaPktLen"),
    DPI_FIELD_D(EM_LINK_DOWNLINKSMAPKTLEN,        YA_FT_UINT32,       "downLinkSmaPktLen"),
    DPI_FIELD_D(EM_LINK_UPLINKFREQPKTLEN,         YA_FT_UINT32,       "upLinkFreqPktLen"),
    DPI_FIELD_D(EM_LINK_DOWNLINKFREQPKTLEN,       YA_FT_UINT32,       "downLinkFreqPktLen"),
    DPI_FIELD_D(EM_LINK_UPLINKBIGPKTINT,          YA_FT_UINT32,       "upLinkBigPktInt"),
    DPI_FIELD_D(EM_LINK_DOWNLINKBIGPKTINT,        YA_FT_UINT32,       "downLinkBigPktInt"),
    DPI_FIELD_D(EM_LINK_UPLINKSMAPKTINT,          YA_FT_UINT32,       "upLinkSmaPktInt"),
    DPI_FIELD_D(EM_LINK_DOWNLINKSMAPKTINT,        YA_FT_UINT32,       "downLinkSmaPktInt"),
    DPI_FIELD_D(EM_LINK_FIRSTFLAGS,               YA_FT_STRING,       "firstFlag"),
    DPI_FIELD_D(EM_LINK_UPLINKSYNSEQNUM,          YA_FT_UINT32,       "upLinkSynSeqNum"),
    DPI_FIELD_D(EM_LINK_DOWNLINKSYNSEQNUM,        YA_FT_UINT32,       "downLinkSynSeqNum"),
    DPI_FIELD_D(EM_LINK_UPLINKSYNTCPWINS,         YA_FT_UINT16,       "upLinkSynTcpWins"),
    DPI_FIELD_D(EM_LINK_DOWNLINKSYNTCPWINS,       YA_FT_UINT16,       "downLinkSynTcpWins"),
    DPI_FIELD_D(EM_LINK_UPLINKTCPOPTS,            YA_FT_STRING,       "upLinkTcpOpts"),
    DPI_FIELD_D(EM_LINK_DOWNLINKTCPOPTS,          YA_FT_STRING,       "downLinkTcpOpts"),
    DPI_FIELD_D(EM_LINK_UPLINKFLAGS,              YA_FT_STRING,       "upLinkFlags"),
    DPI_FIELD_D(EM_LINK_DOWNLINKFLAGS,            YA_FT_STRING,       "downLinkFlags"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSFINCNT,           YA_FT_UINT32,       "tcpFlagsFinCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSSYNCNT,           YA_FT_UINT32,       "tcpFlagsSynCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSRSTCNT,           YA_FT_UINT32,       "tcpFlagsRstCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSPSHCNT,           YA_FT_UINT32,       "tcpFlagsPshCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSACKCNT,           YA_FT_UINT32,       "tcpFlagsAckCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSURGCNT,           YA_FT_UINT32,       "tcpFlagsUrgCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSECECNT,           YA_FT_UINT32,       "tcpFlagsEceCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSCWRCNT,           YA_FT_UINT32,       "tcpFlagsCwrCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSNSCNT,            YA_FT_UINT32,       "tcpFlagsNSCnt"),
    DPI_FIELD_D(EM_LINK_TCPFLAGSSYNACKCNT,        YA_FT_UINT32,       "tcpFlagsSynAckCnt"),
    DPI_FIELD_D(EM_LINK_ESTABLISH,                YA_FT_UINT8,        "tcpEstablish"),
    DPI_FIELD_D(EM_LINK_FINISHED,                 YA_FT_UINT8,        "tcpFinished"),
};



/********************************************************************************************/
/*                                     TCP option                                           */
/********************************************************************************************/


/*
 *  TCP option
 */
#define TCPOPT_NOP              1       /* Padding */
#define TCPOPT_EOL              0       /* End of options */
#define TCPOPT_MSS              2       /* Segment size negotiating */
#define TCPOPT_WINDOW           3       /* Window scaling */
#define TCPOPT_SACK_PERM        4       /* SACK Permitted */
#define TCPOPT_SACK             5       /* SACK Block */
#define TCPOPT_ECHO             6
#define TCPOPT_ECHOREPLY        7
#define TCPOPT_TIMESTAMP        8       /* Better RTT estimations/PAWS */
#define TCPOPT_CC               11
#define TCPOPT_CCNEW            12
#define TCPOPT_CCECHO           13
#define TCPOPT_MD5              19      /* RFC2385 */
#define TCPOPT_SCPS             20      /* SCPS Capabilities */
#define TCPOPT_SNACK            21      /* SCPS SNACK */
#define TCPOPT_RECBOUND         22      /* SCPS Record Boundary */
#define TCPOPT_CORREXP          23      /* SCPS Corruption Experienced */
#define TCPOPT_QS               27      /* RFC4782 Quick-Start Response */
#define TCPOPT_USER_TO          28      /* RFC5482 User Timeout Option */
#define TCPOPT_MPTCP            30      /* RFC6824 Multipath TCP */
#define TCPOPT_TFO              34      /* RFC7413 TCP Fast Open Cookie */
#define TCPOPT_EXP_FD           0xfd    /* Experimental, reserved */
#define TCPOPT_EXP_FE           0xfe    /* Experimental, reserved */
/* Non IANA registered option numbers */
#define TCPOPT_RVBD_PROBE       76      /* Riverbed probe option */
#define TCPOPT_RVBD_TRPY        78      /* Riverbed transparency option */

/*
 *     TCP option lengths
 */
#define TCPOLEN_MSS            4
#define TCPOLEN_WINDOW         3
#define TCPOLEN_SACK_PERM      2
#define TCPOLEN_SACK_MIN       2
#define TCPOLEN_ECHO           6
#define TCPOLEN_ECHOREPLY      6
#define TCPOLEN_TIMESTAMP     10
#define TCPOLEN_CC             6
#define TCPOLEN_CCNEW          6
#define TCPOLEN_CCECHO         6
#define TCPOLEN_MD5           18
#define TCPOLEN_SCPS           4
#define TCPOLEN_SNACK          6
#define TCPOLEN_RECBOUND       2
#define TCPOLEN_CORREXP        2
#define TCPOLEN_QS             8
#define TCPOLEN_USER_TO        4
#define TCPOLEN_MPTCP_MIN      3
#define TCPOLEN_TFO_MIN        2
#define TCPOLEN_RVBD_PROBE_MIN 3
#define TCPOLEN_RVBD_TRPY_MIN 16
#define TCPOLEN_EXP_MIN        2

/*
 *     Multipath TCP subtypes
 */
#define TCPOPT_MPTCP_MP_CAPABLE    0x0    /* Multipath TCP Multipath Capable */
#define TCPOPT_MPTCP_MP_JOIN       0x1    /* Multipath TCP Join Connection */
#define TCPOPT_MPTCP_DSS           0x2    /* Multipath TCP Data Sequence Signal */
#define TCPOPT_MPTCP_ADD_ADDR      0x3    /* Multipath TCP Add Address */
#define TCPOPT_MPTCP_REMOVE_ADDR   0x4    /* Multipath TCP Remove Address */
#define TCPOPT_MPTCP_MP_PRIO       0x5    /* Multipath TCP Change Subflow Priority */
#define TCPOPT_MPTCP_MP_FAIL       0x6    /* Multipath TCP Fallback */
#define TCPOPT_MPTCP_MP_FASTCLOSE  0x7    /* Multipath TCP Fast Close */

struct tcp_val_string{
    int  type;
    const char* string;
};


static const struct tcp_val_string tcp_option_kind_vs[] = {
    { TCPOPT_EOL, "End of Option List"},
    { TCPOPT_NOP, "No-Operation" },
    { TCPOPT_MSS, "Maximum Segment Size" , },
    { TCPOPT_WINDOW, "Window Scale" },
    { TCPOPT_SACK_PERM, "SACK Permitted" },
    { TCPOPT_SACK, "SACK" },
    { TCPOPT_ECHO, "Echo" },
    { TCPOPT_ECHOREPLY, "Echo Reply" },
    { TCPOPT_TIMESTAMP, "Time Stamp Option" },
    { 9, "Partial Order Connection Permitted" },
    { 10, "Partial Order Service Profile" },
    { TCPOPT_CC, "CC" },
    { TCPOPT_CCNEW, "CC.NEW" },
    { TCPOPT_CCECHO, "CC.ECHO" },
    { 14, "TCP Alternate Checksum Request" },
    { 15, "TCP Alternate Checksum Data" },
    { 16, "Skeeter" },
    { 17, "Bubba" },
    { 18, "Trailer Checksum Option" },
    { TCPOPT_MD5, "MD5 Signature Option" },
    { TCPOPT_SCPS, "SCPS Capabilities" },
    { TCPOPT_SNACK, "Selective Negative Acknowledgements" },
    { TCPOPT_RECBOUND, "Record Boundaries" },
    { TCPOPT_CORREXP, "Corruption experienced" },
    { 24, "SNAP" },
    { 25, "Unassigned" },
    { 26, "TCP Compression Filter" },
    { TCPOPT_QS, "Quick-Start Response" },
    { TCPOPT_USER_TO, "User Timeout Option" },
    { 29, "TCP Authentication Option" },

//    { TCPOPT_MPTCP, "Multipath TCP" },
//    { TCPOPT_TFO, "TCP Fast Open Cookie" },
//    { TCPOPT_RVBD_PROBE, "Riverbed Probe" },
//    { TCPOPT_RVBD_TRPY, "Riverbed Transparency" },
//    { TCPOPT_EXP_FD, "RFC3692-style Experiment 1" },
//    { TCPOPT_EXP_FE, "RFC3692-style Experiment 2" },
//    { 0, NULL }
};

struct value_type*cb_link_portinfo(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->proto_type);
    return p;
}
struct value_type*cb_link_portinfoatt(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_link_paylen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_payload_len+flow->dst2src_payload_len);
    return p;
}
struct value_type*cb_link_uppaylen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_payload_len);
    return p;
}
struct value_type*cb_link_downpaylen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_payload_len);
    return p;
}
struct value_type*cb_link_tcpflag(ProtoRecord *pRec)
{
    if(pRec->pkt->tcph)
    {
        uint32_t var = 0;
        const struct dpi_tcphdr *tcph = (const struct dpi_tcphdr *)pRec->pkt->tcph;
        var |= tcph->fin << 0;
        var |= tcph->syn << 1;
        var |= tcph->rst << 2;
        var |= tcph->psh << 3;
        var |= tcph->ack << 4;
        var |= tcph->urg << 5;
        var |= tcph->ece << 6;
        var |= tcph->cwr << 7;
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)var;
        return p;
    }
    return NULL;
}
struct value_type*cb_link_pktnum(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_packets+flow->dst2src_packets);
    return p;
}
struct value_type*cb_link_uplinkpktnum(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_packets);
    return p;
}
struct value_type*cb_link_uplinkbigpktlen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_max_packet_len);
    return p;
}
struct value_type*cb_link_uplinksmapktlen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_min_packet_len);
    return p;
}
struct value_type*cb_link_uplinkbigpktint(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_max_packet_interval/1000);
    return p;
}
struct value_type*cb_link_uplinksmapktint(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_min_packet_interval/1000);
    return p;
}
struct value_type*cb_link_downlinkpktnum(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_packets);
    return p;
}
struct value_type*cb_link_downlinkbigpktlen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_max_packet_len);
    return p;
}
struct value_type*cb_link_downlinksmapktlen(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_max_packet_len);
    return p;
}
struct value_type*cb_link_downlinkbigpktint(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_max_packet_interval/1000);
    return p;
}
struct value_type*cb_link_downlinksmapktint(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_min_packet_interval/1000);
    return p;
}
struct value_type*cb_link_firttlbycli(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->up_ttl);
    return p;
}
struct value_type*cb_link_firttlbysrv(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->down_ttl);
    return p;
}
struct value_type*cb_link_appdirec(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int direction = get_flow_direction(flow);
    /* 描述流内上层应用的方向，0-未知；1-src端为客户端，2-dst端为服务端 */
    int var = direction == FLOW_DIR_SRC2DST ? 1 : 2;
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(var);
    return p;
}
struct value_type*cb_link_tcpflagsfincnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpfincounter);
    return p;
}
struct value_type*cb_link_tcpflagssyncnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpsyncounter);
    return p;
}
struct value_type*cb_link_tcpflagsrstcnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcprstcounter);
    return p;
}
struct value_type*cb_link_tcpflagspshcnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcppshcounter);
    return p;
}
struct value_type*cb_link_tcpflagsackcnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpackcounter);
    return p;
}
struct value_type*cb_link_tcpflagsurgcnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpurgcounter);
    return p;
}
struct value_type*cb_link_tcpflagsececnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpececounter);
    return p;
}
struct value_type*cb_link_tcpflagscwrcnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpcwrcounter);
    return p;
}
struct value_type*cb_link_tcpflagsnscnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpnscounter);
    return p;
}
struct value_type*cb_link_tcpflagssynackcnt(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tcpsynackcounter);
    return p;
}
struct value_type*cb_link_etags(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_link_ttags(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_link_desbytes(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_payload_len+flow->dst2src_payload_len);
    return p;
}
struct value_type*cb_link_uplinkdesbytes(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_payload_len);
    return p;
}
struct value_type*cb_link_downlinkdesbytes(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_payload_len);
    return p;
}
struct value_type*cb_link_stream(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = flow->up_stream.index;
    p->val  = (void*)(size_t)(flow->both_stream.byte);
    return p;
}
struct value_type*cb_link_uplinkstream(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = flow->up_stream.index;
    p->val  = (void*)(size_t)(flow->up_stream.byte);
    return p;
}
struct value_type*cb_link_downlinkstream(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = flow->down_stream.index;
    p->val  = (void*)(size_t)(flow->down_stream.byte);
    return p;
}
struct value_type*cb_link_trans_payload_hex(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_link_uplinktranspayhex(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_link_downlinktranspayhex(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_link_uplinkpaylenset(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    get_payload_len_sequence(flow->up_payload_len_set, flow->up_payload_len_cnt, (char *)pRec->val_temp_buff, sizeof(pRec->val_temp_buff));
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_link_downlinkpaylenset(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    get_payload_len_sequence(flow->down_payload_len_set, flow->down_payload_len_cnt, (char *)pRec->val_temp_buff, sizeof(pRec->val_temp_buff));
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_link_establish(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->handshake);
    return p;
}
struct value_type*cb_link_uplinksynseqnum(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->handshake);
    return p;
}
struct value_type*cb_link_downlinksynseqnum(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->up_seq);
    return p;
}
struct value_type*cb_link_uplinksyntcpwins(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->up_window);
    return p;
}
struct value_type*cb_link_downlinksyntcpwins(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->down_window);
    return p;
}
struct value_type*cb_link_uplinktcpopts(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    tcp_operator_ip_data(flow, pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->synData, flow->synDataLen);
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);;
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_link_downlinktcpopts(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    tcp_operator_ip_data(flow, pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->synackData, flow->synackDataLen);
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);;
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_link_upsesbytes(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_bytes);
    return p;
}
struct value_type*cb_link_downsesbytes(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->dst2src_bytes);
    return p;
}
struct value_type*cb_link_sesbytes(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_bytes + flow->dst2src_bytes);
    return p;
}
struct value_type*cb_link_sesbytesratio(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    int var = ((flow->src2dst_bytes*100)/flow->dst2src_bytes);
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(var);
    return p;
}
struct value_type*cb_link_paylenratio(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    int var = ((flow->src2dst_payload_len*100)/flow->dst2src_payload_len);
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(var);
    return p;
}
static int dissect_tcpopt_item(uint8_t opt_type,char *buff, uint16_t buff_len, const uint8_t *data, uint16_t data_len)
{
    int local_data_len=data_len+2;
    switch(opt_type){
    case TCPOPT_MSS:       /* 2 Segment size negotiating */
    {
        if(local_data_len<TCPOLEN_MSS){
            break;
        }
        uint16_t max_seg_size=get_uint16_ntohs(data, 0);
        snprintf(buff, buff_len, "%s:%d,", tcp_option_kind_vs[opt_type].string, max_seg_size);
    }
        break;

    case TCPOPT_WINDOW:    /* 3 Window scaling */
    {
        if(local_data_len<TCPOLEN_WINDOW){
            break;
        }
        snprintf(buff, buff_len, "%s:%d,", tcp_option_kind_vs[opt_type].string, get_uint8_t(data, 0));
    }
        break;

    case TCPOPT_SACK_PERM:  /* 4 SACK Permitted */
    case TCPOPT_SACK:       /* 5 SACK Block */
        snprintf(buff, buff_len, "%s,", tcp_option_kind_vs[opt_type].string);
        break;


    case TCPOPT_ECHO:       //      6
    case TCPOPT_ECHOREPLY:  //      7
        if(local_data_len<TCPOLEN_ECHOREPLY){
            break;
        }
        snprintf(buff, buff_len, "%s:%u", tcp_option_kind_vs[opt_type].string,
                                          (uint32_t)get_uint32_ntohl(data, 0));
        break;

    case TCPOPT_TIMESTAMP:  /* 8 Better RTT estimations/PAWS */
        if(local_data_len<TCPOLEN_TIMESTAMP){
            break;
        }
        snprintf(buff, buff_len, "%s:%u reply:%u,", tcp_option_kind_vs[opt_type].string,
                                                    (uint32_t)get_uint32_ntohl(data, 0),
                                                    (uint32_t)get_uint32_ntohl(data, 4));
        break;



    case TCPOPT_CC:         //      11
    case TCPOPT_CCNEW:      //      12
    case TCPOPT_CCECHO:     //      13
        if(local_data_len<TCPOLEN_CCECHO){
            break;
        }
        snprintf(buff, buff_len, "%s:%u", tcp_option_kind_vs[opt_type].string,
                                          (uint32_t)get_uint32_ntohl(data, 0));
        break;



    case TCPOPT_MD5:        /* 19 RFC2385 */
        if(local_data_len<TCPOLEN_MD5){
            break;
        }
        snprintf(buff, buff_len, "%s:%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x,",
                                                    tcp_option_kind_vs[opt_type].string,
                                                    data[0],data[1],data[2],data[3],
                                                    data[4],data[5],data[6],data[7],
                                                    data[8],data[9],data[10],data[11],
                                                    data[12],data[13],data[14],data[15]);
        break;


    case TCPOPT_SCPS:       /* 20 SCPS Capabilities */
        if(local_data_len<TCPOLEN_SCPS){
            break;
        }
        snprintf(buff, buff_len, "%s:%d,", tcp_option_kind_vs[opt_type].string, get_uint16_ntohs(data, 0));
        break;
    case TCPOPT_SNACK:      /* 21 SCPS SNACK */
        if(local_data_len<TCPOLEN_SNACK){
            break;
        }
        snprintf(buff, buff_len, "%s:%d,", tcp_option_kind_vs[opt_type].string, get_uint32_ntohl(data, 0));

        break;

    case TCPOPT_RECBOUND:   /* 22 SCPS Record Boundary */
    case TCPOPT_CORREXP:    /* 23 SCPS Corruption Experienced */
        if(local_data_len<TCPOLEN_RECBOUND){
            break;
        }
        snprintf(buff, buff_len, "%s,", tcp_option_kind_vs[opt_type].string);
        break;

    case TCPOPT_QS:         /* 27 RFC4782 Quick-Start Response */
        break;
    case TCPOPT_USER_TO:    /* 28 RFC5482 User Timeout Option */
        if(local_data_len<TCPOLEN_USER_TO){
            break;
        }
        snprintf(buff, buff_len, "%s:%d,", tcp_option_kind_vs[opt_type].string, get_uint16_ntohs(data, 0));
    default:
        break;
    }

    return 0;
}



static int tcp_dissect_options(char *buff, uint32_t buff_len,const uint8_t *tcp_opt, uint32_t opt_len)
{
    uint32_t length=opt_len;

    uint8_t  opt;
    uint32_t optlen=0;
    int      offset=0;
    char     tmp[64];
    uint32_t buff_left_len=buff_len;

    while(length>0){

        opt=get_uint8_t(tcp_opt, offset);
        --length;

        if((opt == TCPOPT_EOL) || (opt == TCPOPT_NOP)){
            offset+=1;
        }else{
            optlen=get_uint8_t(tcp_opt, offset+1);
            --length;

            if(optlen<2 || (optlen - 2>length)){
                return 0;
            }

            dissect_tcpopt_item(opt, tmp, 64, (const uint8_t *)&tcp_opt[offset+2], length);

            if(buff_left_len>(uint32_t)strlen(tmp)){
                strncat(buff, tmp, (int)strlen(tmp));
                buff_left_len-=strlen(tmp);
            }else{
                strncat(buff, tmp, buff_left_len -1);
                break;
            }

            offset+=optlen;
            length-=(optlen-2);

        }

        if(TCPOPT_EOL==opt){
            break;
        }
    }


    return 0;
}


static int tcp_operator_ip_data(struct flow_info *flow, char *buff, uint32_t buff_len,const uint8_t *data, uint32_t data_len)
{
    //flow->synackData
    //flow->synData

    if(!data ||!buff || 0==data_len|| 0==buff_len){
        return -1;
    }

    uint16_t ip_header_len=0;
    uint16_t tcp_header_len=0;
    uint32_t opt_offset=0;
    uint32_t opt_len=0;
    if(4==flow->ip_version){
        if(data_len<( sizeof(struct dpi_iphdr)+sizeof(struct dpi_tcphdr) )){
            return -1;
        }
        struct dpi_iphdr *iph4=(struct dpi_iphdr *)data;
        ip_header_len=iph4->ihl*4;

        struct dpi_tcphdr *tcph=(struct dpi_tcphdr *)&data[ip_header_len];
        tcp_header_len=tcph->doff*4;

        opt_offset = ip_header_len+sizeof(struct dpi_tcphdr);
        opt_len    = tcp_header_len-sizeof(struct dpi_tcphdr);
        if(tcp_header_len>sizeof(struct dpi_tcphdr) && data_len>=(ip_header_len+tcp_header_len)){
            tcp_dissect_options(buff, buff_len,
                                (const uint8_t *)&data[opt_offset],
                                opt_len);
        }
    }else if(6==flow->ip_version){
        if(data_len<(sizeof(struct dpi_ipv6hdr)+sizeof(struct dpi_tcphdr))){
            return -1;
        }
        // struct dpi_ipv6hdr *iph6=(struct dpi_ipv6hdr *)data;
        ip_header_len=sizeof(struct dpi_ipv6hdr);

        struct dpi_tcphdr  *tcph=(struct dpi_tcphdr*)&data[ip_header_len];
        tcp_header_len=tcph->doff*4;

        opt_offset = ip_header_len+sizeof(struct dpi_tcphdr);
        opt_len    = tcp_header_len-sizeof(struct dpi_tcphdr);
        if(tcp_header_len>sizeof(struct dpi_tcphdr) && data_len>=(ip_header_len+tcp_header_len)){
            tcp_dissect_options(buff, buff_len,
                                (const uint8_t *)&data[opt_offset],
                                opt_len);
        }

    }else{
        return -1;
    }
    int result_len=strlen(buff);
    if(result_len>0){
        buff[result_len-1]='\0';  // 去掉末尾的逗号
    }

    return 0;
}

static int get_payload_len_sequence(uint16_t *set, int cnt, char *buff, int size)
{
    int offset = 0;
    char number[20];

    memset(buff, 0, size); //不后悔

    for(int i=0; i < cnt; i++)
    {
        int rc = snprintf(number, sizeof(number), "%d,", set[i]);
        if(rc +offset >= size)
        {
            //截断
            return -1;
        }
        memcpy(buff+offset, number, rc);
        offset+=rc;
    }
    return 0;
}

int write_ip(struct flow_info *flow, int direction, SdtMatchResult *match_result _U_)
{
    int val = 0;
    int *idx = &val;
    char tmp_buff[LOCAL_BUFFER_SIZE]={0};
    struct tbl_log *log_ptr;
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, idx, TBL_LOG_MAX_LEN, match_result);
    precord_t *record = log_ptr->record;
    player_t *layer = precord_layer_put_new_layer(record, "ip");

    char      __str[64];
    int tuple_direction = 0;
    if (flow->drt_port_src[direction] > 0) {
        if (flow->drt_port_src[direction] > flow->drt_port_dst[direction]) {
            tuple_direction = FLOW_DIR_SRC2DST;  // c2s
        } else {
            tuple_direction = FLOW_DIR_DST2SRC;  // s2c
        }
        if (tuple_direction != direction) {
            tuple_direction = 1 - direction;
        }
    } else {
        tuple_direction = direction;
    }

    IPINFO srcIp, dstIp;
    memset(&srcIp, 0, sizeof srcIp); /* 防止输出随机值 */
    memset(&dstIp, 0, sizeof dstIp);
    if (g_config.ip_position_switch)  // 打开geoip模块
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
    int i;
    for(i=0; i<EM_YNAO_IP_MAX;i++){
        switch(i){
        case EM_YNAO_IP_IPVER:
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, flow->ip_version);
            break;
        case EM_YNAO_IP_SRCADDR:
            __str[0] = 0;
            if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
                if (flow->ip_version == 4) get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_src);
            } else {
                if (flow->ip_version == 4) get_iparray_to_string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src);
            }
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_YNAO_IP_SRCADDRV6:
            __str[0] = 0;
            if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
                if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_src);
            } else {
                if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src);
            }
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_YNAO_IP_DSTADDR:
            __str[0] = 0;
            if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
                if (flow->ip_version == 4) get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
            } else {
                if (flow->ip_version == 4) get_iparray_to_string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst);
            }
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_YNAO_IP_DSTADDRV6:
            __str[0] = 0;
            if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
                if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
            } else {
                if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst);
            }
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_YNAO_IP_SRCPORT:
            if (tuple_direction == FLOW_DIR_SRC2DST) {
               write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, ntohs(flow->tuple.inner.port_src));
            } else {
               write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, ntohs(flow->tuple_reverse.inner.port_src));
            }

            break;
        case EM_YNAO_IP_DSTPORT:
            if (tuple_direction == FLOW_DIR_SRC2DST) {
                write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, ntohs(flow->tuple.inner.port_dst));
            } else {
                write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, ntohs(flow->tuple_reverse.inner.port_dst));
            }
            break;
        case EM_YNAO_IP_PROTNUM:
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, flow->tuple.inner.proto);
            break;

        case EM_YNAO_IP_SRCCOUNTRY:
            if(g_config.ip_position_switch){
                 write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.country, strlen(srcIp.country));
	     }else{
                 write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
             }
             break;
        case EM_YNAO_IP_SRCSTATE:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.state, strlen(srcIp.state));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_SRCCITY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.city, strlen(srcIp.city));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_SRCLONGITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.longitude, strlen(srcIp.longitude));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_SRCLATITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.latitude, strlen(srcIp.latitude));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_SRCISP:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.isp, strlen(srcIp.isp));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_SRCASN:
            if(g_config.mmdb_asn_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.asn, strlen(srcIp.asn));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;

        case EM_YNAO_IP_DSTCOUNTRY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.country, strlen(dstIp.country));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_DSTSTATE:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.state, strlen(dstIp.state));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_DSTCITY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.city, strlen(dstIp.city));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_DSTLONGITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.longitude, strlen(dstIp.longitude));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_DSTLATITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.latitude, strlen(dstIp.latitude));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_DSTISP:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.isp, strlen(dstIp.isp));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_YNAO_IP_DSTASN:
            if(g_config.mmdb_asn_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.asn, strlen(dstIp.asn));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;

        case EM_YNAO_IP_PROTINFO: {
            char proto_info[2048] = {0};
            get_protoinfo(proto_info, flow->proto_layer, flow->proto_layer_cnt);
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)proto_info, strlen(proto_info));
            break;
        }
        case EM_YNAO_IP_PROTTYPE:
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)protocol_name_array[flow->real_protocol_id],
                                  strlen((char *)protocol_name_array[flow->real_protocol_id]));
            break;
        case EM_YNAO_IP_PROTNAME:
            write_string_reconds(record, idx, TBL_LOG_MAX_LEN, dpi_high_proto_find_by_id(flow->high_app_proto_id)->name);
            break;
        case EM_YNAO_IP_BEGTIME:
            write_uint64_reconds(record, idx, TBL_LOG_MAX_LEN, flow->create_time / 1e3);
            break;

        case EM_YNAO_IP_ENDTIME:
            write_uint64_reconds(record, idx, TBL_LOG_MAX_LEN, flow->end_time / 1e3);
            break;

        case EM_YNAO_IP_COMDUR:
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (flow->end_time - flow->create_time)/1e3);
            break;


        /* 上行首包TTL 1字节 */
        case EM_YNAO_IP_FIRTTLBYCLI:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->up_ttl);
            break;

        /* 下行首包TTL， 1字节 */
        case EM_YNAO_IP_FIRTTLBYSRV:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->down_ttl);
            break;

        /* 描述流内上层应用的方向，0-未知；1-src端为客户端，2-dst端为服务端 */
        case EM_YNAO_IP_APPDIREC:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,
                            direction == FLOW_DIR_SRC2DST ? 1 : 2);
            break;

        case EM_YNAO_IP_PKTNUM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type,  NULL,  flow->src2dst_packets+flow->dst2src_packets);
            break;

        /* 上行包数 */
        case EM_YNAO_IP_UPLINKPKTNUM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type,  NULL,  flow->src2dst_packets);
            break;

        case EM_YNAO_IP_DOWNLINKPKTNUM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->dst2src_packets);
            break;

        /* 会话所有双向字节数总和 */
        case EM_YNAO_IP_SESBYTES:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,
                                                  flow->src2dst_bytes+flow->dst2src_bytes);
            break;

         /* 会话所有上行字节数总和 */
        case EM_YNAO_IP_UPSESBYTES:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->src2dst_bytes);
            break;

        /* 会话所有下行字节数总和 */
        case EM_YNAO_IP_DOWNSESBYTES:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->dst2src_bytes);
            break;

        case EM_YNAO_IP_SESBYTESRATIO:
            if(flow->dst2src_bytes>0){
                uint32_t ratio=(uint32_t)((flow->src2dst_bytes*100)/flow->dst2src_bytes);
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,ratio);
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;

        case EM_YNAO_IP_PAYLEN:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type,  NULL,  flow->src2dst_payload_len+flow->dst2src_payload_len);
            break;
        /* 上行净荷长度 */
        case EM_YNAO_IP_UPPAYLEN:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type,  NULL,  flow->src2dst_payload_len);
            break;
        /* 下行净荷长度 */
        case EM_YNAO_IP_DOWNPAYLEN:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type,  NULL,  flow->dst2src_payload_len);
            break;

        case EM_YNAO_IP_PAYLENRATIO:
            if(flow->dst2src_payload_len>0){
                uint32_t ratio=(uint32_t)((flow->src2dst_payload_len*100)/flow->dst2src_payload_len);
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,ratio);
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;
        case EM_YNAO_IP_DESBYTES:
            if (flow->tuple.inner.proto == IPPROTO_TCP)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->src2dst_payload_len+flow->dst2src_payload_len);
            else
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        /* 上行数据中，根据TCP序列号推算出的最内层传输层之上的数据长度*/
        case EM_YNAO_IP_UPLINKDESBYTES:
            if (flow->tuple.inner.proto == IPPROTO_TCP)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->src2dst_payload_len);
            else
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        /* 下行数据中，根据TCP序列号推算出的最内层传输层之上的数据长度*/
        case EM_YNAO_IP_DOWNLINKDESBYTES:
            if (flow->tuple.inner.proto == IPPROTO_TCP)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, flow->dst2src_payload_len);
            else
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        /* 筛选字段，双向流数据前N个包 */
        case EM_YNAO_IP_STREAM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, (const uint8_t*)flow->both_stream.byte, flow->both_stream.index);
            break;

        /* 筛选字段，上行请求流数据，前N个包 */
        case EM_YNAO_IP_UPLINKSTREAM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, (const uint8_t*)flow->up_stream.byte, flow->up_stream.index);
            break;

        /* 筛选字段，下行请求流数据，前N个包 */
        case EM_YNAO_IP_DOWNLINKSTREAM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, (const uint8_t*)flow->down_stream.byte, flow->down_stream.index);
            break;

        case EM_YNAO_IP_AVGPKTLEN:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (flow->src2dst_bytes+flow->dst2src_bytes)/(flow->src2dst_packets+flow->dst2src_packets));
            break;
        case EM_YNAO_IP_AVGPKTINT:
            if((flow->src2dst_packets+flow->dst2src_packets)  > 1)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (flow->end_time - flow->create_time)/1e3/(flow->src2dst_packets+flow->dst2src_packets - 1));
            else
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            break;

        default:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_IP;
    log_ptr->log_len     = *idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

int write_link(struct flow_info *flow, int direction, SdtMatchResult *match_result _U_)
{
    int val = 0;
    int *idx = &val;
    char tmp_buff[LOCAL_BUFFER_SIZE]={0};
    struct tbl_log *log_ptr;
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, idx, TBL_LOG_MAX_LEN, match_result);
    precord_t *record = log_ptr->record;
    player_t *layer = precord_layer_put_new_layer(record, "link");

    int i;
    for(i=0; i<EM_LINK_MAX;i++){
        switch(i){

        /* 握手过程 string */
        //case EM_LINK_TCPFLAG:
        //    write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,  NULL,  flow->handshake);
        //    break;
        /* 上行最大包长 */
        #if 0
        /*上行传输层负载HEX */
        case EM_LINK_UPLINKTRANSPAYHEX:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, , );
            break;

         /* 下行传输层负载HEX */
        case EM_LINK_DOWNLINKTRANSPAYHEX:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, , );
            break;
        #endif


        /* 上行有负载前8包传输层负载长度序列。自由文本，以分隔符隔开的8个数字。trans.paylen.set.src-seq(240,70,80):ip流客户端发送的数据负载序列长度分别为240,70,80 */
        case EM_LINK_UPLINKPAYLENSET:
        {
            tmp_buff[0]='\0';
            get_payload_len_sequence(flow->up_payload_len_set, flow->up_payload_len_cnt, (char *)tmp_buff, sizeof(tmp_buff));
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,
                                                                         (const uint8_t *)tmp_buff,
                                                                         strlen(tmp_buff));
        }
            break;

        /* 下行有负载前8包传输层负载长度序列。自由文本，以分隔符隔开的8个数字。trans.paylen.set.dst-seq(180,*,30):ip流服务器负载序列长度分别为180,任意，30 */
        case EM_LINK_DOWNLINKPAYLENSET:
        {
            tmp_buff[0]='\0';
            get_payload_len_sequence(flow->down_payload_len_set,flow->down_payload_len_cnt, (char *)tmp_buff, sizeof(tmp_buff));
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,
                                                                   (const uint8_t *)tmp_buff,
                                                                   strlen(tmp_buff));
        }
            break;

        case EM_LINK_UPLINKBIGPKTLEN:
            if(flow->src2dst_max_packet_len>0)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,  NULL,  flow->src2dst_max_packet_len);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_LINK_DOWNLINKBIGPKTLEN:
            if(flow->dst2src_max_packet_len>0)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->dst2src_max_packet_len);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_LINK_UPLINKSMAPKTLEN:
            if(flow->src2dst_min_packet_len<=g_config.max_pkt_len)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,  NULL,  flow->src2dst_min_packet_len);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_LINK_DOWNLINKSMAPKTLEN:
            if(flow->dst2src_min_packet_len<=g_config.max_pkt_len){
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->dst2src_min_packet_len);
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;
         /*上行高频包长*/
        //case EM_LINK_UPLINKFREQPKTLEN:
        //    break;
        //case EM_LINK_DOWNLINKFREQPKTLEN:
        //    break;

        /* 上行最大包间隔 */
        case EM_LINK_UPLINKBIGPKTINT:
            if(flow->src2dst_max_packet_interval>0)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,  NULL, flow->src2dst_max_packet_interval/1000);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_LINK_DOWNLINKBIGPKTINT:
            if(flow->dst2src_max_packet_interval>0)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->dst2src_max_packet_interval/1000);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);

            break;
        /* 上行最小包间隔 */
        case EM_LINK_UPLINKSMAPKTINT:
            if(flow->src2dst_min_packet_interval<g_config.tcp_flow_timeout*1000000)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->src2dst_min_packet_interval/1000);   //单位毫秒
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_LINK_DOWNLINKSMAPKTINT:
            if(flow->dst2src_min_packet_interval<=g_config.tcp_flow_timeout*1000000){
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->dst2src_min_packet_interval/1000);
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;


        /**************************************************** TCP FLAG ***********************************************************/

        //case EM_LINK_FIRSTFLAGS:
        //    break;

        /*上行SYN包SEQ号 */
        case EM_LINK_UPLINKSYNSEQNUM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->up_seq);
            break;

        /*下行SYN包SEQ号 */
        case EM_LINK_DOWNLINKSYNSEQNUM:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->down_seq);
            break;

        /*上行SYN包TCP窗口大小*/
        case EM_LINK_UPLINKSYNTCPWINS:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->up_window);
            break;
         /*下行SYN包TCP窗口大小*/
        case EM_LINK_DOWNLINKSYNTCPWINS:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->down_window);
            break;

        /* 上行TCP SYN置位包的选项字段，包括字段类型及对应值。例如：Maximum segment size：1460 bytes */
        case EM_LINK_UPLINKTCPOPTS:
            if(flow->synData)
            {
                memset(tmp_buff,0,LOCAL_BUFFER_SIZE);
                tcp_operator_ip_data(flow, tmp_buff, LOCAL_BUFFER_SIZE, flow->synData, flow->synDataLen);

                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, (const uint8_t *)tmp_buff, strlen(tmp_buff));
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;

        /* 下行TCP SYN置位包的选项字段，包括字段类型及对应值。例如：Maximum segment size：1460 bytes */
        case EM_LINK_DOWNLINKTCPOPTS:
            if(flow->synackData)
            {
                memset(tmp_buff,0,LOCAL_BUFFER_SIZE);
                tcp_operator_ip_data(flow, tmp_buff, LOCAL_BUFFER_SIZE, flow->synackData, flow->synackDataLen);

                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type,(const uint8_t *)tmp_buff, strlen(tmp_buff));
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
            break;

        //case EM_LINK_UPLINKFLAGS:
        //case EM_LINK_DOWNLINKFLAGS:

        /* 会话标志位FIN计数 */
        case EM_LINK_TCPFLAGSFINCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpfincounter);
            break;

        /* 会话标志位SYN计数 */
        case EM_LINK_TCPFLAGSSYNCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpsyncounter);
            break;

        /* 会话标志位RST计数 */
        case EM_LINK_TCPFLAGSRSTCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcprstcounter);
            break;

        /* 会话标志位PSH计数 */
        case EM_LINK_TCPFLAGSPSHCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcppshcounter);
            break;

        /* 会话标志位ACK计数 */
        case EM_LINK_TCPFLAGSACKCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpackcounter);
            break;

        /* 会话标志位URG计数 */
        case EM_LINK_TCPFLAGSURGCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpurgcounter);
            break;

        /* 会话标志位ECE计数 */
        case EM_LINK_TCPFLAGSECECNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpececounter);
            break;

        /* 会话标志位CWR计数 */
        case EM_LINK_TCPFLAGSCWRCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpcwrcounter);
            break;

        /* 会话标志位NS计数 */
        case EM_LINK_TCPFLAGSNSCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpnscounter);
            break;

        /* 会话标志位SYN-ACK计数 */
        case EM_LINK_TCPFLAGSSYNACKCNT:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcpsynackcounter);
            break;


        /*TCP握手标识*/
        case EM_LINK_ESTABLISH:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->handshake);
            break;
        case EM_LINK_FINISHED:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, link_field_array[i].type, NULL, flow->tcp_finished);
            break;

        default:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }


    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_LINK;
    log_ptr->log_len     = *idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}


int dpi_pschema_get_link_field(dpi_field_table *field_table_array[])
{
    *field_table_array = link_field_array;
    return ARRAY_LEN(link_field_array);
}

void dpi_sdt_init_ip_map_fields(void)
{
    ip_field_array[EM_YNAO_IP_FIRTTLBYCLI].callback         = cb_link_firttlbycli;
    ip_field_array[EM_YNAO_IP_FIRTTLBYSRV].callback         = cb_link_firttlbysrv;
    ip_field_array[EM_YNAO_IP_APPDIREC].callback            = cb_link_appdirec;
    ip_field_array[EM_YNAO_IP_PKTNUM].callback              = cb_link_pktnum;
    ip_field_array[EM_YNAO_IP_UPLINKPKTNUM].callback        = cb_link_uplinkpktnum;
    ip_field_array[EM_YNAO_IP_DOWNLINKPKTNUM].callback      = cb_link_downlinkpktnum;
    ip_field_array[EM_YNAO_IP_SESBYTES].callback            = cb_link_sesbytes;
    ip_field_array[EM_YNAO_IP_UPSESBYTES].callback          = cb_link_upsesbytes;
    ip_field_array[EM_YNAO_IP_DOWNSESBYTES].callback        = cb_link_downsesbytes;
    ip_field_array[EM_YNAO_IP_SESBYTESRATIO].callback       = cb_link_sesbytesratio;
    ip_field_array[EM_YNAO_IP_PAYLEN].callback              = cb_link_paylen;
    ip_field_array[EM_YNAO_IP_UPPAYLEN].callback            = cb_link_uppaylen;
    ip_field_array[EM_YNAO_IP_DOWNPAYLEN].callback          = cb_link_downpaylen;
    ip_field_array[EM_YNAO_IP_PAYLENRATIO].callback         = cb_link_paylenratio;
    ip_field_array[EM_YNAO_IP_DESBYTES].callback            = cb_link_desbytes;
    ip_field_array[EM_YNAO_IP_UPLINKDESBYTES].callback      = cb_link_uplinkdesbytes;
    ip_field_array[EM_YNAO_IP_DOWNLINKDESBYTES].callback    = cb_link_downlinkdesbytes;
    ip_field_array[EM_YNAO_IP_STREAM].callback              = cb_link_stream;
    ip_field_array[EM_YNAO_IP_UPLINKSTREAM].callback        = cb_link_uplinkstream;
    ip_field_array[EM_YNAO_IP_DOWNLINKSTREAM].callback      = cb_link_downlinkstream;


    //link_field_array[EM_LINK_TCPFLAG].callback             = cb_link_tcpflag;
    map_fields_info_register(ip_field_array, PROTOCOL_IP, EM_YNAO_IP_MAX,"ip");
    dpi_register_proto_schema(ip_field_array, EM_YNAO_IP_MAX, "ip");
    return;
}

void dpi_sdt_init_link_map_fields(void)
{
    link_field_array[EM_LINK_UPLINKTRANSPAYHEX].callback   = cb_link_uplinktranspayhex;
    link_field_array[EM_LINK_DOWNLINKTRANSPAYHEX].callback = cb_link_downlinktranspayhex;
    link_field_array[EM_LINK_UPLINKPAYLENSET].callback     = cb_link_uplinkpaylenset;
    link_field_array[EM_LINK_DOWNLINKPAYLENSET].callback   = cb_link_downlinkpaylenset;
    link_field_array[EM_LINK_UPLINKBIGPKTLEN].callback     = cb_link_uplinkbigpktlen;
    link_field_array[EM_LINK_DOWNLINKBIGPKTLEN].callback   = cb_link_downlinkbigpktlen;
    link_field_array[EM_LINK_UPLINKSMAPKTLEN].callback     = cb_link_uplinksmapktlen;
    link_field_array[EM_LINK_DOWNLINKSMAPKTLEN].callback   = cb_link_downlinksmapktlen;
    //link_field_array[EM_LINK_UPLINKFREQPKTLEN].callback   =
    //link_field_array[EM_LINK_DOWNLINKFREQPKTLEN].callback   =
    link_field_array[EM_LINK_UPLINKBIGPKTINT].callback     = cb_link_uplinkbigpktint;
    link_field_array[EM_LINK_DOWNLINKBIGPKTINT].callback   = cb_link_downlinkbigpktint;
    link_field_array[EM_LINK_DOWNLINKSMAPKTINT].callback   = cb_link_downlinksmapktint;
    link_field_array[EM_LINK_UPLINKSMAPKTINT].callback     = cb_link_uplinksmapktint;
    //link_field_array[EM_LINK_FIRSTFLAGS].callback     =
    link_field_array[EM_LINK_UPLINKSYNSEQNUM].callback     = cb_link_uplinksynseqnum;
    link_field_array[EM_LINK_DOWNLINKSYNSEQNUM].callback   = cb_link_downlinksynseqnum;
    link_field_array[EM_LINK_UPLINKSYNTCPWINS].callback    = cb_link_uplinksyntcpwins;
    link_field_array[EM_LINK_DOWNLINKSYNTCPWINS].callback  = cb_link_downlinksyntcpwins;
    link_field_array[EM_LINK_UPLINKTCPOPTS].callback       = cb_link_uplinktcpopts;
    link_field_array[EM_LINK_DOWNLINKTCPOPTS].callback     = cb_link_downlinktcpopts;
    //link_field_array[EM_LINK_UPLINKFLAGS].callback     = 
    //link_field_array[EM_LINK_DOWNLINKFLAGS].callback     =
    link_field_array[EM_LINK_TCPFLAGSFINCNT].callback      = cb_link_tcpflagsfincnt;
    link_field_array[EM_LINK_TCPFLAGSSYNCNT].callback      = cb_link_tcpflagssyncnt;
    link_field_array[EM_LINK_TCPFLAGSRSTCNT].callback      = cb_link_tcpflagsrstcnt;
    link_field_array[EM_LINK_TCPFLAGSPSHCNT].callback      = cb_link_tcpflagspshcnt;
    link_field_array[EM_LINK_TCPFLAGSACKCNT].callback      = cb_link_tcpflagsackcnt;
    link_field_array[EM_LINK_TCPFLAGSURGCNT].callback      = cb_link_tcpflagsurgcnt;
    link_field_array[EM_LINK_TCPFLAGSECECNT].callback      = cb_link_tcpflagsececnt;
    link_field_array[EM_LINK_TCPFLAGSCWRCNT].callback      = cb_link_tcpflagscwrcnt;
    link_field_array[EM_LINK_TCPFLAGSNSCNT].callback       = cb_link_tcpflagsnscnt;
    link_field_array[EM_LINK_TCPFLAGSSYNACKCNT].callback   = cb_link_tcpflagssynackcnt;
    link_field_array[EM_LINK_ESTABLISH].callback           = cb_link_establish;
    //link_field_array[EM_LINK_FINISHED].callback           =

    map_fields_info_register(link_field_array, PROTOCOL_LINK, EM_LINK_MAX,"link");
    dpi_register_proto_schema(link_field_array, EM_LINK_MAX, "link");
    return;
}

static __attribute((constructor))
void before_init_ip_trans(void){
    register_tbl_array(TBL_LOG_IP, 0, "ip", dpi_sdt_init_ip_map_fields);
    register_tbl_array(TBL_LOG_LINK, 0, "link", dpi_sdt_init_link_map_fields);
}


#endif
