#include "dpi_tbl_log.h"
#include "dpi_common.h"

extern struct global_config g_config;

static const char* RT6402_NSP[] = {"CTCC", "CMCC", "CUCC", "unknow"};
static const char* RT9800_NSP[] = {"unknow", "CTCC", "CMCC", "CUCC"};
static const char* PH_HW_NSP[]  = {"unknow", "CMCC", "CUCC", "CTCC"};
static const char* HW_VLAN[]    = {"00", "01", "10", "11"};  // 0 : unknow, 1 : CMCC, 2 : CUCC, 3 : CTCC 

static struct int_to_string EcodeStr[] = {  
    {0x01,            "VPDN"},
    {0x02,            "WLAN"},
    {0x03,            "3G"},
    {0x04,            "4G"},
    {0x05,            "3G_Gate"},
    {0x06,            "4G_Gate"},
    {0x99,            "OTHER"},
    {0x00,            NULL}
};

static struct int_to_string EcodeStrDB[] = {  
    {0x01,            "111"},
    {0x02,            "139"},
    {0x03,            "124"},
    {0x04,            "144"},
    {0x05,            "200"},
    {0x06,            "201"},
    {0x00,            NULL}
};

static struct int_to_string BflagStr[] = {
    {0x01,            "CTCC"},                        // 中国电信 China Telecom Communication Corporation
    {0x02,            "CNCC"},                        // 中国网通 China Netcom Communication Corporation
    {0x03,            "CUCC"},                        // 中国联通 China Union Communication Corporation
    {0x04,            "GWBN"},                        // 中国长城宽带 China GreatWall Broadband Network Service 
    {0x05,            "CRC"},                         // 中国铁通 China Railway Corporation
    {0x06,            "CMCC"},                        // 中国移动 China Mobile Communications Corporation
    {0x08,            "MOE"},                         // 中国教育部 Ministry of Education
    {0x09,            "CAOS"},                        // 中国中科院 China Academy of Science
    {0x0b,            "SARFT"},                        // 中国广电部门 The State Administration of Radio Film and Television of China
    {0x63,            "OTHER"},                        // 其他
    {0x00,            NULL}
};

static struct int_to_string BflagStrDB[] = {
    {0x01,            "01"},
    {0x02,            "02"},
    {0x03,            "03"},
    {0x04,            "04"},
    {0x05,            "05"},
    {0x06,            "06"},
    {0x08,            "08"},
    {0x09,            "09"},
    {0x0b,            "11"},
    {0x63,            "99"},
    {0x00,            NULL}
};

static void write_fixed_trailer_records(precord_t *log, int * idx, int max_len, struct flow_info * flow, Trailer_Type type)
{
    char operatorStr[32] = {0};
    operatorStr[0] = 0;
    if ((strcmp(g_config.operator_type, "AUTO") == 0))
    {
        switch (type)
        {
        case HW_SH_MAC:
        case PH_DEFAULT:
            flow->data_link_layer.nsp &= 0x03;
            snprintf(operatorStr, sizeof(operatorStr), "%s", PH_HW_NSP[flow->data_link_layer.nsp]);
            break;
        case HW_SH_VLAN:
            snprintf(operatorStr, sizeof(operatorStr), "%s", HW_VLAN[(flow->data_link_layer.vlan_id[0] >> 3) & 0x03]);
            break;
        case RT_6402:
            flow->data_link_layer.nsp &= 0x03;
            snprintf(operatorStr, sizeof(operatorStr), "%s",  RT6402_NSP[flow->data_link_layer.nsp]);
            break;
        case RT_9800:
            flow->data_link_layer.nsp &= 0x03;
            snprintf(operatorStr, sizeof(operatorStr), "%s",   RT9800_NSP[flow->data_link_layer.nsp]);
            break;
        case HY_FS:
            snprintf(operatorStr, sizeof(operatorStr), "%u/%u/%u", flow->data_link_layer.inter_id >> 8 & 0x0f, flow->data_link_layer.inter_id >> 4 & 0x0f, flow->data_link_layer.inter_id & 0x0f); 
            break;
        default:
            break;
        }
    }
    else
    {
        snprintf(operatorStr, sizeof(operatorStr), "%s", g_config.operator_type);
    }

    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++)
    {
        if ((i == EM_COMMON_OPERATOR_TYPE) && g_config.show_operator_switch)
            write_one_str_reconds(log, idx, max_len, operatorStr, strlen(operatorStr));
        else
            write_n_empty_reconds(log, idx, max_len, 1);
    }

    return;
}

static int write_rt_records(precord_t *log, int *idx, int max_len, struct flow_info *flow, Trailer_Type type)
{
    char fields[64];
    const char *tmp;
    struct rt_trailer *trailer = flow->pTrailer;

    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++)
    {

        switch (i)
        {
        case EM_COMMON_TAGTYPE:
            write_one_str_reconds(log, idx, max_len, "0x00", 4);
            break;
        case EM_COMMON_OPERATOR_TYPE:
            if (strncmp(g_config.operator_type, "AUTO", 4) == 0){
                if(type == RT_6402)
                    write_one_str_reconds(log, idx, max_len, RT6402_NSP[flow->data_link_layer.nsp], strlen(RT6402_NSP[flow->data_link_layer.nsp]));
                else
                    write_one_str_reconds(log, idx, max_len, RT9800_NSP[flow->data_link_layer.nsp], strlen(RT9800_NSP[flow->data_link_layer.nsp]));
            }
            else
                write_one_str_reconds(log, idx, max_len, g_config.operator_type, strlen(g_config.operator_type));
            break;
        case EM_COMMON_HW_LAC:
            if(trailer->base == 0xc)
                write_one_num_reconds(log, idx, max_len, trailer->uli >> 16);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_HW_SAC:
            if(trailer->base == 0xc)
                write_one_num_reconds(log, idx, max_len, trailer->uli & 0xffff);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_RTL_TEID:
            write_one_hex_reconds(log, idx, max_len, trailer->teid);
            break;
        case EM_COMMON_RTL_OUTTER_SRC:
            get_ip4string(fields, sizeof(fields), trailer->srcip);
            write_one_str_reconds(log, idx, max_len, fields, strlen(fields));
            break;
        case EM_COMMON_RTL_OUTTER_DST:
            get_ip4string(fields, sizeof(fields), trailer->dstip);
            write_one_str_reconds(log, idx, max_len, fields, strlen(fields));
            break;
        case EM_COMMON_RTL_MSISDN:
            write_uint64_reconds(log, idx, max_len, trailer->msisdn);
            break;
        case EM_COMMON_RTL_IMEI:
            write_uint64_reconds(log, idx, max_len, trailer->imei);
            break;
        case EM_COMMON_RTL_IMSI:
            write_uint64_reconds(log, idx, max_len, trailer->imsi);
            break;
        case EM_COMMON_RTL_TAC:
            write_one_hex_reconds(log, idx, max_len, trailer->tac);
            break;
        case EM_COMMON_RTL_PLMN_ID:
            write_one_num_reconds(log, idx, max_len, trailer->plmn_id);
            break;
        case EM_COMMON_RTL_ULI:
            write_one_hex_reconds(log, idx, max_len, trailer->uli);
            break;
        case EM_COMMON_RTL_ENODE_ID:
            if(trailer->base == 0xd)
                write_one_num_reconds(log, idx, max_len, trailer->uli >> 12);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_RTL_CELL_ID:
            if(trailer->base == 0xd)
                write_one_num_reconds(log, idx, max_len, (trailer->uli >> 4) & 0xff);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_RTL_BS:
            tmp = trailer->base == 11 ? "2G" : trailer->base == 12 ? "3G" : trailer->base == 13 ? "4G" : "unknow";
            write_one_str_reconds(log, idx, max_len, tmp, strlen(tmp));
            break;
        default:
            write_n_empty_reconds(log, idx, max_len, 1);
            break;
        }
    }
    return 0;
}

static int write_hw_default_records(precord_t *log, int *idx, int max_len, struct flow_info *flow, Trailer_Type _type)
{
    int j;
    char fields[64];
    uint64_t msisdn = 0;
    const char *tmp;
    struct hw_default_trailer *trailer = flow->pTrailer;

    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++)
    {
        switch (i)
        {
        case EM_COMMON_TAGTYPE:
            if(BJ_JSD == _type)
                write_one_num_reconds(log, idx, max_len, trailer->data_type);
            else
                write_one_str_reconds(log, idx, max_len, "0x10", 4);
            break;
        case EM_COMMON_OPERATOR_TYPE:
            if (strncmp(g_config.operator_type, "AUTO", 4) == 0)
            {
                if (HW_DEFAULT == _type || BJ_JSD == _type){
                    tmp = val_to_string(trailer->nsp, BflagStr);
                    write_one_str_reconds(log, idx, max_len, tmp ? tmp : "unknow", tmp ? strlen(tmp) : 6);
                }
                else if (HW_DB == _type){
                    tmp = val_to_string(trailer->nsp, BflagStrDB);
                    write_one_str_reconds(log, idx, max_len, tmp ? tmp : "unknow", tmp ? strlen(tmp) : 6);
                }
            }
            else
                write_one_str_reconds(log, idx, max_len, g_config.operator_type, strlen(g_config.operator_type));
            break;
        case EM_COMMON_HW_NCODE:
            if(HW_DEFAULT == _type || BJ_JSD == _type){
                tmp = val_to_string(trailer->base, EcodeStr);
                write_one_str_reconds(log, idx, max_len, tmp ? tmp : "unknow", tmp ? strlen(tmp) : 6);
            }
            else if(HW_DB == _type){
                tmp = val_to_string(trailer->base, EcodeStrDB);
                write_one_str_reconds(log, idx, max_len, tmp ? tmp : "unknow", tmp ? strlen(tmp) : 6);
            }
            break;
        case EM_COMMON_HW_ACCOUNT:
            write_one_str_reconds(log, idx, max_len, (const char*)trailer->tlv_str[EM_HW_ACCOUNT].str, trailer->tlv_str[EM_HW_ACCOUNT].len);
            break;
        case EM_COMMON_HW_ESN:
            write_one_str_reconds(log, idx, max_len, (const char*)trailer->tlv_str[EM_HW_ESN].str, trailer->tlv_str[EM_HW_ESN].len);
            break;
        case EM_COMMON_HW_MEID:
            write_one_str_reconds(log, idx, max_len, (const char*)trailer->tlv_str[EM_HW_MEID].str, trailer->tlv_str[EM_HW_MEID].len);
            break;
        case EM_COMMON_HW_LAC:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_short[EM_HW_LAC]);
            break;
        case EM_COMMON_HW_SAC:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_short[EM_HW_SAC]);
            break;
        case EM_COMMON_HW_CALL_ID:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_short[EM_HW_CI]);
            break;
        case EM_COMMON_HW_ECGI:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_long[EM_HW_ECGI]);
            break;
        case EM_COMMON_HW_BSID:
            write_one_str_reconds(log, idx, max_len, (const char*)trailer->tlv_str[EM_HW_BSID].str, trailer->tlv_str[EM_HW_BSID].len);
            break;
        case EM_COMMON_HW_GRE_KEY:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_long[EM_HW_GRE_KEY]);
            break;
        case EM_COMMON_HW_TAI:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_short[EM_HW_TAI]);
            break;
        case EM_COMMON_HW_EGI_MNC:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_short[EM_HW_MNC]);
            break;
        case EM_COMMON_HW_APN:
            write_one_str_reconds(log, idx, max_len, trailer->apn, strlen(trailer->apn));
            break;
        case EM_COMMON_RTL_TEID:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_long[EM_HW_TEID]);
            break;
        case EM_COMMON_RTL_MSISDN:
            for(j = 0; j < trailer->tlv_str[EM_HW_MSISDN].len; j++)
                msisdn = msisdn * 256 + trailer->tlv_str[EM_HW_MSISDN].str[j];
            write_uint64_reconds(log, idx, max_len, msisdn);
            break;
        case EM_COMMON_RTL_IMEI:
            for(j = 0; j < trailer->tlv_str[EM_HW_IMEI].len; j+= 2){
                fields[j] = trailer->tlv_str[EM_HW_IMEI].str[j+1];
                fields[j+1] = trailer->tlv_str[EM_HW_IMEI].str[j];
            }
            write_one_str_reconds(log, idx, max_len, fields, trailer->tlv_str[EM_HW_IMEI].len % 2 ? j - 1 : j);
            break;
        case EM_COMMON_RTL_TAC:
            write_one_num_reconds(log, idx, max_len, trailer->tlv_short[EM_HW_TAC]);
            break;
        case EM_COMMON_RTL_OUTTER_SRC:
            if(_type == BJ_JSD)
                write_one_str_reconds(log, idx, max_len, trailer->RSClueid, strlen(trailer->RSClueid));
            else
                write_one_num_reconds(log, idx, max_len, trailer->tlv_long[EM_HW_GTP_SIP]);
            break;
        case EM_COMMON_RTL_OUTTER_DST:
            if(_type == BJ_JSD)
                write_one_str_reconds(log, idx, max_len, trailer->BidClueid, strlen(trailer->BidClueid));
            else
                write_one_num_reconds(log, idx, max_len, trailer->tlv_long[EM_HW_GTP_DIP]);
            break;
        case EM_COMMON_RTL_IMSI:
            write_uint64_reconds(log, idx, max_len, trailer->imsi);
            break;
        default:
            write_n_empty_reconds(log, idx, max_len, 1);
            break;
        }
    }
    return 0;
}

static int write_jl_records(precord_t *log, int *idx, int max_len, struct flow_info *flow){
    uint32_t ret;
    const char* tmp;
    uint8_t fields[16];
    struct jl_trailer *trailer = flow->pTrailer;
    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++)
    {
        switch(i)
        {
          case EM_COMMON_TAGTYPE:
              write_one_num_reconds(log, idx, max_len,  trailer->base);
              break;
          case EM_COMMON_HW_NCODE:
              tmp = trailer->base == 4 ? "3G": trailer->base == 3 ? "4G" : "unknow" ;
              write_one_str_reconds(log, idx, max_len, tmp, strlen(tmp));
              break;
          case EM_COMMON_OPERATOR_TYPE:
              if(strcmp(g_config.operator_type, "AUTO"))
                  write_one_str_reconds(log, idx, max_len, g_config.operator_type, strlen(g_config.operator_type));
              else{
                  switch(trailer->mnc){
                      case 0: case 2: case 4: case 7: case 8: case 13:
                          write_one_str_reconds(log, idx, max_len, "CMCC", 4);
                          break;
                      case 1: case 6: case 9: case 10:
                          write_one_str_reconds(log, idx, max_len, "CUCC", 4);
                          break;
                      case 3: case 5: case 11: case 12:
                          write_one_str_reconds(log, idx, max_len, "CTCC", 4);
                          break;
                      default:
                          write_one_str_reconds(log, idx, max_len, "unknow", 6);
                          break;
                  }
              }
              break;
          case EM_COMMON_RTL_IMSI:
              if(trailer->dev_tag[0][0] && (ret = get_bcd(trailer->dev_tag[0] + 1, fields, 8)))
                  write_multi_digit_reconds(log, idx, max_len, fields, ret);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_RTL_MSISDN:
              if(trailer->dev_tag[1][0] && (ret = get_bcd(trailer->dev_tag[1] + 1, fields, 8)))
                  write_multi_digit_reconds(log, idx, max_len, fields, ret);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_RTL_IMEI:
              if(trailer->dev_tag[2][0] && (ret = get_bcd(trailer->dev_tag[2] + 1, fields, 8)))
                  write_multi_digit_reconds(log, idx, max_len, fields, ret);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_HW_APN:
              write_one_str_reconds(log, idx, max_len, trailer->apn, strlen(trailer->apn));
              break;
          case EM_COMMON_HW_BSID:
              write_multi_bsdnum_reconds(log, idx, max_len, trailer->bsid + 1, trailer->bsid[0] ? 6 : 0);
              break;
          case EM_COMMON_HW_ESN:
              write_multi_bsdnum_reconds(log, idx, max_len, trailer->esn + 1, trailer->esn[0] ? 4 : 0);
              break;
          case EM_COMMON_HW_MEID:
              write_multi_bsdnum_reconds(log, idx, max_len, trailer->meid + 1, trailer->meid[0] ? 7 : 0);
              break;
          case EM_COMMON_HW_CALL_ID:
              if(trailer->cgi_ci)
                  write_one_num_reconds(log, idx, max_len, trailer->cgi_ci);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_HW_LAC:
              if(trailer->lac)
                  write_one_num_reconds(log, idx, max_len, trailer->lac);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_RTL_CELL_ID:
              if(trailer->undef[0])
                  write_one_num_reconds(log, idx, max_len, trailer->undef[0]);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_HW_SAC:
              if(trailer->undef[1])
                  write_one_num_reconds(log, idx, max_len, trailer->undef[1]);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_HW_RAC:
              if(trailer->undef[2])
                  write_one_num_reconds(log, idx, max_len, trailer->undef[2]);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_RTL_TAC:
              if(trailer->undef[3])
                  write_one_num_reconds(log, idx, max_len, trailer->undef[3]);
              else
                  write_n_empty_reconds(log, idx, max_len, 1);
              break;
          case EM_COMMON_RTL_ULI:
              if(trailer->cgi_ci){
                  char str[64];
                  snprintf(str, 64, "4G: 0x%08x, eNodeBid: %u, CellId: %u", trailer->cgi_ci, trailer->cgi_ci >> 8, trailer->cgi_ci & 0xff);
                  write_one_str_reconds(log, idx, max_len, str, strlen(str));
              }
              else{
                  write_n_empty_reconds(log, idx, max_len, 1);
              }
              break;
          default:
              write_n_empty_reconds(log, idx, max_len, 1);
              break;

        }
    }
    return 0;
}

static int write_fl_records(precord_t *log, int *idx, int max_len, struct flow_info *flow)
{
    const char* tmp;
    char fields[32];

    struct fl_trailer *trailer= flow->pTrailer;
    if(trailer->len == 0){
        write_n_empty_reconds(log, idx, max_len, EM_COMMON_RTL_BS + 1);
        return 0;
    }

    uint8_t base = trailer->data[FL_BASE] & 0x0f;

    int mnc;
    sprintf(fields, "%u",  get_uint16_ntohs(trailer->data, FL_PLMN_ID));
    if(strlen(fields) > 3)
       mnc = atoi(fields + 3);
    else
       mnc = -1; 

    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++){
        switch (i){
        case EM_COMMON_OPERATOR_TYPE:
            if(strcmp(g_config.operator_type, "AUTO"))
                write_one_str_reconds(log, idx, max_len, g_config.operator_type, strlen(g_config.operator_type));
            else{
                switch(mnc){
                    case 0: case 2: case 4: case 7: case 8: case 13:
                        write_one_str_reconds(log, idx, max_len, "CMCC", 4);
                        break;
                    case 1: case 6: case 9: case 10:
                        write_one_str_reconds(log, idx, max_len, "CUCC", 4);
                        break;
                    case 3: case 5: case 11: case 12:
                        write_one_str_reconds(log, idx, max_len, "CTCC", 4);
                        break;
                    default:
                        write_n_empty_reconds(log, idx, max_len, 1);
                        break;
                }
            }
            break;
        case EM_COMMON_RTL_PLMN_ID:
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, FL_PLMN_ID));
            break;
        case EM_COMMON_RTL_TAC:
            write_one_hex_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, FL_TAC));
            break;
        case EM_COMMON_RTL_MSISDN:
            write_uint64_reconds(log, idx, max_len, be64toh(get_uint64_t(trailer->data, FL_MSISDN)) >> 8);
            break;
        case EM_COMMON_RTL_IMEI:
            write_uint64_reconds(log, idx, max_len, be64toh(get_uint64_t(trailer->data, FL_IMEI)) >> 8);
            break;
        case EM_COMMON_RTL_IMSI:
            write_uint64_reconds(log, idx, max_len, be64toh(get_uint64_t(trailer->data, FL_IMSI)) >> 8);
            break;
        case EM_COMMON_RTL_TEID:
            write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, FL_TEID));
            break;
        case EM_COMMON_RTL_OUTTER_SRC:
            get_ip4string(fields, sizeof(fields), get_uint32_t(trailer->data, FL_SRC_IP));
            write_one_str_reconds(log, idx, max_len, fields, strlen(fields));
            break;
        case EM_COMMON_RTL_OUTTER_DST:
            get_ip4string(fields, sizeof(fields), get_uint32_t(trailer->data, FL_DST_IP));
            write_one_str_reconds(log, idx, max_len, fields, strlen(fields));
            break;
        case EM_COMMON_RTL_ULI:
            write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, FL_ULI));
            break;
        case EM_COMMON_HW_LAC:
            if(base == 0xc)
                write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, FL_ULI) >> 16);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_HW_SAC:
            if(base == 0xc)
                write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, FL_ULI) & 0xffff);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_RTL_ENODE_ID:
            if(base == 0xd)
                write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, FL_ULI) >> 12);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_RTL_CELL_ID:
            if(base == 0xd)
                write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, FL_ULI) & 0xff);
            else
                write_n_empty_reconds(log, idx, max_len, 1);
            break;
        case EM_COMMON_RTL_BS:
            tmp = base == 11 ? "2G" : base == 12 ? "3G" : base == 13 ? "4G" : "unknow";
            write_one_str_reconds(log, idx, max_len, tmp, strlen(tmp));
            break;
        default:
            write_n_empty_reconds(log, idx, max_len, 1);
            break;
        }
    }
    return 0; 
}

static int write_hw_yn_records(precord_t *log, int *idx, int max_len, struct flow_info *flow)
{
    uint32_t ret;
    uint32_t tmp;
    uint8_t fields[16];
    uint8_t tmp_imsi[32];

    struct hw_yn_trailer *trailer= flow->pTrailer;
    if(trailer->len == 0){
        write_n_empty_reconds(log, idx, max_len, EM_COMMON_RTL_BS + 1);
        return 0;
    }


    tmp = get_bcd(trailer->data + HW_IMSI + 2, tmp_imsi, 8);
    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++)
    {
        switch (i)
        {
        case EM_COMMON_TAGTYPE:
            write_one_num_reconds(log, idx, max_len, flow->data_link_layer.base);
            break;
        case EM_COMMON_HW_NCODE:
            switch(flow->data_link_layer.base){
                case 1:
                    write_one_str_reconds(log, idx, max_len, "147", 3);  //radius
                    break;
                case 2: case 5:
                    write_one_str_reconds(log, idx, max_len, "124", 3);  //CDMA UMTS (3G)
                    break;
#if 0
				case 3:
                    write_one_str_reconds(log, idx, max_len, "2G", 2);
                    break;
#endif
                case 4:
                    write_one_str_reconds(log, idx, max_len, "144", 3);  //LTE (4G)
                    break;
                default:
                    write_n_empty_reconds(log, idx, max_len, 1);
                    break;
            }
            break;
        case EM_COMMON_OPERATOR_TYPE:
            if(strcmp(g_config.operator_type, "AUTO"))
                write_one_str_reconds(log, idx, max_len, g_config.operator_type, strlen(g_config.operator_type));
            else{
                if(tmp > 5){
                    switch(get_uint16_ntohs(tmp_imsi, 3)){
                        case 0: case 2: case 4: case 7: case 8: case 0x103:
                            write_one_str_reconds(log, idx, max_len, "06", 2);   //CMCC
                            break;
                        case 1: case 6: case 9: case 0x100:
                            write_one_str_reconds(log, idx, max_len, "03", 2);   //CUCC
                            break;
                        case 3: case 5: case 0x101: case 0x102:
                            write_one_str_reconds(log, idx, max_len, "01", 2);   //CTCC
                            break;
                        default:
                            write_n_empty_reconds(log, idx, max_len, 1);
                            break;
                    }
                }
                else{
                    write_n_empty_reconds(log, idx, max_len, 1);
                }
            }
            break;
        case EM_COMMON_RTL_IMSI:
            if(tmp)
                write_multi_digit_reconds(log, idx, max_len, tmp_imsi, tmp);
            else
                write_one_num_reconds(log, idx, max_len, 0);
            break;

        case EM_COMMON_RTL_MSISDN:
            if((ret = get_bcd(trailer->data + HW_MSISDN + 2, fields, 8)))
                write_multi_digit_reconds(log, idx, max_len, fields, ret);
            else
                write_one_num_reconds(log, idx, max_len, 0);
            break;
        case EM_COMMON_RTL_IMEI:
            if((ret = get_bcd(trailer->data + HW_IMEI + 2, fields, 8)))
                write_multi_digit_reconds(log, idx, max_len, fields, ret);
            else
                write_one_num_reconds(log, idx, max_len, 0);
            break;

        case EM_COMMON_HW_ECGI:
            write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data + HW_ECGI, 5) );
/*
            if(get_uint32_t(trailer->data + HW_ECGI, 3) == 0)
                write_one_num_reconds(log, idx, max_len, 0);
            else
                write_multi_num_reconds(log, idx, max_len, trailer->data + HW_ECGI + 2, 7);
*/
            break;
        case EM_COMMON_HW_TAI: // 包含TAC
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, HW_TAI+5));
            break;
        case EM_COMMON_HW_EGI_MNC: // imsi MCC&MNC
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(tmp_imsi, 3));
            break;
        case EM_COMMON_HW_LAC: // LAC在RAI(0x09)中
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, HW_RAI+5));
            break;
        case EM_COMMON_HW_SAC: // SAC在SAI(0x08)中
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, HW_SAI+7));
            break;
        case EM_COMMON_HW_CALL_ID: // Cell-id在CGI(0x07)中
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, HW_CGI+5));
            break;
        case EM_COMMON_RTL_ENODE_ID:
            write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, HW_ECGI+5) >> 8);
            break;
        case EM_COMMON_RTL_CELL_ID:
            write_one_num_reconds(log, idx, max_len, get_uint32_ntohl(trailer->data, HW_ECGI+5) & 0xff);
            break;
        case EM_COMMON_HW_RAC: // RAC在RAI(0x09)中
            write_one_num_reconds(log, idx, max_len, get_uint16_ntohs(trailer->data, HW_RAI+7));
            break;
        default:
            write_n_empty_reconds(log, idx, max_len, 1);
            break;
        }
    }
    return 0;
}

static int write_hz_soft_records(precord_t *log, int *idx, int max_len, struct flow_info *flow){
    uint16_t plmn_id;
    struct hz_trailer *trailer = flow->pTrailer;
    for (int i = EM_COMMON_TAGTYPE; i < EM_COMMON_RTL_BS + 1; i++)
    {
        switch(i)
        {
          case EM_COMMON_HW_NCODE:
              write_one_num_reconds(log, idx, max_len, trailer->ncode);
              break;
          case EM_COMMON_RTL_IMSI:
              write_one_hex_reconds(log, idx, max_len, trailer->imsi);
              break;
          case EM_COMMON_RTL_MSISDN:
              write_one_hex_reconds(log, idx, max_len, trailer->msisdn);
              break;
          case EM_COMMON_RTL_IMEI:
              write_one_hex_reconds(log, idx, max_len, trailer->imei);
              break;
          case EM_COMMON_HW_LAC:
              write_one_num_reconds(log, idx, max_len, trailer->lac);
              break;
          case EM_COMMON_RTL_CELL_ID:
              write_one_num_reconds(log, idx, max_len, trailer->ci);
              break;
          case EM_COMMON_RTL_TAC:
              write_one_num_reconds(log, idx, max_len, trailer->tac);
              break;
          case EM_COMMON_HW_ECGI:
              write_one_num_reconds(log, idx, max_len, trailer->ecgi);
              break;
          case EM_COMMON_RTL_PLMN_ID:
              plmn_id = (trailer->mcc << 8) + trailer->mnc;
              write_one_num_reconds(log, idx, max_len, plmn_id);
              break;
          case EM_COMMON_HW_EGI_MNC:
              write_one_num_reconds(log, idx, max_len, trailer->mnc);
              break;
          default:
              write_n_empty_reconds(log, idx, max_len, 1);
              break;

        }
    }
    return 0;
}


static int write_mobile_trailer_records(precord_t *log, int *idx, int max_len, struct flow_info *flow, Trailer_Type type)
{
    if (!flow->pTrailer)
    {
        write_n_empty_reconds(log, idx, max_len, EM_COMMON_RTL_BS + 1);
        return 0;
    }

    switch (type)
    {
        case RT_6402:
        case RT_9800:
        case RT_SINO:
            write_rt_records(log, idx, max_len, flow, type);
            break;
        case JL_DEFAULT:
            write_jl_records(log, idx, max_len, flow);
            break;
        case HW_DEFAULT:
        case HW_DB:
        case BJ_JSD:
            write_hw_default_records(log, idx, max_len, flow, type);
            break;
        case HW_YN:
            write_hw_yn_records(log, idx, max_len, flow);
            break;
        case FL_DEFAULT:
            write_fl_records(log, idx, max_len, flow);
            break;
        case HZ_SOFT:
            write_hz_soft_records(log, idx, max_len, flow);
            break;
        default:
            break;
    }
    return 0;
}

void write_trailer_reconds(char *log_content, int *idx, int log_len_max, struct flow_info *flow, Trailer_Type trailer_type, Net_Type net_type)
{
    abort();
    switch(net_type){
        case FIXED_NET:
            //write_fixed_trailer_records(log_content, idx, log_len_max, flow, trailer_type);
            break;
        case MOBILE_NET:
            //write_mobile_trailer_records(log_content, idx, log_len_max, flow, trailer_type);
            break;
        default:
            //write_n_empty_reconds(log_content, idx, log_len_max, EM_COMMON_RTL_BS + 1);
            break;
    }
    return;
}



