#ifndef _DPI_FLOW_H
#define _DPI_FLOW_H
#include <stdbool.h>

#include "dpi_detect.h"

typedef void (*dpi_free_callback)(struct flow_info *info);

void dpi_flow_init();
void * dpi_flow_create();
struct flow_info * dpi_flow_clone(struct flow_info *info);
void dpi_flow_free(struct flow_info *info , dpi_free_callback cb);

DpiHash *dpi_flow_hash_new(int thread);
int      dpi_flow_hash_lookup(const DpiHash *hash, const void *key, void **data);
int      dpi_flow_hash_insert(const DpiHash *hash, const void *key, void *data);
int      dpi_flow_hash_remove(const DpiHash *hash, const void *key);
int      dpi_flow_hash_size(const DpiHash *hash);


#endif