/*******************************************************************************
 * 文 件 名 : dpi_email.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - - - 设计: chenzq      2023/04/11
- - - - 编码: chenzq      2023/04/11
- - - - 修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -

*******************************************************************************/
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include "dpi_email.h"

#include <yaemail/email.h>

#include "dpi_tbl_log.h"
#include "dpi_utils.h"
#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "base64.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

dpi_field_table  email_field_array[] = {
    DPI_FIELD_D(EM_EMAIL_SENDER_EMAIL,          YA_FT_STRING,   "senderEmail"),
    DPI_FIELD_D(EM_EMAIL_SENDER_ALI,            YA_FT_STRING,   "senderAli"),
    DPI_FIELD_D(EM_EMAIL_FROM_IP,               YA_FT_STRING,   "FromIp"),
    DPI_FIELD_D(EM_EMAIL_FROM_IP_CNT,           YA_FT_UINT32,   "FromIpCnt"),
    DPI_FIELD_D(EM_EMAIL_FROM_DOM,              YA_FT_STRING,   "FromDom"),
    DPI_FIELD_D(EM_EMAIL_FROM_DOM_CNT,          YA_FT_UINT32,   "FromDomCnt"),
    DPI_FIELD_D(EM_EMAIL_FROM_ASN,              YA_FT_STRING,   "FromAsn"),
    DPI_FIELD_D(EM_EMAIL_FROM_COUNTRY,          YA_FT_STRING,   "FromCountry"),
    DPI_FIELD_D(EM_EMAIL_RCVR_EMAIL,            YA_FT_STRING,   "rcvrEmail"),
    DPI_FIELD_D(EM_EMAIL_RCVR_ALI,              YA_FT_STRING,   "rcvrAli"),
    DPI_FIELD_D(EM_EMAIL_BY_IP,                 YA_FT_STRING,   "ByIP"),
    DPI_FIELD_D(EM_EMAIL_RCV_DATE,              YA_FT_STRING,   "rcvDate"),
    DPI_FIELD_D(EM_EMAIL_CC,                    YA_FT_STRING,   "CC"),
    DPI_FIELD_D(EM_EMAIL_CC_ALI,                YA_FT_STRING,   "CCAli"),
    DPI_FIELD_D(EM_EMAIL_BCC,                   YA_FT_STRING,   "BCC"),
    DPI_FIELD_D(EM_EMAIL_REP_TO,                YA_FT_STRING,   "repTo"),
    DPI_FIELD_D(EM_EMAIL_DATE,                  YA_FT_STRING,   "date"),
    DPI_FIELD_D(EM_EMAIL_EMA_PROT_TYPE,         YA_FT_STRING,   "emaProtType"),
    DPI_FIELD_D(EM_EMAIL_LOGIN_SRV,             YA_FT_STRING,   "loginSrv"),
    DPI_FIELD_D(EM_EMAIL_SMTP_SRV,              YA_FT_STRING,   "SMTPSrv"),
    DPI_FIELD_D(EM_EMAIL_SMTP_SRV_AGE,          YA_FT_STRING,   "SMTPSrvAge"),
    DPI_FIELD_D(EM_EMAIL_SUBJ,                  YA_FT_STRING,   "subj"),
    DPI_FIELD_D(EM_EMAIL_X_MAI,                 YA_FT_STRING,   "xMai"),
    DPI_FIELD_D(EM_EMAIL_CON_TRA_ENC,           YA_FT_STRING,   "conTraEnc"),
    DPI_FIELD_D(EM_EMAIL_CON_TEX_CHA,           YA_FT_STRING,   "conTexCha"),
    DPI_FIELD_D(EM_EMAIL_BODY_TYPE,             YA_FT_STRING,   "bodyType"),
    DPI_FIELD_D(EM_EMAIL_BODY_TYPE_CNT,         YA_FT_UINT32,   "bodyTypeCnt"),
    DPI_FIELD_D(EM_EMAIL_CON_TYPE,              YA_FT_STRING,   "conType"),
    DPI_FIELD_D(EM_EMAIL_CON_TYPE_CNT,          YA_FT_UINT32,   "conTypeCnt"),
    DPI_FIELD_D(EM_EMAIL_EMA_IND,               YA_FT_STRING,   "emaInd"),
    DPI_FIELD_D(EM_EMAIL_ATT_FILENAME,          YA_FT_STRING,   "attFileName"),
    DPI_FIELD_D(EM_EMAIL_ATT_FILENAME_CNT,      YA_FT_UINT32,   "attFileNameCnt"),
    DPI_FIELD_D(EM_EMAIL_ATT_TYPE,              YA_FT_STRING,   "attType"),
    DPI_FIELD_D(EM_EMAIL_ATT_TYPE_CNT,          YA_FT_UINT32,   "attTypeCnt"),
    DPI_FIELD_D(EM_EMAIL_ATT_MD5,               YA_FT_STRING,   "attMD5"),
    DPI_FIELD_D(EM_EMAIL_ATT_MD5_CNT,           YA_FT_UINT32,   "attMD5Cnt"),
    DPI_FIELD_D(EM_EMAIL_ATT_CON_SIZE,          YA_FT_UINT16,   "attConSize"),
    DPI_FIELD_D(EM_EMAIL_HEAD_SET,              YA_FT_STRING,   "headSet"),
    DPI_FIELD_D(EM_EMAIL_HEAD_SET_CNT,          YA_FT_UINT32,   "headSetCnt"),
    DPI_FIELD_D(EM_EMAIL_MSG_ID,                YA_FT_STRING,   "msgID"),
    DPI_FIELD_D(EM_EMAIL_MSG_ID_CNT,            YA_FT_UINT32,   "msgIDCnt"),
    DPI_FIELD_D(EM_EMAIL_MIME_VER,              YA_FT_STRING,   "mimeVer"),
    DPI_FIELD_D(EM_EMAIL_MIME_VER_CNT,          YA_FT_UINT32,   "mimeVerCnt"),
    DPI_FIELD_D(EM_EMAIL_LOGIN,                 YA_FT_STRING,   "login"),
    DPI_FIELD_D(EM_EMAIL_PWD,                   YA_FT_STRING,   "pwd"),
    DPI_FIELD_D(EM_EMAIL_REAL_FROM ,            YA_FT_STRING,   "realFrom"),
    DPI_FIELD_D(EM_EMAIL_REAL_TO,               YA_FT_STRING,   "realTo"),
    DPI_FIELD_D(EM_EMAIL_CONTENT_WITH_HTML,     YA_FT_STRING,   "contentWithHtml"),
    DPI_FIELD_D(EM_EMAIL_CHARSET,               YA_FT_STRING,   "charset"),
    DPI_FIELD_D(EM_EMAIL_CONTENT_LEN,           YA_FT_UINT32,   "contentLen"),
    DPI_FIELD_D(EM_EMAIL_HOST,                  YA_FT_STRING,   "host"),
    DPI_FIELD_D(EM_EMAIL_DELIVERED_TO,          YA_FT_STRING,   "deliveredTo"),
    DPI_FIELD_D(EM_EMAIL_X_ORI_IP,              YA_FT_STRING,   "xOriIP"),
    DPI_FIELD_D(EM_EMAIL_START_TLS,             YA_FT_UINT32,   "startTLS"),
    DPI_FIELD_D(EM_EMAIL_COMMAND,               YA_FT_STRING,   "Command"),
    DPI_FIELD_D(EM_EMAIL_COUNT,                 YA_FT_UINT32,   "count"),
    DPI_FIELD_D(EM_EMAIL_MAIL_FROM,             YA_FT_STRING,   "mailFrom"),
    DPI_FIELD_D(EM_EMAIL_MAIL_FROM_DOM,         YA_FT_STRING,   "mailFromDom"),
    DPI_FIELD_D(EM_EMAIL_MAIL_FROM_DOM_CNT,     YA_FT_STRING,   "mailFromDomCnt"),
    DPI_FIELD_D(EM_EMAIL_RCVR_DOM,              YA_FT_STRING,   "rcvrDom"),
    DPI_FIELD_D(EM_EMAIL_RCPT_TO,               YA_FT_STRING,   "rcptTo"),
    DPI_FIELD_D(EM_EMAIL_RCPT_TO_DOM,           YA_FT_STRING,   "rcptToDom"),
    DPI_FIELD_D(EM_EMAIL_RCPT_TO_DOM_CNT,       YA_FT_STRING,   "rcptToDomCnt"),
    DPI_FIELD_D(EM_EMAIL_RESENT_FROM,           YA_FT_STRING,   "resentFrom"),
    DPI_FIELD_D(EM_EMAIL_RESENT_TO,             YA_FT_STRING,   "resentTo"),
    DPI_FIELD_D(EM_EMAIL_RESENT_DATE,           YA_FT_STRING,   "resentDate"),
    DPI_FIELD_D(EM_EMAIL_BODY_LEN,              YA_FT_UINT32,   "bodyLen"),
    DPI_FIELD_D(EM_EMAIL_BODY_TRA_ENC,          YA_FT_STRING,   "bodyTraEnc"),
    DPI_FIELD_D(EM_EMAIL_BODY_TEX_CHA,          YA_FT_STRING,   "bodyTexCha"),
    DPI_FIELD_D(EM_EMAIL_NAME,                  YA_FT_STRING,   "name"),
    DPI_FIELD_D(EM_EMAIL_VENDOR,                YA_FT_STRING,   "vendor"),
    DPI_FIELD_D(EM_EMAIL_VER,                   YA_FT_STRING,   "ver"),
    DPI_FIELD_D(EM_EMAIL_OS,                    YA_FT_STRING,   "os"),
    DPI_FIELD_D(EM_EMAIL_OSVER,                 YA_FT_STRING,   "osVer"),
    DPI_FIELD_D(EM_EMAIL_RCVR_EMAIL_CNT,        YA_FT_UINT32,   "rcvrEmailCnt"),
    DPI_FIELD_D(EM_EMAIL_SUBJ_CNT,              YA_FT_UINT32,   "subjectCnt"),
    DPI_FIELD_D(EM_EMAIL_X_MAI_CNT,             YA_FT_STRING,   "xMailerCnt"),
    DPI_FIELD_D(EM_EMAIL_RECEIVED,              YA_FT_STRING,   "received"),
    DPI_FIELD_D(EM_EMAIL_WITH,                  YA_FT_STRING,   "with"),
    DPI_FIELD_D(EM_EMAIL_BYDOM,                 YA_FT_STRING,   "ByDom"),
    DPI_FIELD_D(EM_EMAIL_BYASN,                 YA_FT_STRING,   "ByAsn"),
    DPI_FIELD_D(EM_EMAIL_BYCOUNTRY,             YA_FT_STRING,   "ByCountry"),
    DPI_FIELD_D(EM_EMAIL_USRAGE,                YA_FT_STRING,   "usrAge"),
    DPI_FIELD_D(EM_EMAIL_SENDERDOM,             YA_FT_STRING,   "senderDom"),
    DPI_FIELD_D(EM_EMAIL_CONTNET,               YA_FT_STRING,   "content"),
    DPI_FIELD_D(EM_EMAIL_RESENTSRVAGE,          YA_FT_STRING,   "resentSrvAge"),
    DPI_FIELD_D(EM_EMAIL_BODY,                  YA_FT_STRING,   "body"),
    DPI_FIELD_D(EM_EMAIL_BODYURL,               YA_FT_STRING,   "bodyURL"),
#ifdef DPI_SDT_YNAO
    DPI_FIELD_D(EM_EMAIL_LOCAL_FILENAME,              YA_FT_STRING,       "localFilename"),
    DPI_FIELD_D(EM_EMAIL_LOCAL_FILEPATH,              YA_FT_STRING,       "localFilepath"),
    DPI_FIELD_D(EM_EMAIL_SPF,                         YA_FT_STRING,       "spf"),
    DPI_FIELD_D(EM_EMAIL_ENVELOPE_FROM,               YA_FT_STRING,       "envelope_from"),
    DPI_FIELD_D(EM_EMAIL_ENVELOPE_FROM_DOMAIN,        YA_FT_STRING,       "envelope_from_domain"),
    DPI_FIELD_D(EM_EMAIL_ENVELOPE_FROM_DOMAIN_COUNT,  YA_FT_STRING,       "envelope_from_domain_count"),
    DPI_FIELD_D(EM_EMAIL_FROM_IP_COUNT,               YA_FT_STRING,       "from_ip_count"),
    DPI_FIELD_D(EM_EMAIL_FROM_DOMAIN_COUNT,           YA_FT_STRING,       "from_domain_count"),
    DPI_FIELD_D(EM_EMAIL_BY_IP_COUNT,                 YA_FT_STRING,       "by_ip_count"),
    DPI_FIELD_D(EM_EMAIL_BY_DOMAIN_COUNT,             YA_FT_STRING,       "by_domain_count"),
    DPI_FIELD_D(EM_EMAIL_RESENT_AGENT,                YA_FT_STRING,       "resent_agent"),
    DPI_FIELD_D(EM_EMAIL_CC_ALIAS,                    YA_FT_STRING,       "cc_alias"),
    DPI_FIELD_D(EM_EMAIL_BODY_MD5,                    YA_FT_STRING,       "body_md5"),
    DPI_FIELD_D(EM_EMAIL_BODY_LENGTH,                 YA_FT_STRING,       "body_length"),
    DPI_FIELD_D(EM_EMAIL_BODY_URL_COUNT,              YA_FT_STRING,       "body_url_count"),
    DPI_FIELD_D(EM_EMAIL_INDEX,                       YA_FT_STRING,       "index"),
    DPI_FIELD_D(EM_EMAIL_ATTACHMENT_MD5_COUNT,        YA_FT_STRING,       "attachment_md5_count"),
    DPI_FIELD_D(EM_EMAIL_HEADER_SET_COUNT,            YA_FT_STRING,       "header_set_count"),
    DPI_FIELD_D(EM_EMAIL_SUBJECT_COUNT,               YA_FT_STRING,       "subject_count"),
    DPI_FIELD_D(EM_EMAIL_X_MAILER_COUNT,              YA_FT_STRING,       "x_mailer_count"),
    DPI_FIELD_D(EM_EMAIL_RECEIVER_ALIAS,              YA_FT_STRING,       "receiver_alias"),
    DPI_FIELD_D(EM_EMAIL_SENDER_ALIAS,                YA_FT_STRING,       "sender_alias"),
    DPI_FIELD_D(EM_EMAIL_REPLY,                       YA_FT_STRING,       "reply"),
    DPI_FIELD_D(EM_EMAIL_SENDER_DOMAIN,               YA_FT_STRING,       "sender_domain"),
    DPI_FIELD_D(EM_EMAIL_SENDER_SOFTWARE,             YA_FT_STRING,       "sender_software"),
    DPI_FIELD_D(EM_EMAIL_USER_AGENT,                  YA_FT_STRING,       "user_agent"),
    DPI_FIELD_D(EM_EMAIL_AUTH_RESULT,                 YA_FT_STRING,       "auth_result"),
    DPI_FIELD_D(EM_EMAIL_BANNER,                      YA_FT_STRING,       "banner"),
    DPI_FIELD_D(EM_EMAIL_ENVELOPE_TO,                 YA_FT_STRING,       "envelope_to")
#endif
};

static const char *smtp_cmd_array[] = {
    "EHLO",
    "HELO",
    "MAIL",
    "RCPT",
    "DATA",
    "RSET",
    "NOOP",
    "QUIT",
    "VERFY",
    "AUTH",
    "STARTTLS",
    "XCLIENT",
    "XFORWARD",
    NULL
};

// define pop cmd arrary
static const char *pop_cmd_array[] = {
    "USER",
    "PASS",
    "STAT",
    "LIST",
    "RETR",
    "DELE",
    "RSET",
    "QUIT",
    "NOOP",
    "TOP",
    "UIDL",
    "APOP",
    "AUTH",
    "CAPA",
    "STLS",
    "USER",
    "PASS",
    "STAT",
    "LIST",
    "RETR",
    "DELE",
    "RSET",
    "QUIT",
    "NOOP",
    "TOP",
    "UIDL",
    "APOP",
    "AUTH",
    "CAPA",
    "STLS",
    NULL
};

const char * _get_smtp_command(const char * data, size_t data_len) {
    // only compare first 20 bit
    size_t len = data_len > 20 ? 20 : data_len;
    int i = 0;
    for (i = 0; smtp_cmd_array[i] != NULL; i++) {
        // find smtp_cmd_array[i] from data
        const char * cmd = smtp_cmd_array[i];
        if (dpi_strstr(data, len, cmd, strlen(cmd)) != NULL) {
          return smtp_cmd_array[i];
        }
    }
    return NULL;
}

const char * _get_pop_command(const char * data, size_t data_len) {
    // only compare first 20 bit
    size_t len = data_len > 20 ? 20 : data_len;
    int i = 0;
    for (i = 0; pop_cmd_array[i] != NULL; i++) {
        // find smtp_cmd_array[i] from data
        const char * cmd = pop_cmd_array[i];
        if (dpi_strstr(data, len, cmd, strlen(cmd)) != NULL) {
          return pop_cmd_array[i];
        }
    }
    return NULL;
}

static int _get_index_from_kvpair(KeyValuePair * pair, int pair_num, const char * key) {
    int i = 0;
    for (i = 0; i < pair_num; i++) {
        if (strcmp(pair[i].key, key) == 0) {
            return i;
        }
    }
    return -1;
}

int email_get_reassemble(struct flow_info *flow, int direction,
                         uint8_t **data, uint32_t * data_len) {
  uint32_t reassemble_len;   // 重组长度
  struct list_head *head;   // 链表头结点
  uint32_t actual_len = 0;
  uint32_t expect_len = 0;
  uint32_t index;
  if (direction == FLOW_DIR_SRC2DST) {
    head = &flow->reassemble_src2dst_head;
    reassemble_len = flow->reassemble_pkt_src2dst_num * TCP_PAYLOAD_MAX_LEN;
  } else {
    head = &flow->reassemble_dst2src_head;
    reassemble_len = flow->reassemble_pkt_dst2src_num * TCP_PAYLOAD_MAX_LEN;
  }

  tcp_reassemble_do_guesslen(head, &actual_len, &expect_len);

  do {
    if (expect_len > EMAIL_MAX_REASSMBLE_LEN || expect_len < 100) {
      log_trace("expect_len is too long or too short, expect_len = %d", expect_len);
      break;
    }

    *data = (uint8_t *)dpi_malloc(expect_len + 1500);
    if (! (*data)) {
      break;
    }

    tcp_reassemble_do_padding(head, *data, &index, expect_len+1500);
    *data_len = expect_len;

    return 0;
  } while(0);

  if (direction == FLOW_DIR_SRC2DST) {
   tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
  } else {
   tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);
  }

  return -1;
}

// define imap cmd arrary
const char * dpi_get_email_command(const char *data, int data_len, EmailType type)
{
  size_t len = data_len > 20  ? 20 : data_len;
  char * arrary = NULL;
  int i = 0;

  switch (type) {
  case EMAIL_SMTP:
    return _get_smtp_command(data, data_len);
  case EMAIL_POP:
    return _get_pop_command(data, data_len);
  default:
    return NULL;
  }

  return NULL;
}

void write_email_log(struct flow_info *flow, int direction, dpi_email_t *email) {
  uint8_t k;
  char tmp[1024];
  int i;
  int idx = 0;
  unsigned char _str[512] = { 0 };
  struct tbl_log *log_ptr;
  EmailSession *session = (EmailSession *)flow->app_session;
  char *value = NULL;
  int index = -1;

  if (session == NULL || email == NULL) {
    log_trace("session or email is null");
    return;
  }
  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    log_trace("not enough memory");
    return;
  }

  init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
  dpi_precord_new_record(log_ptr->record, NULL, NULL);
  write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "email");

  for (i = 0; i < EM_EMAIL_MAX; i++) {
    switch (email_field_array[i].index) {
    case EM_EMAIL_FROM_ASN:
      if (session->from_asns_num) {
        session->from_asns[strlen(session->from_asns) - 1] = '\0';
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->from_asns, strlen(session->from_asns));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_FROM_COUNTRY:
      if (session->from_countries_num) {
        session->from_countries[strlen(session->from_countries) - 1] = '\0';
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->from_countries, strlen(session->from_countries));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_HOST:
      if (session->mail_helo_ptr[0] != '\0') {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)session->mail_helo_ptr, strlen(session->mail_helo_ptr));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_START_TLS:
      write_fstring_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "%u", session->starttls_f);
      break;
    case EM_EMAIL_COMMAND:
      if (session->commands[0] != '\0') {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->commands, strlen(session->commands));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_COUNT:
      write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      break;
    case EM_EMAIL_MAIL_FROM:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type, (const uint8_t*)session->from.value, strlen(session->from.value));
      break;
    case EM_EMAIL_MAIL_FROM_DOM:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type, (const uint8_t*)session->from.domin, strlen(session->from.domin));
      break;
    case EM_EMAIL_MAIL_FROM_DOM_CNT:
      write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->from.domin_cnt);
      break;
    case EM_EMAIL_RCPT_TO:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type, (const uint8_t*)session->rcptto.value, strlen(session->rcptto.value));
      break;
    case EM_EMAIL_RCPT_TO_DOM:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type, (const uint8_t*)session->rcptto.domin, strlen(session->rcptto.domin));
      break;
    case EM_EMAIL_RCPT_TO_DOM_CNT:
      write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->rcptto.domin_cnt);
      break;
    case EM_EMAIL_LOGIN:
      if (session->auth_name[0] != '\0') {
        _base64_decode(session->auth_name, sizeof(session->auth_name));
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)session->auth_name, strlen((const char *)session->auth_name));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_PWD:
      if (session->auth_passwd[0] != '\0') {
        _base64_decode(session->auth_passwd, sizeof(session->auth_passwd));
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char *)session->auth_passwd, strlen((const char *)session->auth_passwd));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_EMA_PROT_TYPE:
      write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->email_type, strlen(session->email_type));
      break;
    case EM_EMAIL_SMTP_SRV:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->mail_server, strlen(session->mail_server));
      break;
    case EM_EMAIL_SMTP_SRV_AGE:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->mail_server, strlen(session->mail_server));
      break;
    case EM_EMAIL_NAME:
      index = _get_index_from_kvpair(session->kv_pairs, EMAIL_IMAP_KV_MAX, "name");
      if (index != -1) {
        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->kv_pairs[index].value, strlen(session->kv_pairs[index].value));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_VENDOR:
      index = _get_index_from_kvpair(session->kv_pairs, EMAIL_IMAP_KV_MAX, "vendor");
      if (index != -1) {
        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->kv_pairs[index].value, strlen(session->kv_pairs[index].value));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_VER:
      index = _get_index_from_kvpair(session->kv_pairs, EMAIL_IMAP_KV_MAX, "version");
      if (index != -1) {
        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->kv_pairs[index].value, strlen(session->kv_pairs[index].value));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_OS:
      index = _get_index_from_kvpair(session->kv_pairs, EMAIL_IMAP_KV_MAX, "os");
      if (index != -1) {
        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->kv_pairs[index].value, strlen(session->kv_pairs[index].value));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_OSVER:
      index = _get_index_from_kvpair(session->kv_pairs, EMAIL_IMAP_KV_MAX, "os-version");
      if (index != -1) {
        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,(const uint8_t *)session->kv_pairs[index].value, strlen(session->kv_pairs[index].value));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_ATT_CON_SIZE:
      value = dpi_email_get_by_name(email, email_field_array[i].field_name);
      if (value && value[0]) {
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, atoi(value));
      } else {
        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
      }
      break;
    case EM_EMAIL_AUTH_RESULT:
      if (session->auth_result[0] != '\0') {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->auth_result, strlen(session->auth_result));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_BANNER:
      if (session->banner[0] != '\0') {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->banner, strlen(session->banner));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
    case EM_EMAIL_ENVELOPE_TO:
      // 从MAIL_FROM中获取
      if (session->from.value[0] != '\0') {
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->from.value, strlen(session->from.value));
      } else {
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
      }
      break;
#ifdef DPI_SDT_YNAO
    case EM_EMAIL_LOCAL_FILENAME:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,
          (const uint8_t *)session->mail_filename + strlen(g_config.tbl_out_dir) + 1, strlen(session->mail_filename +  strlen(g_config.tbl_out_dir)+1));

      break;
      case EM_EMAIL_LOCAL_FILEPATH:
      write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type,
          (const uint8_t *)session->mail_filename, strlen(session->mail_filename));

      break;
#endif

    default:
      value =  dpi_email_get_by_name(email, email_field_array[i].field_name);
      if (value != NULL) {
          write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, email_field_array[i].type, (const uint8_t *)value, strlen(value));
      } else {
          write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
      }
      break;
      }
  }
    //record_show(log_ptr->record);
    log_ptr->thread_id= flow->thread_id;
    log_ptr->log_len = idx;        //最后一个字段后面有 "|"
    log_ptr->log_type = TBL_LOG_EMAIL;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->proto_id = PROTOCOL_EMAIL;  // update
    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return;
}

static void init_email_dissector(void)
{
    dpi_register_proto_schema(email_field_array, EM_EMAIL_MAX, "email");

    map_fields_info_register(email_field_array, PROTOCOL_EMAIL, EM_EMAIL_MAX, "email");
    return;
}

static __attribute((constructor)) void before_init_email(void) {
    register_tbl_array(TBL_LOG_EMAIL, 0, "email", init_email_dissector);
}

int dissect_email_miss(struct flow_info *flow, uint8_t direction, uint32_t miss_len) {
  EmailSession *session;
  if (flow->app_session == NULL) {
    return 0;
  }
  session = (EmailSession *)flow->app_session;
  if (session->state == EMAIL_STATE_READING_DATA) {
    struct Emailcache *c = session->cache + 2;

    session->complemail = 0;
    if (c->cache) {
    char tmp_buff[64] = {0};
    if ((int)sizeof(tmp_buff) >= (c->cache_size - c->cache_hold)) {
      // 缓存撑爆前重新申请内存
      // 创建临时缓冲区
      char *new_cache = (char *)realloc(c->cache, c->cache_size + miss_len + 1000);
      if (NULL == new_cache) {
        return 0;
      }
      c->cache = new_cache;
      c->cache_size += miss_len + 1000;
    }
    snprintf(tmp_buff, sizeof(tmp_buff), "[%s %d bytes missing in capture]", direction ? "A2B" : "B2A", miss_len);
    memcpy(c->cache + c->cache_hold, tmp_buff, strlen(tmp_buff));
    c->cache_hold += strlen(tmp_buff);
    }
    return 0;
  }
  session->state = EMAIL_STATE_READING_CMDS;
  session->miss_len += miss_len;
  flow->reassemble_flag = 1; /* for mail out-of-order */
  return 0;
}