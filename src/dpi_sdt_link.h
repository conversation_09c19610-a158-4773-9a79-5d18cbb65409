#ifndef _DPI_SDT_LINK_H_
#define _DPI_SDT_LINK_H_

#include <yaProtoRecord/precord.h>

#include "dpi_memory.h"

#ifdef DPI_SDT_YNAO
enum _ip_enum{
    EM_YNAO_IP_IPVER,
    EM_YNAO_IP_SRCADDR,
    EM_YNAO_IP_SRCADDRV6,
    EM_YNAO_IP_DSTADDR,
    EM_YNAO_IP_DSTADDRV6,
    EM_YNAO_IP_SRCPORT,
    EM_YNAO_IP_DSTPORT,
    EM_YNAO_IP_PROTNUM,
    EM_YNAO_IP_SRCCOUNTRY,
    EM_YNAO_IP_SRCSTATE,
    EM_YNAO_IP_SRCCITY,
    EM_YNAO_IP_SRCLONGITUDE,
    EM_YNAO_IP_SRCLATITUDE,
    EM_YNAO_IP_SRCISP,
    EM_YNAO_IP_SRCASN,
    EM_YNAO_IP_DSTCOUNTRY,
    EM_YNAO_IP_DSTSTATE,
    EM_YNAO_IP_DSTCITY,
    EM_YNAO_IP_DSTLONGITUDE,
    EM_YNAO_IP_DSTLATITUDE,
    EM_YNAO_IP_DSTISP,
    EM_YNAO_IP_DSTASN,
    EM_YNAO_IP_PROTINFO,
    EM_YNAO_IP_PROTTYPE,
    EM_YNAO_IP_PROTNAME,
    EM_YNAO_IP_BEGTIME,
    EM_YNAO_IP_ENDTIME,
    EM_YNAO_IP_COMDUR,

    EM_YNAO_IP_FIRTTLBYCLI,
    EM_YNAO_IP_FIRTTLBYSRV,
    EM_YNAO_IP_APPDIREC,
    EM_YNAO_IP_PKTNUM,
    EM_YNAO_IP_UPLINKPKTNUM,
    EM_YNAO_IP_DOWNLINKPKTNUM,
    EM_YNAO_IP_SESBYTES,
    EM_YNAO_IP_UPSESBYTES,
    EM_YNAO_IP_DOWNSESBYTES,
    EM_YNAO_IP_SESBYTESRATIO,
    EM_YNAO_IP_PAYLEN,
    EM_YNAO_IP_UPPAYLEN,
    EM_YNAO_IP_DOWNPAYLEN,
    EM_YNAO_IP_PAYLENRATIO,
    EM_YNAO_IP_DESBYTES,
    EM_YNAO_IP_UPLINKDESBYTES,
    EM_YNAO_IP_DOWNLINKDESBYTES,
    EM_YNAO_IP_STREAM,
    EM_YNAO_IP_UPLINKSTREAM,
    EM_YNAO_IP_DOWNLINKSTREAM,

    EM_YNAO_IP_AVGPKTLEN,
    EM_YNAO_IP_AVGPKTINT,

    EM_YNAO_IP_MAX
};

enum _link_enum{
    EM_LINK_UPLINKTRANSPAYHEX,
    EM_LINK_DOWNLINKTRANSPAYHEX,
    EM_LINK_UPLINKPAYLENSET,
    EM_LINK_DOWNLINKPAYLENSET,
    EM_LINK_UPLINKBIGPKTLEN,
    EM_LINK_DOWNLINKBIGPKTLEN,
    EM_LINK_UPLINKSMAPKTLEN,
    EM_LINK_DOWNLINKSMAPKTLEN,
    EM_LINK_UPLINKFREQPKTLEN,
    EM_LINK_DOWNLINKFREQPKTLEN,
    EM_LINK_UPLINKBIGPKTINT,
    EM_LINK_DOWNLINKBIGPKTINT,
    EM_LINK_DOWNLINKSMAPKTINT,
    EM_LINK_UPLINKSMAPKTINT,
    EM_LINK_FIRSTFLAGS,
    EM_LINK_UPLINKSYNSEQNUM,
    EM_LINK_DOWNLINKSYNSEQNUM,
    EM_LINK_UPLINKSYNTCPWINS,
    EM_LINK_DOWNLINKSYNTCPWINS,
    EM_LINK_UPLINKTCPOPTS,
    EM_LINK_DOWNLINKTCPOPTS,
    EM_LINK_UPLINKFLAGS,
    EM_LINK_DOWNLINKFLAGS,
    EM_LINK_TCPFLAGSFINCNT,
    EM_LINK_TCPFLAGSSYNCNT,
    EM_LINK_TCPFLAGSRSTCNT,
    EM_LINK_TCPFLAGSPSHCNT,
    EM_LINK_TCPFLAGSACKCNT,
    EM_LINK_TCPFLAGSURGCNT,
    EM_LINK_TCPFLAGSECECNT,
    EM_LINK_TCPFLAGSCWRCNT,
    EM_LINK_TCPFLAGSNSCNT,
    EM_LINK_TCPFLAGSSYNACKCNT,
    EM_LINK_ESTABLISH,
    EM_LINK_FINISHED,

    EM_LINK_MAX
};

void dpi_sdt_init_ip_map_fields(void);

int write_ip(struct flow_info *flow, int direction, SdtMatchResult *match_result _U_);
int write_link(struct flow_info *flow, int direction, SdtMatchResult *match_result _U_);
#else
enum _link_enum{
    EM_LINK_PORTINFO,
    EM_LINK_PORTINFOATT,
    EM_LINK_UPPAYLEN,
    EM_LINK_DOWNPAYLEN,
    EM_LINK_TCPFLAG,
    EM_LINK_UPLINKPKTNUM,
    EM_LINK_UPLINKSIZE,
    EM_LINK_UPLINKBIGPKTLEN,
    EM_LINK_UPLINKSMAPKTLEN,
    EM_LINK_UPLINKBIGPKTINT,
    EM_LINK_UPLINKSMAPKTINT,
    EM_LINK_DOWNLINKPKTNUM,
    EM_LINK_DOWNLINKSIZE,
    EM_LINK_DOWNLINKBIGPKTLEN,
    EM_LINK_DOWNLINKSMAPKTLEN,
    EM_LINK_DOWNLINKBIGPKTINT,
    EM_LINK_DOWNLINKSMAPKTINT,
    EM_LINK_FIRTTLBYCLI,
    EM_LINK_FIRTTLBYSRV,
    EM_LINK_APPDIREC,
    EM_LINK_TCPFLAGSFINCNT,
    EM_LINK_TCPFLAGSSYNCNT,
    EM_LINK_TCPFLAGSRSTCNT,
    EM_LINK_TCPFLAGSPSHCNT,
    EM_LINK_TCPFLAGSACKCNT,
    EM_LINK_TCPFLAGSURGCNT,
    EM_LINK_TCPFLAGSECECNT,
    EM_LINK_TCPFLAGSCWRCNT,
    EM_LINK_TCPFLAGSNSCNT,
    EM_LINK_TCPFLAGSSYNACKCNT,
    EM_LINK_ETAGS,
    EM_LINK_TTAGS,
    EM_LINK_UPLINKCHECKSUM,
    EM_LINK_DOWNLINKCHECKSUM,
    EM_LINK_UPLINKDESBYTES,
    EM_LINK_DOWNLINKDESBYTES,
    EM_LINK_STREAM,
    EM_LINK_UPLINKSTREAM,
    EM_LINK_DOWNLINKSTREAM,
    EM_LINK_TRANS_PAYLOAD_HEX,
    EM_LINK_UPLINKTRANSPAYHEX,
    EM_LINK_DOWNLINKTRANSPAYHEX,
    EM_LINK_UPLINKPAYLENSET,
    EM_LINK_DOWNLINKPAYLENSET,
    EM_LINK_ESTABLISH,
    EM_LINK_UPLINKSYNSEQNUM,
    EM_LINK_DOWNLINKSYNSEQNUM,
    EM_LINK_UPLINKSYNTCPWINS,
    EM_LINK_DOWNLINKSYNTCPWINS,
    EM_LINK_UPLINKTCPOPTS,
    EM_LINK_DOWNLINKTCPOPTS,
    EM_LINK_UPSESBYTES,
    EM_LINK_DOWNSESBYTES,
    EM_LINK_SESBYTES,
    EM_LINK_SESBYTESRATIO,
    EM_LINK_PAYLENRATIO,


    EM_LINK_MAX
};


//int write_link_log(struct flow_info *flow, struct tbl_log *log_ptr, int *idx);

int write_link_log(struct flow_info *flow, int direction, struct tbl_log *log_ptr, int *idx, SdtMatchResult *match_result);
int write_link(precord_t *record, struct flow_info *flow, int direction);
#endif
void dpi_sdt_init_link_map_fields(void);
#endif
