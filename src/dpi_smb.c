/****************************************************************************************
 * 文 件 名 : dpi_smb.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/09/25
编码: wangy            2018/09/25
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <glib.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "charsets.h"
#include "dpi_smb.h"
#include "dpi_pschema.h"


static dpi_field_table  smb_field_array[] = {
    DPI_FIELD_D(EM_SMB_PROTOCOL,                                YA_FT_STRING,           "Protocol"),
    DPI_FIELD_D(EM_SMB_COMMAND,                                 YA_FT_STRING,           "Command"),
    DPI_FIELD_D(EM_SMB_NTSTATUS,                                YA_FT_STRING,           "NTStatus"),
    DPI_FIELD_D(EM_SMB_FLAGS,                                   YA_FT_STRING,           "Flags"),
    DPI_FIELD_D(EM_SMB_FLAGS2,                                  YA_FT_STRING,           "Flags2"),
    DPI_FIELD_D(EM_SMB_PIDHIGH,                                 YA_FT_UINT32,           "PIDHigh"),
    DPI_FIELD_D(EM_SMB_SIGNATURE,                               YA_FT_STRING,           "Signature"),
    DPI_FIELD_D(EM_SMB_TID,                                     YA_FT_UINT32,           "TID"),
    DPI_FIELD_D(EM_SMB_PIDLOW,                                  YA_FT_UINT32,           "PIDLow"),
    DPI_FIELD_D(EM_SMB_UID,                                     YA_FT_UINT32,           "UID"),
    DPI_FIELD_D(EM_SMB_MID,                                     YA_FT_UINT32,           "MID"),
    DPI_FIELD_D(EM_SMB_WORDCOUNT,                               YA_FT_UINT32,           "WordCount"),
    DPI_FIELD_D(EM_SMB_BYTECOUNT,                               YA_FT_UINT32,           "ByteCount"),
    DPI_FIELD_D(EM_SMB_DIALECTNUMS,                             YA_FT_UINT32,           "DialectNums"),
    DPI_FIELD_D(EM_SMB_DIALECTS,                                YA_FT_STRING,           "Dialects"),
    DPI_FIELD_D(EM_SMB_SERVERDIALECTINDEX,                      YA_FT_UINT32,           "ServerDialectIndex"),
    DPI_FIELD_D(EM_SMB_SERVERSECURITYMODE,                      YA_FT_STRING,           "ServerSecurityMode"),
    DPI_FIELD_D(EM_SMB_SERVERMAXMPXCOUNT,                       YA_FT_UINT32,           "ServerMaxMpxCount"),
    DPI_FIELD_D(EM_SMB_SERVERMAXNUMBERVCS,                      YA_FT_UINT32,           "ServerMaxNumberVcs"),
    DPI_FIELD_D(EM_SMB_SERVERMAXBUFFERSIZE,                     YA_FT_UINT32,           "ServerMaxBufferSize"),
    DPI_FIELD_D(EM_SMB_SERVERMAXRAWSIZE,                        YA_FT_UINT32,           "ServerMaxRawSize"),
    DPI_FIELD_D(EM_SMB_SERVERSESSIONKEY,                        YA_FT_STRING,           "ServerSessionKey"),
    DPI_FIELD_D(EM_SMB_SERVERCAPABILITIES,                      YA_FT_STRING,           "ServerCapabilities"),
    DPI_FIELD_D(EM_SMB_SERVERSYSTEMTIME,                        YA_FT_STRING,           "ServerSystemTime"),
    DPI_FIELD_D(EM_SMB_SERVERTIMEZONE,                          YA_FT_STRING,           "ServerTimeZone"),
    DPI_FIELD_D(EM_SMB_SERVERKEYLENGTH,                         YA_FT_UINT32,           "ServerKeyLength"),
    DPI_FIELD_D(EM_SMB_SERVERGUID,                              YA_FT_STRING,           "ServerGUID"),
    DPI_FIELD_D(EM_SMB_SERVERNEGPROTSECURITYBLOB,               YA_FT_STRING,           "ServerNegprotSecurityBlob"),
    DPI_FIELD_D(EM_SMB_SERVERENCRYPTIONKEY,                     EM_F_TYPE_NULL,         "ServerEncryptionKey"),
    DPI_FIELD_D(EM_SMB_SERVERDOMAINNAME,                        EM_F_TYPE_STRING,       "ServerDomainName"),
    DPI_FIELD_D(EM_SMB_SERVERNAME,                              YA_FT_STRING,           "ServerName"),
    DPI_FIELD_D(EM_SMB_NTLMSSPIDENTIFIER,                       EM_F_TYPE_NULL,         "NTLMSSPIdentifier"),
    DPI_FIELD_D(EM_SMB_NTLMMESSAGETYPE,                         EM_F_TYPE_NULL,         "NTLMMessageType"),
    DPI_FIELD_D(EM_SMB_CLIENTSESSSETUPXANDXCOMMAND,             YA_FT_STRING,           "ClientSesssetupXAndXCommand"),
    DPI_FIELD_D(EM_SMB_CLIENTSESSSETUPXANDXOFFSET,              YA_FT_UINT32,           "ClientSesssetupXAndXOffset"),
    DPI_FIELD_D(EM_SMB_CLIENTMAXBUFFERSIZE,                     YA_FT_UINT32,           "ClientMaxBufferSize"),
    DPI_FIELD_D(EM_SMB_CLIENTMAXMPXCOUNT,                       YA_FT_UINT32,           "ClientMaxMpxCount"),
    DPI_FIELD_D(EM_SMB_CLIENTVCNUMBER,                          YA_FT_UINT32,           "ClientVcNumber"),
    DPI_FIELD_D(EM_SMB_CLIENTSESSIONKEY,                        YA_FT_STRING,            "ClientSessionKey"),
    DPI_FIELD_D(EM_SMB_CLIENTANSIPASSWORDLEN,                   YA_FT_UINT32,           "ClientANSIPasswordLen"),
    DPI_FIELD_D(EM_SMB_CLIENTUNICODEPASSWORDLEN,                YA_FT_UINT32,           "ClientUnicodePasswordLen"),
    DPI_FIELD_D(EM_SMB_CLIENTSECURITYBLOBLENGTH,                YA_FT_UINT32,           "ClientSecurityBlobLength"),
    DPI_FIELD_D(EM_SMB_CLIENTCAPABILITIES,                      YA_FT_STRING,           "ClientCapabilities"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_NEGOTIATEFLAGS,                 YA_FT_STRING,           "ClientSB_NegotiateFlags"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_CALLINGWORKSTATIONDOMAIN,       YA_FT_STRING,           "ClientSB_Callingworkstationdomain"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_CALLINGWORKSTATIONNAME,         YA_FT_STRING,           "ClientSB_Callingworkstationname"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_VERSION,                        YA_FT_STRING,           "ClientSB_Version"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_LANMANAGERRESPONSE,             YA_FT_STRING,           "ClientSB_LanManagerResponse"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_NTLMRESPONSE,                   YA_FT_STRING,           "ClientSB_NTLMResponse"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_DOMAINNAME,                     YA_FT_STRING,           "ClientSB_Domainname"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_USERNAME,                       EM_F_TYPE_STRING,       "user"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_HOSTNAME,                       YA_FT_STRING,           "ClientSB_Hostname"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_SESSIONKEY,                     YA_FT_STRING,           "ClientSB_SessionKey"),
    DPI_FIELD_D(EM_SMB_CLIENTANSIPASSWORD,                      YA_FT_STRING,           "ClientANSIPassword"),
    DPI_FIELD_D(EM_SMB_CLIENTUNICODEPASSWORD,                   YA_FT_STRING,           "ClientUnicodePassword"),
    DPI_FIELD_D(EM_SMB_CLIENTACCOUNTNAME,                       YA_FT_STRING,           "ClientAccountName"),
    DPI_FIELD_D(EM_SMB_CLIENTPRIMARYDOMAIN,                     YA_FT_STRING,           "ClientPrimaryDomain"),
    DPI_FIELD_D(EM_SMB_CLIENTNATIVEOS,                          YA_FT_STRING,           "ClientNativeOS"),
    DPI_FIELD_D(EM_SMB_CLIENTNATIVELANMAN,                      YA_FT_STRING,           "ClientNativeLanMan"),
    DPI_FIELD_D(EM_SMB_SERVERSESSSETUPXANDXCOMMAND,             YA_FT_STRING,           "ServerSesssetupXAndXCommand"),
    DPI_FIELD_D(EM_SMB_SERVERSESSSETUPXANDXOFFSET,              YA_FT_UINT32,           "ServerSesssetupXAndXOffset"),
    DPI_FIELD_D(EM_SMB_SERVERACTION,                            YA_FT_STRING,           "ServerAction"),
    DPI_FIELD_D(EM_SMB_SERVERSECURITYBLOBLENGTH,                YA_FT_UINT32,           "ServerSecurityBlobLength"),
    DPI_FIELD_D(EM_SMB_SERVERSB_TARGETNAME,                     YA_FT_STRING,           "ServerSB_TargetName"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NEGOTIATEFLAGS,                 YA_FT_STRING,           "ServerSB_NegotiateFlags"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NTLMSERVERCHALLENGE,            YA_FT_STRING,           "ServerSB_NTLMServerChallenge"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NETBIOSDOMAINNAME,              YA_FT_STRING,           "ServerSB_NetBIOSdomainname"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NETBIOSCOMPUTERNAME,            YA_FT_STRING,           "ServerSB_NetBIOScomputername"),
    DPI_FIELD_D(EM_SMB_SERVERSB_DNSDOMAINNAME,                  YA_FT_STRING,           "ServerSB_DNSdomainname"),
    DPI_FIELD_D(EM_SMB_SERVERSB_DNSCOMPUTERNAME,                YA_FT_STRING,           "ServerSB_DNScomputername"),
    DPI_FIELD_D(EM_SMB_SERVERSB_TIMESTAMP,                      YA_FT_STRING,           "ServerSB_Timestamp"),
    DPI_FIELD_D(EM_SMB_SERVERSB_VERSION,                        EM_F_TYPE_STRING,       "ServerSB_Version"),
    DPI_FIELD_D(EM_SMB_SERVERNATIVEOS,                          EM_F_TYPE_STRING,       "ServerNativeOS"),
    DPI_FIELD_D(EM_SMB_SERVERNATIVELANMAN,                      YA_FT_STRING,           "ServerNativeLanMan"),
    DPI_FIELD_D(EM_SMB_SERVERPRIMARYDOMAIN,                     YA_FT_STRING,           "ServerPrimaryDomain"),
    DPI_FIELD_D(EM_SMB_CLIENTTCONXANDXCOMMAND,                  YA_FT_STRING,           "ClientTConXAndXCommand"),
    DPI_FIELD_D(EM_SMB_CLIENTTCONXANDXOFFSET,                   YA_FT_UINT32,           "ClientTConXAndXOffset"),
    DPI_FIELD_D(EM_SMB_CLIENTFLAGS,                             YA_FT_STRING,           "ClientFlags"),
    DPI_FIELD_D(EM_SMB_CLIENTPASSWORDLENGTH,                    YA_FT_UINT32,           "ClientPasswordLength"),
    DPI_FIELD_D(EM_SMB_CLIENTPASSWORD,                          YA_FT_STRING,           "ClientPassword"),
    DPI_FIELD_D(EM_SMB_CLIENTPATH,                              YA_FT_STRING,           "path"),
    DPI_FIELD_D(EM_SMB_CLIENTSERVICE,                           YA_FT_STRING,           "ClientService"),
    DPI_FIELD_D(EM_SMB_SERVERTCONXANDXCOMMAND,                  YA_FT_STRING,           "ServerTConXAndXCommand"),
    DPI_FIELD_D(EM_SMB_SERVERTCONXANDXOFFSET,                   YA_FT_UINT32,           "ServerTConXAndXOffset"),
    DPI_FIELD_D(EM_SMB_SERVEROPTIONALSUPPORT,                   YA_FT_STRING,           "ServerOptionalSupport"),
    DPI_FIELD_D(EM_SMB_MAXIMALSHAREACCESSRIGHTS,                YA_FT_STRING,           "MaximalShareAccessRights"),
    DPI_FIELD_D(EM_SMB_GUESTMAXIMALSHAREACCESSRIGHTS,           YA_FT_STRING,           "GuestMaximalShareAccessRights"),
    DPI_FIELD_D(EM_SMB_SERVERSERVICE,                           YA_FT_STRING,           "ServerService"),
    DPI_FIELD_D(EM_SMB_SERVERNATIVEFILESYSTEM,                  YA_FT_STRING,           "ServerNativeFileSystem"),
    DPI_FIELD_D(EM_SMB_SERVERSHOREDIRNAME,                      YA_FT_STRING,           "dir"),
    DPI_FIELD_D(EM_SMB_LOADWAY,                                 YA_FT_UINT32,           "LoadWay"),
    DPI_FIELD_D(EM_SMB_FILENAME,                                YA_FT_STRING,           "fileName"),
    DPI_FIELD_D(EM_SMB_FILENAMECNT,                             YA_FT_UINT32,           "fileNameCnt"),
    DPI_FIELD_D(EM_SMB_PIPENAME,                                YA_FT_STRING,           "PipeName"),
    DPI_FIELD_D(EM_SMB_FILEID,                                  YA_FT_UINT32,           "FileId"),
    DPI_FIELD_D(EM_SMB_MAILSLOTNAME,                            YA_FT_STRING,           "MailslotName"),
    DPI_FIELD_D(EM_SMB_FILESIZE,                                YA_FT_UINT32,           "fileSize"),
    DPI_FIELD_D(EM_SMB_FILEATTR,                                YA_FT_UINT32,           "fileAtt"),
    DPI_FIELD_D(EM_SMB_ENDOFFILE,                               YA_FT_UINT32,           "EndOfFile"),
    DPI_FIELD_D(EM_SMB_SEARCHATTR,                              YA_FT_UINT32,           "SearchAttr"),
    DPI_FIELD_D(EM_SMB_REFERRALVERSION,                         YA_FT_UINT32,           "ReferralVersion"),
    DPI_FIELD_D(EM_SMB_INFOLEVEL,                               YA_FT_UINT32,           "InfoLevel"),
    DPI_FIELD_D(EM_SMB_DIALECTNAME,                             YA_FT_STRING,           "DialectName"),
    DPI_FIELD_D(EM_SMB_SMBEXTATTR,                              EM_F_TYPE_NULL,         "SmbExtAttr"),
    DPI_FIELD_D(EM_SMB_AUTH_TYPE,                               YA_FT_STRING,           "authType"),
    DPI_FIELD_D(EM_SMB_AUTH_PATHPWD,                            EM_F_TYPE_STRING,       "pathPwd"),
    DPI_FIELD_D(EM_SMB_ACCRGHTS,                                YA_FT_BYTES,            "accRghts"),
    DPI_FIELD_D(EM_SMB_INFOFILENM,                              EM_F_TYPE_STRING,       "infoFileNm"), //真不知道 标书中此字段云为何物
    DPI_FIELD_D(EM_SMB_STREAMINFO,                              EM_F_TYPE_STRING,       "streamInfo"), //真不知道 标书中此字段云为何物
};

uint32_t smb_reported_length_remaining(struct dpi_pkt_st* pkt, const int offset);
int validate_offset(const struct dpi_pkt_st *pkt, const guint abs_offset);
int check_offset_length_no_exception(const struct dpi_pkt_st *pkt,const int offset, int const length_val,uint32_t *offset_ptr, uint *length_ptr);
int dissect_nt_trans_data_request(struct dpi_pkt_st *pkt, struct smb_info *pinfo, int offset, int len, nt_trans_data *ntd, uint16_t bc, struct smb_session *si);
int dissect_nt_trans_param_request(struct dpi_pkt_st *pkt, struct smb_info *pinfo, int offset,     int len, nt_trans_data *ntd, uint16_t bc, struct smb_session *si);
int smb_bytes_exist(const struct dpi_pkt_st *pkt, const int offset, const int length);
int dissect_file_data(struct dpi_pkt_st *pkt,int offset, uint16_t bc, uint16_t datalen);
int smb_compute_offset(const struct dpi_pkt_st* pkt, const int offset, uint32_t *offset_ptr);
int dissect_nt_sid(struct dpi_pkt_st *pkt, int offset);
int dissect_nt_sec_desc(struct dpi_pkt_st *pkt, int offset, uint8_t *drep);
int dissect_nt_transaction_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo);


static void
smb_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct smb_info *info, int *idx, int i)
{
    //int local_idx=*idx;
    switch(i){
    case EM_SMB_PROTOCOL:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "smb", 3);
        break;
    case EM_SMB_COMMAND:
        if (info->Command)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->Command, strlen(info->Command));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_NTSTATUS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->NTStatus, strlen(info->NTStatus));
        break;
    case EM_SMB_FLAGS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->Flags, strlen(info->Flags));
        break;
    case EM_SMB_FLAGS2:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->Flags2, strlen(info->Flags2));
        break;
    case EM_SMB_PIDHIGH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PIDHigh);    
        break;
    case EM_SMB_SIGNATURE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->Signature, strlen(info->Signature));
        break;
    case EM_SMB_TID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->TID);    
        break;
    case EM_SMB_PIDLOW:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PIDLow);    
        break;
    case EM_SMB_UID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->UID);    
        break;
    case EM_SMB_MID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->MID);    
        break;
    case EM_SMB_WORDCOUNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->WordCount);    
        break;
    case EM_SMB_BYTECOUNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ByteCount);    
        break;
    case EM_SMB_DIALECTNUMS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->DialectNums);    
        break;
    case EM_SMB_DIALECTS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->Dialects, strlen(info->Dialects));
        break;
    case EM_SMB_SERVERDIALECTINDEX:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerDialectIndex);    
        break;
    case EM_SMB_SERVERSECURITYMODE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSecurityMode, strlen(info->ServerSecurityMode));
        break;
    case EM_SMB_SERVERMAXMPXCOUNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerMaxMpxCount);    
        break;
    case EM_SMB_SERVERMAXNUMBERVCS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerMaxNumberVcs);    
        break;
    case EM_SMB_SERVERMAXBUFFERSIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerMaxBufferSize);    
        break;
    case EM_SMB_SERVERMAXRAWSIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerMaxRawSize);    
        break;
    case EM_SMB_SERVERSESSIONKEY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSessionKey, strlen(info->ServerSessionKey));
        break;
    case EM_SMB_SERVERCAPABILITIES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerCapabilities, strlen(info->ServerCapabilities));
        break;
    case EM_SMB_SERVERSYSTEMTIME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSystemTime, strlen(info->ServerSystemTime));
        break;
    case EM_SMB_SERVERTIMEZONE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerTimeZone, strlen(info->ServerTimeZone));
        break;
    case EM_SMB_SERVERKEYLENGTH:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_SERVERGUID:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerGUID, strlen(info->ServerGUID));
        break;
    case EM_SMB_SERVERNEGPROTSECURITYBLOB:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerNegprotSecurityBlob, strlen(info->ServerNegprotSecurityBlob));
        break;
    case EM_SMB_SERVERENCRYPTIONKEY:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_SERVERDOMAINNAME:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        
        break;
    case EM_SMB_SERVERNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerName, strlen(info->ServerName));
        
        break;
    case EM_SMB_NTLMSSPIDENTIFIER:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        
        break;
    case EM_SMB_NTLMMESSAGETYPE:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        
        break;
    case EM_SMB_CLIENTSESSSETUPXANDXCOMMAND:
        if (info->ClientSesssetupXAndXCommand)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSesssetupXAndXCommand, strlen(info->ClientSesssetupXAndXCommand));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_CLIENTSESSSETUPXANDXOFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSesssetupXAndXOffset);    
        break;
    case EM_SMB_CLIENTMAXBUFFERSIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientMaxBufferSize);    
        break;
    case EM_SMB_CLIENTMAXMPXCOUNT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientMaxMpxCount);    
        break;
    case EM_SMB_CLIENTVCNUMBER:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientVcNumber);    
        break;
    case EM_SMB_CLIENTSESSIONKEY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSessionKey, strlen(info->ClientSessionKey));
        break;
    case EM_SMB_CLIENTANSIPASSWORDLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientANSIPasswordLen);    
        break;
    case EM_SMB_CLIENTUNICODEPASSWORDLEN:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientUnicodePasswordLen);    
        break;
    case EM_SMB_CLIENTSECURITYBLOBLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSecurityBlobLength);
        break;
    case EM_SMB_CLIENTCAPABILITIES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientCapabilities, strlen(info->ClientCapabilities));
        break;
    case EM_SMB_CLIENTSB_NEGOTIATEFLAGS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_NegotiateFlags, strlen(info->ClientSB_NegotiateFlags));
        break;
    case EM_SMB_CLIENTSB_CALLINGWORKSTATIONDOMAIN:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_Callingworkstationdomain, strlen(info->ClientSB_Callingworkstationdomain));
        break;
    case EM_SMB_CLIENTSB_CALLINGWORKSTATIONNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_Callingworkstationname, strlen(info->ClientSB_Callingworkstationname));
        break;
    case EM_SMB_CLIENTSB_VERSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_Version, strlen(info->ClientSB_Version));
        break;
    case EM_SMB_CLIENTSB_LANMANAGERRESPONSE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_LanManagerResponse, strlen(info->ClientSB_LanManagerResponse));
        break;
    case EM_SMB_CLIENTSB_NTLMRESPONSE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_NTLMResponse, strlen(info->ClientSB_NTLMResponse));
        break;
    case EM_SMB_CLIENTSB_DOMAINNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_Domainname, strlen(info->ClientSB_Domainname));
        break;
    case EM_SMB_CLIENTSB_USERNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_Username, strlen(info->ClientSB_Username));
        break;
    case EM_SMB_CLIENTSB_HOSTNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_Hostname, strlen(info->ClientSB_Hostname));
        break;
    case EM_SMB_CLIENTSB_SESSIONKEY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientSB_SessionKey, strlen(info->ClientSB_SessionKey));
        break;        
    case EM_SMB_CLIENTANSIPASSWORD:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientANSIPassword, strlen(info->ClientANSIPassword));
        break;
    case EM_SMB_CLIENTUNICODEPASSWORD:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientUnicodePassword, strlen(info->ClientUnicodePassword));
        break;
    case EM_SMB_CLIENTACCOUNTNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientAccountName, strlen(info->ClientAccountName));
        break;
    case EM_SMB_CLIENTPRIMARYDOMAIN:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientPrimaryDomain, strlen(info->ClientPrimaryDomain));
        break;
    case EM_SMB_CLIENTNATIVEOS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientNativeOS, strlen(info->ClientNativeOS));
        break;
    case EM_SMB_CLIENTNATIVELANMAN:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientNativeLanMan, strlen(info->ClientNativeLanMan));
        break;
    case EM_SMB_SERVERSESSSETUPXANDXCOMMAND:
        if (info->ServerSesssetupXAndXCommand)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSesssetupXAndXCommand, strlen(info->ServerSesssetupXAndXCommand));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_SERVERSESSSETUPXANDXOFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSesssetupXAndXOffset);    
        break;
    case EM_SMB_SERVERACTION:
        if (info->ServerAction)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerAction, strlen(info->ServerAction));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_SERVERSECURITYBLOBLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSecurityBlobLength);    
        break;
    case EM_SMB_SERVERSB_TARGETNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_TargetName, strlen(info->ServerSB_TargetName));
        break;
    case EM_SMB_SERVERSB_NEGOTIATEFLAGS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_NegotiateFlags, strlen(info->ServerSB_NegotiateFlags));
        break;
    case EM_SMB_SERVERSB_NTLMSERVERCHALLENGE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_NTLMServerChallenge, strlen(info->ServerSB_NTLMServerChallenge));
        break;
    case EM_SMB_SERVERSB_NETBIOSDOMAINNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_NetBIOSdomainname, strlen(info->ServerSB_NetBIOSdomainname));
        break;
    case EM_SMB_SERVERSB_NETBIOSCOMPUTERNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_NetBIOScomputername, strlen(info->ServerSB_NetBIOScomputername));
        break;
    case EM_SMB_SERVERSB_DNSDOMAINNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_DNSdomainname, strlen(info->ServerSB_DNSdomainname));
        break;
    case EM_SMB_SERVERSB_DNSCOMPUTERNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_DNScomputername, strlen(info->ServerSB_DNScomputername));
        break;
    case EM_SMB_SERVERSB_TIMESTAMP:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_Timestamp, strlen(info->ServerSB_Timestamp));
        break;
    case EM_SMB_SERVERSB_VERSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerSB_Version, strlen(info->ServerSB_Version));
        break;
    case EM_SMB_SERVERNATIVEOS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerNativeOS, strlen(info->ServerNativeOS));
        break;
    case EM_SMB_SERVERNATIVELANMAN:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerNativeLanMan, strlen(info->ServerNativeLanMan));
        break;
    case EM_SMB_SERVERPRIMARYDOMAIN:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerPrimaryDomain, strlen(info->ServerPrimaryDomain));
        break;
    case EM_SMB_CLIENTTCONXANDXCOMMAND:
        if (info->ClientTConXAndXCommand)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientTConXAndXCommand, strlen(info->ClientTConXAndXCommand));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_CLIENTTCONXANDXOFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientTConXAndXOffset);    
        break;
    case EM_SMB_CLIENTFLAGS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientFlags, strlen(info->ClientFlags));
        break;
    case EM_SMB_CLIENTPASSWORDLENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientPasswordLength);    
        break;
    case EM_SMB_CLIENTPASSWORD:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientPassword, strlen(info->ClientPassword));
        break;
    case EM_SMB_CLIENTPATH:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientPath, strlen(info->ClientPath));
        break;
    case EM_SMB_CLIENTSERVICE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientService, strlen(info->ClientService));
        break;
    case EM_SMB_SERVERTCONXANDXCOMMAND:
        if (info->ServerTConXAndXCommand)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerTConXAndXCommand, strlen(info->ServerTConXAndXCommand));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_SERVERTCONXANDXOFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerTConXAndXOffset);    
        break;
    case EM_SMB_SERVEROPTIONALSUPPORT:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerOptionalSupport, strlen(info->ServerOptionalSupport));
        break;
    case EM_SMB_MAXIMALSHAREACCESSRIGHTS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->MaximalShareAccessRights, strlen(info->MaximalShareAccessRights));
        break;
    case EM_SMB_GUESTMAXIMALSHAREACCESSRIGHTS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->GuestMaximalShareAccessRights, strlen(info->GuestMaximalShareAccessRights));
        break;
    case EM_SMB_SERVERSERVICE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerService, strlen(info->ServerService));
        break;
    case EM_SMB_SERVERNATIVEFILESYSTEM:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerNativeFileSystem, strlen(info->ServerNativeFileSystem));
        break;
    case EM_SMB_SERVERSHOREDIRNAME:
        if(0 < strlen(info->request_dir))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->request_dir, strlen(info->request_dir));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_LOADWAY:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->LoadWay);
        break;
    case EM_SMB_FILENAMECNT:
        //printf("EM_SMB_FILENAMECNT %u\n", info->FileNameCnt);
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->FileNameCnt);
        break;
    case EM_SMB_FILENAME:
        if(0 < strlen(info->FileName))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->FileName, strlen(info->FileName));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_PIPENAME:
        if(0 < strlen(info->PipeName))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->PipeName, strlen(info->PipeName));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_FILEID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->FileId);
        break;
    case EM_SMB_MAILSLOTNAME:
        if(0 < strlen(info->MailslotName))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->MailslotName, strlen(info->MailslotName));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_FILESIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->FileSize);
        break;
    case EM_SMB_FILEATTR:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->FileAttr);
        break;
    case EM_SMB_ENDOFFILE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->end_of_file);    
        break;
    case EM_SMB_SEARCHATTR:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->searchAttr);
        break;
    case EM_SMB_REFERRALVERSION:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ReferralVersion);
        break;
    case EM_SMB_INFOLEVEL:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->InfoLevel);
        break;
    case EM_SMB_DIALECTNAME:
        if(0 < strlen(info->DialectName))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->DialectName, strlen(info->DialectName));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_SMBEXTATTR:
        if(0 < strlen(info->SmbExtAttr))
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->SmbExtAttr, strlen(info->SmbExtAttr));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_SMB_AUTH_TYPE:
        if(info->authtype)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char*)info->authtype, strlen((const char*)info->authtype));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;

    case EM_SMB_ACCRGHTS:
        write_one_hexnum_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->AccessRights);
        break;

    default:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = smb_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif
    
    return;
}


static int write_smb_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    struct smb_info *info=(struct smb_info *)field_info;
    if(!info){
        return 0;
    }

    int idx = 0,i=0;
    struct tbl_log *log_ptr;
    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);

    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "smb");

    for(i=0; i<EM_SMB_MAX;i++){
        smb_field_element(log_ptr,flow, direction, info, &idx, i);
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_SMB;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static void identify_smb(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (payload_len > 8) 
    {
        if (get_uint8_t(payload, 4) == 0xff
                && get_uint8_t(payload, 5) == 'S'
                && get_uint8_t(payload, 6) == 'M'
                && get_uint8_t(payload, 7) == 'B')
            flow->real_protocol_id = PROTOCOL_SMB;
    }
    return;
}

static void identify_udp_smb(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if(payload_len > 86)
    {
        int offset = SMB_UDP_NETBIOS_LEN;
        short smb0 = get_uint8_t(payload, offset);
        char smb1 = get_uint8_t(payload, offset+1);
        char smb2 = get_uint8_t(payload, offset+2);
        char smb3 = get_uint8_t(payload, offset+3);
        if ( 0xff == smb0 && 'S' == smb1 && 'M' == smb2 && 'B' == smb3)
            flow->real_protocol_id = PROTOCOL_SMB;
    }
    return;
}

static int dissect_ntlmssp_string (struct dpi_pkt_st *pkt, uint32_t offset,
        uint8_t unicode_strings, int *start, int *end, char *result, int max_len)
{
    uint16_t      string_length;
    uint16_t      string_maxlen;
    uint32_t      string_offset;
    int             fn;
    int         result_length;
    uint16_t     bc;
    if (-1 == dpi_get_le16(pkt, offset, &string_length))
        return offset;
    if (-1 == dpi_get_le16(pkt, offset + 2, &string_maxlen))
        return offset;
    if (-1 == dpi_get_le32(pkt, offset + 4, &string_offset))
        return offset;

    *start = (int)string_offset;
    if (0 == string_length 
            || string_offset <= offset + 8 
            || string_offset + string_length > pkt->payload_len) {
        *end = *start;
        return offset + 8;
    }
    bc = result_length = string_length;
    fn = get_unicode_or_ascii_string(pkt, &string_offset,
            unicode_strings, &result_length,
            0, 1, &bc, result, max_len);
      if (fn == -1)
          return offset + 8;

    *end = string_offset + string_length;
    return offset + 8;
}

static int dissect_ntlmssp_hex(struct dpi_pkt_st *pkt, uint32_t offset, int *start, int *end, char *result, int max_len)
{
    uint16_t      string_length;
    uint16_t      string_maxlen;
    uint32_t      string_offset;
    int          fn;
//    int         result_length;
//    uint16_t     bc;
    if (-1 == dpi_get_le16(pkt, offset, &string_length))
        return offset;
    if (-1 == dpi_get_le16(pkt, offset + 2, &string_maxlen))
        return offset;
    if (-1 == dpi_get_le32(pkt, offset + 4, &string_offset))
        return offset;

    *start = string_offset;
    if (0 == string_length 
            || string_offset <= offset + 8 
            || string_offset + string_length > pkt->payload_len) {
        *end = *start;
        return offset + 8;
    }

    fn = dpi_get_hex_string(pkt, string_offset, string_length, result, max_len);

    if (fn == -1)
        return offset + 8;

    *end = string_offset + string_length;
    return offset + 8;
}

/* Dissect "version" */

/* From MS-NLMP:
    0    Major Version Number    1 byte
    1    Minor Version Number    1 byte
    2    Build Number            short(LE)
    3    (Reserved)                3 bytes
    4    NTLM Current Revision    1 byte
*/

static int dissect_ntlmssp_version(struct dpi_pkt_st *pkt, uint32_t offset, char *result, int max_len)
{
    uint8_t var8_1;
    uint8_t var8_2;
    uint8_t var8_3;
    uint16_t var16;

    if (-1 == dpi_get_uint8(pkt, offset, &var8_1))
        return offset;
    offset ++;
    if (-1 == dpi_get_uint8(pkt, offset, &var8_2))
        return offset;
    offset++;
    if (-1 == dpi_get_le16(pkt, offset, &var16))
        return offset;
    offset += 2;
    if (-1 == dpi_get_uint8(pkt, offset, &var8_3))
        return offset;

    offset += 4;
    snprintf(result, max_len, "Version %u.%u (Build %u); NTLM Current Revision %u", var8_1, var8_2, var16, var8_3);
    return offset + 8;
}

/* tapping into ntlmssph not yet implemented */
static int dissect_ntlmssp_negotiate(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_info *info)
{
    UNUSED(flow);
    uint32_t negotiate_flags;
    int     data_start;
    int     data_end;
    int     item_start;
    int     item_end;

    /* NTLMSSP Negotiate Flags */
    if (-1 == dpi_get_le32(pkt, offset, &negotiate_flags))
        return offset;
    snprintf(info->ClientSB_NegotiateFlags, sizeof(info->ClientSB_NegotiateFlags), "0x%08x", negotiate_flags);
    offset += 4;

    /*
    * XXX - the davenport document says that these might not be
    * sent at all, presumably meaning the length of the message
    * isn't enough to contain them.
    */
    offset = dissect_ntlmssp_string(pkt, offset, 0,
            &data_start, &data_end, info->ClientSB_Callingworkstationdomain, sizeof(info->ClientSB_Callingworkstationdomain));

    offset = dissect_ntlmssp_string(pkt, offset, 0,
            &item_start, &item_end, info->ClientSB_Callingworkstationname, sizeof(info->ClientSB_Callingworkstationname));
    data_start = data_start > item_start ? data_start : item_start;
    data_end   = data_end > item_end ? data_end : item_end;

    /* If there are more bytes before the data block dissect a version field
    if NTLMSSP_NEGOTIATE_VERSION is set in the flags (see MS-NLMP) */
    if ((int)offset < data_start) {
        if (negotiate_flags & NTLMSSP_NEGOTIATE_VERSION)
            dissect_ntlmssp_version(pkt, offset, info->ClientSB_Version, sizeof(info->ClientSB_Version));
    }
    return data_end;
}

static int dissect_ntlmssp_target_info_list(struct dpi_pkt_st *pkt, uint32_t target_info_offset, uint16_t target_info_length,
        struct smb_info *info)
{
    uint32_t item_offset;
    uint16_t item_type = ~0;
    uint16_t item_length;

    /* Now enumerate through the individual items in the list */
    item_offset = target_info_offset;

    while (item_offset < (target_info_offset + target_info_length) && (item_type != NTLM_TARGET_INFO_END)) {
        uint32_t       content_offset;
        uint16_t       content_length;
        uint32_t       type_offset;
        uint32_t       len_offset;
//        const uint8_t *text = NULL;

        /* Content type */
        type_offset = item_offset;
        if (-1 == dpi_get_le16(pkt, type_offset, &item_type))
            break;

        /* Content length */
        len_offset = type_offset + 2;
        if (-1 == dpi_get_le16(pkt, len_offset, &content_length))
            break;

        /* Content value */
        content_offset = len_offset + 2;
        item_length = content_length + 4;

        if (content_offset + content_length > pkt->payload_len)
            break;
        if (content_length > 0) {
            switch (item_type) {
                case NTLM_TARGET_INFO_NB_COMPUTER_NAME:
                    get_utf_16_string(info->ServerSB_NetBIOScomputername, sizeof(info->ServerSB_NetBIOScomputername), pkt->payload + content_offset, content_length, ENC_LITTLE_ENDIAN);
                    break;
                case NTLM_TARGET_INFO_NB_DOMAIN_NAME:
                    get_utf_16_string(info->ServerSB_NetBIOSdomainname, sizeof(info->ServerSB_NetBIOSdomainname), pkt->payload + content_offset, content_length, ENC_LITTLE_ENDIAN);
                    break;
                case NTLM_TARGET_INFO_DNS_COMPUTER_NAME:
                    get_utf_16_string(info->ServerSB_DNScomputername, sizeof(info->ServerSB_DNScomputername), pkt->payload + content_offset, content_length, ENC_LITTLE_ENDIAN);
                    break;
                case NTLM_TARGET_INFO_DNS_DOMAIN_NAME:
                    get_utf_16_string(info->ServerSB_DNSdomainname, sizeof(info->ServerSB_DNSdomainname), pkt->payload + content_offset, content_length, ENC_LITTLE_ENDIAN);
                    break;

                case NTLM_TARGET_INFO_DNS_TREE_NAME:
                case NTLM_TARGET_INFO_TARGET_NAME:
                    break;

                case NTLM_TARGET_INFO_FLAGS:
                    //proto_tree_add_item(target_info_tree, *hf_array_p[item_type], tvb, content_offset, content_length, ENC_LITTLE_ENDIAN);
                    break;

                case NTLM_TARGET_INFO_TIMESTAMP:        
                    dissect_nt_64bit_time_ex(pkt, content_offset, info->ServerSB_Timestamp, sizeof(info->ServerSB_Timestamp));
                    break;

                case NTLM_TARGET_INFO_RESTRICTIONS:
                case NTLM_TARGET_INFO_CHANNEL_BINDINGS:
                    //proto_tree_add_item(target_info_tree, *hf_array_p[item_type], tvb, content_offset, content_length, ENC_NA);
                    break;

                default:
                    //proto_tree_add_expert(target_info_tree, pinfo, &ei_ntlmssp_target_info_attr,
                    //           tvb, content_offset, content_length);
                    break;
            }
        }

        item_offset += item_length;
    }

    return item_offset;
}

static int dissect_ntlmssp_challenge_target_info_blob (struct dpi_pkt_st *pkt, int offset, struct smb_info *info, int *end)
{
    uint16_t challenge_target_info_length;
    uint16_t challenge_target_info_maxlen;
    uint32_t challenge_target_info_offset;

    if (-1 == dpi_get_le16(pkt, offset, &challenge_target_info_length))
        return offset;
    if (-1 == dpi_get_le16(pkt, offset + 2, &challenge_target_info_maxlen))
        return offset;
    if (-1 == dpi_get_le32(pkt, offset + 4, &challenge_target_info_offset))
        return offset;

    /* the target info list is just a blob */
    if (0 == challenge_target_info_length) {
        *end = (challenge_target_info_offset > ((uint32_t)offset) + 8 ? challenge_target_info_offset : ((uint32_t)offset) + 8);
        return offset + 8;
    }

    offset += 8;

    dissect_ntlmssp_target_info_list(pkt, challenge_target_info_offset, challenge_target_info_length, info);

    *end = challenge_target_info_offset + challenge_target_info_length;
    return offset;
}

/* tapping into ntlmssph not yet implemented */
static int dissect_ntlmssp_challenge(struct flow_info *flow, struct dpi_pkt_st *pkt, int offset, struct smb_info *info)
{
    uint32_t         negotiate_flags;
    int             item_start, item_end;
    int             data_start, data_end;       /* MIN and MAX seen */
    uint8_t        unicode_strings   = 0;
    struct smb_session * si;
    
    si = (struct smb_session *)flow->app_session;
    if(si == NULL)
        return offset;

    /* need to find unicode flag */
    if (-1 == dpi_get_le32(pkt, offset + 8, &negotiate_flags))
        return offset;
    si->ntlmssp_flags = negotiate_flags;
    if (negotiate_flags & NTLMSSP_NEGOTIATE_UNICODE)
        unicode_strings = 1;

    /* Target name */
    /*
    * XXX - the davenport document (and MS-NLMP) calls this "Target Name",
    * presumably because non-domain targets are supported.
    * XXX - Original name "domain" changed to "target_name" to match MS-NLMP
    */
    offset = dissect_ntlmssp_string(pkt, offset, unicode_strings,                                  
            &item_start, &item_end, info->ServerSB_TargetName, sizeof(info->ServerSB_TargetName));
    data_start = item_start;
    data_end = item_end;

    /* NTLMSSP Negotiate Flags */
    snprintf(info->ServerSB_NegotiateFlags, sizeof(info->ServerSB_NegotiateFlags), "0x%08x", negotiate_flags);
    offset += 4;

    /* NTLMSSP NT Lan Manager Challenge */
    if (-1 == dpi_get_hex_string(pkt, offset, 8, info->ServerSB_NTLMServerChallenge, sizeof(info->ServerSB_NTLMServerChallenge)))
        return offset;
    offset += 8;

    /* If no more bytes (ie: no "reserved", ...) before start of data block, then return */
    /* XXX: According to Davenport "This form is seen in older Win9x-based systems"      */
    /*      Also: I've seen a capture with an HTTP CONNECT proxy-authentication          */
    /*            message wherein the challenge from the proxy has this form.            */
    if (offset >= data_start) {
        return data_end;
    }

    /* Reserved (function not completely known) */
    /*
    * XXX - SSP key?  The davenport document says
    *
    *    The context field is typically populated when Negotiate Local
    *    Call is set. It contains an SSPI context handle, which allows
    *    the client to "short-circuit" authentication and effectively
    *    circumvent responding to the challenge. Physically, the context
    *    is two long values. This is covered in greater detail later,
    *    in the "Local Authentication" section.
    *
    * It also says that that information may be omitted.
    */
    //proto_tree_add_item (ntlmssp_tree, hf_ntlmssp_reserved,
    //                     tvb, offset, 8, ENC_NA);
    offset += 8;

    /*
    * The presence or absence of this field is not obviously correlated
    * with any flags in the previous NEGOTIATE message or in this
    * message (other than the "Workstation Supplied" and "Domain
    * Supplied" flags in the NEGOTIATE message, at least in the capture
    * I've seen - but those also correlate with the presence of workstation
    * and domain name fields, so it doesn't seem to make sense that they
    * actually *indicate* whether the subsequent CHALLENGE has an
    * address list).
    */
    if (offset < data_start) {
        offset = dissect_ntlmssp_challenge_target_info_blob(pkt, offset, info, &item_end);
        /* XXX: This code assumes that the address list in the data block */
        /*      is always after the target name. Is this OK ?             */
        data_end = data_end > item_end ? data_end : item_end;
    }

    /* If there are more bytes before the data block dissect a version field
    if NTLMSSP_NEGOTIATE_VERSION is set in the flags (see MS-NLMP) */
    if (offset < data_start) {
        if (negotiate_flags & NTLMSSP_NEGOTIATE_VERSION)
            offset = dissect_ntlmssp_version(pkt, offset, info->ServerSB_Version, sizeof(info->ServerSB_Version));
    }

    return offset > data_end ? offset : data_end;
}

static int dissect_ntlmssp_auth(struct flow_info *flow, struct dpi_pkt_st *pkt, int offset, struct smb_info *info)
{
    int data_start, data_end;
//    uint32_t         negotiate_flags;
    uint8_t        unicode_strings      = 0;

    struct smb_session * si;
    si = (struct smb_session *)flow->app_session;

    if (si != NULL) {
        if (si->ntlmssp_flags & NTLMSSP_NEGOTIATE_UNICODE)
            unicode_strings = 1;
    }
    
    offset = dissect_ntlmssp_hex(pkt, offset, &data_start, &data_end,
            info->ClientSB_LanManagerResponse, sizeof(info->ClientSB_LanManagerResponse));
    offset = dissect_ntlmssp_hex(pkt, offset, &data_start, &data_end,
            info->ClientSB_NTLMResponse, sizeof(info->ClientSB_NTLMResponse));

    /* domain name */
    offset = dissect_ntlmssp_string(pkt, offset, unicode_strings, &data_start, &data_end, info->ClientSB_Domainname, sizeof(info->ClientSB_Domainname));
    offset = dissect_ntlmssp_string(pkt, offset, unicode_strings, &data_start, &data_end, info->ClientSB_Username, sizeof(info->ClientSB_Username));
    offset = dissect_ntlmssp_string(pkt, offset, unicode_strings, &data_start, &data_end, info->ClientSB_Hostname, sizeof(info->ClientSB_Hostname));
    offset = dissect_ntlmssp_hex(pkt, offset, &data_start, &data_end, info->ClientSB_SessionKey, sizeof(info->ClientSB_SessionKey));

    return 0;
}

static void dissect_ntlmssp(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len, struct smb_info *info)
{
    uint32_t offset = 0;
    uint32_t type;

    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;
    
    //8 bytes ntlmssp
    offset += 8;

    if (-1 == dpi_get_le32(&pkt, offset, &type))
        return;
    offset += 4;
    
    switch (type) {

        case NTLMSSP_NEGOTIATE:
            dissect_ntlmssp_negotiate(flow, &pkt, offset, info);
            break;

        case NTLMSSP_CHALLENGE:
            dissect_ntlmssp_challenge(flow, &pkt, offset, info);
            break;

        case NTLMSSP_AUTH:
            dissect_ntlmssp_auth(flow, &pkt, offset, info);
            break;

        default:
            break;
    }

    return;
}

#if 0
/* Max string length for displaying Unicode strings.  */
#define    MAX_UNICODE_STR_LEN    256

/* Turn a little-endian Unicode '\0'-terminated string into a string we
   can display.
   XXX - for now, we just handle the ISO 8859-1 characters.
   If exactlen==TRUE then us_lenp contains the exact len of the string in
   bytes. It might not be null terminated !
   bc specifies the number of bytes in the byte parameters; Windows 2000,
   at least, appears, in some cases, to put only 1 byte of 0 at the end
   of a Unicode string if the byte count
*/

static int unicode_to_str(struct dpi_pkt_st *pkt, uint32_t offset, int *us_lenp, uint8_t exactlen, uint16_t bc,
        char *result, int max_len)
{
    char     *p;
    uint16_t   uchar;
    int       len;
    int       us_len;
    uint8_t  overflow = 0;

    p = result;
    len = max_len - 4;
    us_len = 0;
    for (;;) {
        if (bc == 0)
            break;

        if (bc == 1) {
            /* XXX - explain this */
            if (!exactlen)
                us_len += 1;    /* this is a one-byte null terminator */
            break;
        }

        if (-1 == dpi_get_le16(pkt, offset, &uchar))
            break;
        if (uchar == 0) {
            us_len += 2;    /* this is a two-byte null terminator */
            break;
        }

        if (len > 0) {
            if ((uchar & 0xFF00) == 0)
                *p++ = (char) uchar;    /* ISO 8859-1 */
            else
                *p++ = '?'; /* not 8859-1 */
            len--;
        } else
            overflow = 1;

        offset += 2;
        bc -= 2;
        us_len += 2;

        if(exactlen){
            if(us_len>= *us_lenp){
                break;
            }
        }
    }
    if (overflow) {
        /* Note that we're not showing the full string.  */
        *p++ = '.';
        *p++ = '.';
        *p++ = '.';
    }

    *p = '\0';
    *us_lenp = us_len;

    return 0;
}
    
/* nopad == TRUE : Do not add any padding before this string
* exactlen == TRUE : len contains the exact len of the string in bytes.
* bc: pointer to variable with amount of data left in the byte parameters
*     region
*/
static int get_unicode_or_ascii_string(struct dpi_pkt_st *pkt, uint32_t *offsetp,
        uint8_t useunicode, int *len, uint8_t nopad, uint8_t exactlen, uint16_t *bcp,
        char *result, int max_len)
{
    int string_len = 0;
    int copylen;
    uint8_t overflow = 0;

    if (*bcp == 0) {
        /* Not enough data in buffer */
        return -1;
    }

    if (useunicode) {
        if ((!nopad) && (*offsetp % 2)) {
            (*offsetp)++;    /* Looks like a pad byte there sometimes */
            (*bcp)--;

            if (*bcp == 0) {
                /* Not enough data in buffer */
                return -1;
            }
        }

        if(exactlen){
            string_len = *len;
            if (string_len < 0) {
                /* This probably means it's a very large unsigned number; just set
                it to the largest signed number, so that we throw the appropriate
                exception. */
                string_len = 0x7fffffff;
            }
        }
        
        unicode_to_str(pkt, *offsetp, &string_len, exactlen, *bcp, result, max_len);

    } else {
        if(exactlen){
            copylen = *len;

            if (copylen < 0) {
                /* This probably means it's a very large unsigned number; just set
                it to the largest signed number, so that we throw the appropriate
                exception. */
                copylen = 0x7fffffff;
            }

            if (*offsetp + copylen > pkt->payload_len) {
                return -1;
            }

            if (copylen > MAX_UNICODE_STR_LEN) {
                copylen = MAX_UNICODE_STR_LEN;
                overflow = 1;
            }
            if (copylen > max_len - 4) {
                copylen = max_len - 4;
                overflow = 1;
            }

            memcpy(result, pkt->payload + *offsetp, copylen);
            result[copylen] = '\0';

            if (overflow) {
                result[copylen++] = '.';
                result[copylen++] = '.';
                result[copylen++] = '.';                
                result[copylen] = '\0';
            }
            string_len = *len;
        } else {
            string_len = find_str_end_len(pkt->payload + *offsetp, pkt->payload_len - *offsetp);
            if (string_len < 0)
                string_len = pkt->payload_len - *offsetp;
            copylen = string_len;
            if (copylen >= max_len)
                copylen = max_len - 1;
            memcpy(result, pkt->payload + *offsetp, copylen);
            result[copylen] = 0;
            //string = tvb_get_const_stringz(tvb, *offsetp, &string_len);
        }
    }

    *len = string_len;
    return 0;
}
#endif

static int dissect_smb_UTIME(struct dpi_pkt_st *pkt, uint32_t offset)
{
    uint32_t  timeval;
    //time_t ts;

    if (-1 == dpi_get_le32(pkt, offset, &timeval))
        return offset;

    //timeval = ts;
    if (timeval == 0xffffffff) {
        offset += 4;
        return offset;
    }
    
    offset += 4;

    return offset;
}

static int dissect_smb_datetime(struct dpi_pkt_st *pkt, int offset, uint8_t time_first, char *time_str, int len)
{
    uint16_t     dos_time, dos_date;
    struct tm   tm;
    time_t      t;

    static const int mday_noleap[12] = {
        31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31
    };
    static const int mday_leap[12] = {
        31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31
    };

#define ISLEAP(y) ((((y) % 4) == 0) && ((((y) % 100) != 0) || (((y) % 400) == 0)))

    if (time_first) {
        if (-1 == dpi_get_le16(pkt, offset, &dos_time))
            return -1;
        if (-1 == dpi_get_le16(pkt, offset + 2, &dos_date))
            return -1;
    } else {
        if (-1 == dpi_get_le16(pkt, offset, &dos_date))
            return -1;
        if (-1 == dpi_get_le16(pkt, offset + 2, &dos_time))
            return -1;
    }

    if (((dos_date == 0xffff) && (dos_time == 0xffff)) ||
        ((dos_date == 0) && (dos_time == 0))) {
        offset += 4;
        return offset;
    }

    tm.tm_sec   = (dos_time & 0x1f) * 2;
    tm.tm_min   = (dos_time>>5)  & 0x3f;
    tm.tm_hour  = (dos_time>>11) & 0x1f;
    tm.tm_mday  = dos_date & 0x1f;
    tm.tm_mon   = ((dos_date>>5) & 0x0f) - 1;
    tm.tm_year  = ((dos_date>>9) & 0x7f) + 1980 - 1900;
    tm.tm_isdst = -1;

    /*
     * Do some sanity checks before calling "mktime()";
     * "mktime()" doesn't do them, it "normalizes" out-of-range
     * values.
     */
    if ((tm.tm_sec > 59) || (tm.tm_min > 59) || (tm.tm_hour > 23)
            || (tm.tm_mon < 0) || (tm.tm_mon > 11) 
            || (ISLEAP(tm.tm_year + 1900) ? (tm.tm_mday > mday_leap[tm.tm_mon]) : (tm.tm_mday > mday_noleap[tm.tm_mon])) 
            || ((t = mktime(&tm)) == -1)) {
        offset += 4;
        return offset;
    }
            
    timet_to_datetime(t, time_str, len);

    offset += 4;
    return offset;
}

#define TIME_FIXUP_CONSTANT 11644473600

static int dissect_nt_64bit_time_ex(struct dpi_pkt_st *pkt, int offset, char *time_str, int len)
{
    char time_tmp[128];
    uint64_t secs;
    //uint32_t nsecs;
    uint32_t filetime_high, filetime_low;

    /* XXX there seems also to be another special time value which is fairly common :
       0x40000000 00000000
       the meaning of this one is yet unknown
    */

    if (dpi_get_le32(pkt, offset, &filetime_low))
        return 0;
    if (dpi_get_le32(pkt, offset + 4, &filetime_high))
        return 0;

    if (filetime_low == 0 && filetime_high == 0) {
        goto out;
    } else if(filetime_low == 0 && filetime_high == 0x80000000){
        secs = filetime_low;
        //nsecs = filetime_high;
        timet_to_datetime(secs, time_tmp, sizeof(time_tmp));
        snprintf(time_str, len, "%s (relative time)", time_tmp);
    } else if(filetime_low == 0xffffffff && filetime_high == 0x7fffffff){
        secs = filetime_low;
        //nsecs = filetime_high;
        timet_to_datetime(secs, time_tmp, sizeof(time_tmp));
        snprintf(time_str, len, "%s (absolute time)", time_tmp);
    } else {
        secs = (((uint64_t)filetime_high << 32) | filetime_low) / 10000000;
        secs -= TIME_FIXUP_CONSTANT;
        //nsecs = filetime_high;
        timet_to_datetime(secs, time_tmp, sizeof(time_tmp));
        snprintf(time_str, len, "%s", time_tmp);
    }

    
out:
    offset += 8;
    return offset;
}

static int dissect_null(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(offset);
    UNUSED(si);
    UNUSED(info);

    return pkt->payload_len;
}

static int dissect_empty(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(si);
    uint8_t wc;
    uint16_t bc;

    WORD_COUNT;

    BYTE_COUNT;

    info->WordCount = wc;
    info->ByteCount = bc;

    END_OF_SMB

    return offset;
}

static int dissect_unknown(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(si);
    uint8_t  wc;
    uint16_t bc;

    WORD_COUNT;
    info->WordCount = wc;
    if (wc != 0) {
        offset += wc*2;
    }

    BYTE_COUNT;

    if (bc != 0) {
        info->ByteCount = bc;
        offset += bc;
        bc = 0;
    }

    END_OF_SMB
    return offset;
}


static int dissect_old_dir_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);

    int dn_len;
    //const char *dn;
    uint8_t wc;
    uint16_t bc;

    WORD_COUNT;

    BYTE_COUNT;
    info->WordCount = wc;
    info->ByteCount = bc;

    /* buffer format */
    CHECK_BYTE_COUNT(1);
    //proto_tree_add_item(tree, hf_smb_buffer_format, tvb, offset, 1, ENC_LITTLE_ENDIAN);
    COUNT_BYTES(1);

    /* dir name */
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &dn_len, 0, 0, &bc, info->request_dir, sizeof(info->request_dir));

    COUNT_BYTES(dn_len);

    END_OF_SMB

    return offset;
}

static int dissect_open_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(info);
    int         fn_len;
    int          fn;
    uint8_t      wc;
    uint16_t     bc;
    char result[256];
    //smb_fid_saved_info_t *fsi; /* eo_smb needs to track this info */

    //DISSECTOR_ASSERT(si);

    WORD_COUNT;

    /* desired access */
    offset += 2;
    //offset = dissect_access(tvb, tree, offset, hf_smb_desired_access);

    /* Search Attributes */
    offset += 2;
    //offset = dissect_search_attributes(tvb, tree, offset);

    BYTE_COUNT;

    /* buffer format */
    CHECK_BYTE_COUNT(1);
    //proto_tree_add_item(tree, hf_smb_buffer_format, tvb, offset, 1, ENC_LITTLE_ENDIAN);
    COUNT_BYTES(1);

    /* file name */
    fn = get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len,
        0, 0, &bc, result, sizeof(result));
    if (fn == -1)
        goto endofcommand;

    COUNT_BYTES(fn_len);

    END_OF_SMB

    return offset;
}

static int dissect_open_file_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(si);
    UNUSED(info);
    uint8_t  wc;
    uint16_t bc;
    uint16_t fid;
//    smb_fid_info_t *fid_info   = NULL; /* eo_smb needs to track this info */
//    uint16_t fattr;
//    uint8_t isdir = 0;

    WORD_COUNT;

    /* fid */
    if (-1 == dpi_get_le16(pkt, offset, &fid)) return 0;

#if 0
    fid_info = dissect_smb_fid(tvb, pinfo, tree, offset, 2, fid, TRUE, FALSE, FALSE, si);
    if (fid_info) {
        /* This command is used to create and open a new file or open
        and truncate an existing file to zero length */
        fid_info->end_of_file = 0;
        if (fid_info->fsi) {
            /* File Type */
            fattr = fid_info->fsi->file_attributes;
            /* XXX Volumes considered as directories */
            isdir = (fattr & SMB_FILE_ATTRIBUTE_DIRECTORY) || (fattr & SMB_FILE_ATTRIBUTE_VOLUME);
            if (isdir == 0) {
                fid_info->type = SMB_FID_TYPE_FILE;
            } else {
                fid_info->type = SMB_FID_TYPE_DIR;
            }
        }
    }
#endif

    offset += 2;

    // File Attributes
    info->FileAttr = get_uint16_t(pkt->payload, offset);
    offset += 2;

    /* last write time */
    offset += 4;
    //offset = dissect_smb_UTIME(tvb, tree, offset, hf_smb_last_write_time);

    // File Size
    info->FileSize = get_uint32_t(pkt->payload, offset);
    offset += 4;

    /* granted access */
    offset += 2;
    //offset = dissect_access(tvb, tree, offset, hf_smb_granted_access);

    BYTE_COUNT;

    END_OF_SMB

    return offset;
}

static int dissect_create_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);

    int         fn_len;
    int         fn;
    uint8_t      wc;
    uint16_t     bc;
    uint16_t file_attributes = 0;

    WORD_COUNT;

    /* file attributes */
    /* We read the two lower bytes into the four-bytes file-attributes, because they are compatible */
    
    if (-1 == dpi_get_le16(pkt, offset, &file_attributes))
        return 0;
    offset += 2;

    /* creation time */
    offset = dissect_smb_UTIME(pkt, offset);

    BYTE_COUNT;

    /* buffer format */
    CHECK_BYTE_COUNT(1);
    //proto_tree_add_item(tree, hf_smb_buffer_format, tvb, offset, 1, ENC_LITTLE_ENDIAN);
    COUNT_BYTES(1);

    /* File Name */
    fn = get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len, 0, 0, &bc, info->request_dir, sizeof(info->request_dir));
    if (fn == -1)
        goto endofcommand;

    COUNT_BYTES(fn_len);

    END_OF_SMB

    return offset;
}

static int dissect_create_file_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(si);
    UNUSED(info);

    uint8_t  wc;
    uint16_t bc;
    uint16_t fid;
//    uint16_t fattr;
//    uint8_t    sdir = 0;

    WORD_COUNT;

    /* fid */
    if (-1 == dpi_get_le16(pkt, offset, &fid))
        return 0;

    offset += 2;

    BYTE_COUNT;

    END_OF_SMB

    return offset;
}

static int dissect_session_setup_andx_request(struct flow_info *flow,
        struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    uint32_t var32;
    uint8_t      wc, cmd        = 0xff;
    uint16_t     bc;
    uint16_t     andxoffset     = 0;
    int         an_len;
    int         an;
    int         dn_len;
    int dn;
    uint16_t     pwlen          = 0;
    uint16_t     sbloblen       = 0;
    //uint16_t sbloblen_short;
    uint16_t     apwlen         = 0, upwlen = 0;
    uint8_t    unicodeflag;

    WORD_COUNT;

    info->WordCount = wc;
    /* next smb command */
    if (-1 == dpi_get_uint8(pkt, offset, &cmd)) return 0;
    
    if (cmd != 0xff) {
        info->ClientSesssetupXAndXCommand = val_to_string(cmd, smb_cmd_vals);
    } else {
        info->ClientSesssetupXAndXCommand = "No further commands (0xff)";
    }
    offset += 1;

    /* reserved byte */
    //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 1, ENC_NA);
    offset += 1;

    /* andxoffset */
    if (-1 == dpi_get_le16(pkt, offset, &andxoffset)) return 0;
    info->ClientSesssetupXAndXOffset = andxoffset;
    offset += 2;

    /* Maximum Buffer Size */
    if (-1 == dpi_get_le16(pkt, offset, &info->ClientMaxBufferSize)) return 0;
    offset += 2;

    /* Maximum Multiplex Count */
    if (-1 == dpi_get_le16(pkt, offset, &info->ClientMaxMpxCount)) return 0;
    offset += 2;

    /* VC Number */
    if (-1 == dpi_get_le16(pkt, offset, &info->ClientVcNumber)) return 0;
    offset += 2;

    /* session key */
    if (-1 == dpi_get_le32(pkt, offset, &var32))
        return 0;
    snprintf(info->ClientSessionKey, sizeof(info->ClientSessionKey), "0x%08x", var32);
    offset += 4;

    switch (wc) {
    case 10:
        /* password length, ASCII*/
        if (-1 == dpi_get_le16(pkt, offset, &pwlen)) return 0;
        info->ClientANSIPasswordLen = pwlen;
        offset += 2;
        /* 4 reserved bytes */
        //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 4, ENC_NA);
        offset += 4;
        break;

    case 12:
        /* security blob length */
        if (-1 == dpi_get_le16(pkt, offset, &sbloblen)) return 0;
        //proto_tree_add_uint(tree, hf_smb_security_blob_len, tvb, offset, 2, sbloblen);
        offset += 2;

        /* 4 reserved bytes */
        //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 4, ENC_NA);
        offset += 4;

        /* capabilities */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        snprintf(info->ClientCapabilities, sizeof(info->ClientCapabilities), "0x%08x", var32);
        offset += 4;
        //if (-1 == dpi_get_hex_string(pkt, offset, 4, info->ClientCapabilities, sizeof(info->ClientCapabilities))) return 0;
        //dissect_negprot_capabilities(tvb, tree, offset);
        //offset += 4;

        break;

    case 13:
        /* password length, ANSI*/
        if (-1 == dpi_get_le16(pkt, offset, &apwlen)) return 0;
        info->ClientANSIPasswordLen = apwlen;
        offset += 2;

        /* password length, Unicode*/
        if (-1 == dpi_get_le16(pkt, offset, &upwlen)) return 0;
        info->ClientUnicodePasswordLen = upwlen;
        offset += 2;

        /* 4 reserved bytes */
        //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 4, ENC_NA);
        offset += 4;

        /* capabilities */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        snprintf(info->ClientCapabilities, sizeof(info->ClientCapabilities), "0x%08x", var32);
        offset += 4;

        //if (-1 == dpi_get_hex_string(pkt, offset, 4, info->ClientCapabilities, sizeof(info->ClientCapabilities))) return 0;
        //offset += 4;

        break;
    }

    BYTE_COUNT;
    info->ByteCount = bc;

    if (wc == 12) {
        if (sbloblen) {
            if (sbloblen > pkt->payload_len - offset) {
                sbloblen = pkt->payload_len - offset;
            }
            const uint8_t *sblobptr = pkt->payload + offset;

            uint16_t id = 0;
            for (id = 0; id < sbloblen; id++) {
                if (memcmp(sblobptr + id, "NTLMSSP", 7) == 0) {
					info->authtype = sblobptr + id;
                    dissect_ntlmssp(flow, sblobptr + id, sbloblen - id, info);
                    break;
                }
            }
            
            COUNT_BYTES(sbloblen);
        }

        /* OS
         * Eventhough this field should honour the unicode flag
         * some ms clients gets this wrong.
         * At least XP SP1 sends this in ASCII
         * even when the unicode flag is on.
         * Test if the first three bytes are "Win"
         * and if so just override the flag.
         */
        unicodeflag = si->unicode;
        int cmp = dpi_strneql(pkt, offset, "Win", 3);
        if (cmp == -1) 
            return 0;
        else if (cmp == 0)
            unicodeflag = 0;

        an = get_unicode_or_ascii_string(pkt, &offset,
                unicodeflag, &an_len, 0, 0, &bc, info->ClientNativeOS, sizeof(info->ClientNativeOS));
        if (an == -1)
            goto endofcommand;

        COUNT_BYTES(an_len);

        /* LANMAN */
        /* XXX - pre-W2K NT systems appear to stick an extra 2 bytes of
         * padding/null string/whatever in front of this. W2K doesn't
         * appear to. I suspect that's a bug that got fixed; I also
         * suspect that, in practice, nobody ever looks at that field
         * because the bug didn't appear to get fixed until NT 5.0....
         *
         * Eventhough this field should honour the unicode flag
         * some ms clients gets this wrong.
         * At least XP SP1 sends this in ASCII
         * even when the unicode flag is on.
         * Test if the first three bytes are "Win"
         * and if so just override the flag.
         */
        unicodeflag = si->unicode;
        cmp = dpi_strneql(pkt, offset, "Win", 3);
        if (cmp == -1) 
            return 0;
        else if (cmp == 0)
            unicodeflag = 0;

        an = get_unicode_or_ascii_string(pkt, &offset,
            unicodeflag, &an_len, 0, 0, &bc, info->ClientNativeLanMan, sizeof(info->ClientNativeLanMan));
        if (an == -1)
            goto endofcommand;
        COUNT_BYTES(an_len);

        /* Primary domain */
        /* XXX - pre-W2K NT systems sometimes appear to stick an extra
         * byte in front of this, at least if all the strings are
         * ASCII and the account name is empty. Another bug?
         */
        an = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &dn_len, 0, 0, &bc, info->ClientPrimaryDomain, sizeof(info->ClientPrimaryDomain));
        if (an == -1)
            goto endofcommand;
        COUNT_BYTES(dn_len);
    } else {
        switch (wc) {

        case 10:
            if (pwlen) {
                /* password, ASCII */
                CHECK_BYTE_COUNT(pwlen);
                dpi_get_string_ascii(pkt, offset, pwlen, info->ClientANSIPassword, sizeof(info->ClientANSIPassword));
                COUNT_BYTES(pwlen);
            }

            break;

        case 13:
            if (apwlen) {
                /* password, ANSI */
                CHECK_BYTE_COUNT(apwlen);
                dpi_get_string_ascii(pkt, offset, apwlen, info->ClientANSIPassword, sizeof(info->ClientANSIPassword));
                COUNT_BYTES(apwlen);
            }

            if (upwlen) {
                /* password, Unicode */
                CHECK_BYTE_COUNT(upwlen);
                dpi_get_string_ascii(pkt, offset, upwlen, info->ClientANSIPassword, sizeof(info->ClientANSIPassword));
                if (upwlen > 24) {
                    //proto_tree *subtree;
                    //subtree = proto_item_add_subtree(item, ett_smb_unicode_password);
                    //dissect_ntlmv2_response(tvb, pinfo, subtree, offset, upwlen);
                }
                COUNT_BYTES(upwlen);
            }
            break;
        }

        /* Account Name */
        an = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &an_len, 0, 0, &bc, info->ClientAccountName, sizeof(info->ClientAccountName));
        if (an == -1)
            goto endofcommand;
        COUNT_BYTES(an_len);

        /* Primary domain */
        /* XXX - pre-W2K NT systems sometimes appear to stick an extra
         * byte in front of this, at least if all the strings are
         * ASCII and the account name is empty. Another bug?
         */
        dn = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &dn_len, 0, 0, &bc, info->ClientPrimaryDomain, sizeof(info->ClientPrimaryDomain));
        if (dn == -1)
            goto endofcommand;
        COUNT_BYTES(dn_len);

        /* OS */
        an = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &an_len, 0, 0, &bc, info->ClientNativeOS, sizeof(info->ClientNativeOS));
        if (an == -1)
            goto endofcommand;
        COUNT_BYTES(an_len);

        /* LANMAN */
        /* XXX - pre-W2K NT systems appear to stick an extra 2 bytes of
         * padding/null string/whatever in front of this. W2K doesn't
         * appear to. I suspect that's a bug that got fixed; I also
         * suspect that, in practice, nobody ever looks at that field
         * because the bug didn't appear to get fixed until NT 5.0....
         */
        an = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &an_len, 0, 0, &bc, info->ClientNativeLanMan, sizeof(info->ClientNativeLanMan));
        if (an == -1)
            goto endofcommand;
        COUNT_BYTES(an_len);
    }

    END_OF_SMB

    if (cmd != 0xff) {     /* there is an andX command */
        if (andxoffset < offset) {
            return offset;
        }
        dissect_smb_command(flow, pkt, andxoffset, cmd, si, info);
    }

    return offset;
}

static int dissect_session_setup_andx_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    uint8_t wc, cmd = 0xff;
    uint16_t andxoffset = 0, bc;
    uint16_t sbloblen = 0;
    uint16_t var16;
    int an_len;
    int an;

    WORD_COUNT;
    info->WordCount = wc;

    /* next smb command */
    if (-1 == dpi_get_uint8(pkt, offset, &cmd)) 
        return 0;
    
    if (cmd != 0xff) {
        info->ServerSesssetupXAndXCommand = val_to_string(cmd, smb_cmd_vals);
    } else {
        info->ServerSesssetupXAndXCommand = "No further commands (0xff)";
    }
    offset += 1;

    /* reserved byte */
    //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 1, ENC_NA);
    offset += 1;

    /* andxoffset */
    if (-1 == dpi_get_le16(pkt, offset, &andxoffset))
        return 0;
    info->ServerSesssetupXAndXOffset = andxoffset;
    offset += 2;

    /* flags */
    if (-1 == dpi_get_le16(pkt, offset, &var16))
        return 0;
    if (var16 == 0x0001)
        info->ServerAction = "Client logged in as GUEST ? TURE";
    else
        info->ServerAction = "Client logged in as GUEST ? FALSE";
    offset += 2;

    if (wc == 4) {
        /* security blob length */
        if (-1 == dpi_get_le16(pkt, offset, &sbloblen))
            return 0;
        info->ServerSecurityBlobLength = sbloblen;
        offset += 2;
    }

    BYTE_COUNT;
    info->ByteCount = bc;

    if (wc == 4) {
        //proto_item *blob_item;

        /* security blob */
        /* don't try to eat too much of we might get an exception on
         * short frames and then we will not see anything at all
         * of the security blob.
         */
        if (sbloblen > pkt->payload_len - offset) {
            sbloblen = pkt->payload_len - offset;
        }
        const uint8_t *sblobptr = pkt->payload + offset;
        uint16_t id = 0;
        for (id = 0; id < sbloblen; id++) {
            if (memcmp(sblobptr + id, "NTLMSSP", 7) == 0){
				info->authtype = sblobptr + id;
                dissect_ntlmssp(flow, sblobptr + id, sbloblen - id, info);
                break;
            }
        }

        //blob_item = proto_tree_add_item(tree, hf_smb_security_blob,
        //                tvb, offset, sbloblen, ENC_NA);

        if (sbloblen) {
            //tvbuff_t *blob_tvb;
            //proto_tree *blob_tree;

            //blob_tree = proto_item_add_subtree(blob_item,
            //                   ett_smb_secblob);
            CHECK_BYTE_COUNT(sbloblen);

            /*blob_tvb = tvb_new_subset_length(tvb, offset, sbloblen);

            if (si && si->ct && si->ct->raw_ntlmssp &&
                (tvb_strneql(tvb, offset, "NTLMSSP", 7) == 0)) {
              call_dissector(ntlmssp_handle, blob_tvb, pinfo,
                     blob_tree);
            }
            else {
              call_dissector(gssapi_handle, blob_tvb, pinfo,
                     blob_tree);
            }*/

            COUNT_BYTES(sbloblen);
        }
    }

    /* OS */
    an = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &an_len, 0, 0, &bc, info->ServerNativeOS, sizeof(info->ServerNativeOS));
    if (an == -1)
        goto endofcommand;
    COUNT_BYTES(an_len);

    /* LANMAN */
    an = get_unicode_or_ascii_string(pkt, &offset,
            si->unicode, &an_len, 0, 0, &bc, info->ServerNativeLanMan, sizeof(info->ServerNativeLanMan));
    if (an == -1)
        goto endofcommand;
    COUNT_BYTES(an_len);

    if ((wc == 3) || (wc == 4)) {
        /* Primary domain */
        an = get_unicode_or_ascii_string(pkt, &offset,
                si->unicode, &an_len, 0, 0, &bc, info->ServerPrimaryDomain, sizeof(info->ServerPrimaryDomain));
        if (an == -1)
            goto endofcommand;
        
        COUNT_BYTES(an_len);
    }

    END_OF_SMB

    if (cmd != 0xff) {     /* there is an andX command */
        if (andxoffset < offset) {
            return offset;
        }
        dissect_smb_command(flow, pkt, andxoffset, cmd, si, info);
    }

    return offset;
}

static int dissect_negprot_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    UNUSED(si);
    
    int index = 0;
    int i = 0;
    int ret;
    uint16_t bc;
    uint8_t wc;

    WORD_COUNT;
    info->WordCount = wc;

    BYTE_COUNT;
    info->ByteCount = bc;

    while (bc) {
        int len;
//        const uint8_t *str;
    
        /* Buffer Format */
        CHECK_BYTE_COUNT(1);
        //proto_tree_add_item(dtr, hf_smb_buffer_format, tvb, offset, 1,
        //    ENC_LITTLE_ENDIAN);
        COUNT_BYTES(1);
        
        len = find_str_end_len(pkt->payload + offset, pkt->payload_len - offset);
        if (len < 0)
            break;

        /*Dialect Name */
        CHECK_BYTE_COUNT(len);
        if(i < DIALECTS_LEN)
        {
            memcpy(info->tmp_dialect_name[i],(const char *)pkt->payload + offset, DIALECTS_NAME_LEN);
            i++;
        }
        ret = snprintf(info->Dialects + index, sizeof(info->Dialects) - index, "%s;", (const char *)pkt->payload + offset);
        index += ret;

        COUNT_BYTES(len);

        info->DialectNums++;

    }

    END_OF_SMB

    return offset;
}

static int dissect_negprot_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    UNUSED(flow);
    uint8_t var8;
    uint16_t var16;
    uint32_t var32;
    uint8_t      wc;
    uint16_t     dialect;
    int          dn;
    int         dn_len;
    uint16_t     bc;
    uint16_t     chl          = 0;
    uint32_t     caps         = 0;
    int i;

    WORD_COUNT;
    info->WordCount = wc;

    // Dialect Index
    if (dpi_get_le16(pkt, offset, &dialect) == -1)
        return 0;
    // Dialect Name
    if (dialect < DIALECTS_LEN && strlen(info->tmp_dialect_name[dialect]) > 0)
        memcpy(info->DialectName, info->tmp_dialect_name[dialect], DIALECTS_NAME_LEN);
    else
        memcpy(info->DialectName, "unknown", 7);
    info->ServerDialectIndex = dialect;
    offset += 2;

    switch(wc) {
    case 13:
        /* Security Mode */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        snprintf(info->ServerSecurityMode, sizeof(info->ServerSecurityMode), "0x%04x", var16);
        offset += 2;

        /* Maximum Transmit Buffer Size */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        info->ServerMaxBufferSize = var16;
        offset += 2;

        /* Maximum Multiplex Count */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        info->ServerMaxMpxCount = var16;
        offset += 2;

        /* Maximum Vcs Number */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        info->ServerMaxNumberVcs = var16;
        offset += 2;

        /* raw mode */
        //offset = dissect_negprot_rawmode(tvb, tree, offset);
        offset += 2;

        /* session key */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        snprintf(info->ServerSessionKey, sizeof(info->ServerSessionKey), "0x%08x", var32);
        offset += 4;

        /* current time and date at server */
        offset = dissect_smb_datetime(pkt, offset, 1, info->ServerSystemTime, sizeof(info->ServerSystemTime));

        /* time zone */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        snprintf(info->ServerTimeZone, sizeof(info->ServerTimeZone), "%d min from UTC", (int16_t)var16);
        offset += 2;

        /* challenge length */
        if (-1 == dpi_get_le16(pkt, offset, &chl))
            return 0;
        //proto_tree_add_uint(tree, hf_smb_challenge_length, tvb, offset, 2, chl);
        offset += 2;

        /* 2 reserved bytes */
        //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 2, ENC_NA);
        offset += 2;

        break;

    case 17:
        /* Security Mode */
        if (-1 == dpi_get_uint8(pkt, offset, &var8))
            return 0;
        snprintf(info->ServerSecurityMode, sizeof(info->ServerSecurityMode), "0x%02x", var8);
        offset += 1;

        /* Maximum Multiplex Count */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        info->ServerMaxMpxCount = var16;
        offset += 2;

        /* Maximum Vcs Number */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        info->ServerMaxNumberVcs = var16;
        offset += 2;

        /* Maximum Transmit Buffer Size */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        info->ServerMaxBufferSize = var32;
        offset += 4;

        /* maximum raw buffer size */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        info->ServerMaxRawSize = var32;
        offset += 4;

        /* session key */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        snprintf(info->ServerSessionKey, sizeof(info->ServerSessionKey), "0x%08x", var32);
        offset += 4;

        /* server capabilities */
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        caps = var32;
        snprintf(info->ServerCapabilities, sizeof(info->ServerCapabilities), "0x%08x", var32);
        offset += 4;

        /* system time */
        offset = dissect_nt_64bit_time_ex(pkt, offset, info->ServerSystemTime, sizeof(info->ServerSystemTime));

        /* time zone */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        snprintf(info->ServerTimeZone, sizeof(info->ServerTimeZone), "%d min from UTC", (int16_t)var16);
        offset += 2;

        /* challenge length */
        if (-1 == dpi_get_le16(pkt, offset, &chl))
            return 0;
        //proto_tree_add_uint(tree, hf_smb_challenge_length,
        //    tvb, offset, 1, chl);
        offset += 1;

        break;
    }

    BYTE_COUNT;
    info->ByteCount = bc;

    switch(wc) {
    case 13:
        /* encrypted challenge/response data */
        if (chl) {
            CHECK_BYTE_COUNT(chl);
            //proto_tree_add_item(tree, hf_smb_challenge, tvb, offset, chl, ENC_NA);
            COUNT_BYTES(chl);
        }

        /*
         * Primary domain.
         *
         * XXX - not present if negotiated dialect isn't
         * "DOS LANMAN 2.1" or "LANMAN2.1", but we'd either
         * have to see the request, or assume what dialect strings
         * were sent, to determine that.
         *
         * Is this something other than a primary domain if the
         * negotiated dialect is Windows for Workgroups 3.1a?
         * It appears to be 8 bytes of binary data in at least
         * one capture - is that an encryption key or something
         * such as that?
         */
        dn = get_unicode_or_ascii_string(pkt, &offset,
                si->unicode, &dn_len, 0, 0, &bc, info->ServerPrimaryDomain, sizeof(info->ServerPrimaryDomain));
        if (dn == -1)
            goto endofcommand;
        COUNT_BYTES(dn_len);
        break;

    case 17:
        if (!(caps & SERVER_CAP_EXTENDED_SECURITY)) {
            /* encrypted challenge/response data */
            /* XXX - is this aligned on an even boundary? */
            if (chl) {
                CHECK_BYTE_COUNT(chl);
                //proto_tree_add_item(tree, hf_smb_challenge,
                //    tvb, offset, chl, ENC_NA);
                COUNT_BYTES(chl);
            }

            /* domain */
            /* this string is special, unicode is flagged in caps */
            /* This string is NOT padded to be 16bit aligned.
               (seen in actual capture)
               XXX - I've seen a capture where it appears to be
               so aligned, but I've also seen captures where
               it is.  The captures where it appeared to be
               aligned may have been from buggy servers. */
            /* However, don't get rid of existing setting */
            si->unicode = (caps & SERVER_CAP_UNICODE) || si->unicode;

            dn = get_unicode_or_ascii_string(pkt, &offset,
                    si->unicode, &dn_len, 0, 0, &bc, info->ServerPrimaryDomain, sizeof(info->ServerPrimaryDomain));
            if (dn == -1)
                goto endofcommand;

            COUNT_BYTES(dn_len);

            /* server name, seen in w2k pro capture */
            dn = get_unicode_or_ascii_string(pkt, &offset,
                si->unicode, &dn_len, 0, 0, &bc, info->ServerName, sizeof(info->ServerName));
            if (dn == -1)
                goto endofcommand;
            COUNT_BYTES(dn_len);

        } else {
            uint16_t sbloblen;

            /* guid */
            /* XXX - show it in the standard Microsoft format
               for GUIDs? */
            CHECK_BYTE_COUNT(16);
            if (offset + 16 < pkt->payload_len) {
                for (i = 0; i < 16; i++) {
                    snprintf(info->ServerGUID + i * 2, sizeof(info->ServerGUID) - i * 2, "%02x", pkt->payload[offset + i]);
                }
            }
            COUNT_BYTES(16);

            /* security blob */
            /* If it runs past the end of the captured data, don't
             * try to put all of it into the protocol tree as the
             * raw security blob; we might get an exception on
             * short frames and then we will not see anything at all
             * of the security blob.
             */
            sbloblen = bc;
            if (sbloblen > pkt->payload_len - offset) {
                sbloblen = pkt->payload_len - offset;
            }
            
            for (i = 0; i < sbloblen; i++) {
                if (i * 2 + 2 > (int)sizeof(info->ServerNegprotSecurityBlob))
                    break;
                snprintf(info->ServerNegprotSecurityBlob + i * 2, sizeof(info->ServerNegprotSecurityBlob) - i * 2, "%02x", pkt->payload[offset + i]);
            }

            //blob_item = proto_tree_add_item(
            //    tree, hf_smb_security_blob,
            //    tvb, offset, sbloblen, ENC_NA);

            /*
             * If Extended security and BCC == 16, then raw
             * NTLMSSP is in use. We need to save this info
             */

            if (bc) {
            //    tvbuff_t *gssapi_tvb;
            //    proto_tree *gssapi_tree;

            //    gssapi_tree = proto_item_add_subtree(
            //        blob_item, ett_smb_secblob);

                /*
                 * Set the reported length of this to
                 * the reported length of the blob,
                 * rather than the amount of data
                 * available from the blob, so that
                 * we'll throw the right exception if
                 * it's too short.
                 */
            //    gssapi_tvb = tvb_new_subset_length_caplen(
            //        tvb, offset, sbloblen, bc);

            //    call_dissector(
            //        gssapi_handle, gssapi_tvb, pinfo,
            //        gssapi_tree);

            //    if (si->ct)
            //      si->ct->raw_ntlmssp = 0;

                COUNT_BYTES(bc);
            }
            else {

              /*
               * There is no blob. We just have to make sure
               * that subsequent routines know to call the
               * right things ...
               */

            //if (si->ct)
            //    si->ct->raw_ntlmssp = 1;

            }
        }
        break;
    }

    END_OF_SMB

    return offset;
}

static int dissect_tree_connect_andx_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
//    uint8_t var8;
    uint16_t var16;

    uint8_t        wc, cmd = 0xff;
    uint16_t       bc;
    uint16_t       andxoffset = 0, pwlen = 0;
    int           an_len;
    int an;

    WORD_COUNT;
    info->WordCount = wc;

    /* next smb command */
    if (-1 == dpi_get_uint8(pkt, offset, &cmd)) return 0;
    
    if (cmd != 0xff) {
        info->ClientTConXAndXCommand = val_to_string(cmd, smb_cmd_vals);
    } else {
        info->ClientTConXAndXCommand = "No further commands (0xff)";
    }
    offset += 1;

    /* reserved byte */
    //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 1, ENC_NA);
    offset += 1;

    /* andxoffset */
    if (-1 == dpi_get_le16(pkt, offset, &andxoffset)) return 0;
    info->ClientTConXAndXOffset = andxoffset;
    offset += 2;

    /* flags */
    if (-1 == dpi_get_le16(pkt, offset, &var16))
        return 0;
    snprintf(info->ClientFlags, sizeof(info->ClientFlags), "0x%04x", var16);
    offset += 2;

    /* password length*/
    if (-1 == dpi_get_le16(pkt, offset, &pwlen))
        return 0;
    info->ClientPasswordLength = pwlen;
    offset += 2;

    BYTE_COUNT;
    info->ByteCount = bc;

    /* password */
    CHECK_BYTE_COUNT(pwlen);
    if (offset + pwlen <= pkt->payload_len) {
        memcpy(info->ClientPassword, pkt->payload + offset, pwlen);
    }
    COUNT_BYTES(pwlen);

    /* Path */
    an = get_unicode_or_ascii_string(pkt, &offset, si->unicode, &an_len,
        0, 0, &bc, info->ClientPath, sizeof(info->ClientPath));
    if (an == -1)
        goto endofcommand;
    COUNT_BYTES(an_len);

    /*
     * NOTE: the Service string is always ASCII, even if the
     * "strings are Unicode" bit is set in the flags2 field
     * of the SMB.
     */

    /* Service */
    /* XXX - what if this runs past bc? */
    an_len = find_str_end_len(pkt->payload + offset, pkt->payload_len - offset);
    if (an_len < 0) {
        an_len = pkt->payload_len - offset;
    }
    CHECK_BYTE_COUNT(an_len);
    strncpy(info->ClientService, (const char *)pkt->payload + offset, an_len);
    info->ClientService[an_len] = 0;
    COUNT_BYTES(an_len);

    END_OF_SMB
    if (cmd != 0xff) {    /* there is an andX command */
        if (andxoffset < offset) {
            return offset;
        }
        dissect_smb_command(flow, pkt, andxoffset, cmd, si, info);
    }

    return offset;
}

static int dissect_tree_connect_andx_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info)
{
    uint16_t var16;
    uint32_t var32;
    uint8_t        wc, cmd = 0xff;
    uint16_t       andxoffset     = 0;
    uint16_t       bc;
    int           an_len;
    int an;

    WORD_COUNT;
    info->WordCount = wc;

    /* next smb command */
    if (-1 == dpi_get_uint8(pkt, offset, &cmd)) return 0;
    
    if (cmd != 0xff) {
        info->ServerTConXAndXCommand = val_to_string(cmd, smb_cmd_vals);
    } else {
        info->ServerTConXAndXCommand = "No further commands (0xff)";
    }
    offset += 1;

    /* reserved byte */
    //proto_tree_add_item(tree, hf_smb_reserved, tvb, offset, 1, ENC_NA);
    offset += 1;

    /* andxoffset */
    if (-1 == dpi_get_le16(pkt, offset, &andxoffset)) return 0;
    info->ServerTConXAndXOffset = andxoffset;
    offset += 2;

    /* There are three valid formats of tree connect response.
       All have the first two words: andx_cmd, andx_off,
       and then have additional words as follows:
        wc=2: (ancient LanMan -- no more words)
        wc=3: (NT, non-ext) opt_support
        wc=7: (NT, extended) opt_support,
            tree_access(2w), guest_access(2w)
       byte_count follows those words as usual */

    if (wc >= 3) {
        /* flags */
        if (-1 == dpi_get_le16(pkt, offset, &var16))
            return 0;
        snprintf(info->ServerOptionalSupport, sizeof(info->ServerOptionalSupport), "0x%04x", var16);
        offset += 2;
    }

    if (wc == 7) {
        /*
         * Refer to [MS-SMB] - v20100711
         * When a server returns extended information, the response
         * takes the following format, with WordCount = 7.
         * MaximalShareAccessRights, and GuestMaximalShareAccessRights fields
         * has added.
         */        
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        snprintf(info->MaximalShareAccessRights, sizeof(info->MaximalShareAccessRights), "0x%08x", var32);
        offset += 4;
        
        if (-1 == dpi_get_le32(pkt, offset, &var32))
            return 0;
        snprintf(info->GuestMaximalShareAccessRights, sizeof(info->GuestMaximalShareAccessRights), "0x%08x", var32);
        offset += 4;
    }

    BYTE_COUNT;
    info->ByteCount = bc;

    /*
     * NOTE: even though the SNIA CIFS spec doesn't say there's
     * a "Service" string if there's a word count of 2, the
     * document at
     *
     *    ftp://ftp.microsoft.com/developr/drg/CIFS/dosextp.txt
     *
     * (it's in an ugly format - text intended to be sent to a
     * printer, with backspaces and overstrikes used for boldfacing
     * and underlining; UNIX "col -b" can be used to strip the
     * overstrikes out) says there's a "Service" string there, and
     * some network traffic has it.
     */

    /*
     * NOTE: the Service string is always ASCII, even if the
     * "strings are Unicode" bit is set in the flags2 field
     * of the SMB.
     */

    /* Service */
    /* XXX - what if this runs past bc? */
    an_len = find_str_end_len(pkt->payload + offset, pkt->payload_len - offset);
    if (an_len < 0) {
        an_len = pkt->payload_len - offset;
    }
    CHECK_BYTE_COUNT(an_len);
    strncpy(info->ServerService, (const char *)pkt->payload + offset, an_len);
    info->ServerService[an_len] = 0;
    COUNT_BYTES(an_len);

    if (bc != 0) {
        /*
         * Sometimes this isn't present.
         */

        /* Native FS */
        an = get_unicode_or_ascii_string(pkt, &offset, si->unicode, &an_len,
            0, 0, &bc, info->ServerNativeFileSystem, sizeof(info->ServerNativeFileSystem));
        if (an == -1)
            goto endofcommand;
        COUNT_BYTES(an_len);

    }

    END_OF_SMB

    if (cmd != 0xff) {    /* there is an andX command */
        if (andxoffset < offset) {
            return offset;
        }
        dissect_smb_command(flow, pkt, andxoffset, cmd, si, info);
    }

    return offset;
}

typedef struct _smb_function {
    int (*request)(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info);
    int (*response)(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info);
} smb_function;

static smb_function smb_dissector[256] = {
    /* 0x00 Create Dir*/                 {dissect_old_dir_request            , dissect_empty},
    /* 0x01 Delete Dir*/                 {dissect_old_dir_request            , dissect_empty},
    /* 0x02 Open File*/                  {dissect_open_file_request          , dissect_open_file_response},
    /* 0x03 Create File*/                {dissect_create_file_request        , dissect_create_file_response},
    /* 0x04 Close File*/                 {dissect_null                         , dissect_empty},
    /* 0x05 Flush File*/                 {dissect_null                        , dissect_empty},
    /* 0x06 Delete File*/                {dissect_delete_file_request                       , dissect_empty},
    /* 0x07 Rename File*/                {dissect_rename_file_request                       , dissect_null},
    /* 0x08 Query Info*/                 {dissect_null                       , dissect_null},
    /* 0x09 Set Info*/                   {dissect_null                       , dissect_empty},
    /* 0x0a Read File*/                  {dissect_read_file_request          , dissect_read_file_response},
    /* 0x0b Write File*/                 {dissect_null                 , dissect_null},
    /* 0x0c Lock Byte Range*/            {dissect_null               , dissect_empty},
    /* 0x0d Unlock Byte Range*/          {dissect_null               , dissect_empty},
    /* 0x0e Create Temp*/                {dissect_null               , dissect_null},
    /* 0x0f Create New*/                 {dissect_create_file_request        , dissect_null},

    /* 0x10 Check Dir*/                  {dissect_old_dir_request            , dissect_empty},
    /* 0x11 Process Exit*/               {dissect_empty                      , dissect_empty},
    /* 0x12 Seek File*/                  {dissect_null          , dissect_null},
    /* 0x13 Lock And Read*/              {dissect_null          , dissect_null},
    /* 0x14 Write And Unlock*/           {dissect_null          , dissect_null},
    /* 0x15 */                           {dissect_unknown                    , dissect_unknown},
    /* 0x16 */                           {dissect_unknown                    , dissect_unknown},
    /* 0x17 */                           {dissect_unknown                    , dissect_unknown},
    /* 0x18 */                           {dissect_unknown                    , dissect_unknown},
    /* 0x19 */                           {dissect_unknown                    , dissect_unknown},
    /* 0x1a Read Raw*/                   {dissect_null           , dissect_unknown},
    /* 0x1b Read MPX*/                   {dissect_null           , dissect_null},
    /* 0x1c Read MPX Secondary*/         {dissect_unknown                    , dissect_unknown},
    /* 0x1d Write Raw*/                  {dissect_null          , dissect_null},
    /* 0x1e Write MPX*/                  {dissect_null          , dissect_null},
    /* 0x1f Write MPX Secondary*/        {dissect_unknown                    , dissect_unknown},

    /* 0x20 Write Complete*/             {dissect_unknown                    , dissect_null},
    /* 0x21 */                           {dissect_unknown                    , dissect_unknown},
    /* 0x22 Set Info2*/                  {dissect_null        , dissect_empty},
    /* 0x23 Query Info2*/                {dissect_null        , dissect_null},
    /* 0x24 Locking And X*/              {dissect_null        , dissect_null},
    /* 0x25 Transaction*/                {dissect_transaction_request        , dissect_transaction_response},
    /* 0x26 Transaction Secondary*/      {dissect_transaction_request        , dissect_unknown}, /*This SMB has no response */
    /* 0x27 IOCTL*/                      {dissect_unknown                    , dissect_unknown},
    /* 0x28 IOCTL Secondary*/            {dissect_unknown                    , dissect_unknown},
    /* 0x29 Copy File*/                  {dissect_null               , dissect_null},
    /* 0x2a Move File*/                  {dissect_null               , dissect_null},
    /* 0x2b Echo*/                       {dissect_null               , dissect_null},
    /* 0x2c Write And Close*/            {dissect_null               , dissect_null},
    /* 0x2d Open And X*/                 {dissect_null          , dissect_null},
    /* 0x2e Read And X*/                 {dissect_null          , dissect_null},
    /* 0x2f Write And X*/                {dissect_null          , dissect_null},

    /* 0x30 */                           {dissect_unknown       , dissect_unknown},
    /* 0x31 Close And Tree Disconnect */  {dissect_null         , dissect_empty},
    /* 0x32 Transaction2*/                 {dissect_null          , dissect_null},
    /* 0x33 Transaction2 Secondary*/     {dissect_null          , dissect_unknown}, /*This SMB has no response */
    /* 0x34 Find Close2*/                {dissect_null          , dissect_empty},
    /* 0x35 Find Notify Close*/          {dissect_null          , dissect_empty},
    /* 0x36 */  {dissect_unknown, dissect_unknown},
    /* 0x37 */  {dissect_unknown, dissect_unknown},
    /* 0x38 */  {dissect_unknown, dissect_unknown},
    /* 0x39 */  {dissect_unknown, dissect_unknown},
    /* 0x3a */  {dissect_unknown, dissect_unknown},
    /* 0x3b */  {dissect_unknown, dissect_unknown},
    /* 0x3c */  {dissect_unknown, dissect_unknown},
    /* 0x3d */  {dissect_unknown, dissect_unknown},
    /* 0x3e */  {dissect_unknown, dissect_unknown},
    /* 0x3f */  {dissect_unknown, dissect_unknown},

    /* 0x40 */  {dissect_unknown, dissect_unknown},
    /* 0x41 */  {dissect_unknown, dissect_unknown},
    /* 0x42 */  {dissect_unknown, dissect_unknown},
    /* 0x43 */  {dissect_unknown, dissect_unknown},
    /* 0x44 */  {dissect_unknown, dissect_unknown},
    /* 0x45 */  {dissect_unknown, dissect_unknown},
    /* 0x46 */  {dissect_unknown, dissect_unknown},
    /* 0x47 */  {dissect_unknown, dissect_unknown},
    /* 0x48 */  {dissect_unknown, dissect_unknown},
    /* 0x49 */  {dissect_unknown, dissect_unknown},
    /* 0x4a */  {dissect_unknown, dissect_unknown},
    /* 0x4b */  {dissect_unknown, dissect_unknown},
    /* 0x4c */  {dissect_unknown, dissect_unknown},
    /* 0x4d */  {dissect_unknown, dissect_unknown},
    /* 0x4e */  {dissect_unknown, dissect_unknown},
    /* 0x4f */  {dissect_unknown, dissect_unknown},

    /* 0x50 */  {dissect_unknown, dissect_unknown},
    /* 0x51 */  {dissect_unknown, dissect_unknown},
    /* 0x52 */  {dissect_unknown, dissect_unknown},
    /* 0x53 */  {dissect_unknown, dissect_unknown},
    /* 0x54 */  {dissect_unknown, dissect_unknown},
    /* 0x55 */  {dissect_unknown, dissect_unknown},
    /* 0x56 */  {dissect_unknown, dissect_unknown},
    /* 0x57 */  {dissect_unknown, dissect_unknown},
    /* 0x58 */  {dissect_unknown, dissect_unknown},
    /* 0x59 */  {dissect_unknown, dissect_unknown},
    /* 0x5a */  {dissect_unknown, dissect_unknown},
    /* 0x5b */  {dissect_unknown, dissect_unknown},
    /* 0x5c */  {dissect_unknown, dissect_unknown},
    /* 0x5d */  {dissect_unknown, dissect_unknown},
    /* 0x5e */  {dissect_unknown, dissect_unknown},
    /* 0x5f */  {dissect_unknown, dissect_unknown},

    /* 0x60 */  {dissect_unknown, dissect_unknown},
    /* 0x61 */  {dissect_unknown, dissect_unknown},
    /* 0x62 */  {dissect_unknown, dissect_unknown},
    /* 0x63 */  {dissect_unknown, dissect_unknown},
    /* 0x64 */  {dissect_unknown, dissect_unknown},
    /* 0x65 */  {dissect_unknown, dissect_unknown},
    /* 0x66 */  {dissect_unknown, dissect_unknown},
    /* 0x67 */  {dissect_unknown, dissect_unknown},
    /* 0x68 */  {dissect_unknown, dissect_unknown},
    /* 0x69 */  {dissect_unknown, dissect_unknown},
    /* 0x6a */  {dissect_unknown, dissect_unknown},
    /* 0x6b */  {dissect_unknown, dissect_unknown},
    /* 0x6c */  {dissect_unknown, dissect_unknown},
    /* 0x6d */  {dissect_unknown, dissect_unknown},
    /* 0x6e */  {dissect_unknown, dissect_unknown},
    /* 0x6f */  {dissect_unknown, dissect_unknown},

    /* 0x70 Tree Connect*/                 {dissect_tree_connect_request, dissect_null},
    /* 0x71 Tree Disconnect*/             {dissect_empty                      , dissect_empty},
    /* 0x72 Negotiate Protocol*/         {dissect_negprot_request            , dissect_negprot_response},
    /* 0x73 Session Setup And X*/        {dissect_session_setup_andx_request , dissect_session_setup_andx_response},
    /* 0x74 Logoff And X*/                 {dissect_null                       , dissect_null},
    /* 0x75 Tree Connect And X*/         {dissect_tree_connect_andx_request                       , dissect_tree_connect_andx_response},
    /* 0x76 */  {dissect_unknown, dissect_unknown},
    /* 0x77 */  {dissect_unknown, dissect_unknown},
    /* 0x78 */  {dissect_unknown, dissect_unknown},
    /* 0x79 */  {dissect_unknown, dissect_unknown},
    /* 0x7a */  {dissect_unknown, dissect_unknown},
    /* 0x7b */  {dissect_unknown, dissect_unknown},
    /* 0x7c */  {dissect_unknown, dissect_unknown},
    /* 0x7d */  {dissect_unknown, dissect_unknown},
    /* 0x7e */  {dissect_unknown, dissect_unknown},
    /* 0x7f */  {dissect_unknown, dissect_unknown},

    /* 0x80 Query Info Disk*/            {dissect_empty              , dissect_null},
    /* 0x81 Search Dir*/                 {dissect_null               , dissect_null},
    /* 0x82 Find*/                       {dissect_null               , dissect_null},
    /* 0x83 Find Unique*/                {dissect_null               , dissect_null},
    /* 0x84 Find Close*/                 {dissect_null               , dissect_null},
    /* 0x85 */  {dissect_unknown, dissect_unknown},
    /* 0x86 */  {dissect_unknown, dissect_unknown},
    /* 0x87 */  {dissect_unknown, dissect_unknown},
    /* 0x88 */  {dissect_unknown, dissect_unknown},
    /* 0x89 */  {dissect_unknown, dissect_unknown},
    /* 0x8a */  {dissect_unknown, dissect_unknown},
    /* 0x8b */  {dissect_unknown, dissect_unknown},
    /* 0x8c */  {dissect_unknown, dissect_unknown},
    /* 0x8d */  {dissect_unknown, dissect_unknown},
    /* 0x8e */  {dissect_unknown, dissect_unknown},
    /* 0x8f */  {dissect_unknown, dissect_unknown},

    /* 0x90 */  {dissect_unknown, dissect_unknown},
    /* 0x91 */  {dissect_unknown, dissect_unknown},
    /* 0x92 */  {dissect_unknown, dissect_unknown},
    /* 0x93 */  {dissect_unknown, dissect_unknown},
    /* 0x94 */  {dissect_unknown, dissect_unknown},
    /* 0x95 */  {dissect_unknown, dissect_unknown},
    /* 0x96 */  {dissect_unknown, dissect_unknown},
    /* 0x97 */  {dissect_unknown, dissect_unknown},
    /* 0x98 */  {dissect_unknown, dissect_unknown},
    /* 0x99 */  {dissect_unknown, dissect_unknown},
    /* 0x9a */  {dissect_unknown, dissect_unknown},
    /* 0x9b */  {dissect_unknown, dissect_unknown},
    /* 0x9c */  {dissect_unknown, dissect_unknown},
    /* 0x9d */  {dissect_unknown, dissect_unknown},
    /* 0x9e */  {dissect_unknown, dissect_unknown},
    /* 0x9f */  {dissect_unknown, dissect_unknown},

    /* 0xa0 NT Transaction*/             {dissect_null , dissect_unknown},
    /* 0xa1 NT Trans secondary*/         {dissect_null , dissect_unknown},
    /* 0xa2 NT CreateAndX*/              {dissect_nt_create_andx_request , dissect_nt_create_andx_response},
    /* 0xa3 */                             {dissect_unknown, dissect_unknown},
    /* 0xa4 NT Cancel*/                     {dissect_null      , dissect_unknown}, /*no response to this one*/
    /* 0xa5 NT Rename*/                  {dissect_nt_rename_file_request , dissect_empty},
    /* 0xa6 */  {dissect_unknown, dissect_unknown},
    /* 0xa7 */  {dissect_unknown, dissect_unknown},
    /* 0xa8 */  {dissect_unknown, dissect_unknown},
    /* 0xa9 */  {dissect_unknown, dissect_unknown},
    /* 0xaa */  {dissect_unknown, dissect_unknown},
    /* 0xab */  {dissect_unknown, dissect_unknown},
    /* 0xac */  {dissect_unknown, dissect_unknown},
    /* 0xad */  {dissect_unknown, dissect_unknown},
    /* 0xae */  {dissect_unknown, dissect_unknown},
    /* 0xaf */  {dissect_unknown, dissect_unknown},

    /* 0xb0 */  {dissect_unknown, dissect_unknown},
    /* 0xb1 */  {dissect_unknown, dissect_unknown},
    /* 0xb2 */  {dissect_unknown, dissect_unknown},
    /* 0xb3 */  {dissect_unknown, dissect_unknown},
    /* 0xb4 */  {dissect_unknown, dissect_unknown},
    /* 0xb5 */  {dissect_unknown, dissect_unknown},
    /* 0xb6 */  {dissect_unknown, dissect_unknown},
    /* 0xb7 */  {dissect_unknown, dissect_unknown},
    /* 0xb8 */  {dissect_unknown, dissect_unknown},
    /* 0xb9 */  {dissect_unknown, dissect_unknown},
    /* 0xba */  {dissect_unknown, dissect_unknown},
    /* 0xbb */  {dissect_unknown, dissect_unknown},
    /* 0xbc */  {dissect_unknown, dissect_unknown},
    /* 0xbd */  {dissect_unknown, dissect_unknown},
    /* 0xbe */  {dissect_unknown, dissect_unknown},
    /* 0xbf */  {dissect_unknown, dissect_unknown},

    /* 0xc0 Open Print File*/            {dissect_null  , dissect_null},
    /* 0xc1 Write Print File*/           {dissect_null , dissect_empty},
    /* 0xc2 Close Print File*/           {dissect_null , dissect_empty},
    /* 0xc3 Get Print Queue*/            {dissect_null  , dissect_null},
    /* 0xc4 */  {dissect_unknown, dissect_unknown},
    /* 0xc5 */  {dissect_unknown, dissect_unknown},
    /* 0xc6 */  {dissect_unknown, dissect_unknown},
    /* 0xc7 */  {dissect_unknown, dissect_unknown},
    /* 0xc8 */  {dissect_unknown, dissect_unknown},
    /* 0xc9 */  {dissect_unknown, dissect_unknown},
    /* 0xca */  {dissect_unknown, dissect_unknown},
    /* 0xcb */  {dissect_unknown, dissect_unknown},
    /* 0xcc */  {dissect_unknown, dissect_unknown},
    /* 0xcd */  {dissect_unknown, dissect_unknown},
    /* 0xce */  {dissect_unknown, dissect_unknown},
    /* 0xcf */  {dissect_unknown, dissect_unknown},

    /* 0xd0 Send Single Block Message*/         {dissect_null      , dissect_empty},
    /* 0xd1 Send Broadcast Message*/            {dissect_null      , dissect_empty},
    /* 0xd2 Forward User Name*/                 {dissect_null                         , dissect_empty},
    /* 0xd3 Cancel Forward*/                    {dissect_null                         , dissect_empty},
    /* 0xd4 Get Machine Name*/                  {dissect_empty     , dissect_null},
    /* 0xd5 Send Start of Multi-block Message*/ {dissect_null , dissect_null},
    /* 0xd6 Send End of Multi-block Message*/   {dissect_null                       , dissect_empty},
    /* 0xd7 Send Text of Multi-block Message*/  {dissect_null  , dissect_empty},
    /* 0xd8 SMBreadbulk*/                       {dissect_unknown                                , dissect_unknown},
    /* 0xd9 SMBwritebulk*/                      {dissect_unknown                                , dissect_unknown},
    /* 0xda SMBwritebulkdata*/                  {dissect_unknown                                , dissect_unknown},
    /* 0xdb */  {dissect_unknown, dissect_unknown},
    /* 0xdc */  {dissect_unknown, dissect_unknown},
    /* 0xdd */  {dissect_unknown, dissect_unknown},
    /* 0xde */  {dissect_unknown, dissect_unknown},
    /* 0xdf */  {dissect_unknown, dissect_unknown},

    /* 0xe0 */  {dissect_unknown, dissect_unknown},
    /* 0xe1 */  {dissect_unknown, dissect_unknown},
    /* 0xe2 */  {dissect_unknown, dissect_unknown},
    /* 0xe3 */  {dissect_unknown, dissect_unknown},
    /* 0xe4 */  {dissect_unknown, dissect_unknown},
    /* 0xe5 */  {dissect_unknown, dissect_unknown},
    /* 0xe6 */  {dissect_unknown, dissect_unknown},
    /* 0xe7 */  {dissect_unknown, dissect_unknown},
    /* 0xe8 */  {dissect_unknown, dissect_unknown},
    /* 0xe9 */  {dissect_unknown, dissect_unknown},
    /* 0xea */  {dissect_unknown, dissect_unknown},
    /* 0xeb */  {dissect_unknown, dissect_unknown},
    /* 0xec */  {dissect_unknown, dissect_unknown},
    /* 0xed */  {dissect_unknown, dissect_unknown},
    /* 0xee */  {dissect_unknown, dissect_unknown},
    /* 0xef */  {dissect_unknown, dissect_unknown},

    /* 0xf0 */  {dissect_unknown, dissect_unknown},
    /* 0xf1 */  {dissect_unknown, dissect_unknown},
    /* 0xf2 */  {dissect_unknown, dissect_unknown},
    /* 0xf3 */  {dissect_unknown, dissect_unknown},
    /* 0xf4 */  {dissect_unknown, dissect_unknown},
    /* 0xf5 */  {dissect_unknown, dissect_unknown},
    /* 0xf6 */  {dissect_unknown, dissect_unknown},
    /* 0xf7 */  {dissect_unknown, dissect_unknown},
    /* 0xf8 */  {dissect_unknown, dissect_unknown},
    /* 0xf9 */  {dissect_unknown, dissect_unknown},
    /* 0xfa */  {dissect_unknown, dissect_unknown},
    /* 0xfb */  {dissect_unknown, dissect_unknown},
    /* 0xfc */  {dissect_unknown, dissect_unknown},
    /* 0xfd */  {dissect_unknown, dissect_unknown},
    /* 0xfe */  {dissect_unknown, dissect_unknown},
    /* 0xff */  {dissect_unknown, dissect_unknown},
};

static int dissect_smb_command(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, uint8_t cmd,
        struct smb_session *si, struct smb_info *info)
{
    if (cmd != 0xff) {
        int (*dissector)(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *info);
        dissector = (si->request) ?
            smb_dissector[cmd].request : smb_dissector[cmd].response;

        offset = (*dissector)(flow, pkt, offset, si, info);
    }
    return offset;
}

#if 1
//extended attributes
int validate_offset(const struct dpi_pkt_st *pkt, const guint abs_offset)
{
    if (G_LIKELY(abs_offset <= pkt->payload_len))
        return 0;
    else
        return 2;
}
int check_offset_length_no_exception(const struct dpi_pkt_st *pkt,
    const int offset, int const length_val,uint32_t *offset_ptr, uint *length_ptr)
{
    uint32_t end_offset;
    int   exception;
    exception = smb_compute_offset(pkt, offset, offset_ptr);
    if (exception)
        return exception;
    if (length_val < -1)
        return 1;
    // Compute the length
    if (length_val == -1)
        *length_ptr = pkt->payload_len - *offset_ptr;
    else
        *length_ptr = length_val;
    // Compute the offset of the first byte past the length.
    end_offset = *offset_ptr + *length_ptr;
    // Check for an overflow
    if (end_offset < *offset_ptr)
        return 1;
    return validate_offset(pkt, end_offset);
}

int smb_bytes_exist(const struct dpi_pkt_st *pkt, const int offset, const int length)
{
    uint32_t abs_offset, abs_length;
    int exception;
    if (length < 0)
        return FALSE;
    exception = check_offset_length_no_exception(pkt, offset, length, &abs_offset, &abs_length);
    if (exception)
        return FALSE;
    return TRUE;
}

static fragment_head* smb_trans_defragment(struct smb_info *pinfo, struct dpi_pkt_st *pkt,int offset, 
    uint32_t count, uint32_t pos, uint32_t totlen, struct smb_session *si)
{
    fragment_head *fd_head = NULL;
    int more_frags;
    if ( !smb_bytes_exist(pkt, offset, count) || (pos > totlen) || (count > totlen) || ((pos + count) > totlen))
        return NULL;
    more_frags = totlen > (pos + count);
    
    // ---------------------------------- todo .... ----------------------------------
    

    //tmp
    return NULL;
}

int dissect_nt_sid(struct dpi_pkt_st *pkt, int offset)
{
    int sa_offset, i;
    uint8_t revision, num_auth;
    uint32_t sa_field;
    uint64_t authority=0;
    // Revision of SID
    revision = pkt->payload[offset++];
    // Number of subauthority fields
    num_auth = pkt->payload[offset++];
    // Identifier Authority
    for(i=0; i<6; i++)
    {
        authority = (authority << 8) + pkt->payload[offset++];
        offset++;
    }
    sa_offset = offset;
    for(i=1; i<num_auth+1; i++) 
    {
        sa_field = get_uint32_t(pkt->payload, offset);
        offset+=4;
    }
    return offset;
}

static int dissect_nt_ace_object(struct dpi_pkt_st *pkt, int offset)
{
    uint32_t flags;
    // flags
    flags = get_uint32_t(pkt->payload, offset);
    offset+=4;
    // is there a GUID ? 
    if(flags&0x00000001)
        offset+=16;
    // is there an inherited GUID ?
    if(flags&0x00000002)
        offset+=16;
    return offset;
}

static int dissect_nt_v2_ace(struct dpi_pkt_st *pkt, int offset, uint8_t *drep)
{
    int old_offset = offset;
    uint32_t access;
    uint16_t size;
    uint8_t type;
    // type
    type = pkt->payload[offset++];
    offset += 1;
    // flags
    offset += 1;
    // size
    size = get_uint16_t(pkt->payload, offset);
    if (size < 4)
        return old_offset;
    offset += 2;
    switch(type)
    {
        case ACE_TYPE_ACCESS_ALLOWED:
        case ACE_TYPE_ACCESS_DENIED:
        case ACE_TYPE_SYSTEM_AUDIT:
        case ACE_TYPE_SYSTEM_ALARM:
        case ACE_TYPE_ALLOWED_COMPOUND:
        case ACE_TYPE_ACCESS_ALLOWED_OBJECT:
        case ACE_TYPE_ACCESS_DENIED_OBJECT:
        case ACE_TYPE_SYSTEM_AUDIT_OBJECT:
        case ACE_TYPE_SYSTEM_ALARM_OBJECT:
        case ACE_TYPE_ACCESS_ALLOWED_CALLBACK:
        case ACE_TYPE_ACCESS_DENIED_CALLBACK:
        case ACE_TYPE_ACCESS_ALLOWED_CALLBACK_OBJECT:
        case ACE_TYPE_ACCESS_DENIED_CALLBACK_OBJECT:
        case ACE_TYPE_SYSTEM_AUDIT_CALLBACK:
        case ACE_TYPE_SYSTEM_ALARM_CALLBACK:
        case ACE_TYPE_SYSTEM_AUDIT_CALLBACK_OBJECT:
        case ACE_TYPE_SYSTEM_ALARM_CALLBACK_OBJECT:
        case ACE_TYPE_SYSTEM_MANDATORY_LABEL:
            if (drep != NULL)
                offset += 4;
            else
            {
                access = get_uint32_t(pkt->payload, offset);
                offset += 4;
            }
            switch(type)
            {
                case ACE_TYPE_ACCESS_ALLOWED_OBJECT:
                case ACE_TYPE_ACCESS_DENIED_OBJECT:
                case ACE_TYPE_SYSTEM_AUDIT_OBJECT:
                case ACE_TYPE_SYSTEM_ALARM_OBJECT:
                case ACE_TYPE_ACCESS_ALLOWED_CALLBACK_OBJECT:
                case ACE_TYPE_ACCESS_DENIED_CALLBACK_OBJECT:
                case ACE_TYPE_SYSTEM_AUDIT_CALLBACK_OBJECT:
                case ACE_TYPE_SYSTEM_ALARM_CALLBACK_OBJECT:
                offset = dissect_nt_ace_object(pkt, offset);
            }
            // SID
            offset = dissect_nt_sid(pkt, offset);
    }
    return old_offset + size;
}

static int dissect_nt_acl(struct dpi_pkt_st *pkt, int offset_a, uint8_t *drep)
{
    int pre_ace_offset;
    uint16_t revision;
    uint32_t num_aces;
    volatile int offset_v = offset_a;
    volatile int missing_data = FALSE;
    volatile int bad_ace = FALSE;
    revision = get_uint16_t(pkt->payload, offset_v);
    offset_v += 2;
    switch(revision)
    {
        case ACL_REVISION_NT4:
        case ACL_REVISION_ADS:
        case 3:  // weirdo type
            // size
            offset_v += 2;
            num_aces = get_uint32_t(pkt->payload, offset_v);
            offset_v += 4;
            while(num_aces-- && !missing_data && !bad_ace) 
            {
                pre_ace_offset = offset_v;
                offset_v = dissect_nt_v2_ace(pkt, offset_v, drep);
            }
    }
    return offset_v;
}
int dissect_nt_sec_desc(struct dpi_pkt_st *pkt, int offset, uint8_t *drep)
{
    uint16_t revision;
    int start_offset = offset;
    volatile int offset_v=offset;
    volatile int end_offset;
    volatile int item_offset;
    uint32_t owner_sid_offset;
    volatile uint32_t group_sid_offset;
    volatile uint32_t sacl_offset;
    volatile uint32_t dacl_offset;
    // revision
    revision = get_uint16_t(pkt->payload, offset_v);
    offset_v += 2;
    switch(revision)
    {
        case 1:
            offset_v += 2;
            owner_sid_offset = get_uint32_t(pkt->payload, offset_v);
            if(owner_sid_offset != 0 && owner_sid_offset < 20)
                owner_sid_offset = 0;
            offset_v += 4;
            // offset to group sid
            group_sid_offset = get_uint32_t(pkt->payload, offset_v);
            if(group_sid_offset != 0 && group_sid_offset < 20)
                group_sid_offset = 0;
            offset_v += 4;
            // offset to sacl
            sacl_offset = get_uint32_t(pkt->payload, offset_v);
            if(sacl_offset != 0 && sacl_offset < 20)
                sacl_offset = 0;
            offset_v += 4;
            // offset to dacl
            dacl_offset = get_uint32_t(pkt->payload, offset_v);
            if(dacl_offset != 0 && dacl_offset < 20)
                dacl_offset = 0;
            offset_v += 4;
            end_offset = offset_v;
            // owner SID
            if(owner_sid_offset)
            {
                item_offset = start_offset+owner_sid_offset;
                if (item_offset < start_offset)
                    break;
                offset_v = dissect_nt_sid(pkt, item_offset);
                if (offset_v > end_offset)
                    end_offset = offset_v;
            }
            // group SID
            if(group_sid_offset)
            {
                item_offset = start_offset+group_sid_offset;
                if (item_offset < start_offset)
                    break;
                offset_v = dissect_nt_sid(pkt, item_offset);
                if (offset_v > end_offset)
                    end_offset = offset_v;
                break;
            }
            // sacl
            if(sacl_offset)
            {
                item_offset = start_offset+sacl_offset;
                if (item_offset < start_offset)
                    break;
                offset_v = dissect_nt_acl(pkt, item_offset, drep);
                if (offset_v > end_offset)
                    end_offset = offset_v;
            }
            // dacl
            if(dacl_offset)
            {
                item_offset = start_offset+dacl_offset;
                if (item_offset < start_offset)
                    break;
                offset_v = dissect_nt_acl(pkt, item_offset, drep);
                if (offset_v > end_offset)
                    end_offset = offset_v;
            }
            break;
        default:
            end_offset = offset_v;
            break;
    }
    return offset_v;
}

int dissect_nt_trans_data_request(struct dpi_pkt_st *pkt, struct smb_info *pinfo, int offset, 
    int len, nt_trans_data *ntd, uint16_t bc, struct smb_session *si)
{
    int old_offset = offset;
    uint16_t bcp = bc;

    switch(ntd->subcmd) 
    {
    case NT_TRANS_CREATE:
        // security descriptor
        if (ntd->sd_len)
            offset = dissect_nt_sec_desc(pkt, offset, NULL);
        // extended attributes
        if (ntd->ea_len) 
        {
            int min = ntd->ea_len < sizeof(pinfo->SmbExtAttr) ? ntd->ea_len : sizeof(pinfo->SmbExtAttr);
            memcpy(pinfo->SmbExtAttr,pkt->payload+offset, min);
            offset += ntd->ea_len;
        }
        break;
    case NT_TRANS_IOCTL:
        offset += bc;
        break;
    case NT_TRANS_SSD:
        break;
    case NT_TRANS_NOTIFY:
        break;
    case NT_TRANS_RENAME:
        break;
    case NT_TRANS_QSD:
        break;
    case NT_TRANS_GET_USER_QUOTA:
        break;
    case NT_TRANS_SET_USER_QUOTA:
        break;
    }
    /* ooops there were data we didn't know how to process */
    if ((offset-old_offset) < bc) {
        offset += bc - (offset-old_offset);
    }
    return offset;
}

int dissect_nt_trans_param_request(struct dpi_pkt_st *pkt, struct smb_info *pinfo, int offset, 
    int len, nt_trans_data *ntd, uint16_t bc, struct smb_session *si)
{
    uint32_t fn_len, create_flags, access_mask, share_access, create_options;
    char fn[256] = {0};
    switch(ntd->subcmd) 
    {
    case NT_TRANS_CREATE:
        // Create flags
        create_flags = get_uint32_t(pkt->payload, offset);
        offset += 4;
        bc -= 4;
        // root directory fid
        //proto_tree_add_item(tree, hf_smb_root_dir_fid, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        COUNT_BYTES(4);
        // nt access mask
        access_mask = get_uint32_t(pkt->payload, offset);
        offset += 4;
        bc -= 4;
        // allocation size
        COUNT_BYTES(8);
        // todo .... Extended Attributes -----------------------------------------------------------------------------------------------------------------------
        int mask = get_uint32_t(pkt->payload, offset);
        //offset = dissect_file_ext_attr_bits(tvb, tree, offset, 4, mask);
        bc -= 4;
        // share access
        share_access = get_uint32_t(pkt->payload, offset);
        offset += 4;
        bc -= 4;
        // create disposition
        COUNT_BYTES(4);
        // create options
        create_options = get_uint32_t(pkt->payload, offset);
        offset += 4;
        bc -= 4;
        // sd length
        ntd->sd_len = get_uint32_t(pkt->payload, offset);
        COUNT_BYTES(4);
        // ea length
        ntd->ea_len = get_uint32_t(pkt->payload, offset);
        COUNT_BYTES(4);
        // file name len
        fn_len = get_uint32_t(pkt->payload, offset);
        COUNT_BYTES(4);
        // impersonation level
        COUNT_BYTES(4);
        // security flags
        offset += 1;
        bc -= 1;
        // May need to skip alignment padding
        if (offset&1) 
            offset += 1;
        // file name
        get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len,
                0, 0, &bc, fn, sizeof(fn));
        if (strlen(fn) > 0) 
        {
            memcpy(pinfo->FileName,fn,sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES(fn_len);
        }
        break;
    case NT_TRANS_IOCTL:
        break;
    case NT_TRANS_SSD:
        // fid
        pinfo->FileId = get_uint16_t(pkt->payload, offset);
        offset += 2;
        // reserved bytes
        offset += 2;
        // security information
        offset += 4;
        break;
    case NT_TRANS_NOTIFY:
        break;
    case NT_TRANS_RENAME:
        break;
    case NT_TRANS_QSD:
        // fid
        pinfo->FileId = get_uint16_t(pkt->payload, offset);
        offset += 2;
        // 2 reserved bytes
        offset += 2;
        // security information
        offset += 4;
        break;
    case NT_TRANS_GET_USER_QUOTA:
        break;
    case NT_TRANS_SET_USER_QUOTA:
        break;
    }
    return offset;
}

int dissect_nt_transaction_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    uint8_t wc, sc;
    uint16_t subcmd, bc;
    uint32_t pc = 0, pd = 0, po = 0, dc = 0, od = 0, dd = 0;
    uint32_t td = 0, tp = 0,padcnt;
    nt_trans_data ntd;
    memset(&ntd, 0, sizeof(ntd));
    WORD_COUNT;
    offset += 3;
    tp = get_uint32_t(pkt->payload, offset);
    offset += 4;
    td = get_uint32_t(pkt->payload, offset);
    offset += 4;
    if (wc >= 19)
        offset += 8;
    pc = get_uint32_t(pkt->payload, offset);
    offset += 4;
    po = get_uint32_t(pkt->payload, offset);
    offset += 4;
    if (wc < 19)
        offset += 4;
    dc = get_uint32_t(pkt->payload, offset);
    offset += 4;
    od = get_uint32_t(pkt->payload, offset);
    offset += 4;
    // data displacement
    if (wc < 19)
    {
        dd = get_uint32_t(pkt->payload, offset);
        offset += 4;
    }
    // setup count
    if (wc >= 19)
    {
        sc = get_uint32_t(pkt->payload, offset);
        offset += 1;
        subcmd = get_uint16_t(pkt->payload, offset);
        ntd.subcmd = subcmd;
    }
    else
        sc = 0;
    
    offset += 2;
    if (sc) 
        offset += sc*2;
    BYTE_COUNT;
/*
    if ( (td && (td != dc)) || (tp && (tp != pc)) ) 
    {
        if (pc) 
        {
                r_fd = smb_trans_defragment(tree, pinfo, tvb,
                                 po, pc, pd, td+tp, si);
        }
        if ((r_fd == NULL) && dc) {
            r_fd = smb_trans_defragment(tree, pinfo, tvb,
                                 od, dc, dd+tp, td+tp, si);
        }
    }
    if (r_fd) {
        proto_item *frag_tree_item;

        pd_tvb = tvb_new_chain(tvb, r_fd->tvb_data);
        add_new_data_source(pinfo, pd_tvb, "Reassembled SMB");

        show_fragment_tree(r_fd, &smb_frag_items, tree, pinfo, pd_tvb, &frag_tree_item);
    }
 **/
    if (0/*pd_tvb*/)
    {
/*
        // we have reassembled data, grab param and data from there,重组
        dissect_nt_trans_param_request(pd_tvb, pinfo, 0, tree, tp,
            &ntd, (guint16)tvb_reported_length(pd_tvb), nti, si);
        dissect_nt_trans_data_request(pd_tvb, pinfo, tp, tree, td, &ntd, nti, si);
        COUNT_BYTES(bc);
*/
    } 
    //不重组
    else 
    {
        if (po > (guint32)offset) 
        {
            padcnt = po-offset;
            if (padcnt > bc)
                padcnt = bc;
            CHECK_BYTE_COUNT(padcnt);
            COUNT_BYTES(padcnt);
        }
        if (pc) 
        {
            CHECK_BYTE_COUNT(pc);
            dissect_nt_trans_param_request(pkt, pinfo, offset, pc, &ntd, bc, si);
            COUNT_BYTES(pc);
        }
        // data
        if (od > (guint32)offset) 
        {
            padcnt = od-offset;
            if (padcnt > bc)
                padcnt = bc;
            COUNT_BYTES(padcnt);
        }
        if (dc) 
        {
            CHECK_BYTE_COUNT(dc);
            dissect_nt_trans_data_request(pkt, pinfo, offset, pc, &ntd, bc, si);
            COUNT_BYTES(dc);
        }
    }
    
    END_OF_SMB
    return 0;
}
#endif

int dissect_tree_connect_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    int an_len, pwlen;
    char an[256] = {0};
    uint8_t      wc;
    uint16_t     bc;
    WORD_COUNT;
    BYTE_COUNT;
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    //path
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &an_len,
                0, 0, &bc, an, sizeof(an));
    if (an != NULL)
        memcpy(pinfo->ClientPath, an, sizeof(pinfo->ClientPath));
    END_OF_SMB
    return offset;
}

static int dissect_dfs_inconsistency_data(struct dpi_pkt_st *pkt, struct smb_info *pinfo, int offset, uint16_t *bcp, struct smb_session *si)
{
    int fn_len,bc;
    char fn[256] = {0};
    CHECK_BYTE_COUNT_TRANS_SUBR(2);
    pinfo->ReferralVersion = get_uint16_t(pkt->payload,offset);
    COUNT_BYTES_TRANS_SUBR(2);

    /* referral size */
    CHECK_BYTE_COUNT_TRANS_SUBR(2);
    COUNT_BYTES_TRANS_SUBR(2);

    /* referral server type */
    CHECK_BYTE_COUNT_TRANS_SUBR(2);
    COUNT_BYTES_TRANS_SUBR(2);

    /* referral flags */
    CHECK_BYTE_COUNT_TRANS_SUBR(2);
    offset += 2;
    *bcp  -= 2;

    /* node name */
    get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len,
                0, 0, (uint16_t*)&bc, fn, sizeof(fn));
    CHECK_STRING_TRANS_SUBR(fn);
    COUNT_BYTES_TRANS_SUBR(fn_len);

    return offset;
}

static int dissect_transaction2_request_data(struct dpi_pkt_st *pkt, struct smb_info *pinfo,int offset, int subcmd, uint16_t dc, struct smb_session *si)
{
    switch(subcmd) 
    {
        case 0x0011:    /*TRANS2_REPORT_DFS_INCONSISTENCY*/
            offset = dissect_dfs_inconsistency_data(pkt, pinfo, offset, &dc, si);
            break;
        default:
            break;
    }
    if (dc != 0)
        offset += dc;
    return offset;
}

int dissect_transaction_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    uint8_t wc, sc = 0;
    int so = offset;
    int sl = 0;
    uint16_t bc = 0;
    int an_len = 0;
    char an[256] = {0};
    int                   spo    = offset;
    int                   spc    = 0;
    uint16_t              od     = 0, po = 0, pc = 0, dc = 0, pd, dd = 0;
    int                   subcmd = -1;
    int                   padcnt;
    uint32_t               to;    
    //offset == 36(tcp) or offset == 114(udp: 含82字节 netBIOS 协议)
    WORD_COUNT;
    //wc == 16, si->cmd == 37
    if (wc == 8 || (wc == 9 && si->cmd == SMB_COM_TRANSACTION2_SECONDARY))
    {
        offset += 4;
        pc = get_uint16_t(pkt->payload, offset);
        offset += 2;
        po = get_uint16_t(pkt->payload, offset);
        offset += 2;
        pd = get_uint16_t(pkt->payload, offset);
        offset += 2;
        dc = get_uint16_t(pkt->payload, offset);
        offset += 2;
        od = get_uint16_t(pkt->payload, offset);
        offset += 2;
        dd = get_uint16_t(pkt->payload, offset);
        offset += 2;
        if (si->cmd == SMB_COM_TRANSACTION2 || si->cmd == SMB_COM_TRANSACTION2_SECONDARY)
        {
            //pinfo->FileId = get_uint16_t(pkt->payload, offset);
            offset += 2;
        }
        so = offset;
        sl = 0;
    }
    else
    {
        offset += 18;
        pc = get_uint16_t(pkt->payload, offset);
        offset += 2;
        po = get_uint16_t(pkt->payload, offset);
        offset += 2;
        dc = get_uint16_t(pkt->payload, offset);
        offset += 2;
        //data_offset
        od = get_uint16_t(pkt->payload, offset);
        offset += 2;
        dd = 0;
        //setup_count
        sc = get_uint8_t(pkt->payload,offset);
        offset += 2;
        //opcode
        so = offset;
        sl = sc * 2;
        //printf("------pc == %d, po == %d, dc == %d, od == %d, sc == %d, so == %d, sl == %d\n",pc,po,dc,od,sc,so,sl);
        if (sc)
        {
            switch(si->cmd)
            {
                case SMB_COM_TRANSACTION2:
                    subcmd = get_uint16_t(pkt->payload, offset);    //todo ....转主机序
                    break;
                case SMB_COM_TRANSACTION:
                    break;
            }
            offset += sl;
        }
    }
    BYTE_COUNT;
    //offset == 71
    if (wc != 8)
    {
        if (si->cmd == SMB_COM_TRANSACTION)
        {
            get_unicode_or_ascii_string(pkt, &offset, si->unicode, &an_len,
                0, 0, &bc, an, sizeof(an));
            COUNT_BYTES(an_len);
        }
    }
    spo = so;
    spc = offset - spo;
    
    if (po > offset) 
    {
        padcnt = po-offset;
        if (padcnt > bc)
            padcnt = bc;
        COUNT_BYTES(padcnt);
    }
    if (pc) 
    {
        CHECK_BYTE_COUNT(pc);
        switch(si->cmd) 
        {
        case SMB_COM_TRANSACTION2:
            offset = dissect_transaction2_request_parameters(pkt,pinfo, si, offset, subcmd, pc);
            bc -= pc;
            break;
        case SMB_COM_TRANSACTION:
            COUNT_BYTES(pc);
            break;
        }
    }
    if (od > offset) 
    {
        padcnt = od-offset;
        if (padcnt > bc)
            padcnt = bc;
        COUNT_BYTES(padcnt);
    }
    if (dc) 
    {
        CHECK_BYTE_COUNT(dc);
        switch(si->cmd) 
        {
        case SMB_COM_TRANSACTION2:
            offset = dissect_transaction2_request_data(pkt, pinfo,offset, subcmd, dc, si);
            bc -= dc;
            break;

        case SMB_COM_TRANSACTION:
            COUNT_BYTES(dc);
            break;
        }
    }
    
    const uint8_t* s_tvb = NULL;
    if (si->cmd == SMB_COM_TRANSACTION)
    {
        if (dd == 0)
        {
            if (sl) 
                s_tvb = pkt->payload+so;
            else
                s_tvb = NULL;
        }
    }
    
    if (0 >= strlen(an))
        goto endofcommand;
    if (strncmp("\\PIPE\\", an, 6) == 0 && s_tvb)
    {
        int an_len = strlen(an);
        memcpy(pinfo->PipeName, an, an_len < 256 ? an_len : 255);
        unsigned int off_index = (const uint8_t *)s_tvb - pkt->payload;
        if(off_index + 2 <= pkt->payload_len)
        {
            uint16_t function = get_uint16_t(s_tvb, 0);
            if(off_index + 4 <= pkt->payload_len)
            {
                switch (function)
                {
                    case PEEK_NAMED_PIPE:
                    case Q_NM_P_HAND_STATE:
                    case SET_NM_P_HAND_STATE:
                    case Q_NM_PIPE_INFO:
                    case TRANSACT_NM_PIPE:
                    case RAW_READ_NM_PIPE:
                    case RAW_WRITE_NM_PIPE:
                        pinfo->FileId = get_uint16_t(s_tvb, 2);
                        break;

                    case CALL_NAMED_PIPE:
                    case WAIT_NAMED_PIPE:
                    default:
                        break;
                }
            }
        }
    }
    else if (strncmp("\\MAILSLOT\\", an, 10) == 0)
    {
        //offset - 82: 82为 netBIOS 协议的长度
        //此处的 culoffset 是smb协议的绝对偏移量(从0开始)
        //int culoffset = spo + 2 + 2 + 2 + 2 - SMB_UDP_NETBIOS_LEN;
        memcpy(pinfo->MailslotName, an, strlen(an));
    }
    END_OF_SMB;
    return offset;
}

int dissect_transaction2_request_parameters(struct dpi_pkt_st *pkt, struct smb_info* pinfo, struct smb_session *si, int offset, int subcmd, uint16_t bc)
{
    char fn[256] = {0};
    int fn_len;
    switch(subcmd)
    {
        case 0x0000:
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(4);
            offset += 4;
            bc -= 4;
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            CHECK_BYTE_COUNT_TRANS(10);
            COUNT_BYTES_TRANS(10);
            //filename
            get_unicode_or_ascii_string(pkt,(uint32_t*) &offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x0001:
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            // Find First2 information level
            CHECK_BYTE_COUNT_TRANS(2);
            pinfo->InfoLevel = get_uint16_t(pkt->payload, offset);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x0002:
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x0003:
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            break;
        case 0x0004:
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            break;
        case 0x0005:
/*
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            //todo ....
            //get_stringz_enc(wmem_packet_scope(), tvb, offset, &fn_len, (si->unicode ? ENC_UTF_16|ENC_LITTLE_ENDIAN : ENC_ASCII|ENC_NA));
            //CHECK_STRING_TRANS(fn);
            //memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            //pinfo->FileNameCnt++;
            //COUNT_BYTES_TRANS(fn_len);
*/
            break;
        case 0x0006:
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            get_unicode_or_ascii_string(pkt,(uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x0007:
            // fid
            CHECK_BYTE_COUNT_TRANS(2);
            pinfo->FileId = get_uint16_t(pkt->payload, offset); //todo .... 转主机序
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            break;
        case 0x0008:
            // fid
            CHECK_BYTE_COUNT_TRANS(2);
            pinfo->FileId = get_uint16_t(pkt->payload, offset); //todo .... 转主机序
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            break;
        case 0x0009:
        case 0x000a:
        case 0x000e:
            break;
        case 0x000b:
            CHECK_BYTE_COUNT_TRANS(2);
            offset += 2;
            bc -= 2;
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x000c:
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            break;
        case 0x000d:
            CHECK_BYTE_COUNT_TRANS(4);
            COUNT_BYTES_TRANS(4);
            get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x0010:
            CHECK_BYTE_COUNT_TRANS(2);
            COUNT_BYTES_TRANS(2);
            get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;
        case 0x0011:
            get_unicode_or_ascii_string(pkt, (uint32_t*)&offset, si->unicode, (int*)&fn_len, 0, 0, &bc, fn, sizeof(fn));
            CHECK_STRING_TRANS(fn);
            memcpy(pinfo->FileName, fn, sizeof(pinfo->FileName));
            pinfo->FileNameCnt++;
            COUNT_BYTES_TRANS(fn_len);
            break;    
    }
    if (bc != 0)
        offset += bc;
    return offset;
}

int smb_compute_offset(const struct dpi_pkt_st* pkt, const int offset, uint32_t *offset_ptr)
{
    if (offset >= 0) 
    {
        if ((uint32_t) offset <= pkt->payload_len)
            *offset_ptr = offset;
        else 
            return 1;
    }
    else 
    {
        if ((guint) -offset <= pkt->payload_len)
            *offset_ptr = pkt->payload_len + offset;
        else
            return 2;
    }
    return 0;
}

uint32_t smb_reported_length_remaining(struct dpi_pkt_st* pkt, const int offset)
{
    guint abs_offset;
    int   exception;

    exception = smb_compute_offset(pkt, offset, &abs_offset);
    if (exception)
        return 0;
#if 0
    if (tvb->reported_length >= abs_offset)
        return tvb->reported_length - abs_offset;
#endif
    if(pkt->payload_len >= abs_offset)
        return pkt->payload_len - abs_offset;
    else
        return 0;
}

static int dissect_file_data_dcerpc(struct dpi_pkt_st *pkt,int offset, uint16_t bc, uint16_t datalen)
{
    int tvblen;
    if (bc > datalen) 
    {
        offset += bc-datalen;
        bc = datalen;
    }
    tvblen = smb_reported_length_remaining(pkt, offset);
    if (bc > tvblen)
        offset += tvblen;
    else
        offset += bc;
    return offset;
}

int dissect_file_data(struct dpi_pkt_st *pkt,int offset, uint16_t bc, uint16_t datalen)
{
    int tvblen;
    if (bc > datalen) 
    {
        offset += bc-datalen;
        bc = datalen;
    }
    tvblen = smb_reported_length_remaining(pkt, offset);
    if (bc > tvblen)
        offset += tvblen;
    else
        //file data
        offset += bc;
    return offset;
}

static int dissect_file_data_maybe_dcerpc(struct dpi_pkt_st *pkt,int offset, uint16_t bc,uint16_t datalen, uint32_t ofs)
{
    if ( ofs == 0)
        // dcerpc call
        return dissect_file_data_dcerpc(pkt, offset, bc, datalen);
    else
        // ordinary file data
        return dissect_file_data(pkt, offset, bc, datalen);
}

//#define CHUNLI_DEBUG  // 追踪信息 编译开关, 取消注释生效
#ifdef CHUNLI_DEBUG
#define DEBUG(...) (printf(__VA_ARGS__))
#define ABORT(...) {DEBUG(__VA_ARGS__);abort();}
#else
#define DEBUG(...) {}
#define ABORT(...) {}
#endif

struct smb_create_request_t
{
    uint8_t  WordCount;
    uint8_t  AndXCommand;
    uint8_t  Reserved1;
    uint16_t AndXOffset;
    uint8_t  Reserved2;
    uint16_t FileNameLen;
    uint32_t CreateFlags;
    uint32_t RootFID;
    uint32_t AccessMask;
    uint64_t AllocationSize;
    uint32_t FileAttributes;
    uint32_t ShareAccess;
    uint32_t Disposition;
    uint32_t CreateOptions;
    uint32_t Impersonation;
    uint8_t  SecurityFlags;
    uint16_t ByteCount;
    uint8_t  Padding;
} __attribute__((packed));
static int dissect_nt_create_andx_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_create_request_t))
    {
        DEBUG("smb_create_request_t 长度错误\n");
        return pkt->payload_len;
    }

    const struct smb_create_request_t *create_request = (const struct smb_create_request_t*)(pkt->payload + offset);

    //更新 offset
    uint32_t offset_to_filename = offset + sizeof(struct smb_create_request_t);
    int msglen = offsetof(struct smb_create_request_t, Padding) + create_request->ByteCount;
    offset += msglen;

    //协议有效性
    if(0 != create_request->Reserved1 || 0 != create_request->Reserved2)
    {
        DEBUG("smb_create_request reserved 消息错误\n");
        return offset;
    }

    //动作有效性
    if(0 == create_request->FileNameLen)
    {
        DEBUG("FileNameLen 空\n");
        return offset;
    }

    int  len;
    char filename[1024];
    uint16_t filename_len = create_request->FileNameLen;
    memset(filename, 0, sizeof(filename));

    int ret = get_unicode_or_ascii_string(pkt, &offset_to_filename, 1, &len, 0, 0, &filename_len, filename, (int)(sizeof(filename)));
    if(ret == -1)
    {
        DEBUG("smb_create_request 没有找到文件名\n");
        return offset;
    }
    //printf("filename [%s]\n", filename);

    //在这里 区分不出来, 目录/文件, 要在 响应里面才知道
    snprintf(pinfo->FileName, sizeof(pinfo->FileName), "%s", filename);
    pinfo->FileNameCnt++;
    return offset;
}

static int dissect_nt_create_andx_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    uint8_t          wc, cmd    = 0xff;
    uint16_t         andxoffset = 0;
    uint16_t         fid        = 0;
    uint16_t         ftype,bc;
    uint8_t          isdir;
    uint32_t         mask;
    WORD_COUNT;
    cmd = pkt->payload[offset++];
    // reserved byte
    offset += 1;
    andxoffset = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // oplock level
    offset += 1;
    fid = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // create action
    offset += 4;
    // create time
    offset += 8;
    // access time
    offset += 8;
    // last write time
    offset += 8;
    // last change time
    offset += 8;
    // Extended File Attributes
    mask = get_uint32_t(pkt->payload, offset);
    offset += 4;
    // allocation size
    offset += 8;
    // end of file
    pinfo->end_of_file = get_uint64_t(pkt->payload, offset);
    offset += 8;
    // File Type
    ftype = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // IPC State
    offset += 2;
    // is directory
    isdir = get_uint8_t(pkt->payload, offset);
    offset += 1;
 
    // Volume GUID
    offset += 16;

    // Server unique 64-bit file ID
    offset += 8;

    // Maximal Access Rights
    pinfo->AccessRights = get_uint32_t(pkt->payload, offset);
    offset += 4;

    if (wc >= 42) 
    {
        offset += 16;
        // The file ID comes next
        offset += 8;
        offset += 4;
        offset += 4;
    }
    BYTE_COUNT
    
    END_OF_SMB
    return offset;
}

static int dissect_transaction_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    uint8_t sc, wc;
    uint16_t od = 0, po = 0, pc = 0, pd = 0, dc = 0, dd = 0, td = 0, tp = 0, bc;
    int padcnt;
    int dissected_trans;
    int save_fragmented;
    
    WORD_COUNT;
    // total param count, only a 16bit integer here
    tp = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // total data count, only a 16 bit integer here
    td = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // reserved bytes
    offset += 2;
    // param count
    pc = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // param offset
    po = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // param disp
    pd = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // data count
    dc = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // data offset
    od = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // data disp
    dd = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // setup count
    sc = pkt->payload[offset++];
    //reserved byte
    offset += 1;
    offset += 2*sc;
    BYTE_COUNT;
    if (po > offset)
    {
        padcnt = po-offset;
        if (padcnt > bc)
            padcnt = bc;
        COUNT_BYTES(padcnt);
    }
    COUNT_BYTES(pc);
    if (od > offset)
    {
        padcnt = od-offset;
        if (padcnt > bc)
            padcnt = bc;
        COUNT_BYTES(padcnt);
    }
    if (dc > bc)
        dc = bc;
    COUNT_BYTES(dc);
#if 0
    if (si->cmd == SMB_COM_TRANSACTION2) {
        /* TRANSACTION2 parameters*/
        dissect_transaction2_response_data(d_tvb, pinfo, tree, si);
    }
#endif
    
    END_OF_SMB
    return offset;
}

static int dissect_delete_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    int         fn_len;
    char fn[256] = {0};
    guint8      wc;
    guint16     bc;

    WORD_COUNT;

    // search attributes
    pinfo->searchAttr = get_uint16_t(pkt->payload, offset);
    offset += 2;
    BYTE_COUNT;
    // buffer format
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    // file name
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len, 0, 0, &bc, fn, sizeof(fn));
    if (strlen(fn) <= 0)
        goto endofcommand;
    memcpy(pinfo->FileName, fn, strlen(fn));
    pinfo->FileNameCnt++;
    COUNT_BYTES(fn_len);
    END_OF_SMB
    return offset;
}

static int dissect_rename_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    int fn_len;
    char fn[256] = {0};
    char old_name[256] = {0};
    uint16_t bc;
    uint8_t      wc;
    
    WORD_COUNT;
    // search attributes
    pinfo->searchAttr = get_uint16_t(pkt->payload, offset);
    offset += 2;

    BYTE_COUNT;
    // buffer format
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    // old file name
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len, 0, 0, &bc, fn, sizeof(fn));
    if (strlen(fn) <= 0)
        goto endofcommand;
    memcpy(old_name, fn, strlen(fn));
    memset(fn, 0, sizeof(fn));
    COUNT_BYTES(fn_len);
    // buffer format 
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    // file name
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len, 0, 0, &bc, fn, sizeof(fn));
    if (strlen(fn) <= 0)
        goto endofcommand;
    memcpy(pinfo->FileName, fn, strlen(fn));
    pinfo->FileNameCnt++;
    COUNT_BYTES(fn_len);
    
    END_OF_SMB
    return offset;
}

static int dissect_nt_rename_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    int         fn_len;
    char        fn[256] = {0};
    uint8_t     wc;
    uint16_t    bc;

    WORD_COUNT;
    // search attributes
    pinfo->searchAttr = get_uint16_t(pkt->payload, offset);
    offset += 2;
    offset += 2;
    offset += 4;
    BYTE_COUNT;
    // buffer format
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    // old file name
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len, 0, 0, &bc, fn, sizeof(fn));
    if (strlen(fn) <= 0)
        goto endofcommand;
    memset(fn, 0, sizeof(fn));
    COUNT_BYTES(fn_len);
    // buffer format
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    // file name
    get_unicode_or_ascii_string(pkt, &offset, si->unicode, &fn_len, 0, 0, &bc, fn, sizeof(fn));
    if (strlen(fn) <= 0)
        goto endofcommand;
    memcpy(pinfo->FileName, fn, strlen(fn));
    pinfo->FileNameCnt++;
    COUNT_BYTES(fn_len);

    END_OF_SMB
    return offset;
}

//请求：获取 chunk_len、chunk_offset
static int dissect_read_file_request(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    uint8_t       wc;
    uint16_t      cnt = 0, bc;
    uint32_t      ofs = 0;
    uint32_t      fid;
    WORD_COUNT;
    fid = get_uint16_t(pkt->payload, offset);
    pinfo->FileId = fid;
    offset += 2;
    // read count
    cnt = get_uint16_t(pkt->payload, offset);
    pinfo->FileChunkLen = cnt;
    offset += 2;
    // offset
    ofs = get_uint32_t(pkt->payload, offset);
    offset += 4;
    // remaining
    offset += 2;
    pinfo->FileChunkOffset = ofs;
    BYTE_COUNT;

    END_OF_SMB
    return offset;
}
//响应：获取 chunk_len
static int dissect_read_file_response(struct flow_info *flow, struct dpi_pkt_st *pkt, uint32_t offset, struct smb_session *si, struct smb_info *pinfo)
{
    uint16_t cnt = 0, bc;
    uint8_t wc;
    int fid = 0;
    uint32_t datalen = 0, dataoffset = 0;
    uint32_t tvblen;
    WORD_COUNT;
    // read count
    cnt = get_uint16_t(pkt->payload, offset);
    offset += 2;
    // 8 reserved bytes
    offset += 8;
    BYTE_COUNT;
    //buffer format
    CHECK_BYTE_COUNT(1);
    COUNT_BYTES(1);
    // data len
    CHECK_BYTE_COUNT(2);
    datalen = get_uint16_t(pkt->payload, offset);
    COUNT_BYTES(2);
    dataoffset = offset;
    // file data, might be DCERPC on a pipe
    if (bc) 
    {
        offset = dissect_file_data_maybe_dcerpc(pkt, offset, bc, bc, 0);
        bc = 0;
    }
    // feed the export object tap listener
    tvblen = smb_reported_length_remaining(pkt, dataoffset);
    END_OF_SMB

    return offset;
}

static int dissect_smb(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(seq);
    UNUSED(direction);
    UNUSED(flag);
    uint32_t offset = 0;
    uint8_t flags;
    uint16_t flags2;
    struct smb_session *si;
//    smb_saved_info_t *sip      = NULL;
//    smb_saved_info_key_t key;
//    smb_saved_info_key_t *new_key;
    uint8_t errclass = 0;
//    uint16_t errcode  = 0;
//    uint32_t pid_mid;
//    nstime_t t, deltat;
    struct dpi_pkt_st pkt;
    struct smb_info info;

    memset(&info, 0, sizeof(info));

    pkt.payload = payload;
    pkt.payload_len = payload_len;
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct smb_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct smb_session));
        si = (struct smb_session *)flow->app_session;
    }
    
    si = (struct smb_session *)flow->app_session;
    if (payload_len < 8)
        return 0;
    
    short smb0 = get_uint8_t(payload, 4);
    char smb1 = get_uint8_t(payload, 5);
    char smb2 = get_uint8_t(payload, 6);
    char smb3 = get_uint8_t(payload, 7);
    if (0xff != smb0 || 'S' != smb1 || 'M' != smb2 || 'B' != smb3)
    {
        if(payload_len > 86)
        {
            //netbios
            offset = SMB_UDP_NETBIOS_LEN;
            smb0 = get_uint8_t(payload, offset);
            smb1 = get_uint8_t(payload, offset+1);
            smb2 = get_uint8_t(payload, offset+2);
            smb3 = get_uint8_t(payload, offset+3);
            if ((0xff != smb0 || 'S' != smb1 || 'M' != smb2 || 'B' != smb3))
                return 0;
            else
                goto udpStart;
        }
        return 0;
    }
    //netbios header
    offset += 4;

    // start off using the local variable, we will allocate a new one if we need to
    udpStart:
    if (dpi_get_uint8(&pkt, offset + 4, &si->cmd) == -1) return 0;
#if 0
    if (si->cmd != SMB_COM_NEGOTIATE 
            && si->cmd != SMB_COM_SESSION_SETUP_ANDX 
            && si->cmd != SMB_COM_TREE_CONNECT_ANDX)
        return 0;
#endif
#if 1
    if (si->cmd != 0xff) 
    {
        info.Command = val_to_string(si->cmd, smb_cmd_vals);
        //if (info.Command == NULL)
            //return 0;
    } 
    else 
        return 0;
#endif
    if (dpi_get_uint8(&pkt, offset + 9, &flags) == -1) return 0;
    /*
    * XXX - in some SMB-over-OSI-transport and SMB-over-Vines traffic,
    * the direction flag appears never to be set, even for what appear
    * to be replies.  Do some SMB servers fail to set that flag,
    * under the assumption that the client knows it's a reply because
    * it received it?
    */
    if ((flags & SMB_FLAGS_DIRN) == 0)
        si->request = 1;
    else
        si->request = 0;
    info.LoadWay = si->request;
    if (dpi_get_le16(&pkt, offset + 10, &flags2) == -1) return 0;
    if (flags2 & 0x8000) {
        si->unicode = 1; /* Mark them as Unicode */
    } else {
        si->unicode = 0;
    }
    if (dpi_get_le16(&pkt, offset + 24, &si->tid) == -1) return 0;
    if (dpi_get_le16(&pkt, offset + 26, &si->pid) == -1) return 0;
    if (dpi_get_le16(&pkt, offset + 28, &si->uid) == -1) return 0;
    if (dpi_get_le16(&pkt, offset + 30, &si->mid) == -1) return 0;
    //pid_mid = (si->pid << 16) | si->mid;
    si->info_level = -1;
    si->info_count = -1;
    
    offset += 4;  /* Skip the marker */


    if ((si->request)
            &&  (si->mid == 0)
            &&  (si->uid == 0)
            &&  (si->pid == 0)
            &&  (si->tid == 0)) {
        /* this is a broadcast SMB packet, there will not be a reply.
        We don't need to do anything
        */
        si->unidir = 1;
    } else if ((si->cmd == SMB_COM_NT_CANCEL)                  /* NT Cancel */
            || (si->cmd == SMB_COM_TRANSACTION_SECONDARY)    /* Transaction Secondary */
            || (si->cmd == SMB_COM_TRANSACTION2_SECONDARY)   /* Transaction2 Secondary */
            || (si->cmd == SMB_COM_NT_TRANSACT_SECONDARY)) { /* NT Transaction Secondary */
        /* Ok, we got a special request type. This request is either
            an NT Cancel or a continuation relative to a real request
            in an earlier packet.  In either case, we don't expect any
            responses to this packet.  For continuations, any later
            responses we see really just belong to the original request.
            Anyway, we want to remember this packet somehow and
            remember which original request it is associated with so
            we can say nice things such as "This is a Cancellation to
            the request in frame x", but we don't want the
            request/response matching to get messed up.

            The only thing we do in this case is trying to find which original
            request we match with and insert an entry for this "special"
            request for later reference. We continue to reference the original
            requests smb_saved_info_t but we don't touch it or change anything
            in it.
        */

        si->unidir = 1;  /*we don't expect an answer to this one*/

    } else { /* normal bidirectional request or response */
        si->unidir = 0;
    }
    /* smb command */
    //proto_tree_add_uint(htree, hf_smb_cmd, tvb, offset, 1, si->cmd);
    offset += 1;

    if (flags2 & 0x4000) {
        /* handle NT 32 bit error code */
        if (dpi_get_le32(&pkt, offset + 30, &si->nt_status) == -1) return 0;
        offset += 4;
    } else {
        /* handle DOS error code & class */
        if (dpi_get_uint8(&pkt, offset, &errclass) == -1) return 0;
        offset += 1;

        /* reserved byte */
        //proto_tree_add_item(htree, hf_smb_reserved, tvb, offset, 1, ENC_NA);
        offset += 1;

        /* error code */
        /* XXX - the type of this field depends on the value of
        * "errcls", so there is isn't a single value_string array
        * fo it, so there can't be a single field for it.
        */
        //errcode = tvb_get_letohs(tvb, offset);
        //proto_tree_add_uint_format_value(htree, hf_smb_error_code, tvb,
        //    offset, 2, errcode, "%s",
        //    decode_smb_error(errclass, errcode));
        offset += 2;
    }
    /* flags */
    offset += 1;
    //offset = dissect_smb_flags(tvb, htree, offset);

    /* flags2 */
    offset += 2;
    //offset = dissect_smb_flags2(tvb, htree, offset);

    /*
     * The document at
     *
     *    http://www.samba.org/samba/ftp/specs/smbpub.txt
     *
     * (a text version of "Microsoft Networks SMB FILE SHARING
     * PROTOCOL, Document Version 6.0p") says that:
     *
     *    the first 2 bytes of these 12 bytes are, for NT Create and X,
     *    the "High Part of PID";
     *
     *    the next four bytes are reserved;
     *
     *    the next four bytes are, for SMB-over-IPX (with no
     *    NetBIOS involved) two bytes of Session ID and two bytes
     *    of SequenceNumber.
     *
     * Network Monitor 2.x dissects the four bytes before the Session ID
     * as a "Key", and the two bytes after the SequenceNumber as
     * a "Group ID".
     *
     * The "High Part of PID" has been seen in calls other than NT
     * Create and X, although most of them appear to be I/O on DCE RPC
     * pipes opened with the NT Create and X in question.
     */
    //proto_tree_add_item(htree, hf_smb_pid_high, tvb, offset, 2, ENC_LITTLE_ENDIAN);
    if (dpi_get_le16(&pkt, offset, &info.PIDHigh) == -1) return 0;
    offset += 2;
    /*
        * According to http://ubiqx.org/cifs/SMB.html#SMB.4.2.1
        * and http://ubiqx.org/cifs/SMB.html#SMB.5.5.1 the 8
        * bytes after the "High part of PID" are an 8-byte
        * signature ...
    */

    if (dpi_get_hex_string(&pkt, offset, 8, info.Signature, sizeof(info.Signature)) == -1) return 0;
    offset += 8;
    offset += 2;

    /* TID
    * TreeConnectAndX(0x75) is special, here it is the mere fact of
    * having a response that means that the share was mapped and we
    * need to track it

    if (!pinfo->fd->flags.visited && (si->cmd == 0x75) && !si->request) {
        offset = dissect_smb_tid(tvb, pinfo, htree, offset, (guint16)si->tid, TRUE, FALSE, si);
    } else {
        offset = dissect_smb_tid(tvb, pinfo, htree, offset, (guint16)si->tid, FALSE, FALSE, si);
    }
    */
    if (dpi_get_le16(&pkt, offset, &info.TID) == -1) return 0;
    offset += 2;
    
    /* PID */
    if (dpi_get_le16(&pkt, offset, &si->pid) == -1) return 0;
    info.PIDLow = si->pid;
    offset += 2;
    
    /* UID */
    if (dpi_get_le16(&pkt, offset, &si->uid) == -1) return 0;
    info.UID = si->uid;
    offset += 2;

    /* MID */
    if (dpi_get_le16(&pkt, offset, &si->mid) == -1) return 0;
    info.MID = si->mid;
    offset += 2;
    //offset == 36(tcp) or offset == 114(udp: 含82字节 netBIOS 协议)
    dissect_smb_command(flow, &pkt, offset, si->cmd, si, &info);
    write_smb_log(flow, direction, &info, NULL);
    return 0;
}


static void init_smb_dissector(void)
{
    dpi_register_proto_schema(smb_field_array,EM_SMB_MAX,"smb");
    port_add_proto_head(IPPROTO_TCP, 139, PROTOCOL_SMB);
    port_add_proto_head(IPPROTO_TCP, 445, PROTOCOL_SMB);

    port_add_proto_head(IPPROTO_UDP, 139, PROTOCOL_SMB);
    port_add_proto_head(IPPROTO_UDP, 445, PROTOCOL_SMB);

    tcp_detection_array[PROTOCOL_SMB].proto = PROTOCOL_SMB;
    tcp_detection_array[PROTOCOL_SMB].identify_func = identify_smb;
    tcp_detection_array[PROTOCOL_SMB].dissect_func = dissect_smb;

    udp_detection_array[PROTOCOL_SMB].proto = PROTOCOL_SMB;
    udp_detection_array[PROTOCOL_SMB].identify_func = identify_udp_smb;
    udp_detection_array[PROTOCOL_SMB].dissect_func = dissect_smb;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_SMB].excluded_protocol_bitmask, PROTOCOL_SMB);
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_SMB].excluded_protocol_bitmask, PROTOCOL_SMB);


    map_fields_info_register(smb_field_array,PROTOCOL_SMB, EM_SMB_MAX,"smb");

    pschema_t *schema = dpi_pschema_get_proto("smb");
    pschema_register_field(schema, "clusterShift", YA_FT_STRING, "desc");
    pschema_register_field(schema, "chunkShift", YA_FT_STRING, "desc");
    pschema_register_field(schema, "compressUnitShift", YA_FT_STRING, "desc");
    pschema_register_field(schema, "compressFormat", YA_FT_STRING, "desc");
    pschema_register_field(schema, "compressFileSize", YA_FT_STRING, "desc");
    pschema_register_field(schema, "dltPending", YA_FT_STRING, "desc");
    pschema_register_field(schema, "numofLinks", YA_FT_STRING, "desc");
    pschema_register_field(schema, "nameofFileSys", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lenofFileSys", YA_FT_STRING, "desc");
    pschema_register_field(schema, "fileSysAttrs", YA_FT_STRING, "desc");
    pschema_register_field(schema, "deviceType", YA_FT_STRING, "desc");
    pschema_register_field(schema, "bytesPersec", YA_FT_STRING, "desc");
    pschema_register_field(schema, "secAllocPerUnit", YA_FT_STRING, "desc");
    pschema_register_field(schema, "total_freeallocUnits", YA_FT_STRING, "desc");
    pschema_register_field(schema, "volLabel", YA_FT_STRING, "desc");
    pschema_register_field(schema, "volSerNbr", YA_FT_STRING, "desc");
    pschema_register_field(schema, "unitAva", YA_FT_STRING, "desc");
    pschema_register_field(schema, "unit", YA_FT_STRING, "desc");
    pschema_register_field(schema, "fileSys", YA_FT_STRING, "desc");
    pschema_register_field(schema, "reqEchData", YA_FT_STRING, "desc");
    pschema_register_field(schema, "freeUnits", YA_FT_STRING, "desc");
    pschema_register_field(schema, "blksize", YA_FT_STRING, "desc");
    pschema_register_field(schema, "blkPerunit", YA_FT_STRING, "desc");
    pschema_register_field(schema, "totalUnits", YA_FT_STRING, "desc");
    pschema_register_field(schema, "compltnFilter", YA_FT_STRING, "desc");
    pschema_register_field(schema, "watchTree", YA_FT_STRING, "desc");
    pschema_register_field(schema, "cliPrimaryDom", YA_FT_STRING, "desc");
    pschema_register_field(schema, "chall", YA_FT_STRING, "desc");
    pschema_register_field(schema, "srvtimeZone", YA_FT_STRING, "desc");
    pschema_register_field(schema, "systime", YA_FT_STRING, "desc");
    pschema_register_field(schema, "capabilities", YA_FT_STRING, "desc");
    pschema_register_field(schema, "sesskey", YA_FT_STRING, "desc");
    pschema_register_field(schema, "maxRawSz", YA_FT_STRING, "desc");
    pschema_register_field(schema, "maxBuffSz", YA_FT_STRING, "desc");
    pschema_register_field(schema, "maxNumVcs", YA_FT_STRING, "desc");
    pschema_register_field(schema, "secMod", YA_FT_STRING, "desc");
    pschema_register_field(schema, "dialectIndx", YA_FT_STRING, "desc");
    pschema_register_field(schema, "dialectStr", YA_FT_STRING, "desc");
    pschema_register_field(schema, "natFileSys", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lastWrtDate", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lastAccTime", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lastAccDate", YA_FT_STRING, "desc");
    pschema_register_field(schema, "crtTime", YA_FT_STRING, "desc");
    pschema_register_field(schema, "crtDate", YA_FT_STRING, "desc");
    pschema_register_field(schema, "oldFileNm", YA_FT_STRING, "desc");
    pschema_register_field(schema, "numLckRng", YA_FT_STRING, "desc");
    pschema_register_field(schema, "numUnLckRng", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lckType", YA_FT_STRING, "desc");
    pschema_register_field(schema, "offset", YA_FT_STRING, "desc");
    pschema_register_field(schema, "name", YA_FT_STRING, "desc");
    pschema_register_field(schema, "subCommand", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lastWrtTime", YA_FT_STRING, "desc");
    pschema_register_field(schema, "lastAcc", YA_FT_STRING, "desc");
    pschema_register_field(schema, "oplockLev", YA_FT_STRING, "desc");
    pschema_register_field(schema, "shareAcc", YA_FT_STRING, "desc");
    pschema_register_field(schema, "desireAcc", YA_FT_STRING, "desc");
    pschema_register_field(schema, "isDir", YA_FT_STRING, "desc");
    pschema_register_field(schema, "timeout", YA_FT_STRING, "desc");
    pschema_register_field(schema, "allosz", YA_FT_STRING, "desc");
    pschema_register_field(schema, "FID", YA_FT_STRING, "desc");
    pschema_register_field(schema, "unicodePass", YA_FT_STRING, "desc");
    pschema_register_field(schema, "accntnm", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrSecFeature", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrFlag2", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrFlags", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrSrvStatus", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrTID", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrCommand", YA_FT_STRING, "desc");
    pschema_register_field(schema, "hdrPID", YA_FT_STRING, "desc");
    pschema_register_field(schema, "fileAtt", YA_FT_STRING, "desc");
    pschema_register_field(schema, "usrID", YA_FT_STRING, "desc");
    pschema_register_field(schema, "endofFile", YA_FT_STRING, "desc");
    pschema_register_field(schema, "maxLenofFileSys", YA_FT_STRING, "desc");
    // pschema_register_field(schema, "user", YA_FT_STRING, "desc");
    pschema_register_field(schema, "accRghts", YA_FT_STRING, "desc");
    pschema_register_field(schema, "infoFileNm", YA_FT_STRING, "desc");
    pschema_register_field(schema, "streamInfo", YA_FT_STRING, "desc");

    return;
}

static __attribute((constructor)) void     before_init_smb(void){
    register_tbl_array(TBL_LOG_SMB, 0, "smb", init_smb_dissector);
}
