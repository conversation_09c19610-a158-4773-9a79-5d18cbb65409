﻿/****************************************************************************************
 * 文 件 名 : dpi_bgp.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chenzq              2019/06/19
编码: chenzq            2019/06/19
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef __DPI_BGP__
#define __DPI_BGP__


typedef struct _capability_infi {
	uint8_t		cap_type;
	uint8_t		cap_len;
	uint8_t		cap_action;

} capcability_info;

typedef struct _community_info {
	uint16_t	as;
	uint16_t	value;
} community_info;

/* evpn nlri (reach or unreach) */
typedef struct _mp_evpn_nlri {
	uint8_t		route_type;
	uint8_t		evpn_length;
	uint8_t		route_distinguisher[8];
	uint8_t		esi_type; // type

	uint8_t		nlri[256];
	uint16_t	length;
} mp_evpn_nlri;

/* ls nlri (reach or unreach */
typedef struct _mp_ls_nlri {
	uint16_t	nlri_type;
	uint16_t	nlri_length;

	// ...

	uint8_t		nlri[256];
	uint16_t	length;
} mp_ls_nlri;

typedef struct _mp_nlri {
	uint8_t		nlri[256];
	uint16_t	length;

	mp_evpn_nlri evpn;
	mp_ls_nlri	ls;

	uint8_t		type;  // 自定义的 nlri type： 1=mp_evpn_nlri 2=mp_ls_nlri
} mp_nlri;

struct bgp_info
{
    uint16_t bgp_len;
    uint8_t bgp_type;
    const char *bgp_type_str;

    //open
    uint8_t bgp_open_version;
    uint16_t bgp_open_myas;
    uint16_t bgp_open_holdtime;
    char bgp_open_identifier[32];
    uint8_t bgp_open_opt_len;
    uint8_t bgp_open_ah_code;
    char bgp_open_ah_data[1024];
	uint8_t bgp_open_param_type;
	capcability_info cap_info[256];
	uint16_t 		 cap_size;


	//update
	uint16_t bgp_update_nlri_length;
	char 	 bgp_update_withdrawn_routes[256]; // withdrawn routes
    uint16_t bgp_update_wr_len;//withdrawn_routes_length
    uint16_t bgp_update_tpa_len;//total_path_attribute_length
    uint8_t  bgp_update_origin;
    const char * bgp_update_origin_str;
    int32_t bgp_update_number_as_segment;
	uint32_t bgp_update_as_path[100]; // asn的集合
	uint8_t bgp_update_asn_num; // asn的数量
    char bgp_update_next_hop[32];
    uint32_t bgp_update_med;//multiple exit discriminator
    uint32_t bgp_update_local_pref;
    char bgp_update_communities[1024];
	community_info comm_info[256];
	int comm_size;
	int bgp_ext_com_type_high[256];
	int bgp_ext_com_type_size;
    uint32_t bgp_update_aggregator_as;
    char bgp_update_aggregatorIp[32];
    char bgp_update_originator_id[32];//originator identifier
    char bgp_update_cluster_list[1024];
    uint16_t bgp_update_mp_reach_nlri_afi;
	uint16_t bgp_update_mp_unreach_nlri_afi;
    uint8_t bgp_update_mp_reach_nlri_safi;
	uint8_t bgp_update_mp_unreach_nlri_safi;
	uint8_t bgp_update_mp_reach_nexthop[64];  // ipv4 or ipv6
	uint8_t bgp_update_mp_reach_nexthop_len;
	mp_nlri bgp_update_mp_reach_nlri;
	mp_nlri bgp_update_mp_unreach_nlri;
    char bgp_update_network_layer_rechability[1024];//Network Layer Reachability
    char bgp_update_mp_rechability[1024];
    char bgp_update_mp_unrechability[1024];


    //notification
    uint8_t bgp_notification_mae_code;//major error ocde
    const char *bgp_notification_mae_str;
    uint8_t bgp_notification_mie_code;//minor code
	char   	bgp_notification_err_data[1024];
	uint16_t bgp_notification_err_length;

    //route refresh
    uint16_t bgp_route_refresh_afi;
    uint8_t bgp_route_refresh_subtype;
    const char *bgp_route_refresh_subtype_str;
    uint8_t bgp_route_refresh_safi;
    const char *bgp_route_refresh_safi_str;
	const uint8_t *marker;
};

/* OPEN message Optional Parameter types  */
#define BGP_OPTION_AUTHENTICATION    1   /* RFC1771 */
#define BGP_OPTION_CAPABILITY        2   /* RFC2842 */


#define BGP_SIZE_OF_PATH_ATTRIBUTE       2
/* attribute flags, from RFC1771 */
#define BGP_ATTR_FLAG_OPTIONAL        0x80
#define BGP_ATTR_FLAG_TRANSITIVE      0x40
#define BGP_ATTR_FLAG_PARTIAL         0x20
#define BGP_ATTR_FLAG_EXTENDED_LENGTH 0x10
#define BGP_ATTR_FLAG_UNUSED          0x0F

/* attribute types */
#define BGPTYPE_ORIGIN               1 /* RFC4271           */
#define BGPTYPE_AS_PATH              2 /* RFC4271           */
#define BGPTYPE_NEXT_HOP             3 /* RFC4271           */
#define BGPTYPE_MULTI_EXIT_DISC      4 /* RFC4271           */
#define BGPTYPE_LOCAL_PREF           5 /* RFC4271           */
#define BGPTYPE_ATOMIC_AGGREGATE     6 /* RFC4271           */
#define BGPTYPE_AGGREGATOR           7 /* RFC4271           */
#define BGPTYPE_COMMUNITIES          8 /* RFC1997           */
#define BGPTYPE_ORIGINATOR_ID        9 /* RFC4456           */
#define BGPTYPE_CLUSTER_LIST        10 /* RFC4456           */
#define BGPTYPE_DPA                 11 /* DPA (deprecated) [RFC6938]  */
#define BGPTYPE_ADVERTISER          12 /* ADVERTISER (historic) (deprecated) [RFC1863][RFC4223][RFC6938] */
#define BGPTYPE_RCID_PATH           13 /* RCID_PATH / CLUSTER_ID (historic) (deprecated) [RFC1863][RFC4223][RFC6938] */
#define BGPTYPE_MP_REACH_NLRI       14 /* RFC4760           */
#define BGPTYPE_MP_UNREACH_NLRI     15 /* RFC4760           */
#define BGPTYPE_EXTENDED_COMMUNITY  16 /* RFC4360           */
#define BGPTYPE_AS4_PATH            17 /* RFC 6793          */
#define BGPTYPE_AS4_AGGREGATOR      18 /* RFC 6793          */
#define BGPTYPE_SAFI_SPECIFIC_ATTR  19 /* SAFI Specific Attribute (SSA) (deprecated) draft-kapoor-nalawade-idr-bgp-ssa-00.txt */
#define BGPTYPE_CONNECTOR_ATTRIBUTE 20 /* Connector Attribute (deprecated) [RFC6037] */
#define BGPTYPE_AS_PATHLIMIT        21 /* AS_PATHLIMIT (deprecated) [draft-ietf-idr-as-pathlimit] */
#define BGPTYPE_PMSI_TUNNEL_ATTR    22 /* RFC6514 */
#define BGPTYPE_TUNNEL_ENCAPS_ATTR  23 /* RFC5512 */
#define BGPTYPE_TRAFFIC_ENGINEERING 24 /* Traffic Engineering [RFC5543] */
#define BGPTYPE_IPV6_ADDR_SPEC_EC   25 /* IPv6 Address Specific Extended Community [RFC5701] */
#define BGPTYPE_AIGP                26 /* RFC7311 */
#define BGPTYPE_PE_DISTING_LABLES   27 /* PE Distinguisher Labels [RFC6514] */
#define BGPTYPE_BGP_ENTROPY_LABEL   28 /* BGP Entropy Label Capability Attribute (deprecated) [RFC6790][RFC7447] */
#define BGPTYPE_LINK_STATE_ATTR     29 /* RFC7752 */
#define BGPTYPE_30                  30 /* Deprecated [RFC8093] */
#define BGPTYPE_31                  31 /* Deprecated [RFC8093] */
#define BGPTYPE_LARGE_COMMUNITY     32 /* RFC8092 */
#define BGPTYPE_BGPSEC_PATH         33 /* BGPsec_Path [RFC-ietf-sidr-bgpsec-protocol-22] */
#define BGPTYPE_BGP_PREFIX_SID      40 /* BGP Prefix-SID [draft-ietf-idr-bgp-prefix-sid] */
#define BGPTYPE_LINK_STATE_OLD_ATTR 99 /* squatted value used by at least 2
                                          implementations before IANA assignment */
#define BGPTYPE_ATTR_SET           128 /* RFC6368           */
#define BGPTYPE_129                129 /* Deprecated [RFC8093] */
#define BGPTYPE_241                241 /* Deprecated [RFC8093] */
#define BGPTYPE_242                242 /* Deprecated [RFC8093] */
#define BGPTYPE_243                243 /* Deprecated [RFC8093] */

                                          /* well-known communities, from RFC1997 */
#define BGP_COMM_NO_EXPORT           0xFFFFFF01
#define BGP_COMM_NO_ADVERTISE        0xFFFFFF02
#define BGP_COMM_NO_EXPORT_SUBCONFED 0xFFFFFF03
#define FOURHEX0                     0x00000000
#define FOURHEXF                     0xFFFF0000

/*
 * Address family numbers, from
 *
 *  http://www.iana.org/assignments/address-family-numbers
 */
#define AFNUM_RESERVED          0       /* Reserved */
#define AFNUM_INET              1       /* IP (IP version 4) */
#define AFNUM_INET6             2       /* IP6 (IP version 6) */
#define AFNUM_NSAP              3       /* NSAP */
#define AFNUM_HDLC              4       /* HDLC (8-bit multidrop) */
#define AFNUM_BBN1822           5       /* BBN 1822 */
#define AFNUM_802               6       /* 802 (includes all 802 media plus Ethernet "canonical format") */
#define AFNUM_E163              7       /* E.163 */
#define AFNUM_E164              8       /* E.164 (SMDS, Frame Relay, ATM) */
#define AFNUM_F69               9       /* F.69 (Telex) */
#define AFNUM_X121              10      /* X.121 (X.25, Frame Relay) */
#define AFNUM_IPX               11      /* IPX */
#define AFNUM_ATALK             12      /* Appletalk */
#define AFNUM_DECNET            13      /* Decnet IV */
#define AFNUM_BANYAN            14      /* Banyan Vines */
#define AFNUM_E164NSAP          15      /* E.164 with NSAP format subaddress */
#define AFNUM_DNS               16      /* DNS (Domain Name System) */
#define AFNUM_DISTNAME          17      /* Distinguished Name */
#define AFNUM_AS_NUMBER         18      /* AS Number */
#define AFNUM_XTP_IP4           19      /* XTP over IP version 4 */
#define AFNUM_XTP_IP6           20      /* XTP over IP version 6 */
#define AFNUM_XTP               21      /* XTP native mode XTP */
#define AFNUM_FC_WWPN           22      /* Fibre Channel World-Wide Port Name */
#define AFNUM_FC_WWNN           23      /* Fibre Channel World-Wide Node Name */
#define AFNUM_GWID              24      /* GWID */
#define AFNUM_L2VPN             25      /* RFC4761 RFC6074 */
#define AFNUM_L2VPN_OLD        196
#define AFNUM_MPLS_TP_SEI       26      /* MPLS-TP Section Endpoint Identifier, RFC7212 */
#define AFNUM_MPLS_TP_LSPEI     27      /* MPLS-TP LSP Endpoint Identifier, RFC7212 */
#define AFNUM_MPLS_TP_PEI       28      /* MPLS-TP Pseudowire Endpoint Identifier, RFC7212 */
#define AFNUM_MT_IP             29      /* MT IP: Multi-Topology IP version 4, RFC7307 */
#define AFNUM_MT_IPV6           30      /* MT IPv6: Multi-Topology IP version 6, RFC7307 */
#define AFNUM_EIGRP_COMMON      16384   /* EIGRP Common Service Family, Donnie Savage */
#define AFNUM_EIGRP_IPV4        16385   /* EIGRP IPv4 Service Family, Donnie Savage */
#define AFNUM_EIGRP_IPV6        16386   /* EIGRP IPv6 Service Family, Donnie Savage */
#define AFNUM_LCAF              16387   /* LISP Canonical Address Format, David Meyer */
#define AFNUM_BGP_LS            16388   /* BGP-LS, RFC7752 */
#define AFNUM_EUI48             16389   /* 48-bit MAC, RFC7042 */
#define AFNUM_EUI64             16390   /* 64-bit MAC, RFC7042 */
#define AFNUM_OUI               16391   /* OUI, RFC7961 */
#define AFNUM_MAC_24            16392   /* MAC/24, RFC7961 */
#define AFNUM_MAC_40            16393   /* MAC/40, RFC7961 */
#define AFNUM_IPv6_64           16394   /* IPv6/64, RFC7961 */
#define AFNUM_RB_PID            16395   /* RBridge Port ID, RFC7961 */
#define AFNUM_TRILL_NICKNAME    16396   /* TRILL Nickname, RFC7455 */

 /* BGP MPLS information */
#define BGP_MPLS_BOTTOM_L_STACK 0x000001

/* AS_PATH segment types */
#define AS_SET             1   /* RFC1771 */
#define AS_SEQUENCE        2   /* RFC1771 */
#define AS_CONFED_SET      4   /* RFC1965 has the wrong values, corrected in  */
#define AS_CONFED_SEQUENCE 3   /* draft-ietf-idr-bgp-confed-rfc1965bis-01.txt */


#define BGP_ORF_PREFIX_CISCO    0x80 /* Cisco */
#define BGP_ORF_COMM_CISCO      0x81 /* Cisco */
#define BGP_ORF_EXTCOMM_CISCO   0x82 /* Cisco */
#define BGP_ORF_ASPATH_CISCO    0x83 /* Cisco */

#define BGP_ORF_COMM        0x02 /* RFC5291 */
#define BGP_ORF_EXTCOMM     0x03 /* RFC5291 */
#define BGP_ORF_ASPATH      0x04 /* draft-ietf-idr-aspath-orf-02.txt */
/* RFC5291 */
#define BGP_ORF_ACTION      0xc0
#define BGP_ORF_ADD         0x00
#define BGP_ORF_REMOVE      0x01
#define BGP_ORF_REMOVEALL   0x02

/* Extended community & Route dinstinguisher formats */
#define FORMAT_AS2_LOC      0x00    /* Format AS(2bytes):AN(4bytes) */
#define FORMAT_IP_LOC       0x01    /* Format IP address:AN(2bytes) */
#define FORMAT_AS4_LOC      0x02    /* Format AS(4bytes):AN(2bytes) */

#endif
