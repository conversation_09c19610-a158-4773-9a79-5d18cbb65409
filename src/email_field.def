
#ifndef DPI_DEFINE_FIELD_NULL
#define DPI_DEFINE_FIELD_NULL(type, name)
#endif

DPI_DEFINE_FIELD_STR(EMAIL_DATE,         "date")
DPI_DEFINE_FIELD_STR(EMAIL_FROM,         "from")
DPI_DEFINE_FIELD_STR(EMAIL_TO,           "to")
DPI_DEFINE_FIELD_STR(EMAIL_CC,           "cc")
DPI_DEFINE_FIELD_STR(EMAIL_BCC,          "bcc")
DPI_DEFINE_FIELD_STR(EMAIL_SUBJECT,      "subject")
DPI_DEFINE_FIELD_STR(EMAIL_REFERENCE,    "reference")
DPI_DEFINE_FIELD_STR(EMAIL_MIME_VERSION, "mime-version")
DPI_DEFINE_FIELD_STR(EMAIL_MESSAGE_ID,   "message-id")
DPI_DEFINE_FIELD_STR(EMAIL_CONTENT_TYPE, "content-type")
DPI_DEFINE_FIELD_STR(EMAIL_X_PRIORITY,   "x-priority")
DPI_DEFINE_FIELD_STR(EMAIL_X_HAS_ATTACH, "x-has-attach")
DPI_DEFINE_FIELD_STR(EMAIL_X_MAILER,     "x-mailer")
DPI_DEFINE_FIELD_NULL(EMAIL_HEADER_MAX,  "NULL_FOR_HEADER_BOUND")

#undef DPI_DEFINE_FIELD_STR
#undef DPI_DEFINE_FIELD_NULL
