import ftplib
import time
import os, logging
from datetime import datetime

# FTP服务器配置
FTP_HOST = "***************"
FTP_USER = "shared" 
FTP_PASS = "yview.cn"
FTP_DIR  = "/sdx_rule/ks"

data_dir = "/tmp/sdx_rule"
rule_agent_path = data_dir + "/SDT_Rule_update.txt"
rule_file_path = data_dir + "/SDT_Rule.xml"
version_path = data_dir + "/KSVersion.txt"

ftp_version_path = FTP_DIR + "/KSVersion.txt"

last_version = None

def monitor_and_download_ks_file(ftp: ftplib.FTP):
    global last_version
    # 读取KSVersion.txt内容
    with open(version_path, 'w') as f:
        ftp.retrlines('RETR %s' % (ftp_version_path), f.write)
    with open(version_path, 'r') as f:
        current_version = f.read().strip()

    # 检查版本是否变化
    if current_version != last_version:
        print(f"检测到新版本: {current_version}")

        # 构建要下载的XML文件名
        xml_filename = f"{FTP_DIR}/KSZC_{current_version}.xml"

        # 下载XML文件
        with open(rule_file_path, 'wb') as f:
            ftp.retrbinary(f'RETR {xml_filename}', f.write)
        print(f"已下载文件: {xml_filename} --> {rule_file_path}")

        with open(rule_agent_path, "w") as f:
            f.write(rule_file_path)

        # 更新last_version
        last_version = current_version


def main():
    os.makedirs(data_dir, exist_ok=True)

    # 连接FTP服务器
    ftp = ftplib.FTP()
    ftp.set_debuglevel(0)           # 0 代表不打印日志
    ftp.encoding = 'utf-8'          # 重新定义编码格式
    ftp.connect(FTP_HOST, 21)       # 连接ftp
    ftp.login(FTP_USER, FTP_PASS)
    ftp.set_pasv(False)             # ftp有主动 被动模式 需要调整

    try:
        while True:
            try:
                monitor_and_download_ks_file(ftp)
            except Exception as e:
                print(f"发生错误: {str(e)}")
            # 等待30秒后再次检查
            time.sleep(10)
    except KeyboardInterrupt:
        pass

    ftp.quit()


if __name__ == "__main__":
    main()

