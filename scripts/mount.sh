#!/bin/bash

# nfs 挂载脚本
# 用法：./mount.sh nfs_ip nfs_path
# 例子：./mount.sh ***********:80  /mnt/nfs

mount_path="/mnt/nfs"
# get arg1 and arg2
nfs_ip=$1
nfs_path=$2

# if nfs_ip with port ,then split it
if [[ $nfs_ip =~ ":" ]]; then
    IFS=":" read -r nfs_ip nfs_port <<< "$nfs_ip"
    # nfs_ip=${nfs_ip%:*}
    # nfs_port=${nfs_ip#*:}
fi

# if mount_path not exist,then create it
if [ ! -d $mount_path ]; then
    mkdir -p $mount_path
fi

# echo mount
echo mount -t nfs $2:$nfs_path $mount_path -o port=$nfs_port

# mount
mount -t nfs $nfs_ip:$nfs_path $mount_path -o port=$nfs_port
# mount nfs
# mount
# mount -t nfs -o username=yview,password=123 ***********:/mnt/nfs  $mount_path
# mount -t nfs  ***************:/home/<USER>


if mount | grep -q "$mount_path"; then
    echo "挂载成功"
else
    echo  "挂载失败"
fi


exit 0
