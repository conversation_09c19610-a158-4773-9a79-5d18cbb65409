/****************************************************************************************
 * 文 件 名 : cmd_dpi.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include "command.h"
#include "vtysh_config.h"
#include <ctype.h>
#include <sys/time.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <sys/types.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <arpa/inet.h>

#include "dpi_vtysh_msg.h"

char server_ip[64] = "127.0.0.1";
short server_port = SOCKET_PORT;

int sockfd;

int init_socket_client(const char *ip, int port)
{
//    struct timeval timeout = {0, 100000};
    struct sockaddr_in servaddr;

    if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        perror("socket");
        return -1;
    }

    memset(&servaddr, 0, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_port = htons(port);

    if (inet_pton(AF_INET, ip, &servaddr.sin_addr) <= 0) {
        printf("invalue ip\n");
        return -1;
    }

    if (connect(sockfd, (struct sockaddr *)&servaddr, sizeof(servaddr)) < 0) {
        perror("connect");
        return -1;
    }

//    setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, (char *)&timeout, sizeof(struct timeval));

    return 0;
}

int close_socket_client()
{
    close(sockfd);
    return 0;
}

static void do_send_and_recv(const char *send_msg, int send_len, char *recv_msg, int recv_max_len)
{
    int retval;
    int recv_len;

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return;
    
    if (send(sockfd, send_msg, send_len, 0) < 0) {
        printf("send msg error \n");
        return;
    }
    
    if ((recv_len = recv(sockfd, recv_msg, recv_max_len, 0)) < 0) {
        printf("recv msg error\n");
        return;
    }
    
    if (recv_len < MSG_MAX_LEN)
        recv_msg[recv_len] = 0;
    
    close_socket_client();
    
    printf("%s\n", recv_msg);
}

DEFUN(show_thread_info,
        show_thread_info_cmd,
        "show thread info",
        "show thread info\n"
        "show thread info\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_THREAD_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_flow_detail_info,
        show_flow_detail_info_cmd,
        "show flow detail",
        "show flow detail\n"
        "show flow detail\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_FLOW_DETAIL_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_flow_inc_info,
        show_flow_inc_info_cmd,
        "show flow increase",
        "show flow increase\n"
        "show flow increase\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_FLOW_INC_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_flow_total_info,
        show_flow_total_info_cmd,
        "show flow total",
        "show flow total\n"
        "show flow total\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_FLOW_TOTAL_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(clean_flow_total_info,
        clean_flow_total_info_cmd,
        "clean flow total",
        "clean flow total\n"
        "clean flow total\n")

{
    int retval;
    int recv_len;
    int type = MSG_CLEAN_FLOW_TOTAL_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_flow_hash_info,
        show_flow_hash_info_cmd,
        "show flow hash",
        "show flow hash\n"
        "show flow hash\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_FLOW_HASH_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_mempool_detail_info,
        show_mempool_detail_info_cmd,
        "show mempool detail",
        "show mempool detail\n"
        "show mempool detail\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_MEMPOOL_DETAIL_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
}
        
DEFUN(show_ring_detail_info,
        show_ring_detail_info_cmd,
        "show ring detail",
        "show ring detail\n"
        "show ring detail\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_RING_DETAIL_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_traffic_speed_info,
        show_traffic_speed_info_cmd,
        "show traffic speed",
        "show traffic speed\n"
        "show traffic speed\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_TRAFFIC_SPEED_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(show_dev_traffic_speed_info,
        show_dev_traffic_speed_info_cmd,
        "show dev speed",
        "show dev speed\n"
        "show dev speed\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_DEV_TRAFFIC_SPEED_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
}

DEFUN(show_current_traffic_speed,
        show_current_traffic_speed_cmd,
        "show current speed",
        "show current speed\n"
        "show current speed\n")

{
//    int retval;
    int recv_len;
    int type = MSG_SHOW_CURRENT_TRAFFIC_SPEED;
    char msg[MSG_MAX_LEN];
    
    struct timeval timeout = {0, 100000};
    struct sockaddr_in servaddr;

    if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        perror("socket");
        return -1;
    }

    memset(&servaddr, 0, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_port = htons(server_port);

    if (inet_pton(AF_INET, server_ip, &servaddr.sin_addr) <= 0) {
        printf("invalue ip\n");
        return -1;
    }

    if (connect(sockfd, (struct sockaddr *)&servaddr, sizeof(servaddr)) < 0) {
        perror("connect");
        return -1;
    }

    setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, (char *)&timeout, sizeof(struct timeval));

//    retval = init_socket_client(server_ip, server_port);
//    if (retval < 0)
//        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        //printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        //printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 


DEFUN(show_flow_timeout_info,
        show_flow_timeout_info_cmd,
        "show flow timeout",
        "show flow timeout\n"
        "show flow timeout\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_FLOW_TIMEOUT;
    
//    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];

//    int *type = (int *)&msg_send[0];
//    *type = MSG_SHOW_FLOW_TIMEOUT;

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg_recv, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg_recv[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg_recv);
    return 0;
} 


DEFUN(show_flow_identify_pkt_num_info,
        show_flow_identify_pkt_num_info_cmd,
        "show flow identify pkt num",
        "show flow identify pkt num\n"
        "show flow identify pkt num\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_FLOW_IDENTIFY_PKT_NUM;
    
//    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];

//    int *type = (int *)&msg_send[0];
//    *type = MSG_SHOW_FLOW_TIMEOUT;

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg_recv, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg_recv[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg_recv);
    return 0;
} 
        
DEFUN(set_tcp_timeout,
    set_tcp_timeout_cmd,
    "set tcp timeout NUM",
    "set tcp timeout\n"
    "set tcp timeout\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    uint16_t *value;

    type = (int *)&msg_send[0];    
    value = (uint16_t *)&msg_send[4];
    
    *type = MSG_SET_TCP_TIMEOUT;
    *value = atoi(argv[0]);

    do_send_and_recv(msg_send, 6, msg_recv, MSG_MAX_LEN);

    return 0;
} 
        
DEFUN(show_fail_info,
    show_fail_info_cmd,
    "show fail info",
    "show dpdk dpi tbl info\n")
{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;

    type = (int *)&msg_send[0]; 
    *type = MSG_SHOW_FAIL_INFO;
    
    do_send_and_recv(msg_send, 4, msg_recv, MSG_MAX_LEN);
    return 0;
}
DEFUN(set_udp_timeout,
    set_udp_timeout_cmd,
    "set udp timeout NUM",
    "set udp timeout\n"
    "set udp timeout\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    uint16_t *value;

    type = (int *)&msg_send[0]; 
    value = (uint16_t *)&msg_send[4];
    
    *type = MSG_SET_UDP_TIMEOUT;
    *value = atoi(argv[0]);
    
    do_send_and_recv(msg_send, 6, msg_recv, MSG_MAX_LEN);

    return 0;
} 

DEFUN(set_sctp_timeout,
    set_sctp_timeout_cmd,
    "set sctp timeout NUM",
    "set sctp timeout\n"
    "set sctp timeout\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    uint16_t *value;

    type = (int *)&msg_send[0]; 
    value = (uint16_t *)&msg_send[4];
    
    *type = MSG_SET_SCTP_TIMEOUT;
    *value = atoi(argv[0]);
    
    do_send_and_recv(msg_send, 6, msg_recv, MSG_MAX_LEN);

    return 0;
} 

DEFUN(set_tcp_identify_pkt_num,
    set_tcp_identify_pkt_num_cmd,
    "set tcp identify pkt num NUM",
    "set tcp identify pkt num\n"
    "set tcp identify pkt num\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    uint16_t *value;

    type = (int *)&msg_send[0]; 
    value = (uint16_t *)&msg_send[4];
    
    *type = MSG_SET_TCP_IDENTIFY_PKT_NUM;
    *value = atoi(argv[0]);
    
    do_send_and_recv(msg_send, 6, msg_recv, MSG_MAX_LEN);

    return 0;
} 
    
DEFUN(set_udp_identify_pkt_num,
    set_udp_identify_pkt_num_cmd,
    "set udp identify pkt num NUM",
    "set udp identify pkt num\n"
    "set udp identify pkt num\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    uint16_t *value;

    type = (int *)&msg_send[0]; 
    value = (uint16_t *)&msg_send[4];
    
    *type = MSG_SET_UDP_IDENTIFY_PKT_NUM;
    *value = atoi(argv[0]);
    
    do_send_and_recv(msg_send, 6, msg_recv, MSG_MAX_LEN);

    return 0;
}

DEFUN(set_sctp_identify_pkt_num,
    set_sctp_identify_pkt_num_cmd,
    "set sctp identify pkt num NUM",
    "set sctp identify pkt num\n"
    "set sctp identify pkt num\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    uint16_t *value;

    type = (int *)&msg_send[0]; 
    value = (uint16_t *)&msg_send[4];
    
    *type = MSG_SET_SCTP_IDENTIFY_PKT_NUM;
    *value = atoi(argv[0]);
    
    do_send_and_recv(msg_send, 6, msg_recv, MSG_MAX_LEN);

    return 0;
} 

DEFUN(disable_protocol_identify,
    disable_protocol_identify_cmd,
    "disable (HTTP|DNS|L2TP|FTP_CONTROL|SMTP|POP|IMAP|SIP|TELNET|SSH|TFTP|RIP|CLASSICSTUN|STUN|MYSQL|TDS|WEIXIN|RADIUS|SMB) identify",
    "disable protocol identify\n"
    "disable HTTP identify\n"
    "disable DNS identify\n"
    "disable L2TP identify\n"
    "disable FTP_CONTROL identify\n"
    "disable SMTP identify\n"
    "disable POP identify\n"
    "disable IMAP identify\n"
    "disable SIP identify\n"
    "disable TELNET identify\n"
    "disable SSH identify\n"
    "disable TFTP identify\n"
    "disable RIP identify\n"
    "disable CLASSICSTUN identify\n"
    "disable WEIXIN identify\n"
    "disable WEIXIN identify\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int argv_max_len = 32 - sizeof(int) - 1;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_DISABLE_PROTOCOL_IDENTIFY;
    send_len = strlen(argv[0]) > argv_max_len ? argv_max_len : strlen(argv[0]);
    strncpy(msg_send + sizeof(int), argv[0], send_len);
    send_len += sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 

DEFUN(enable_protocol_identify,
    enable_protocol_identify_cmd,
    "enable (HTTP|DNS|L2TP|FTP_CONTROL|SMTP|POP|IMAP|SIP|TELNET|SSH|TFTP|RIP|CLASSICSTUN|STUN|MYSQL|TDS|WEIXIN|RADIUS|SMB) identify",
    "enable protocol identify\n"
    "enable HTTP identify\n"
    "enable DNS identify\n"
    "enable L2TP identify\n"
    "enable FTP_CONTROL identify\n"
    "enable SMTP identify\n"
    "enable POP identify\n"
    "enable IMAP identify\n"
    "enable SIP identify\n"
    "enable TELNET identify\n"
    "enable SSH identify\n"
    "enable TFTP identify\n"
    "enable RIP identify\n"
    "enable CLASSICSTUN identify\n"
    "enable WEIXIN identify\n"
    "enable RADIUS identify\n")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int argv_max_len = 32 - sizeof(int) - 1;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_ENABLE_PROTOCOL_IDENTIFY;
    send_len = strlen(argv[0]) > argv_max_len ? argv_max_len : strlen(argv[0]);
    strncpy(msg_send + sizeof(int), argv[0], send_len);
    send_len += sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 
/*
DEFUN(enable_conversation_identify,
    enable_conversation_identify_cmd,
    "enable conversation identify",
    "enable conversation identify"
    "enable conversation identify")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    *type = MSG_ENABLE_CONVERSATION_IDENTIFY;
    send_len = sizeof(int);
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);
    return 0;
} 
    
DEFUN(disable_conversation_identify,
    disable_conversation_identify_cmd,
    "disable conversation identify",
    "disable conversation identify"
    "disable conversation identify")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    *type = MSG_DISABLE_CONVERSATION_IDENTIFY;
    send_len = sizeof(int);
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);
    return 0;
} 
*/    
DEFUN(show_protocol_identify,
    show_protocol_identify_cmd,
    "show protocol identify",
    "show protocol identify"
    "show protocol identify")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_SHOW_PROTOCOL_IDENTIFY;
    send_len = sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 


DEFUN(show_log_level,
    show_log_level_cmd,
    "show log level",
    "show log level"
    "show log level")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_SHOW_LOG_LEVEL;
    send_len = sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 


DEFUN(set_log_level,
    set_log_level_cmd,
    "set log level (DEBUG|WARNING|ERROR)",
    "set log level"
    "set log level")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int argv_max_len = 32 - sizeof(int) - 1;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_SET_LOG_LEVEL;
    send_len = strlen(argv[0]) > argv_max_len ? argv_max_len : strlen(argv[0]);
    strncpy(msg_send + sizeof(int), argv[0], send_len);
    send_len += sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 
    
DEFUN(stop_rcv_pkts,
    stop_rcv_pkts_cmd,
    "stop rcv_pkts",
    "stop rcv_pkts"
    "stop rcv_pkts")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_STOP_RCV_PKTS;
    send_len = sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 

DEFUN(start_rcv_pkts,
    start_rcv_pkts_cmd,
    "start rcv_pkts",
    "start rcv_pkts"
    "start rcv_pkts")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_START_RCV_PKTS;
    send_len = sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 

DEFUN(terminate_dpi,
    terminate_dpi_cmd,
    "terminate dpi",
    "terminate dpi"
    "terminate dpi")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_NORMAL_EXIT;
    send_len = sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 
    
DEFUN(show_tbl_log_info,
        show_tbl_log_info_cmd,
        "show tbl log",
        "show tbl log\n"
        "show tbl log\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_TBL_LOG_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
} 

DEFUN(clean_tbl_log_info,
        clean_tbl_log_info_cmd,
        "clean tbl log",
        "clean tbl log\n"
        "clean tbl log\n")

{
    int retval;
    int recv_len;
    int type = MSG_CLEAN_TBL_LOG_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
}

DEFUN(show_http_post_info,
        show_http_post_info_cmd,
        "show http post",
        "show http post\n"
        "show http post\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_HTTP_POST_COUNT;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
}

DEFUN(show_eth_dev_info,
        show_eth_dev_info_cmd,
        "show eth dev info",
        "show eth dev info\n"
        "show eth dev info\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_ETH_DEV_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
}

DEFUN(show_port_rss_info,
        show_port_rss_info_cmd,
        "show port rss info",
        "show port rss info\n"
        "show port rss info\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_PORT_RSS_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;
    
    close_socket_client();

    printf("%s\n", msg);
    return 0;
}

DEFUN(show_rules_info,
        show_rules_info_cmd,
        "show rules info",
        "show rules info\n"
        "show rules info\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_RULES_INFO;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;

    close_socket_client();

    printf("%s\n", msg);
    return 0;
}

DEFUN(show_rules_match,
        show_rules_match_cmd,
        "show rules match",
        "show rules match\n"
        "show rules match\n")

{
    int retval;
    int recv_len;
    int type = MSG_SHOW_RULES_MATCH;
    char msg[MSG_MAX_LEN];

    retval = init_socket_client(server_ip, server_port);
    if (retval < 0)
        return -1;

    if (send(sockfd, &type, sizeof(type), 0) < 0) {
        printf("send msg error \n");
        return -1;
    }

    if ((recv_len = recv(sockfd, msg, MSG_MAX_LEN, 0)) < 0) {
        printf("recv msg error\n");
        return -1;
    }

    if (recv_len < MSG_MAX_LEN)
        msg[recv_len] = 0;

    close_socket_client();

    printf("%s\n", msg);
    return 0;
}


DEFUN(show_rules_id,
        show_rules_id_cmd,
        "show rules id NUM",
        "show rules id\n"
        "show rules id\n")

{
    
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int *value;

    type = (int *)&msg_send[0]; 
    value = (int *)&msg_send[4];
    
    *type = MSG_SHOW_RULES_ID;
    *value = atoi(argv[0]);

    do_send_and_recv(msg_send, 8, msg_recv, MSG_MAX_LEN);

    return 0;
}


DEFUN(dpi_test_module,
    dpi_test_module_cmd,
    "dpi test module STR",
    "dpi test module"
    "dpi test module")

{
    char msg_send[32];
    char msg_recv[MSG_MAX_LEN];
    int *type;
    int argv_max_len = 32 - sizeof(int) - 1;
    int send_len;

    type = (int *)&msg_send[0]; 
    
    *type = MSG_DPI_TEST_MODULE;
    send_len = strlen(argv[0]) > argv_max_len ? argv_max_len : strlen(argv[0]);
    strncpy(msg_send + sizeof(int), argv[0], send_len);
    send_len += sizeof(int);
    
    do_send_and_recv(msg_send, send_len, msg_recv, MSG_MAX_LEN);

    return 0;
} 


/*****************************end******************************************/

int cmd_dpi_init()
{
    cmd_install_element (ENABLE_NODE, &show_thread_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_flow_inc_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_flow_detail_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_flow_total_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_flow_hash_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_mempool_detail_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_ring_detail_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_traffic_speed_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_dev_traffic_speed_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_current_traffic_speed_cmd);
    cmd_install_element (ENABLE_NODE, &show_log_level_cmd);
    cmd_install_element (ENABLE_NODE, &show_tbl_log_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_rules_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_rules_match_cmd);
    cmd_install_element (ENABLE_NODE, &show_rules_id_cmd);

    cmd_install_element (ENABLE_NODE, &show_flow_timeout_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_flow_identify_pkt_num_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_protocol_identify_cmd);
    cmd_install_element (ENABLE_NODE, &show_http_post_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_fail_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_eth_dev_info_cmd);
    cmd_install_element (ENABLE_NODE, &show_port_rss_info_cmd);

    cmd_install_element (ENABLE_NODE, &dpi_test_module_cmd);
    
    cmd_install_element (ENABLE_NODE, &start_rcv_pkts_cmd);
    cmd_install_element (ENABLE_NODE, &stop_rcv_pkts_cmd);

    cmd_install_element (CONFIG_NODE, &show_thread_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_flow_inc_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_flow_detail_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_flow_total_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_flow_hash_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_mempool_detail_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_ring_detail_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_traffic_speed_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_dev_traffic_speed_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_log_level_cmd);
    cmd_install_element (CONFIG_NODE, &show_protocol_identify_cmd);
    cmd_install_element (CONFIG_NODE, &show_tbl_log_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_rules_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_rules_match_cmd);
    cmd_install_element (CONFIG_NODE, &show_rules_id_cmd);

    cmd_install_element (CONFIG_NODE, &show_flow_timeout_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_flow_identify_pkt_num_info_cmd);

    cmd_install_element (CONFIG_NODE, &set_tcp_timeout_cmd);
    cmd_install_element (CONFIG_NODE, &set_udp_timeout_cmd);
    cmd_install_element (CONFIG_NODE, &set_sctp_timeout_cmd);        
    cmd_install_element (CONFIG_NODE, &set_tcp_identify_pkt_num_cmd);
    cmd_install_element (CONFIG_NODE, &set_udp_identify_pkt_num_cmd);
    cmd_install_element (CONFIG_NODE, &set_sctp_identify_pkt_num_cmd);

    cmd_install_element (CONFIG_NODE, &disable_protocol_identify_cmd);
    cmd_install_element (CONFIG_NODE, &enable_protocol_identify_cmd);
    cmd_install_element (CONFIG_NODE, &set_log_level_cmd);
    
    cmd_install_element (CONFIG_NODE, &start_rcv_pkts_cmd);
    cmd_install_element (CONFIG_NODE, &stop_rcv_pkts_cmd);

    cmd_install_element (CONFIG_NODE, &clean_flow_total_info_cmd);
    cmd_install_element (CONFIG_NODE, &clean_tbl_log_info_cmd);
    
    cmd_install_element (CONFIG_NODE, &show_http_post_info_cmd);
    cmd_install_element (CONFIG_NODE, &show_fail_info_cmd);

    cmd_install_element (CONFIG_NODE, &terminate_dpi_cmd);

    cmd_install_element (CONFIG_NODE, &dpi_test_module_cmd);
    return 0;
}

