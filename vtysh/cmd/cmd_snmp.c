#include "command.h"
#include "vtysh_config.h"
#include <ctype.h>
#include <sys/time.h>
#include <unistd.h>

#define SYS_OID    "1.3.6.1.4.1.8888.1.1"
#define DEFAULT_CONF    "/etc/my-snmpd.conf"

static char szLocation[256] = {0};
static char szContact[256] = {0};
static char szRO[256] = {0};
static char szRW[256] = {0};

static void in_process_snmp_config()
{
    FILE *fp, *fp2;
    char info[1024];

    system("killall snmpd > /dev/null 2>&1");
    if(strlen(szRO) < 1)
        return;

    fp = fopen("/BUILD", "r");
    if(fp)
    {
            fgets(info, sizeof(info), fp);
            fclose(fp);
    }
    else
    {
            sprintf(info, "Build on:%s %s", __DATE__, __TIME__);
    }

    fp = fopen("/usr/share/snmp/snmpd.conf", "w");
    if(fp == NULL)
    {
        fprintf(stderr, "set the snmp config error[%s]\n", strerror(errno));
        return;
    }

    fprintf(fp, "rocommunity %s\n", szRO);
    fprintf(fp, "rwcommunity %s\n", szRW);    
    fprintf(fp, "syslocation %s\n", strlen(szLocation) < 1 ? "location":szLocation);
    fprintf(fp, "syscontact %s\n", strlen(szContact) < 1 ? "contact":szContact);
    fprintf(fp, "sysservices 72\n");
    fprintf(fp, "sysname %s\n", host.name);
    fprintf(fp, "sysdescr %s %s\n", host.name, info);
    fprintf(fp, "sysobjectid %s\n", SYS_OID);

    if((fp2 = fopen(DEFAULT_CONF, "r")) != NULL)
    {
        memset(info, 0, sizeof(info));
        while(fgets(info, sizeof(info), fp2))
            fputs(info, fp);
        fclose(fp2);
    }

    fclose(fp);

    cmd_execute_system_command ("snmpd", 0, NULL);
}

DEFUN(config_snmp_location,
       config_snmp_location_cmd,
       "snmp-server location WORD",
       "Set the snmp config\n"
       "set the snmp location info \n"
       "the snmp location info"
       )
{
    char info[256];

    config_del_line_byleft(config_top, "snmp-server location");
    sprintf(info, "snmp-server location %s", argv[0]);
    config_add_line(config_top, info);

    strcpy(szLocation, argv[0]);

    return CMD_SUCCESS;
}

DEFUN(config_snmp_contact,
       config_snmp_contact_cmd,
       "snmp-server contact WORD",
       "Set the snmp config\n"
       "set the snmp contact info\n"
       "the snmp contact info"
       )
{
    char info[256];

    config_del_line_byleft(config_top, "snmp-server contact");
    sprintf(info, "snmp-server contact %s", argv[0]);
    config_add_line(config_top, info);

    strcpy(szContact, argv[0]);

    return CMD_SUCCESS;
}

DEFUN(config_snmp_community,
       config_snmp_community_cmd,
       "snmp-server RO WORD RW WORD",
       "Set the snmp config\n"
       "set the snmp RO/RW community"
       "RO\n"
       "the snmp read-only community\n"
       "RW\n"
       "the snmp read-write community\n"
       )
{
    char info[256];

    config_del_line_byleft(config_top, "snmp-server RO");
    sprintf(info, "snmp-server RO %s RW %s", argv[1], argv[3]);
    config_add_line(config_top, info);

    strcpy(szRO, argv[1]);
    strcpy(szRW, argv[3]);

    ENSURE_CONFIG(vty);
    in_process_snmp_config();
    return CMD_SUCCESS;
}

DEFUN (no_config_snmp_community,
       no_config_snmp_community_cmd,
       "no snmp-server",
       NO_STR
       "Disable snmp server service\n")
{
    config_del_line_byleft(config_top, "snmp-server RO");
    memset(szRO, 0, sizeof(szRO));
    ENSURE_CONFIG(vty);
    in_process_snmp_config();

    return CMD_SUCCESS;
}

int cmd_snmp_init()
{

  cmd_install_element (CONFIG_NODE, &config_snmp_location_cmd);
  cmd_install_element (CONFIG_NODE, &config_snmp_contact_cmd);
  cmd_install_element (CONFIG_NODE, &config_snmp_community_cmd);
  cmd_install_element (CONFIG_NODE, &no_config_snmp_community_cmd);

  return 0;
}
