CC=gcc
CFLAGS=-Wall -g -I. -I../include
LIBS=-lreadline -lcrypt -lncurses
STRIP=strip
TARGETDIR=/home/<USER>/sbin
OUTPUT_DIR=../run

all:prepare $(OUTPUT_DIR)/vtysh
prepare:
	@echo Parse the cmd directory ...
	@sh parse.sh
OBJECT=${patsubst %.c, %.o, ${wildcard *.c cmd/*.c}}
$(OUTPUT_DIR)/vtysh:${OBJECT}
	${CC} -o $@ $^ ${LIBS}
#	cp vtysh /root/initrd/ramfs/sbin -f
install:vtysh
	${STRIP} vtysh
	rm -f ${TARGETDIR}/vtysh
	cp vtysh ${TARGETDIR}/

.c.o:
.c.h:
clean:
	rm -f *.o cmd/*.o $(OUTPUT_DIR)/vtysh
