set(CPAC<PERSON>_GENERATOR "TGZ" CACHE STRING "Semicolon separated list of generators")
set(CPACK_PACKAGE_NAME "${PROJECT_NAME}")
message("PACK NAME ===============> ${CPACK_PACKAGE_NAME}")

set(CPACK_PACKAGE_VERSION ${PROGRAM_VERSION})
if (PROJECT_VERSION_TWEAK)
  set(CPACK_PACKAGE_VERSION ${CPACK_PACKAGE_VERSION}.${PROJECT_VERSION_TWEAK})
endif()
message("CPACK_PACKAGE_VERSION ${CPACK_PACKAGE_VERSION}")

message("file name ${CPACK_PACKAGE_FILE_NAME}")
set(CPACK_PACKAGE_FILE_NAME "${CPACK_PACKAGE_NAME}-${CPACK_PACKAGE_VERSION}")

message("file name ${CPACK_PACKAGE_FILE_NAME}")

include(CPack)