# 启动命令,需要搭配 setup_dpdk_env.sh -b enp5s0f0 使用
# -l 交由 dpdk 管理的线程，包括收包线程和解析线程，0号线程禁止使用
# -r 收包线程数量，最多配置 2 个即可
# -p 解析线程数量，配置需在 0-10 范围内，否则不生效
# -a 老化线程数量，最好和 -p 为倍数关系，在 -l 约束的范围外随意设置
# -l 输出线程配置，在 -l 约束的范围外随意设置
# -m 筛选匹配线程，在 -l 约束的范围外随意设置
# -p -a -l -m -a 后面的数字为 lscpu 显示的  NUMA node[n] CPU(s),若设置，该线程会绑定到该cpu
# 除此之外  -p -a -l -m -a 可以不配置，程序会 根据 配置文件
# DISSECTOR_THREAD_NUM
# FLOW_AGAIN_THREAD_NUM
# OUTPUT_THREAD_NUM
# MATCH_THREAD_NUM  动态配置程序
#
CMD_LINE = "./yaDpiSdt -l 0-10 -- -r 1,2 -p 3,4,5,6,7,8,9,10 -a 10,11 -l 11 -m 12 -o 13 -c 14"
# CMD_LINE = "./yaDpiSdt -l 0-8 -- -r 1,2 -p 3,4,5,6,7,8 -o 13"
PROCESS_WORK_MODE     = "dpdk"                    #配置MODE: dpdk, scan, smart_nic
PROCESS_SOCKET_IP     = "192.168.XXX.XXX"         #当运行模式为socket时，需要配置当前服务器绑定的ip地址
PROCESS_SOCKET_PORT   = 10009                     #当运行模式为socket是，需要配置当期服务器绑定的端口号

# 性能参数
DPDK_RX_ENABLE_SAVE_BAD_PACKET = 0                  #DPDK 网卡硬件 RX 接收错误帧 [0: 不接受坏帧. 1:接受坏帧] [建议使用X722,不要使用82599]
DPDK_TX_DISABLE_HW_APPEND_CRC  = 0                  #DPDK 网卡硬件 TX 禁止填充CRC[0: 填充CRC.    1:不填充CRC]
TCP_REASSEMBLE_MEMPOOL_NUM   = 524288               #TCP 内存池节点数量
RXQ_NUM                      = 4                    #每个网卡对应的收包队列数，此参数配合run/start.sh里-r后的选项对发包队列进行配置
DISSECTOR_THREAD_NUM         = 16                   #dpi的解析线程数，此参数暂不使用，解析线程数由run/start.sh里-p后的选项配置
MBUF_SIZE                    = 2048                 #每个rte_mbuf可携带数据包的长度，默认2048
MAX_PKT_LEN                  = 2000                 #链路层携带的最大字节数i，默认1518
NB_MBUF                      = 1048576              #内存池的最大包缓冲数，1048576=2**20
MAX_FLOW_NUM                 = 1048576
MAX_HASH_NODE_PER_THREAD     = 200000
MEMPOOL_CACHE_SIZE           = 256                  #一组socket里每个核心所分配的包高速缓冲数
PACKET_RING_SIZE             = 131072                #每个解析线程收包队列里可缓冲的包数,必须为2的幂
WRITE_TBL_MAXTIME            = 20                   #每个打开的tbl文件的最长打开时间,单位是秒
TCP_RSM_OUT_OF_ORDER         = 50                   #TCP报文缓存个数,对抗乱序
TCP_FLOW_DELETE              = 5                    #针对协议tcp重组在超时可能会在做一次解析，解析结果需要规则匹配,此时flow不能删，此配置为延后多长时间删
TCP_FLOW_TIMEOUT             = 3000                   #tcp数据包的超时会话时间,单位是毫秒
UDP_FLOW_TIMEOUT             = 3000                   #udp数据包的超时会话时间，单位是毫秒
SCTP_FLOW_TIMEOUT            = 3000                   #sctp数据包的超时会话时间，单位是毫秒
DISSECTOR_THREAD_CONV_TIMEOUT = 65            #对解析线程定时器固定超时时间，单位是秒(s)

TCP_IDENTIFY_PKT_NUM         = 20                   #解析tcp时，当处理的某对IP的包数大于此数时，可对其应用层协议跳过识别过程直接进行解析
UDP_IDENTIFY_PKT_NUM         = 20                   #解析udp时，当处理的某对IP的包数大于此数时，可对其应用层协议跳过识别过程直接进行解析
SCTP_IDENTIFY_PKT_NUM        = 10                   #解析sctp时，当处理的某对IP的包数大于此数时，可对其应用层协议跳过识别过程直接进行解析
TCP_RESSEAMBLE_MAX_NUM       = 32                   #可重组的tcp单向最大会话数
LOG_MAX_NUM                  = 10000                #每个tbl文件里可存信息的最大条数
IDLE_SCAN_PERION             = 10000                #对所有会话的固定超时时间，单位是微妙(us)
MAX_PKT_BURST                = 512                  #收包线程一次读取包数
MAX_TIMEOUT_NUM              = 500                  #定时超时最大流数
TBL_LOG_CONTENT_256K         = 1024

# 功能业务
LOG_OUTPUT_LEVEL             = ERROR              #写入日志文件的输出级别，可配置为 TRACE, DEBUG, INFO, WARN, ERROR, FATAL
TBL_OUT_DIR                  = /tmp/tbls/sdt        #生成的tbl文件目录
TBL_FIELD_TABLE_DIR          = /root/program/field  #协议字段表输出目录，field目录必须自己创建
SHOW_OPERATOR_SWITCH         = 1                    #固网信息：1 = 显示、0 = 不显示
HARDWARE_TYPE                = NONE                 #建联硬件类型组合,共有以下10种：
                                                            #NONE -> 无　
                                                            #M:RT6402 -> 移动荣腾6402机型,
                                                            #M:RT9800 -> 移动荣腾9800机型,
                                                            #M:JL -> 移动金陵,
                                                            #M:HW:YN -> 移动恒为云南,
                                                            #M:HW:DB -> 移动恒为东北,
                                                            #M:HW:DEFAULT -> 移动恒为默认
                                                            #F:HW:SH:VLAN -> 固网恒为上海VLAN,
                                                            #F:HW:SH:MAC -> 固网恒为上海MAC,
                                                            #F:PH -> 固网普慧

DATA_SOURCE            = 3                          #数据来源,
                                                    #1:通用再现
                                                    #2:全量在线
                                                    #3:离线,配置为3时,RESV3表示任务名,RESV8表示数据包来源文件名
OFFLINE_READ_OVER_STOP = 0                          #离线解析时,读完数据后是否要停止程序,1停,0不停,默认0
OPERATOR_SWITCH        = CTCC
RING_RSS_MODE          = 0
TBL_OUT_MINUTE_DIR         = 0                       #实体文件目录控制 打开为输出 分钟级别目录 不打开为日期级别

#DEVNO = 221                               #tbl中的devno字段：1.自定义；2.无自定义，默认值是AUTO，统计设备各ip地址末端（如：***************末端为221），选择使用最多的末端作为设备号
#OPERATOR_SWITCH = 'TestStr'               #tbl中OPERATOR_TYPE字段，1.自定义字符串，字符串可以使用双引号""、单引号''或者不用引号；2.默认值AUTO，使用默认值时从数据流中读取运营商信息。
LOG_OUTPUT_PATH             = ./log.txt             #日志文件的路径
CONVERSATION_SWITCH         = 1                     #关联识别开关，0 关，1 开
CONVERSATION_IDENTIFY_PKT_NUM = 1                     #关联协议最大识别包数; 主要应对rtl回放报文乱序,可调大该值,最大65535.默认1
RTL_FLAG                    = 1                     #是否将荣腾标签写入tbl，0 否，1 是
IP_POSITION_SWITCH          = 0                     #ip地理位置模块开关,非0时打开,默认为0
TBL_WXF_FILTER_SWITCH       = 1                     #wxf开关，0 关，1 开,默认开
WRITE_SCTP_TBL              = 1                     #sctp 写tbl开关，默认关闭

HTTPS_DEFAULT_SWITCH        = 0                     #https默认开关,如果开启白名单或黑名单,但没在名单里找到域名,那么如果本参数配为0,则解析这条数据,如配为1,则不解析,默认为0
HTTPS_WHITELIST_SWITCH      = 0                     #https白名单开关,0不开启,1开启,默认为0
HTTPS_BLACKLIST_SWITCH      = 0                     #https黑名单开关,0不开启,1开启,默认为0
HTTPS_WHITELIST_FILENAME    = ./sslw.txt            #https白名单路径,只有上面白名单开关置1时才起作用,可配绝对路径,也可配相对dpi的相对路径
HTTPS_BLACKLIST_FILENAME    = ./sslb.txt            #https黑名单路径,只有上面黑名单开关置1时才起作用,可配绝对路径,也可配相对dpi的相对路径
HTTP_RESPONSE_LIMITE        = 1                     #HTTP下行的还原, 默认只限制输出content_type:text/html. [1:开启限制 0:无限制]
HTTP_EXQUISITE_SWITCH       = 1
DECODE_IDENTITY_SWITCH      = 0                    #decode 非标端口协议识别开关

DNS_DEFAULT_SWITCH          = 0                     #dns默认开关,如果开启白名单或黑名单,但没在名单里找到域名,那么如果本参数配为0,则解析这条数据,如配为1,则不解析,默认为0
DNS_WHITELIST_SWITCH        = 0                     #dns白名单开关,0不开启,1开启,默认为0
DNS_BLACKLIST_SWITCH        = 0                     #dns黑名单开关,0不开启,1开启,默认为0
DNS_WHITELIST_FILENAME      =  ./dnsw.txt           #dns白名单路径,只有上面白名单开关置1时才起作用,可配绝对路径,也可配相对dpi的相对路径
DNS_BLACKLIST_FILENAME      =  ./dnsb.txt           #dns黑名单路径,只有上面黑名单开关置1时才起作用,可配绝对路径,也可配相对dpi的相对路径

IPV4_BLACKLIST_SWITCH       = 0                     #ip4黑名单开关,0不开启,1开启,默认为0
IPV4_BLACKLIST_FILENAME     = ./ip4.txt             #ip4黑名单路径,只有上面黑名单开关置1时才起作用,可配绝对路径,也可配相对dpi的相对路径

# 内容还原开关，值为0关闭，值为1开启，默认关闭
POP_CONTENT_SWITCH          = 1
SMTP_CONTENT_SWITCH         = 1
IMAP_CONTENT_SWITCH         = 1
FTP_CONTENT_SWITCH          = 1
TELNET_CONTENT_SWITCH       = 1
TFTP_CONTENT_SWITCH         = 1

# 协议识别开关,关闭之后停止识别解析此类协议，0 关闭，1 开启，默认开启
FLOW_LOG_SWITCH             = 1                    #是否把flow信息写入tbl文件，0 否，0 是

PROTOCOL_SWITCH_HTTP        = 1
PROTOCOL_SWITCH_DNS         = 1
PROTOCOL_SWITCH_SSL         = 1
PROTOCOL_SWITCH_X509        = 0 #SDT中无需开启X509
PROTOCOL_SWITCH_SSH         = 1
PROTOCOL_SWITCH_EMAIL       = 1 #当此开关为1时，三大邮件不受自身的协议开关控制，強制为开
PROTOCOL_SWITCH_SMTP        = 0 #内存泄漏太多暂不启用
PROTOCOL_SWITCH_POP         = 0 #内存泄漏太多暂不启用
PROTOCOL_SWITCH_IMAP        = 0 #内存泄漏太多暂不启用
PROTOCOL_SWITCH_FTP_CONTROL = 1
PROTOCOL_SWITCH_FTP_DATA    = 1
PROTOCOL_SWITCH_SMB         = 1
PROTOCOL_SWITCH_MYSQL       = 1
PROTOCOL_SWITCH_TELNET      = 1
PROTOCOL_SWITCH_AH          = 1
PROTOCOL_SWITCH_BGP         = 1
PROTOCOL_SWITCH_CDP         = 1
PROTOCOL_SWITCH_CLDAP       = 1
PROTOCOL_SWITCH_CLASSICSTUN = 1
PROTOCOL_SWITCH_DCERPC      = 1
PROTOCOL_SWITCH_DHCP        = 1
PROTOCOL_SWITCH_DTLS        = 1
PROTOCOL_SWITCH_EIGRP       = 1
PROTOCOL_SWITCH_ESP         = 1
PROTOCOL_SWITCH_ESMTP       = 1
PROTOCOL_SWITCH_GRE         = 1
PROTOCOL_SWITCH_GTP_CONTROL = 1
PROTOCOL_SWITCH_H323        = 1
PROTOCOL_SWITCH_IAX2        = 1
PROTOCOL_SWITCH_ICMP        = 1
PROTOCOL_SWITCH_ISAKMP      = 1
PROTOCOL_SWITCH_ISUP_L5     = 1
PROTOCOL_SWITCH_KERBEROS    = 1
PROTOCOL_SWITCH_L2TP        = 1
PROTOCOL_SWITCH_LDAP        = 1
PROTOCOL_SWITCH_MEGACO      = 1
PROTOCOL_SWITCH_MGCP        = 1
PROTOCOL_SWITCH_OCSP        = 1
PROTOCOL_SWITCH_OSPF        = 1
PROTOCOL_SWITCH_PPTP        = 1
PROTOCOL_SWITCH_RADIUS      = 1
PROTOCOL_SWITCH_RTP         = 1
PROTOCOL_SWITCH_RTSP        = 1
PROTOCOL_SWITCH_SDP_L5      = 1
PROTOCOL_SWITCH_S0AP        = 1
PROTOCOL_SWITCH_SIP         = 1
PROTOCOL_SWITCH_SNMP        = 1
PROTOCOL_SWITCH_STUN        = 1
PROTOCOL_SWITCH_TURN        = 1
PROTOCOL_SWITCH_TDS         = 1
PROTOCOL_SWITCH_TFTP        = 1
PROTOCOL_SWITCH_VNC         = 1
PROTOCOL_SWITCH_RTCP        = 1
PROTOCOL_SWITCH_PGSQL       = 1
PROTOCOL_SWITCH_TNS         = 1
PROTOCOL_SWITCH_MODBUS      = 1
PROTOCOL_SWITCH_DNP3        = 1
PROTOCOL_SWITCH_S7COMM      = 1
PROTOCOL_SWITCH_IEC104      = 1
PROTOCOL_SWITCH_TLL         = 0
PROTOCOL_SWITCH_CWMP        = 1
PROTOCOL_SWITCH_SYSLOG      = 1
PROTOCOL_SWITCH_SOCKS       = 1


#重复字段输出开关 0不输出,1输出,默认不输出
CLDAP_REPEAT = 0
H323_REPEAT  = 0
LDAP_REPEAT  = 0
RTSP_REPEAT  = 0

PCAP_FLOW_DIR=/tmp/tbls
# PCAP包采集配置
#PCAP_FLOW_PRO  = "SSL,SSH"                  #需要输出PCAP包的协议名称(须大写,英文逗号隔开),目前只能抓SSH，SSL，ISAKMP，DTLS的包
#PCAP_FLOW_DIR  = "/tmp/flow_pcap"           #输出报文路径
#PCAP_FLOW_NUM  = 10                         #每个流存储报文条数,配置为m时,保存选中的流的前m条报文,等于-1时存储整条流的所有报文
#PCAP_FLOW_SIZE = 10                         #每个文件存储大小, 单位是 MB
#PCAP_FLOW_RATE = 1000                       #流抽样率,默认0不抽样,配置n大于零时,每n条流抽取其中的一条,配置为-3时只抽取ssl的实际加密数据,越过协商阶段

# 通用的解析错误输出PCAP报文
ERROR_PCAP_SIZE            = 0                    #解析失败PCAP文件上限(0: 关闭 N:限定转储文件大小,单位MB)
ERROR_PCAP_DIR             = "/tmp/tbls/ERR"      #解析失败时, 每个PCAP文件存储路径

# HTTP 专属设置
HTTP_SKIP_BODY             = 1                     #设置是否允许跳过HTTP BODY解析, 不解析BODY数据域
HTTP_TCP_OUT_OF_ORDER_NUM  = 400                   #HTTP TCP 报文乱序最大容忍值(建议20 ~ 800, 值越大,重组效果越好,越耗内存)
HTTP_TCP_MISS_PADDING_LEN  = 2000                  #HTTP TCP 报文缺包长度小于N时,允许PADDING(0: 关闭)
HTTP_TCP_MISS_PADDING_STR  = "  "                  #当允许 HTTP TCP LOSS PADDING  PADING自定义字符设定(默认空格)
HTTP_TCP_ERROR_PCAP_DUMP   = 0                     #HTTP解析识别, 输出错包.(0:关闭 1:开启 需开启 ERROR_PCAP_SIZE功能)
HTTP_STRIP_CACHE_SIZE      = 1024576
HTTP_STORE_FILE_SIZE       = 1024576

HTTP_DROP_NO_CONTENT_TYPE  = 0                     #是否丢弃无content-type字段的http数据，1-丢弃，0-不丢弃
#HTTP_CONTENT_TYPE_FILTER   = "枪"      #http content-type过滤规则，配置支持正则，以逗号分隔最多支持10个
HTTP_SWITCH_STORE_FILE     = 0                     #是否开启http写文件功能，1-开启，0-关闭
HTTP_SWITCH_RESPONSE_FILE  = 0                     #http写文件，对于下行数据是否要写文件开关，1-开启写下行文件，0-关闭不写下行数据到文件


SSL_SSH_MIN_ENC_NUM = 4                     #SSL,SSH双向加密前两个报文,所以每个pcap文件应该有四个报文,有的时候不够4个,此配置项可配置最低能容忍几个
SSL_PCAP = 0                                #SSL双向加密的前两个报文输出到TBL目录，0 不输出，1 输出
SSH_PCAP = 0                                #SSH双向加密的前两个报文输出到TBL目录，0 不输出，1 输出


SSL_FLOW_MODE = 0                           #SSL是否流模式解析,1是,0否,默认0
X509_WRITE_SWITCH = 0                       #写X.509证书开关,0不写,1写
X509_VERIFY_SWITCH = 0                      #X.509是否认证开关,1认证,0不认证,默认不认证
SIP_FROM_TO_NOTEQUAL = 0                    #sip中如果from=to时,1不输出,0输出,默认0
GTP_ONLY_FOR_ULI_DEVICE_TAG = 0             #gtp_c中如只需要位置信息及三元组则本开关置1，默认置0
DNS_ONLY_ANS = 0                            #dns是否只输出响应,1是,0否,默认0
WRITE_ONE_ESP = 1                           #1一个流写一条esp,0有多少写多少,默认为1
WRITE_L2TP_INNER = 1                        #l2tp隧道内inner ip port要不要写,1要<很多>,0不要,默认1
SHOW_TRAFFIC_SPEED_ALL = 1                  #针对vtysh命令show traffic speed新增SHOW_TRAFFIC_SPEED_ALL配置项,配为1可以对比解析速度,0不显示,默认1

SSH_LOGIN_VALID_NUM = 20                    #SSH登录是否成功的报文数量阈值
RDP_LOGIN_VALID_NUM = 40                    #RDP登录是否成功的报文数量阈值


# sdt 相关配置项
#--------------------------------------------------------------------------------------------------------------------------------------
UNKNOWN_CACHE_MAX       = 0                 # 协议识别成功之前, 每个会话最多缓存多少个报文缓存个数, 协议识别成功之后 缓存的报文用于TCP重组
SDT_FORWARD_MAC_POOL    = "A1:A2:A3:A4:A5:A6,B1-B2-B3-B4-B5-B6" #帧转发的默认MAC池
SDT_CACHE_MAX_PKTS      = 100               # 会话报文缓存个数, 用于输出会话的packet_dump
SDT_ENABLE_IPFF_MATCH   = 1                 #是否允许单包匹配[0:禁止 1:允许]
SDT_RULES_DEFAULT_ACTION= "packet_dump(0,0);" #默认action
SDT_IP_WITH_MASK        = 0                 # 强制 规则语法的IP带上掩码
SDT_RULES_FILE          = ./rules.sdt       # sdt规则文件(研发自测用途)
SDT_PCAP_IDENTIFICATION = "YVIEW"           #输出PCAP的标识名
SDT_RELOAD_RULE_SWITCH  = 1                 # 是否拉取历史规则
SDT_OUT_PCAP_MAX_MB     = 500               # 500M
SDT_OUT_PCAP_MAX_TIME   = 15                #无实意项
PKT_STREAM_MEMPOOL_NUM  = 2000000           #无实意项
SDT_OUT_EVENT_MAX_LINE  = 10000             #event文件按照多少行切割
SDT_RULES_MAX_NUM       = 8000000           #SDT用HASH表存储 规则的ACtion
SDT_LOCAL_PORT          = 8888              # 本地http监听端口
SDT_WEB_ADDR            = "***********:8086"              # web管理服务端口
SDT_USER_ID             = 1                 # web user id
SDT_SWITCH_MULT_HIT     = 2                 # 多规则匹配 1-只支持1个，2-支持多个
SDT_MATCH_STATUS        = 0                 # 规则匹配结果打印开关0-关闭，1-开启匹配成功打印，2-开启匹配失败打印, 3-成功和失败都开启
SDT_FIELDS_MAP_DIR      = ../field          # 字段映射表


SDT_SEND_KAFKA             = 0                    # 解析结果是否发送到kafak服务器
SDT_KAFKA_IP               = "*************:8866"  # kafka服务器ip


SDX_MAC_PACKET_HEADER_FLAG = 0               # sdx 特殊mac头解析支持， 1-支持，0-不支持
SDX_MATCH_ALGORITHM        = 1               # sdx 规则匹配算法选择 0-bitmap，1-tries前缀树
DUMP_PCAP_WITH_SDX_MAC     = 0               # 输出 pcap 是否包含51字节 sdx mac 头。默认不带头

SDT_OUT_PRODUCE_DATA_DEV_NAME = YV-S2000       # 产生数据的设备名, 用于输出元数据文件名称
#SDT_OUT_PRODUCE_DATA_DEV_IP = *************   # 产生数据的IP地址, 用于输出文件名称  此配置不启用
SDX_IP_INTERFACE = "ens192"      # 通信口，将从此接口上获取设备 ip

HARDWARE_ZIDNUMBER           = 0  #设备zd号
SDX_STAT_REPORT_TIME       = 300             # 统计信息上报频率（秒）
SDX_STAT_REPORT_WEB_ADDR   = "***********:8086"  # 统计信息上报服务器

SDX_STAT_PROGRAM_CASE_NAME = "yaDpiSdt_01"   # 运行实例名配置
SDX_OUTPUT_ALL_FIELD       = 1    #未解析的字段输出空

SDX_RX_PORT_LIST           = "all"              # 输入端口配置，pci 地址，多个用逗号隔开; "all"代表从所有端口收包
SDX_TX_PORT_LIST           = "0000:0b:00.0"     # 输出端口配置，pci 地址，多个用逗号隔开
TXQ_NUM                    = 1                  # 只有转发开关开启后，此发送队列值的设置才有效
SDT_DATA_COLLECT_ENABLE    = 0                  # 转发为 SDX头 报文类型

SDX_DATA_FROM              = ML       # 数据来源：ML，KS，ZW
SDX_SIGTYPE                = 0x1	  # 0x1 : 信号处理节点机
			               			  #	0x2: IP统计设备
			               			  #	0x3: 业务解承载设备
			               			  #	0x4:整体信号分流器
			               			  #	0x5:通道化信号分流器（高阶通道化）
			               			  #	0x6:高阶虚级联处理设备（通道化分流器）
			               			  #	0x7:低阶虚级联处理设备0x8:ATM处理设备
			               			  #	0x9:DDN处理设备
			               			  #	0xa－0xf:保留

SDX_FTP_PORT               = 21
SDX_MDSECDEG                = "UNKNOWN"          #元数据密级
SDX_FILESECDEG              = "UNKNOWN"          #数据文件密级
SDX_SECDEGPRO               = "UNKNOWN"          #安全等级保护

SDX_WEB_CONFIG_ADDR         = ""
SDT_REFLECT_RULE_SWITCH     = 0      #是否为 规则反射节点
#VTYSH_SOCKET_PORT   = 8888

[core]
DISSECTOR_THREAD_NUM       = 6                # 解析线程数量
FLOW_AGAIN_THREAD_NUM      = 2                # 老化线程数量，建议少于解析线程，并且和解析线程为倍数关系
OUTPUT_THREAD_NUM          = 4                # 输出线程数量
MATCH_THREAD_NUM           = 2                # 匹配线程数量

[input]
INPUT_TYPE                 = 1                 # 输入模式 0：读取网卡， 1：离线读取pcap文件
INPUT_PCAP_DIR             = /home/<USER>/pcap/x509      # 离线读取pcap文件目录

[output]
# OUTPUT_WRITE_SWITCH         = 1                 # 输出是否落盘
ADAPT_TYPE                  = 0                 # 适配类型，json适配，lua 适配  0-lua 1-json
ADAPT_DIR_LUA               = "./adapt_lua"     # 适配文件目录, 只有 ADAPT_TYPE 为 0 时有效
DATE_SUBDIR_FLAG            = 0             #是否增加日期子目录http/20240515/
TBL_FILENUM_PERDAY_PATH = /tmp/.tbl_filenum_per_day_Metedata.txt  #生成tbl文件计数，重启时不会覆盖tbl文件

[low]
CMD_LINE_LOW = "./yaDpiSdt -l 0-6 -- -r 1 -p 2 -a 3 -l 3 -m 4 -o 5 -c 6"
#CMD_LINE_LOW = "./yaDpiSdt --file-prefix=dpisdt --allow "0000:AB:CD.0" --vdev=virtio_user0,path=/tmp/sock0 -l 0-5 -- -r 1 -p 2 -a 3 -l 3 -m 4 -o 5" #virtio_user接收端
#"./yaNewFilter -l 0,1,2,3,4,5,6,7 --vdev=net_vhost0,iface=/tmp/sock0 --vdev=net_vhost1,iface=/tmp/sock1" #net_vhost发送端

NB_MBUF                     = 65536  # default value: 1048576,minimum value 512
RXQ_NUM                     = 4      #每个网卡对应的收包队列数，此参数配合run/start.sh里-r后的选项对发包队列进行配置
PACKET_RING_SIZE            = 65536    # 每个解析线程收包队列里可缓冲的包数,必须为2的幂
MAX_HASH_NODE_PER_THREAD    = 4096     # default value: 4194304
MAX_CONVERSATION_HASH_NODE  = 256    # default value: 1048576
MAX_FLOW_NUM                = 8192
TBL_RING_SIZE               = 16384    # default value: 65536, 必须为 2 的幂,minimum value 512
TBL_LOG_CONTENT_256K        = 1024   # default value: 262144
SDT_WEB_ADDR                = ""  # web管理服务端口
SDX_STAT_REPORT_WEB_ADDR    = ""  # 统计信息上报服务器
SDX_WEB_CONFIG_ADDR         = ""

[arkime]
# Elasticsearch发送配置
ES_ENABLE                   = 1                       # 启用ES发送 (0:禁用 1:本地json 2:http发送到es)
ES_HOST                     = localhost                # ES服务器地址
ES_PORT                     = 9200                     # ES端口
ES_USERNAME                 = ""                         # ES用户名(可选)
ES_PASSWORD                 = ""                         # ES密码(可选)
ES_TIMEOUT                  = 30                       # 超时时间(秒)

# Arkime PCAP文件管理配置
ARKIME_PCAP_DIR             = /tmp/tbls/raw          # PCAP文件存储目录
ARKIME_MAX_FILE_SIZE_MB     = 1                    # 最大文件大小(MB) 默认1GB
ARKIME_MAX_FILE_TIME_SEC    = 3600                     # 最大文件时间(秒) 默认1小时
ARKIME_NODE_NAME            = localhost                # 捕获节点名称
JADE_FILE_PATCH             = /tmp/tbls/parsers     #jade文件生成目录
