# 启动命令,从 pcap 目录读取,需要搭配 setup_dpdk_env.sh -b
CMD_LINE = "./yaDpi --vdev="pcap_dir,dir=/root/pcaps" -l 0-1 -- -r 1 -p 2 -l 3"

# 性能参数
RXQ_NUM = 4                                #每个网卡对应的收包队列数，此参数配合run/start.sh里-r后的选项对发包队列进行配置
DISSECTOR_THREAD_NUM = 16                  #dpi的解析线程数，此参数暂不使用，解析线程数由run/start.sh里-p后的选项配置
MBUF_SIZE = 2048                           #每个rte_mbuf可携带数据包的长度，默认2048
MAX_PKT_LEN = 2000                         #链路层携带的最大字节数i，默认1518
# NB_MBUF = 1048576                        #内存池的最大包缓冲数，1048576=2**20
MEMPOOL_CACHE_SIZE = 256                   #一组socket里每个核心所分配的包高速缓冲数
PACKET_RING_SIZE = 16384                   #每个解析线程收包队列里可缓冲的包数,必须为2的幂
WRITE_TBL_MAXTIME = 60                     #每个打开的tbl文件的最长打开时间,单位是秒
TCP_FLOW_TIMEOUT = 30                      #tcp数据包的超时会话时间,单位是秒
UDP_FLOW_TIMEOUT = 30                      #udp数据包的超时会话时间，单位是秒
SCTP_FLOW_TIMEOUT = 30                     #sctp数据包的超时会话时间，单位是秒
TCP_IDENTIFY_PKT_NUM = 10                  #解析tcp时，当处理的某对IP的包数大于此数时，可对其应用层协议跳过识别过程直接进行解析
UDP_IDENTIFY_PKT_NUM = 8                   #解析udp时，当处理的某对IP的包数大于此数时，可对其应用层协议跳过识别过程直接进行解析
SCTP_IDENTIFY_PKT_NUM = 10                 #解析sctp时，当处理的某对IP的包数大于此数时，可对其应用层协议跳过识别过程直接进行解析
TCP_RESSEAMBLE_MAX_NUM = 32                #可重组的tcp单向最大会话数
LOG_MAX_NUM = 10000                        #每个tbl文件里可存信息的最大条数
IDLE_SCAN_PERION = 10000                   #对所有会话的固定超时时间，单位是微妙(us) 

# 功能业务
LOG_OUTPUT_LEVEL = ERROR                   #写入日志文件的输出级别，可配置为ERROE,DEBUG,WARNING
TBL_OUT_DIR = /tmp/tbls                    #生成的tbl文件目录
TBL_FIELD_TABLE_DIR = /root/program/field  #协议字段表输出目录，field目录必须自己创建

HARDWARE_TYPE = NONE                       #建联硬件类型组合,共有以下10种：NONE -> 无　
                                           #                               M:RT6402 -> 移动荣腾6402机型, M:RT9800 -> 移动荣腾9800机型, M:JL -> 移动金陵,  M:HW:YN -> 移动恒为云南,  M:HW:DB -> 移动恒为东北, M:HW:DEFAULT -> 移动恒为默认
                                           #                               F:HW:SH:VLAN -> 固网恒为上海VLAN,  F:HW:SH:MAC -> 固网恒为上海MAC, F:PH:DEFAULT -> 普慧

#DEVNO = 221                               #tbl中的devno字段：1.自定义；2.无自定义，默认值是AUTO，统计设备各ip地址末端（如：***************末端为221），选择使用最多的末端作为设备号
#OPERATOR_SWITCH = 'TestStr'               #tbl中OPERATOR_TYPE字段，1.自定义字符串，字符串可以使用双引号""、单引号''或者不用引号；2.默认值AUTO，使用默认值时从数据流中读取运营商信息。
LOG_OUTPUT_PATH = ./log.txt                #日志文件的路径
CONVERSATION_SWITCH = 1                    #关联识别开关，0 关，1 开
RTL_FLAG = 1                               #是否将荣腾标签写入tbl，0 否，1 是
FLOW_LOG_SWITCH = 1                        #是否把flow信息写入tbl文件，0 否，1 是
IP_POSITION_SWITCH = 0                     #ip地理位置模块开关,非0时打开,默认为0
TBL_WXF_FILTER_SWITCH = 1                  #wxf开关，0 关，1 开,默认开

WHITELIST_SWITCH = 0                       #https白名单开关,0不开启解析所有域名,1开启只解析白名单域名
WHITELIST_FILENAME = ../etc/whitelist.txt  #白名单路径,只有上面白名单开关置1时才起作用
HTTP_RESPONSE_LIMITE     = 1               #HTTP下行的还原, 默认只限制输出content_type:text/html. [1:开启限制 0:无限制]

# 内容还原开关，值为0关闭，值为1开启，默认关闭
POP_CONTENT_SWITCH = 0
SMTP_CONTENT_SWITCH = 0
IMAP_CONTENT_SWITCH = 0
FTP_CONTENT_SWITCH = 0
TELNET_CONTENT_SWITCH = 0
TFTP_CONTENT_SWITCH = 0

# 协议识别开关,关闭之后停止识别解析此类协议，0 关闭，1 开启，默认开启
PROTOCOL_SWITCH_CDP = 1
PROTOCOL_SWITCH_CLDAP = 1
PROTOCOL_SWITCH_CLASSICSTUN = 1
PROTOCOL_SWITCH_DCERPC = 1
PROTOCOL_SWITCH_DHCP = 1
PROTOCOL_SWITCH_DNS = 1
PROTOCOL_SWITCH_DTLS = 1
PROTOCOL_SWITCH_EIGRP = 1
PROTOCOL_SWITCH_ESP = 1
PROTOCOL_SWITCH_ESMTP = 1
PROTOCOL_SWITCH_FTP_CONTROL = 1
PROTOCOL_SWITCH_FTP_DATA = 1
PROTOCOL_SWITCH_GRE = 1
PROTOCOL_SWITCH_GTP_CONTROL = 1
PROTOCOL_SWITCH_H323 = 1
PROTOCOL_SWITCH_HTTP = 1
PROTOCOL_SWITCH_IAX2 = 1
PROTOCOL_SWITCH_IMAP = 1
PROTOCOL_SWITCH_ISAKMP = 1
PROTOCOL_SWITCH_ISUP_L5 = 1
PROTOCOL_SWITCH_KERBEROS = 1
PROTOCOL_SWITCH_L2TP = 1
PROTOCOL_SWITCH_LDAP = 1
PROTOCOL_SWITCH_MEGACO = 1
PROTOCOL_SWITCH_MGCP = 1
PROTOCOL_SWITCH_MYSQL = 1
PROTOCOL_SWITCH_OCSP = 1
PROTOCOL_SWITCH_OSPF = 1
PROTOCOL_SWITCH_POP = 1
PROTOCOL_SWITCH_PPTP = 1
PROTOCOL_SWITCH_RADIUS = 1
PROTOCOL_SWITCH_RTP = 1
PROTOCOL_SWITCH_RTSP = 1
PROTOCOL_SWITCH_SDP_L5 = 1
PROTOCOL_SWITCH_S1AP = 1
PROTOCOL_SWITCH_SIP = 1
PROTOCOL_SWITCH_SMB = 1
PROTOCOL_SWITCH_SNMP = 1
PROTOCOL_SWITCH_SSH = 1
PROTOCOL_SWITCH_SSL = 1
PROTOCOL_SWITCH_STUN = 1
PROTOCOL_SWITCH_TELNET = 1
PROTOCOL_SWITCH_TDS = 1
PROTOCOL_SWITCH_TFTP = 1
PROTOCOL_SWITCH_VNC = 1
PROTOCOL_SWITCH_X509 = 1

#重复字段输出开关 0不输出,1输出,默认不输出
CLDAP_REPEAT = 0
H323_REPEAT  = 0
L2TP_REPEAT  = 0 
LDAP_REPEAT  = 0
PPTP_REPEAT  = 0
RTSP_REPEAT  = 0

# PCAP包采集配置
#PCAP_FLOW_PRO  = "SSL,DTLS,SSH,ISAKMP"      #需要输出PCAP包的协议名称(须大写,英文逗号隔开)
#PCAP_FLOW_DIR  = "/tmp/tbls/flow_pcap"      #输出报文路径
#PCAP_FLOW_NUM  = 10                         #每个流存储报文条数
#PCAP_FLOW_SIZE = 10                         #每个文件存储大小, 单位是 MB
#PCAP_FLOW_RATE = 1000                       #取样率

SSL_PCAP = 0                                #SSL加密的前两个报文输出到TBL目录，0 不输出，1 输出
SSH_PCAP = 0                                #SSH加密的前两个报文输出到TBL目录，0 不输出，1 输出  


SIP_FROM_TO_NOTEQUAL = 0                    #sip中如果from=to,则不输出
GTP_ONLY_FOR_ULI_DEVICE_TAG = 0             #gtp_c中如只需要位置信息及三元组则本开关置1，默认置0 


# 低内存配置，并注释掉上文中相同的配置参数
NB_MBUF = 512                              # default value: 1048576,minimum value 512
MAX_FLOW_NUM = 1024                        # default value: 16777216
TCP_REASSEMBLE_MEMPOOL_NUM = 1024          # default value: 1048576, 必须为大于 768 的 2 的幂 ,minimum value 512
MAX_HASH_NODE_PER_THREAD = 64              # default value: 4194304
MAX_CONVERSATION_HASH_NODE = 256           # default value: 1048576
TBL_RING_SIZE = 512                        # default value: 65536, 必须为 2 的幂,minimum value 512
TBL_LOG_CONTENT_256K = 4096                # default value: 262144
