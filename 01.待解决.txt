隧道类嵌套协议 - 目前不支持协议的嵌套 


IP4IP4
IP4IP6

IP6IP6
IP6IP4

L2TP
GTP

L2TP OVER GTP

SOCK4

相互嵌套：

GTP((L2TP(SOCK4(HTTP WEBSock(TCP_stream))))
 
 



加密：
SSTP
PPTP
OPENVPN
SOCK5
IPC


// 删除掉的无用的C文件， 日记：
1. 不符合27类 SDT 筛选规则的协议
2. Makefile中未加载过的C文件

以下：
    dissect_isup.c     dpi_email.h         dpi_ldap.c           dpi_ospf.c    dpi_smpp.c
    dpi_anydesk.c      dpi_flow_log.c      dpi_ldp.c            dpi_pop.c     dpi_smpp.h
    dpi_cflow.c        dpi_gsm_a_common.c  dpi_lotus_nrpc.c     dpi_pptp.c    dpi_smtp.c
    dpi_cflow.h        dpi_gsm_sms.c       dpi_lotus_nrpc.h     dpi_radius.c  dpi_snmp.c
    dpi_classicstun.c  dpi_h323.c          dpi_m3ua.c           dpi_radius.h  dpi_snmp.h
    dpi_cloudstack.c   dpi_iax2.c          dpi_m3ua.h           dpi_rip.c     dpi_ssdp.c
    dpi_cloudstack.h   dpi_iax2.h          dpi_mac_interface.c  dpi_rsvp.c    dpi_stp.c
    dpi_cmpp.c         dpi_iec104.c        dpi_megaco.c         dpi_rtcp.c    dpi_stun.c
    dpi_cmpp.h         dpi_igmp.c          dpi_mgcp.c           dpi_rtp.c     dpi_t124.c
    dpi_cwmp_2.c       dpi_igmp.h          dpi_nas_eps.c        dpi_rtsp.c    dpi_t125.c
    dpi_dcerpc.c       dpi_imap.c          dpi_nspi.c           dpi_rtsp.h    dpi_teredo.c
    dpi_dcerpc.h       dpi_isakmp.c        dpi_ntlmssp.c        dpi_s1ap.c    dpi_tll-2.c
    dpi_dhcp.c         dpi_isakmp.h        dpi_ntlmssp.h        dpi_sccp.c    dpi_tll.c
    dpi_diameter.c     dpi_isis.c          dpi_oicq.c           dpi_sftp.c    dpi_tll.h
    dpi_dnp3.c         dpi_jt808.c         dpi_openvpn.c        dpi_sgmp.c    dpi_tpkt.c
    dpi_dtls.c         dpi_jt808.h         dpi_openvpn.h        dpi_sip.c     dpi_weixin.c
    dpi_eigrp.c        dpi_kerberos.c      dpi_ositp.c          dpi_skip.c    dpi_weixin.h
    dpi_email.c        dpi_kerberos.h      dpi_ositp.h          dpi_skip.h

