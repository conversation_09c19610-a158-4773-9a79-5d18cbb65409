#!/usr/bin/env python3

import requests, json
from sdt_logger import logger
from sdt_exception import TestRuleLoadError


class SdtHttpOperator:
    timeout = (3, 60)

    def __init__(self, host, port) -> None:
        self.__addr = '{}:{}'.format(host, port)
        self.__session = requests.Session()

    def __del__(self):
        self.__session.close()

    def __enter__(self):
        self.begin_transaction()
        if not self.__trans_id:
            raise TestRuleLoadError("sdt app 事务开始失败")
        return self

    def __exit__(self, ect, ecv, traceback):
        if not self.__trans_id:
            return True
        if ect:
            logger.error(ecv)
            self.rollback_transaction()
            return False
        if self.commit_transaction() == False:
            raise TestRuleLoadError("sdt app 事务提交失败")
        return True

    def get_cur_transid(self):
        return self.__trans_id

    def begin_transaction(self) -> int:
        self.__trans_id = None
        res = self.__session.get(url=f"http://{self.__addr}/sdx/rule/transaction",
                                params={'action': 'begin'},
                                timeout=self.timeout)
        res = res.json()
        if res['statusCode'] != 0:
            return None
        self.__trans_id = int(res['trans_id'])
        return self.__trans_id

    def commit_transaction(self) -> bool:
        res = self.__session.get(url=f"http://{self.__addr}/sdx/rule/transaction",
                                params={'action': 'commit', 'trans_id': self.__trans_id},
                                timeout=(3, 60 * 20))   # dpi 在事务提交这一步相当耗时
        res = res.json()
        return res['statusCode'] == 0

    def rollback_transaction(self) -> bool:
        res = self.__session.get(url=f"http://{self.__addr}/sdx/rule/transaction",
                                params={'action': 'rollback'},
                                timeout=self.timeout)
        res = res.json()
        return res['statusCode'] == 0

    def push_task_rule(self, rule_list):
        res = self.__session.post(url=f"http://{self.__addr}/module/addTaskRule",
                                params={'Ver':2024, 'trans_id': self.__trans_id},
                                headers={'content-type': "application/json"},
                                data=json.dumps({'rule_num': len(rule_list), 'rules': rule_list}),
                                timeout=self.timeout)
        return res.json()


def main():
    rules = [
        {'unit': '32202', 'task_id': '02', 'method': 'KS-ZC', 'topic_name': '',
        'task_sub_type': '11', 'task_mode': None, 'mode_param': None,
        'rule_id': '2', 'rule_groupid': '32', 'rule_group': '32-02', 'rule_mode': '0',
        'rule_text': 'IPFF 6@*:*>*:8080'
        },
        
        {'unit': '32202', 'task_id': '02', 'method': 'KS-ZC', 'topic_name': '',
        'task_sub_type': '11', 'task_mode': None, 'mode_param': None,
        'rule_id': '3', 'rule_groupid': '32', 'rule_group': '32-03', 'rule_mode': '0',
        'rule_text': 'IPFF 6@*:*>*:8888'
        }
    ]

    opt = SdtHttpOperator('127.0.0.1', 8888)
    opt.begin_transaction()
    opt.push_task_rule(rules)
    opt.commit_transaction()

if __name__ == '__main__':
    main()