{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "slink", "to_proto_name": "slink", "rule_proto_name": "slink", "field": [{"from_name": "payloaddsthex", "to_name": "payloaddsthex", "to_type": 13}, {"from_name": "payloadsrchex", "to_name": "payloadsrchex", "to_type": 13}, {"from_name": "packetsdst", "to_name": "packetsdst", "to_type": 9}, {"from_name": "bytesdst", "to_name": "bytesdst", "to_type": 9}, {"from_name": "paylendstset", "to_name": "paylendstset", "to_type": 13}, {"from_name": "paylensrcset", "to_name": "paylensrcset", "to_type": 13}, {"from_name": "packetssrc", "to_name": "packetssrc", "to_type": 9}, {"from_name": "bytessrc", "to_name": "bytessrc", "to_type": 9}, {"from_name": "tcpflag", "to_name": "tcpflag", "to_type": 13}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineNum4", "to_name": "lineNum4", "to_type": 9}, {"from_name": "lineNum3", "to_name": "lineNum3", "to_type": 9}, {"from_name": "lineNum2", "to_name": "lineNum2", "to_type": 9}, {"from_name": "lineNum1", "to_name": "lineNum1", "to_type": 9}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 9}, {"from_name": "SesBytes", "to_name": "SesBytes", "to_type": 9}, {"from_name": "SesPackets", "to_name": "SesPackets", "to_type": 9}, {"from_name": "ipProtocol", "to_name": "ipProtocol", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 13}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 13}, {"from_name": "dstAddrv6", "to_name": "dstAddrv6", "to_type": 13}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 13}, {"from_name": "srcAddrv6", "to_name": "srcAddrv6", "to_type": 13}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 13}]}