{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "lacp", "to_proto_name": "lacp", "rule_proto_name": "lacp", "field": [{"from_name": "PartnerStateFlagExpired", "to_name": "parisexp", "to_type": 9}, {"from_name": "PartnerStateFlagDefaulted", "to_name": "parisdef", "to_type": 9}, {"from_name": "PartnerStateFlagDistrib", "to_name": "paris<PERSON>", "to_type": 9}, {"from_name": "PartnerStateFlagCollecting", "to_name": "pariscol", "to_type": 9}, {"from_name": "PartnerStateFlagSync", "to_name": "<PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "PartnerStateFlagAggregation", "to_name": "parisagg", "to_type": 9}, {"from_name": "PartnerStateFlagTimeout", "to_name": "parist<PERSON>ut", "to_type": 9}, {"from_name": "PartnerStateFlagActivity", "to_name": "parisact", "to_type": 9}, {"from_name": "PartnerPort", "to_name": "par<PERSON>", "to_type": 9}, {"from_name": "PartnerPortPriority", "to_name": "parporp<PERSON>", "to_type": 9}, {"from_name": "<PERSON><PERSON><PERSON>", "to_name": "parkey", "to_type": 9}, {"from_name": "PartnerSystemID", "to_name": "parsysid", "to_type": 13}, {"from_name": "PartnerSystemPriority", "to_name": "parsyspri", "to_type": 9}, {"from_name": "ActorStateFlagExpired", "to_name": "actisexp", "to_type": 9}, {"from_name": "ActorStateFlagDefaulted", "to_name": "actisdef", "to_type": 9}, {"from_name": "ActorStateFlagDistrib", "to_name": "actisdis", "to_type": 9}, {"from_name": "ActorStateFlagCollecting", "to_name": "actiscol", "to_type": 9}, {"from_name": "ActorStateFlagSync", "to_name": "<PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "ActorStateFlagAggregation", "to_name": "actisagg", "to_type": 9}, {"from_name": "ActorStateFlagTimeout", "to_name": "actistimout", "to_type": 9}, {"from_name": "ActorStateFlagActivity", "to_name": "actisact", "to_type": 9}, {"from_name": "Actor<PERSON><PERSON>", "to_name": "actpor", "to_type": 9}, {"from_name": "ActorPortPriority", "to_name": "actporpri", "to_type": 9}, {"from_name": "<PERSON><PERSON><PERSON>", "to_name": "actkey", "to_type": 9}, {"from_name": "ActorSystemID", "to_name": "actsysid", "to_type": 13}, {"from_name": "ActorSystemPriority", "to_name": "actsyspri", "to_type": 13}, {"from_name": "SrcMAC", "to_name": "srcMacAddr", "to_type": 13}]}