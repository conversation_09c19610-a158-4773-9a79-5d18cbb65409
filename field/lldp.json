{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "lldp", "to_proto_name": "lldp", "rule_proto_name": "lldp", "field": [{"from_name": "OID", "to_name": "snmpOID", "to_type": 9}, {"from_name": "InterfaceNumber", "to_name": "snmpIntNum", "to_type": 9}, {"from_name": "InterfaceType", "to_name": "snmpIntType", "to_type": 9}, {"from_name": "ManagementAddress", "to_name": "snmpManAddr", "to_type": 9}, {"from_name": "ManagementAddressType", "to_name": "snmpAddrType", "to_type": 9}, {"from_name": "Capabilities", "to_name": "sysCap", "to_type": 9}, {"from_name": "SystemDescription", "to_name": "sysDes", "to_type": 13}, {"from_name": "SystemName", "to_name": "sysName", "to_type": 13}, {"from_name": "PortId", "to_name": "portID", "to_type": 9}, {"from_name": "PortIdType", "to_name": "portIDSubType", "to_type": 9}, {"from_name": "ChassisId", "to_name": "chaID", "to_type": 13}]}