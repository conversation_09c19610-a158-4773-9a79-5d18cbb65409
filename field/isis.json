{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "isis", "to_proto_name": "isis", "rule_proto_name": "isis", "field": [{"from_name": "mt_ip_metric1", "to_name": "MTIPRchIPv6Metr", "to_type": 13}, {"from_name": "mt_ip_distri1", "to_name": "MTIPRchIPv6Dist", "to_type": 13}, {"from_name": "mt_ip_prefix1", "to_name": "MTIPRchIPv6Perf", "to_type": 13}, {"from_name": "", "to_name": "MTIPRchIPv4Metr", "to_type": 13}, {"from_name": "", "to_name": "MTIPRchIPv4Dist", "to_type": 13}, {"from_name": "", "to_name": "MTIPRchIPv4Pref", "to_type": 13}, {"from_name": "", "to_name": "MTIPRchNeigID", "to_type": 13}, {"from_name": "mt_is_unrsv_bw1", "to_name": "MTISRchUnresvBw", "to_type": 13}, {"from_name": "mt_is_reservable_bw1", "to_name": "MTISRchResvBw", "to_type": 13}, {"from_name": "mt_is_max_bw1", "to_name": "MTISRchMaxBw", "to_type": 13}, {"from_name": "mt_is_neighbor_ipv4_addr1", "to_name": "MTISRchIPv4NeigAddr", "to_type": 13}, {"from_name": "mt_is_ipv4_addr1", "to_name": "MTISRchIPv4Addr", "to_type": 13}, {"from_name": "mt_is_admin_group1", "to_name": "MTISRchAdminGrp", "to_type": 13}, {"from_name": "", "to_name": "MTISRchMetr", "to_type": 13}, {"from_name": "", "to_name": "MTISRchNeigID", "to_type": 13}, {"from_name": "mt_is_type", "to_name": "MTType", "to_type": 13}, {"from_name": "", "to_name": "TEMetr", "to_type": 13}, {"from_name": "", "to_name": "extdISRchUnresvBw", "to_type": 13}, {"from_name": "", "to_name": "extdISRchResvBw", "to_type": 13}, {"from_name": "", "to_name": "extdISRchMaxBw", "to_type": 13}, {"from_name": "extd_is_neighbor_addr1", "to_name": "extdISRchNeigAddr", "to_type": 13}, {"from_name": "extd_is_inter_addr1", "to_name": "extdISRchInterfAddr", "to_type": 13}, {"from_name": "", "to_name": "extdISRchAdminGrp", "to_type": 13}, {"from_name": "extd_is_metric1", "to_name": "extdISRchNeigMetr", "to_type": 13}, {"from_name": "extd_is_id1", "to_name": "extdISRchNeigSysID", "to_type": 13}, {"from_name": "extd_ip_metric1", "to_name": "extdIPRchMetr", "to_type": 13}, {"from_name": "extd_ip_distri1", "to_name": "extdIPRchDist", "to_type": 13}, {"from_name": "extd_ip_prefix1", "to_name": "extdIPRchPref", "to_type": 13}, {"from_name": "", "to_name": "IPExtlRchPref", "to_type": 13}, {"from_name": "", "to_name": "IPIntlRchPref", "to_type": 13}, {"from_name": "lsp_id", "to_name": "LSPID", "to_type": 13}, {"from_name": "sequence_number", "to_name": "seqNum", "to_type": 13}, {"from_name": "hostname", "to_name": "hostName", "to_type": 13}, {"from_name": "is_type", "to_name": "ISType", "to_type": 13}, {"from_name": "", "to_name": "adjNeigID", "to_type": 13}, {"from_name": "protocols_supported", "to_name": "protSupport", "to_type": 13}, {"from_name": "area_address", "to_name": "areaAddr", "to_type": 13}, {"from_name": "ip6_address", "to_name": "IPv6Addr", "to_type": 13}, {"from_name": "ip4_address", "to_name": "IPAddr", "to_type": 13}, {"from_name": "disignated_system_id", "to_name": "LANID", "to_type": 13}, {"from_name": "ptp_neighbor_id", "to_name": "neigID", "to_type": 13}, {"from_name": "auth_info", "to_name": "auth", "to_type": 13}, {"from_name": "", "to_name": "TLVVal", "to_type": 13}, {"from_name": "", "to_name": "TLVType", "to_type": 9}, {"from_name": "holding_timer", "to_name": "holTime", "to_type": 10}, {"from_name": "sender_system_id", "to_name": "srcSysID", "to_type": 13}, {"from_name": "circuit_type", "to_name": "cirType", "to_type": 9}, {"from_name": "maximun_area_address", "to_name": "maximumAreNum", "to_type": 9}, {"from_name": "pdu_type", "to_name": "PDUType", "to_type": 9}, {"from_name": "system_id_length", "to_name": "IDLen", "to_type": 9}, {"from_name": "version1", "to_name": "ver/protIDExt", "to_type": 9}, {"from_name": "protocol_discriminator", "to_name": "intRouProtDis", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}