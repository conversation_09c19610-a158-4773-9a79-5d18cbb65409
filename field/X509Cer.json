{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "X509Cer", "to_proto_name": "X509Cer", "rule_proto_name": "cert", "field": [{"from_name": "", "to_name": "certFullText", "to_type": 13}, {"from_name": "", "to_name": "Certype", "to_type": 9}, {"from_name": "", "to_name": "KeyPur", "to_type": 13}, {"from_name": "", "to_name": "basicCons", "to_type": 13}, {"from_name": "basicConsPathLen", "to_name": "basicConsPathLen", "to_type": 13, "rule_name": "basic_constriants.pathlen", "tll": 1}, {"from_name": "basicConsCA", "to_name": "basicConsCA", "to_type": 13, "rule_name": "basic_constriants.ca", "tll": 1}, {"from_name": "authAccess", "to_name": "authinfo", "to_type": 13, "rule_name": "authinfo", "tll": 1}, {"from_name": "dns_num", "to_name": "subAltDNSCnt", "to_type": 9, "tll": 1}, {"from_name": "totaltime", "to_name": "daysTotal", "to_type": 9, "tll": 1}, {"from_name": "extension", "to_name": "extSet", "to_type": 13, "rule_name": "ext.set", "tll": 1}, {"from_name": "print", "to_name": "hash", "to_type": 13, "rule_name": "fp_hash", "tll": 1}, {"from_name": "print_alg", "to_name": "fpAlg", "to_type": 13, "rule_name": "fp_algorithm", "tll": 1}, {"from_name": "subj_pubkey", "to_name": "pubkey", "to_type": 13, "rule_name": "public_key", "tll": 1}, {"from_name": "lasttime", "to_name": "daysRem", "to_type": 9, "rule_name": "days.remaining", "tll": 1}, {"from_name": "subject", "to_name": "subject", "to_type": 13, "rule_name": "subject", "tll": 1}, {"from_name": "issuer", "to_name": "issuer", "to_type": 13, "rule_name": "issuer", "tll": 1}, {"from_name": "", "to_name": "Protabname", "to_type": 13}, {"from_name": "ext_sums", "to_name": "extCnt", "to_type": 9, "rule_name": "ext.cnt", "tll": 1}, {"from_name": "access_location", "to_name": "certAuthInfAccLoc", "to_type": 13}, {"from_name": "access_method", "to_name": "certAuthInfAccMet", "to_type": 13}, {"from_name": "crl_dist_points", "to_name": "certRevListSrc", "to_type": 13, "rule_name": "crldist_points", "tll": 1}, {"from_name": "", "to_name": "extKeyUsage", "to_type": 13}, {"from_name": "", "to_name": "subDirAtt", "to_type": 13}, {"from_name": "issuer_other", "to_name": "issAltName", "to_type": 13}, {"from_name": "issuer_ip", "to_name": "issAltIP", "to_type": 13}, {"from_name": "issuer_dns", "to_name": "issAltNameSys", "to_type": 13}, {"from_name": "subj_other", "to_name": "subAltName", "to_type": 13}, {"from_name": "subj_ip", "to_name": "subAltIP", "to_type": 13, "rule_name": "alt_ip", "tll": 1}, {"from_name": "subj_dns", "to_name": "subAltDNS", "to_type": 13, "rule_name": "alt_domain", "tll": 1}, {"from_name": "policy", "to_name": "certPol", "to_type": 13, "rule_name": "policies", "tll": 1}, {"from_name": "secretkey_endtime", "to_name": "priKeyUsaPerNotAft", "to_type": 15}, {"from_name": "secretkey_begintime", "to_name": "priKeyUsaPerNotBef", "to_type": 15}, {"from_name": "key_usage", "to_name": "keyUsage", "to_type": 13, "rule_name": "key_usage", "tll": 1}, {"from_name": "subj_key_id", "to_name": "subKeyID", "to_type": 13, "rule_name": "subject_keyid", "tll": 1}, {"from_name": "auth_key_id", "to_name": "authKeyID", "to_type": 13, "rule_name": "auth_keyid", "tll": 1}, {"from_name": "signature", "to_name": "sigVal", "to_type": 13}, {"from_name": "signature_alg", "to_name": "sigAlg", "to_type": 13, "rule_name": "algorithm.id", "tll": 1}, {"from_name": "dsa_g", "to_name": "DSAPubKeyG", "to_type": 13}, {"from_name": "dsa_q", "to_name": "DSAPubKeyQ", "to_type": 13}, {"from_name": "dsa_p", "to_name": "DSAPubKeyP", "to_type": 13}, {"from_name": "dh_publickey", "to_name": "DHPub<PERSON>ey", "to_type": 13}, {"from_name": "dh_base", "to_name": "DHPGen", "to_type": 13}, {"from_name": "dh_module", "to_name": "DHPriMod", "to_type": 13}, {"from_name": "key_exponent", "to_name": "RSAExp", "to_type": 13}, {"from_name": "key_module", "to_name": "RSAMod", "to_type": 13}, {"from_name": "endtime", "to_name": "valNotAft", "to_type": 15, "rule_name": "notafter", "tll": 1}, {"from_name": "begintime", "to_name": "valNotBef", "to_type": 15, "rule_name": "notbefore", "tll": 1}, {"from_name": "subject_email", "to_name": "subPosOffBox", "to_type": 13}, {"from_name": "subject_unit", "to_name": "subOrgUniName", "to_type": 13}, {"from_name": "subject_party", "to_name": "subOrgName", "to_type": 13, "rule_name": "subject.on", "tll": 1}, {"from_name": "issuer_road", "to_name": "subStrAddr", "to_type": 13}, {"from_name": "subject_province", "to_name": "subStaOrProName", "to_type": 13}, {"from_name": "subject_locality", "to_name": "subLoaName", "to_type": 13}, {"from_name": "subject_nation", "to_name": "subConName", "to_type": 13}, {"from_name": "subject_firstname", "to_name": "subComName", "to_type": 13, "rule_name": "subject.cn", "tll": 1}, {"from_name": "issuer_email", "to_name": "issPosOffBox", "to_type": 13}, {"from_name": "issuer_unit", "to_name": "issOrgUniName", "to_type": 13}, {"from_name": "issuer_party", "to_name": "issOrgName", "to_type": 13, "rule_name": "issuer.on", "tll": 1}, {"from_name": "issuer_road", "to_name": "issStrAddr", "to_type": 13}, {"from_name": "issuer_province", "to_name": "issStaOrProName", "to_type": 13}, {"from_name": "issuer_locality", "to_name": "issLoaName", "to_type": 13}, {"from_name": "issuer_nation", "to_name": "issConName", "to_type": 13}, {"from_name": "issuer_firstname", "to_name": "issComName", "to_type": 13, "rule_name": "issuer.cn", "tll": 1}, {"from_name": "issuer_length", "to_name": "issDataLen", "to_type": 9}, {"from_name": "sequence", "to_name": "srvNum", "to_type": 13, "rule_name": "serial", "tll": 1}, {"from_name": "version", "to_name": "ver", "to_type": 9, "rule_name": "version", "tll": 1}, {"from_name": "", "to_name": "ProtabID", "to_type": 10}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}