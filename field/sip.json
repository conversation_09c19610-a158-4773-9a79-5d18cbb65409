{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "sip", "to_proto_name": "sip", "rule_proto_name": "sip", "field": [{"from_name": "sdp_keep_time1", "to_name": "sdp_keep_time1", "to_type": 13}, {"from_name": "sdp_keep_time0", "to_name": "sdp_keep_time0", "to_type": 13}, {"from_name": "sdp_t_start_time1", "to_name": "sdp_t_start_time1", "to_type": 13}, {"from_name": "call_tagged", "to_name": "call_tagged", "to_type": 13}, {"from_name": "called", "to_name": "called", "to_type": 13}, {"from_name": "calling", "to_name": "calling", "to_type": 13}, {"from_name": "media_format2", "to_name": "media_format2", "to_type": 13}, {"from_name": "media_format1", "to_name": "media_format1", "to_type": 13}, {"from_name": "media_protocol", "to_name": "media_protocol", "to_type": 13}, {"from_name": "media_port", "to_name": "media_port", "to_type": 13}, {"from_name": "media_type", "to_name": "media_type", "to_type": 13}, {"from_name": "media_descrip", "to_name": "media_descrip", "to_type": 13}, {"from_name": "td_stop_time", "to_name": "td_stop_time", "to_type": 13}, {"from_name": "td_start_time", "to_name": "td_start_time", "to_type": 13}, {"from_name": "time_descrip", "to_name": "time_descrip", "to_type": 13}, {"from_name": "connec_address", "to_name": "connec_address", "to_type": 13}, {"from_name": "connec_address_type", "to_name": "connec_address_type", "to_type": 13}, {"from_name": "connec_network_type", "to_name": "connec_network_type", "to_type": 13}, {"from_name": "connection", "to_name": "connection", "to_type": 13}, {"from_name": "owner_address", "to_name": "owner_address", "to_type": 13}, {"from_name": "owner_adddress_type", "to_name": "owner_adddress_type", "to_type": 13}, {"from_name": "owner_network_type", "to_name": "owner_network_type", "to_type": 13}, {"from_name": "owner_session_version", "to_name": "owner_session_version", "to_type": 13}, {"from_name": "owner_session_id", "to_name": "owner_session_id", "to_type": 13}, {"from_name": "owner_name", "to_name": "owner_name", "to_type": 13}, {"from_name": "owner_session", "to_name": "owner_session", "to_type": 13}, {"from_name": "session_version", "to_name": "session_version", "to_type": 13}, {"from_name": "", "to_name": "message_body", "to_type": 13}, {"from_name": "", "to_name": "message_header", "to_type": 13}, {"from_name": "sip_rtp_flowid", "to_name": "sip_rtp_flowid", "to_type": 13}, {"from_name": "rseq", "to_name": "rseq", "to_type": 13}, {"from_name": "timestamp", "to_name": "timestamp", "to_type": 13}, {"from_name": "sdp_a_attributes0", "to_name": "sdp_a_attributes0", "to_type": 13}, {"from_name": "sdp_c_connections0", "to_name": "sdp_c_connections0", "to_type": 13}, {"from_name": "sdp_m_payloads0", "to_name": "sdp_m_payloads0", "to_type": 13}, {"from_name": "sdp_m_proto0", "to_name": "sdp_m_proto0", "to_type": 13}, {"from_name": "sdp_m_port0", "to_name": "sdp_m_port0", "to_type": 13}, {"from_name": "sdp_m_media0", "to_name": "sdp_m_media0", "to_type": 13}, {"from_name": "sdp_t_stop_time0", "to_name": "sdp_t_stop_time0", "to_type": 13}, {"from_name": "sdp_t_start_time0", "to_name": "sdp_t_start_time0", "to_type": 13}, {"from_name": "sdp_b_bandwidths", "to_name": "sdp_b_bandwidths", "to_type": 13}, {"from_name": "sdp_i_info", "to_name": "sdp_i_info", "to_type": 13}, {"from_name": "sdp_c_addr", "to_name": "sdp_c_addr", "to_type": 13}, {"from_name": "sdp_c_addrtype", "to_name": "sdp_c_addrtype", "to_type": 13}, {"from_name": "sdp_c_nettype", "to_name": "sdp_c_nettype", "to_type": 13}, {"from_name": "sdp_s_name", "to_name": "sdp_s_name", "to_type": 13}, {"from_name": "sdp_o_addr", "to_name": "sdp_o_addr", "to_type": 13}, {"from_name": "sdp_o_addrtype", "to_name": "sdp_o_addrtype", "to_type": 13}, {"from_name": "sdp_o_nettype", "to_name": "sdp_o_nettype", "to_type": 13}, {"from_name": "sdp_o_sess_version", "to_name": "sdp_o_sess_version", "to_type": 13}, {"from_name": "sdp_o_sess_id", "to_name": "sdp_o_sess_id", "to_type": 13}, {"from_name": "sdp_o_username", "to_name": "sdp_o_username", "to_type": 13}, {"from_name": "sdp_v_version", "to_name": "sdp_v_version", "to_type": 13}, {"from_name": "cseq_method", "to_name": "cseq_method", "to_type": 13}, {"from_name": "cseq_sequence_num", "to_name": "cseq_sequence_num", "to_type": 13}, {"from_name": "CSeq", "to_name": "CSeq", "to_type": 13}, {"from_name": "Call-Info", "to_name": "Call-Info", "to_type": 13}, {"from_name": "Call-ID", "to_name": "Call-ID", "to_type": 13}, {"from_name": "to_tag", "to_name": "to_tag", "to_type": 13}, {"from_name": "to_host_port", "to_name": "to_host_port", "to_type": 13}, {"from_name": "to_host_part", "to_name": "to_host_part", "to_type": 13}, {"from_name": "to_country_code", "to_name": "to_country_code", "to_type": 13}, {"from_name": "to_e164_num", "to_name": "to_e164_num", "to_type": 13}, {"from_name": "to_user_part", "to_name": "to_user_part", "to_type": 13}, {"from_name": "from_tag", "to_name": "from_tag", "to_type": 13}, {"from_name": "from_host_port", "to_name": "from_host_port", "to_type": 13}, {"from_name": "from_host_part", "to_name": "from_host_part", "to_type": 13}, {"from_name": "from_country_code", "to_name": "from_country_code", "to_type": 13}, {"from_name": "from_e164_num", "to_name": "from_e164_num", "to_type": 13}, {"from_name": "from_user_part", "to_name": "from_user_part", "to_type": 13}, {"from_name": "via_recieved", "to_name": "via_recieved", "to_type": 13}, {"from_name": "via_rport", "to_name": "via_rport", "to_type": 13}, {"from_name": "via_branch", "to_name": "via_branch", "to_type": 13}, {"from_name": "via_sent_port", "to_name": "via_sent_port", "to_type": 13}, {"from_name": "via_sent_addr", "to_name": "via_sent_addr", "to_type": 13}, {"from_name": "via_transport", "to_name": "via_transport", "to_type": 13}, {"from_name": "req_uri", "to_name": "req_uri", "to_type": 13}, {"from_name": "sip_version", "to_name": "sip_version", "to_type": 13}, {"from_name": "sip_method", "to_name": "sip_method", "to_type": 13}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}