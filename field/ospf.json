{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "ospf", "to_proto_name": "ospf", "rule_proto_name": "ospf", "field": [{"from_name": "LS_opt_N", "to_name": "rouLSNT", "to_type": 13}, {"from_name": "", "to_name": "tosExtRouTag", "to_type": 13}, {"from_name": "", "to_name": "tosFwdIPAddr", "to_type": 13}, {"from_name": "", "to_name": "fwdAddrV6", "to_type": 13}, {"from_name": "Reference_id", "to_name": "refLsID", "to_type": 13}, {"from_name": "Reference_type", "to_name": "refLsType", "to_type": 13}, {"from_name": "", "to_name": "asnssaT", "to_type": 13}, {"from_name": "Reference_ADV_router", "to_name": "refAdvRou", "to_type": 13}, {"from_name": "Prefix_addr", "to_name": "addrPre", "to_type": 13}, {"from_name": "Prefix_P_flag", "to_name": "preP", "to_type": 13}, {"from_name": "Prefix_LA_flag", "to_name": "preLa", "to_type": 13}, {"from_name": "Prefix_NU_flag", "to_name": "preNu", "to_type": 13}, {"from_name": "Prefix_len", "to_name": "preLen", "to_type": 13}, {"from_name": "", "to_name": "dstRou", "to_type": 13}, {"from_name": "ROUTER_link_num_of_metric_0", "to_name": "tos", "to_type": 13}, {"from_name": "ROUTER_link_type_0", "to_name": "rouItemType", "to_type": 13}, {"from_name": "ROUTER_nr_links", "to_name": "linkNum", "to_type": 13}, {"from_name": "", "to_name": "RouIPv6Addr", "to_type": 13}, {"from_name": "Traffic_engineering_type", "to_name": "teTlvType", "to_type": 13}, {"from_name": "", "to_name": "remIntIPV6", "to_type": 13}, {"from_name": "", "to_name": "teInstID", "to_type": 13}, {"from_name": "Traffic_engineering_metric", "to_name": "teMetric", "to_type": 13}, {"from_name": "Prefix_count", "to_name": "preNum", "to_type": 13}, {"from_name": "Local_interface_id", "to_name": "linkLocIPv6Addr", "to_type": 13}, {"from_name": "LS_Len", "to_name": "lsaLen", "to_type": 13}, {"from_name": "LS_Checksum", "to_name": "lsChe", "to_type": 13}, {"from_name": "LS_Numbre", "to_name": "lsaNum", "to_type": 13}, {"from_name": "master_flag", "to_name": "msTag", "to_type": 13}, {"from_name": "more_flag", "to_name": "moreTag", "to_type": 13}, {"from_name": "init_flag", "to_name": "initTag", "to_type": 13}, {"from_name": "", "to_name": "synTag", "to_type": 13}, {"from_name": "interface_mtu", "to_name": "mtu", "to_type": 13}, {"from_name": "interface_id", "to_name": "intID", "to_type": 13}, {"from_name": "opt_V6", "to_name": "optV6", "to_type": 13}, {"from_name": "opt_MT", "to_name": "optMt", "to_type": 13}, {"from_name": "opt_E", "to_name": "optE", "to_type": 13}, {"from_name": "opt_MC", "to_name": "optMc", "to_type": 13}, {"from_name": "opt_N", "to_name": "optN", "to_type": 13}, {"from_name": "opt_DC", "to_name": "optDc", "to_type": 13}, {"from_name": "opt_L", "to_name": "optL", "to_type": 13}, {"from_name": "instance_id", "to_name": "instID", "to_type": 13}, {"from_name": "head_checksum", "to_name": "chckSum", "to_type": 13}, {"from_name": "", "to_name": "pktLen", "to_type": 13}, {"from_name": "LSA_link_id", "to_name": "LSALinkID", "to_type": 10}, {"from_name": "LSA_link_type", "to_name": "LSALinkType", "to_type": 9}, {"from_name": "Admin_group", "to_name": "adminGrp", "to_type": 9}, {"from_name": "Unreservable_bandwith", "to_name": "unresBW", "to_type": 13}, {"from_name": "Maximum_reservable_bandwith", "to_name": "maxResBW", "to_type": 10}, {"from_name": "Maximum_bandwidth", "to_name": "maxBW", "to_type": 10}, {"from_name": "Traffic_engineering_metric", "to_name": "traEngMet", "to_type": 10}, {"from_name": "Remote_interface_id", "to_name": "remIntIP", "to_type": 10}, {"from_name": "Local_interface_id", "to_name": "locIntIPAddr", "to_type": 10}, {"from_name": "Local_interface_id", "to_name": "rouIntAddr", "to_type": 10}, {"from_name": "neighbor_routerid_v3", "to_name": "neigRouID", "to_type": 10}, {"from_name": "Neighbor_interfaceid_v3", "to_name": "neigIntID", "to_type": 10}, {"from_name": "As_ext_router_tag", "to_name": "extRouTag", "to_type": 10}, {"from_name": "As_ext_type", "to_name": "extType", "to_type": 9}, {"from_name": "As_ext_forward_addr", "to_name": "fwdAddr", "to_type": 10}, {"from_name": "NETWORK_attached_router_0", "to_name": "attRou", "to_type": 10}, {"from_name": "ROUTER_link_0_metric_0", "to_name": "metric", "to_type": 9}, {"from_name": "ROUTER_link_type_0", "to_name": "linkType", "to_type": 9}, {"from_name": "ROUTER_link_data_0", "to_name": "linkData", "to_type": 10}, {"from_name": "ROUTER_link_id_0", "to_name": "linkID", "to_type": 10}, {"from_name": "Lsa_netmask", "to_name": "LSANetMask", "to_type": 10}, {"from_name": "Abr_flag", "to_name": "borBit", "to_type": 1}, {"from_name": "Asbr_flag", "to_name": "extBit", "to_type": 1}, {"from_name": "Virtual_flag", "to_name": "VLFlag", "to_type": 1}, {"from_name": "LS_Age", "to_name": "lsaAge", "to_type": 10}, {"from_name": "Sequence_number", "to_name": "LSASeqNum", "to_type": 10}, {"from_name": "Advertising_router", "to_name": "advRou", "to_type": 10}, {"from_name": "Link_state_id", "to_name": "linkStaID", "to_type": 10}, {"from_name": "LS_type", "to_name": "linkStaType", "to_type": 10}, {"from_name": "Db_sequence", "to_name": "DBSeqNum", "to_type": 10}, {"from_name": "Hello_active_neighbor", "to_name": "neighbor", "to_type": 10}, {"from_name": "Hello_backup_designated_router", "to_name": "BDR", "to_type": 10}, {"from_name": "Hello_designated_router", "to_name": "DR", "to_type": 10}, {"from_name": "Hello_router_dead_interval", "to_name": "deadInt", "to_type": 10}, {"from_name": "Hello_router_priority", "to_name": "rou<PERSON><PERSON>", "to_type": 9}, {"from_name": "NETWORK_netmask", "to_name": "netMask", "to_type": 10}, {"from_name": "Hello_interval", "to_name": "helInt", "to_type": 10}, {"from_name": "Cryptographic_data", "to_name": "authData", "to_type": 13}, {"from_name": "Cryptographic_seqnum", "to_name": "cryptSeqNum", "to_type": 10}, {"from_name": "Cryptographic_keyid", "to_name": "keyID", "to_type": 9}, {"from_name": "Auth_password", "to_name": "authPwd", "to_type": 13}, {"from_name": "Auth_type", "to_name": "authType", "to_type": 9}, {"from_name": "Area_id", "to_name": "areaID", "to_type": 10}, {"from_name": "Src_router", "to_name": "rouID", "to_type": 10}, {"from_name": "Message_type", "to_name": "msgType", "to_type": 9}, {"from_name": "Version", "to_name": "ver", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}