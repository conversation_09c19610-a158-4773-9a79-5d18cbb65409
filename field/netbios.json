{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "netbios", "to_proto_name": "netbios", "rule_proto_name": "netbios", "field": [{"from_name": "calleName", "to_name": "calleName", "to_type": 13}, {"from_name": "calliName", "to_name": "calliName", "to_type": 13}, {"from_name": "msgType", "to_name": "msgType", "to_type": 9}, {"from_name": "addData", "to_name": "addData", "to_type": 13}, {"from_name": "addTtl", "to_name": "addTtl", "to_type": 13}, {"from_name": "addClass", "to_name": "addClass", "to_type": 13}, {"from_name": "addType", "to_name": "addType", "to_type": 13}, {"from_name": "addName", "to_name": "addName", "to_type": 13}, {"from_name": "authData", "to_name": "authData", "to_type": 13}, {"from_name": "authTtl", "to_name": "authTtl", "to_type": 13}, {"from_name": "authClass", "to_name": "authClass", "to_type": 13}, {"from_name": "authType", "to_name": "authType", "to_type": 13}, {"from_name": "authName", "to_name": "authName", "to_type": 13}, {"from_name": "ansData", "to_name": "ansData", "to_type": 13}, {"from_name": "ansTtl", "to_name": "ansTtl", "to_type": 13}, {"from_name": "ansClass", "to_name": "ansClass", "to_type": 13}, {"from_name": "qryClass", "to_name": "qryClass", "to_type": 13}, {"from_name": "resRecType", "to_name": "resRecType", "to_type": 13}, {"from_name": "ans<PERSON>ame", "to_name": "ans<PERSON>ame", "to_type": 13}, {"from_name": "qryRecType", "to_name": "qryRecType", "to_type": 13}, {"from_name": "qryName", "to_name": "qryName", "to_type": 13}, {"from_name": "sessDatePktSize", "to_name": "sessDataPktSize", "to_type": 9}, {"from_name": "maxTotalSessPoss", "to_name": "maxTotalSessPoss", "to_type": 9}, {"from_name": "maxPendingNum", "to_name": "maxPendingNum", "to_type": 9}, {"from_name": "pendingNum", "to_name": "pendingNUm", "to_type": 9}, {"from_name": "", "to_name": "maxTotalCmdNum", "to_type": 9}, {"from_name": "", "to_name": "totalCmdNum", "to_type": 9}, {"from_name": "", "to_name": "freeCmdNum", "to_type": 9}, {"from_name": "noResrcNum", "to_name": "noResrcNum", "to_type": 9}, {"from_name": "retransNum", "to_name": "retransNUm", "to_type": 9}, {"from_name": "goodRecvsNum", "to_name": "goodRecvsNum", "to_type": 9}, {"from_name": "goodSendsNum", "to_name": "goodSendsNum", "to_type": 9}, {"from_name": "sendAbortsNum", "to_name": "sendAbortsNum", "to_type": 9}, {"from_name": "collNum", "to_name": "collNUm", "to_type": 9}, {"from_name": "aligErrNum", "to_name": "aligErrNum", "to_type": 9}, {"from_name": "crcsNum", "to_name": "crcsNum", "to_type": 9}, {"from_name": "statisticsPer", "to_name": "statisticsPer", "to_type": 13}, {"from_name": "verNum", "to_name": "verNum", "to_type": 13}, {"from_name": "testRes", "to_name": "testRes", "to_type": 13}, {"from_name": "jumpers", "to_name": "jumpers", "to_type": 9}, {"from_name": "tid", "to_name": "uintID", "to_type": 13}, {"from_name": "nbstatName", "to_name": "nbstatName", "to_type": 13}, {"from_name": "nbAddr", "to_name": "nbAddr", "to_type": 13}, {"from_name": "rcode", "to_name": "rcode", "to_type": 13}, {"from_name": "raflag", "to_name": "raflag", "to_type": 13}, {"from_name": "rdflag", "to_name": "rdflag", "to_type": 13}, {"from_name": "tcflag", "to_name": "tcflag", "to_type": 13}, {"from_name": "aaflag", "to_name": "aaflag", "to_type": 13}, {"from_name": "opcode", "to_name": "opcode", "to_type": 9}, {"from_name": "AddNum", "to_name": "AddNum", "to_type": 9}, {"from_name": "AuthNum", "to_name": "AuthNum", "to_type": 9}, {"from_name": "AnsNum", "to_name": "AnsNum", "to_type": 9}, {"from_name": "QuestNum", "to_name": "QuestNum", "to_type": 9}, {"from_name": "tid", "to_name": "tid", "to_type": 13}, {"from_name": "addr", "to_name": "addr", "to_type": 10}, {"from_name": "netbiosSuf", "to_name": "netbiosSuf", "to_type": 9}, {"from_name": "netbiosName", "to_name": "netbiosName", "to_type": 13}, {"from_name": "dstHostName", "to_name": "dstHostName", "to_type": 13}, {"from_name": "srcHostName", "to_name": "srcHostName", "to_type": 13}, {"from_name": "srcHostAddr", "to_name": "srcHostAddr", "to_type": 10}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "srcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}