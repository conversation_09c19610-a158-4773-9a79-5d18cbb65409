{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "pptp", "to_proto_name": "pptp", "rule_proto_name": "pptp", "field": [{"from_name": "pRecvACCM", "to_name": "RecvAccm", "to_type": 9}, {"from_name": "pSendACCM", "to_name": "SendAccm", "to_type": 9}, {"from_name": "pAlignErrCnt", "to_name": "AlignErrs", "to_type": 13}, {"from_name": "pTimeOutErrCnt", "to_name": "TimeoutErrs", "to_type": 9}, {"from_name": "pBFOverCnt", "to_name": "BufOverruns", "to_type": 9}, {"from_name": "pHWOverCnt", "to_name": "HardwareOverruns", "to_type": 9}, {"from_name": "pFramErrCnt", "to_name": "FramingErrs", "to_type": 9}, {"from_name": "pCrcErrCnt", "to_name": "CrcErrs", "to_type": 9}, {"from_name": "pCallStat", "to_name": "CallStat", "to_type": 9}, {"from_name": "pktTransDelay", "to_name": "pktTransDelay", "to_type": 13}, {"from_name": "inCallResult", "to_name": "IncomRes", "to_type": 9}, {"from_name": "phyChannelID", "to_name": "phyChnId", "to_type": 9}, {"from_name": "pConnSpeed", "to_name": "connSpeed", "to_type": 13}, {"from_name": "pCauseCode", "to_name": "causeCode", "to_type": 13}, {"from_name": "outCallResult", "to_name": "OutGoingRes", "to_type": 9}, {"from_name": "peerCallID", "to_name": "peerCallId", "to_type": 9}, {"from_name": "subAddress", "to_name": "subAddr", "to_type": 13}, {"from_name": "phoneNumber", "to_name": "phoneNum", "to_type": 13}, {"from_name": "pktProcDelay", "to_name": "pktProcDelay", "to_type": 9}, {"from_name": "pktRecvWinSize", "to_name": "pktRecvWinSize", "to_type": 9}, {"from_name": "framingType", "to_name": "framingType", "to_type": 9}, {"from_name": "bearerType", "to_name": "bearerType", "to_type": 9}, {"from_name": "ocrqMaxBps", "to_name": "maxBps", "to_type": 13}, {"from_name": "ocrqMinBps", "to_name": "minBps", "to_type": 13}, {"from_name": "callSerialNum", "to_name": "callSerialNum", "to_type": 13}, {"from_name": "echoResult", "to_name": "KeepAliveRes", "to_type": 9}, {"from_name": "echoID", "to_name": "id", "to_type": 13}, {"from_name": "stopResult", "to_name": "StopConnRes", "to_type": 9}, {"from_name": "stopReason", "to_name": "reason", "to_type": 9}, {"from_name": "errorCode", "to_name": "err<PERSON><PERSON>", "to_type": 9}, {"from_name": "startConnResult", "to_name": "startConnRes", "to_type": 9}, {"from_name": "firmwareRev", "to_name": "firmwareRev", "to_type": 13}, {"from_name": "maxChannels", "to_name": "maxChannels", "to_type": 9}, {"from_name": "bearerCap", "to_name": "bearerCap", "to_type": 9}, {"from_name": "framingCap", "to_name": "famingCap", "to_type": 9}, {"from_name": "pptpVersion", "to_name": "protoVer", "to_type": 9}, {"from_name": "callID", "to_name": "callID", "to_type": 9}, {"from_name": "dialedNumber", "to_name": "calle<PERSON><PERSON>", "to_type": 13}, {"from_name": "dialingNumber", "to_name": "calliNum", "to_type": 13}, {"from_name": "hostName", "to_name": "hostName", "to_type": 13}, {"from_name": "vendorName", "to_name": "venName", "to_type": 13}, {"from_name": "controlMessageType", "to_name": "conType", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}