{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "isakmp", "to_proto_name": "isakmp", "rule_proto_name": "isakmp", "field": [{"from_name": "idList", "to_name": "IDList", "to_type": 13}, {"from_name": "IdKeyId", "to_name": "keyID", "to_type": 13}, {"from_name": "IdIpAddrSubnet", "to_name": "ipSubnet", "to_type": 13}, {"from_name": "IdIPAddr", "to_name": "ipAddr", "to_type": 13}, {"from_name": "IdFQDN", "to_name": "fqdn", "to_type": 13}, {"from_name": "initVector", "to_name": "initVec", "to_type": 13}, {"from_name": "IdProtoID", "to_name": "protoID", "to_type": 13}, {"from_name": "KE_key_len", "to_name": "KeyLen", "to_type": 13}, {"from_name": "AuthData", "to_name": "authData", "to_type": 13}, {"from_name": "AuthMet", "to_name": "AuthMsgMet", "to_type": 13}, {"from_name": "spi", "to_name": "spi", "to_type": 13}, {"from_name": "<PERSON><PERSON>", "to_name": "NonceData", "to_type": 13}, {"from_name": "Sig", "to_name": "signData", "to_type": 13}, {"from_name": "Hash", "to_name": "hashData", "to_type": 13}, {"from_name": "IdPort", "to_name": "port", "to_type": 13}, {"from_name": "IdType", "to_name": "IDTyp", "to_type": 13}, {"from_name": "", "to_name": "idDir", "to_type": 13}, {"from_name": "Flags", "to_name": "flags", "to_type": 13}, {"from_name": "PayloadTypes", "to_name": "<PERSON>er", "to_type": 13}, {"from_name": "ResponseAuthenticationData", "to_name": "authDataInResp", "to_type": 13}, {"from_name": "RequestAuthenticationData", "to_name": "authDataInInit", "to_type": 13}, {"from_name": "ResponseIdentification", "to_name": "IDInResp", "to_type": 13}, {"from_name": "RequestIdentification", "to_name": "IDInInit", "to_type": 13}, {"from_name": "", "to_name": "respEncryDataSize", "to_type": 9}, {"from_name": "", "to_name": "initEncryDataSize", "to_type": 9}, {"from_name": "IKE2_Checksum", "to_name": "intAlgInIKE2", "to_type": 9}, {"from_name": "IKE2_Random", "to_name": "pseRandFunInIKE2", "to_type": 9}, {"from_name": "IKE2_ENCryptMethod", "to_name": "encryAlgInIKE2", "to_type": 9}, {"from_name": "IKE2_Notification", "to_name": "notMsgTypeInIKE2", "to_type": 9}, {"from_name": "Notification", "to_name": "notMsgType", "to_type": 9}, {"from_name": "ResponseRandom", "to_name": "respNon", "to_type": 13}, {"from_name": "RequestRandom", "to_name": "initNon", "to_type": 13}, {"from_name": "CertificateType", "to_name": "certCod", "to_type": 9}, {"from_name": "ResponseKeyExchange", "to_name": "respKeyExc", "to_type": 13}, {"from_name": "RequestKeyExchange", "to_name": "iniKeyExc", "to_type": 13}, {"from_name": "ResponseVendorID", "to_name": "respVenID", "to_type": 13}, {"from_name": "RequestVendorID", "to_name": "initVenID", "to_type": 13}, {"from_name": "LifeDuration", "to_name": "lifeDur", "to_type": 10}, {"from_name": "LifeType", "to_name": "lifeType", "to_type": 9}, {"from_name": "ResponseAuthenticationMethod", "to_name": "respAuthMet", "to_type": 9}, {"from_name": "RequestAuthenticationMethod", "to_name": "initAuthMet", "to_type": 9}, {"from_name": "ResponseGroupType", "to_name": "respGroType", "to_type": 9}, {"from_name": "RequestGroupType", "to_name": "initGroType", "to_type": 9}, {"from_name": "ResponseGroupDescription", "to_name": "respGroDes", "to_type": 9}, {"from_name": "RequestGroupDescription", "to_name": "initGroDes", "to_type": 9}, {"from_name": "ResponseHashAlgorithm", "to_name": "respHasAlg", "to_type": 9}, {"from_name": "RequestHashAlgorithm", "to_name": "initHasAlg", "to_type": 9}, {"from_name": "Response<PERSON>eyLength", "to_name": "respKeyLen", "to_type": 9}, {"from_name": "RequestKeyLength", "to_name": "initKeyLen", "to_type": 9}, {"from_name": "ResponseEncryptionAlgorithm", "to_name": "respEncryAlg", "to_type": 9}, {"from_name": "RequestEncryptionAlgorithm", "to_name": "initEncryAlg", "to_type": 9}, {"from_name": "TransformID", "to_name": "traID", "to_type": 9}, {"from_name": "TransformNumber", "to_name": "traNum", "to_type": 9}, {"from_name": "ProtocolID", "to_name": "proID", "to_type": 9}, {"from_name": "ProposalNumber", "to_name": "proNum", "to_type": 9}, {"from_name": "Situation", "to_name": "situ", "to_type": 10}, {"from_name": "doi", "to_name": "DOI", "to_type": 10}, {"from_name": "MessageID", "to_name": "msgID", "to_type": 10}, {"from_name": "ExchangeType", "to_name": "excType", "to_type": 9}, {"from_name": "ReponseVersion", "to_name": "respVer", "to_type": 9}, {"from_name": "RequestVersion", "to_name": "initVer", "to_type": 9}, {"from_name": "ResponderCookie", "to_name": "resp<PERSON><PERSON><PERSON>", "to_type": 13}, {"from_name": "Initiator<PERSON><PERSON><PERSON>", "to_name": "iniCookie", "to_type": 13}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}