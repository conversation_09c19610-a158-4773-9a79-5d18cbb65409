{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "http", "to_proto_name": "http", "rule_proto_name": "http", "field": [{"from_name": "conDispDown", "to_name": "conDispDown", "to_type": 13, "rule_name": "content_disp.s"}, {"from_name": "conDispUp", "to_name": "conDispUp", "to_type": 13, "rule_name": "content_disp.c", "tll": 1}, {"from_name": "", "to_name": "conEncBySrvCnt", "to_type": 9, "tll": 1}, {"from_name": "<PERSON>_Sin<PERSON>ole", "to_name": "xSinHol", "to_type": 13, "rule_name": "xsinkhole", "tll": 1}, {"from_name": "Location", "to_name": "Location", "to_type": 13, "rule_name": "location", "tll": 1}, {"from_name": "Content-Encoding", "to_name": "conEncBySrv", "to_type": 9, "rule_name": "content_enc.s", "tll": 1}, {"from_name": "Content-MD5", "to_name": "conMD5BySrv", "to_type": 13, "tll": 1}, {"from_name": "", "to_name": "respBodyN", "to_type": 13, "tll": 1}, {"from_name": "rspBody", "to_name": "respBody", "to_type": 13, "rule_name": "body.s", "tll": 1}, {"from_name": "", "to_name": "respHeadCnt", "to_type": 9, "tll": 1}, {"from_name": "rspHeadFieldsMD5", "to_name": "respHeadMd5", "to_type": 13, "tll": 1}, {"from_name": "rspHeadFields", "to_name": "respHead", "to_type": 13, "rule_name": "header.s", "tll": 1}, {"from_name": "", "to_name": "respVerCnt", "to_type": 9, "tll": 1}, {"from_name": "rspVersion", "to_name": "respVer", "to_type": 13, "rule_name": "version.s", "tll": 1}, {"from_name": "", "to_name": "xForForCnt", "to_type": 9, "tll": 1}, {"from_name": "imsi", "to_name": "imsi", "to_type": 13, "rule_name": "imsi.c", "tll": 1}, {"from_name": "imei", "to_name": "imei", "to_type": 13, "rule_name": "imei.c", "tll": 1}, {"from_name": "", "to_name": "cookieKeyCnt", "to_type": 9, "tll": 1}, {"from_name": "<PERSON><PERSON><PERSON><PERSON>", "to_name": "<PERSON><PERSON><PERSON>", "to_type": 13, "rule_name": "cookie.key", "tll": 1}, {"from_name": "", "to_name": "conMD5ByCli", "to_type": 13, "tll": 1}, {"from_name": "", "to_name": "reqBodyN", "to_type": 13, "tll": 1}, {"from_name": "reqBody", "to_name": "reqBody", "to_type": 13, "rule_name": "body.c", "tll": 1}, {"from_name": "", "to_name": "userCnt", "to_type": 9, "tll": 1}, {"from_name": "Username", "to_name": "user", "to_type": 13, "rule_name": "user", "tll": 1}, {"from_name": "UserAgentCount", "to_name": "usrAgeCnt", "to_type": 9, "tll": 1}, {"from_name": "uriSearch", "to_name": "uriSearch", "to_type": 13, "rule_name": "uri.search", "tll": 1}, {"from_name": "URI-Keys-Count", "to_name": "uriKeyCnt", "to_type": 9, "tll": 1}, {"from_name": "URI-Keys", "to_name": "<PERSON><PERSON><PERSON><PERSON>", "to_type": 13, "rule_name": "uri.key", "tll": 1}, {"from_name": "URI-Path-Count", "to_name": "uriPathCnt", "to_type": 9, "tll": 1}, {"from_name": "URI-Path", "to_name": "<PERSON><PERSON><PERSON><PERSON>", "to_type": 13, "rule_name": "uri.path", "tll": 1}, {"from_name": "URI-Count", "to_name": "uriCnt", "to_type": 9, "tll": 1}, {"from_name": "Host-Count", "to_name": "hostCnt", "to_type": 9, "tll": 1}, {"from_name": "", "to_name": "authCnt", "to_type": 9, "tll": 1}, {"from_name": "Accept-Encoding", "to_name": "accEncByCli", "to_type": 13, "rule_name": "accept_encoding.c", "tll": 1}, {"from_name": "Accept-Language", "to_name": "accLanByCli", "to_type": 13, "rule_name": "accept_language.c", "tll": 1}, {"from_name": "Accept", "to_name": "accByCli", "to_type": 13, "rule_name": "accept.c", "tll": 1}, {"from_name": "reqHeadFieldsCount", "to_name": "reqHeadCnt", "to_type": 9, "tll": 1}, {"from_name": "reqMethodCount", "to_name": "metCnt", "to_type": 9, "tll": 1}, {"from_name": "reqVersionCount", "to_name": "reqVerCnt", "to_type": 9, "tll": 1}, {"from_name": "", "to_name": "contDown", "to_type": 13}, {"from_name": "", "to_name": "fileName", "to_type": 13}, {"from_name": "rspFullTextLen", "to_name": "fullTextLen", "to_type": 9}, {"from_name": "", "to_name": "fullTextHeader", "to_type": 13}, {"from_name": "", "to_name": "httpEmbPro", "to_type": 13}, {"from_name": "", "to_name": "httpRelKey", "to_type": 13}, {"from_name": "rspAccept-Charset", "to_name": "accChaDown", "to_type": 13}, {"from_name": "Last-Modified", "to_name": "lasMod", "to_type": 15}, {"from_name": "Expires", "to_name": "expires", "to_type": 15}, {"from_name": "Allow", "to_name": "allow", "to_type": 13}, {"from_name": "rspContent-Type", "to_name": "conTypDown", "to_type": 13}, {"from_name": "Refresh", "to_name": "refresh", "to_type": 13}, {"from_name": "WWW-Authenticate", "to_name": "wwwAuth", "to_type": 13}, {"from_name": "Retry-After", "to_name": "retAft", "to_type": 13}, {"from_name": "ETag", "to_name": "eTag", "to_type": 13}, {"from_name": "rspAccept-Ranges", "to_name": "accRanDown", "to_type": 13}, {"from_name": "Trailer", "to_name": "trail", "to_type": 13}, {"from_name": "rspPragma", "to_name": "praDown", "to_type": 13}, {"from_name": "rspConnection", "to_name": "conDown", "to_type": 13}, {"from_name": "rspCache-Control", "to_name": "cacConDown", "to_type": 13}, {"from_name": "TE", "to_name": "te", "to_type": 13}, {"from_name": "Max-Forwards", "to_name": "maxFor", "to_type": 9}, {"from_name": "If-Unmodified-Since", "to_name": "ifUnModSin", "to_type": 15}, {"from_name": "If-Range", "to_name": "ifRan", "to_type": 13}, {"from_name": "If-None-Match", "to_name": "ifNonMat", "to_type": 13}, {"from_name": "If-Modified-Since", "to_name": "ifModSin", "to_type": 13}, {"from_name": "If-Match", "to_name": "ifMat", "to_type": 13}, {"from_name": "Accept-Ranges", "to_name": "acctRanUp", "to_type": 13}, {"from_name": "Accept-<PERSON><PERSON><PERSON>", "to_name": "accChaUp", "to_type": 13}, {"from_name": "Upgrade", "to_name": "upg", "to_type": 13}, {"from_name": "Pragma", "to_name": "praUp", "to_type": 13}, {"from_name": "Connection", "to_name": "conUp", "to_type": 13}, {"from_name": "Cache-Control", "to_name": "cacConUp", "to_type": 13}, {"from_name": "reqHeadFieldsMD5", "to_name": "reqHeadMd5", "to_type": 9, "tll": 1}, {"from_name": "re<PERSON><PERSON><PERSON><PERSON><PERSON>s", "to_name": "reqHead", "to_type": 9, "rule_name": "header.c", "tll": 1}, {"from_name": "Version", "to_name": "reqV<PERSON>", "to_type": 13, "rule_name": "version.c", "tll": 1}, {"from_name": "", "to_name": "statCodeCnt", "to_type": 9, "tll": 1}, {"from_name": "Via-Count", "to_name": "viaCnt", "to_type": 9, "tll": 1}, {"from_name": "Range", "to_name": "rangeofCli", "to_type": 13}, {"from_name": " ", "to_name": "extHdrs", "to_type": 13}, {"from_name": "X-Powered-By", "to_name": "xPowBy", "to_type": 13, "rule_name": "xpowby", "tll": 1}, {"from_name": "Proxy-Type", "to_name": "proAuth", "to_type": 13}, {"from_name": "Age", "to_name": "srvAge", "to_type": 13}, {"from_name": "Method", "to_name": "met", "to_type": 13, "rule_name": "method", "tll": 1}, {"from_name": "Status", "to_name": "statCode", "to_type": 13, "rule_name": "statuscode", "tll": 1}, {"from_name": "X-Forwarded-For", "to_name": "xForFor", "to_type": 13, "rule_name": "xff_ip", "tll": 1}, {"from_name": "Via", "to_name": "via", "to_type": 13, "rule_name": "via", "tll": 1}, {"from_name": "User-Agent", "to_name": "usrAge", "to_type": 13, "rule_name": "user_agent", "tll": 1}, {"from_name": "Transfer-Encoding", "to_name": "traEnc", "to_type": 13, "rule_name": "trans_enc", "tll": 1}, {"from_name": "", "to_name": "setCookieVal", "to_type": 13, "tll": 1}, {"from_name": "Set-<PERSON><PERSON>", "to_name": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_type": 13, "rule_name": "set_cookie.key", "tll": 1}, {"from_name": "", "to_name": "srvCnt", "to_type": 9, "tll": 1}, {"from_name": "Server", "to_name": "srv", "to_type": 13, "rule_name": "server", "tll": 1}, {"from_name": "<PERSON><PERSON><PERSON>", "to_name": "refURL", "to_type": 13, "rule_name": "ref", "tll": 1}, {"from_name": "Proxy-Authorization", "to_name": "<PERSON><PERSON><PERSON><PERSON>", "to_type": 13}, {"from_name": "Proxy-Authenticate", "to_name": "<PERSON><PERSON><PERSON><PERSON>", "to_type": 13}, {"from_name": "Location", "to_name": "loc", "to_type": 13}, {"from_name": "From", "to_name": "from", "to_type": 13, "rule_name": "from", "tll": 1}, {"from_name": "Date", "to_name": "date", "to_type": 13, "rule_name": "date", "tll": 1}, {"from_name": "Cookie2", "to_name": "cookie2", "to_type": 13}, {"from_name": "<PERSON><PERSON>", "to_name": "cookie", "to_type": 13, "rule_name": "cookie", "tll": 1}, {"from_name": "Content-Type", "to_name": "conType", "to_type": 13}, {"from_name": "Content-MD5", "to_name": "conMD5", "to_type": 13}, {"from_name": "Url", "to_name": "conURL", "to_type": 13}, {"from_name": "reqContent-Length", "to_name": "conLenBy<PERSON>li", "to_type": 9, "rule_name": "content_len.c", "tll": 1}, {"from_name": "rspContent-Length", "to_name": "conLenSrv", "to_type": 9, "rule_name": "content_len.s", "tll": 1}, {"from_name": "Content-Language", "to_name": "conLan", "to_type": 13, "rule_name": "content_lan", "tll": 1}, {"from_name": "Content-Encoding", "to_name": "conEncByCli", "to_type": 13, "rule_name": "content_enc.c", "tll": 1}, {"from_name": "Authorization", "to_name": "authInfo", "to_type": 13, "rule_name": "auth", "tll": 1}, {"from_name": "Vary", "to_name": "varConEnc", "to_type": 13, "rule_name": "vary", "tll": 1}, {"from_name": "URI", "to_name": "uri", "to_type": 13, "rule_name": "uri", "tll": 1}, {"from_name": "Host", "to_name": "host", "to_type": 13, "rule_name": "host", "tll": 1}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}