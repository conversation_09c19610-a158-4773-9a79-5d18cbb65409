{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "icmp", "to_proto_name": "icmp", "rule_proto_name": "icmp", "field": [{"from_name": "unr_ttl", "to_name": "excTTL", "to_type": 9, "rule_name": "unr.ttl", "tll": 1}, {"from_name": "response_time", "to_name": "resTime", "to_type": 15, "rule_name": "resp_time", "tll": 1}, {"from_name": "checksum", "to_name": "checkSum", "to_type": 9}, {"from_name": "mulCastAddr", "to_name": "mulCastAddr", "to_type": 13}, {"from_name": "pointer", "to_name": "excPointer", "to_type": 9}, {"from_name": "mtu", "to_name": "nextHopMtu", "to_type": 9}, {"from_name": "unr_dstip", "to_name": "ndpTarAddr", "to_type": 13}, {"from_name": "curM<PERSON>", "to_name": "ndpCurMtu", "to_type": 9}, {"from_name": "validtime", "to_name": "ndpValLifeTime", "to_type": 9}, {"from_name": "prefix", "to_name": "ndpPreFix", "to_type": 13}, {"from_name": "prelen", "to_name": "ndpPreLen", "to_type": 9}, {"from_name": "linkaddr", "to_name": "ndpLinkAddr", "to_type": 13}, {"from_name": "lifetime", "to_name": "ndpLifeTime", "to_type": 9}, {"from_name": "qurIP4", "to_name": "qurIpv4Addr", "to_type": 13}, {"from_name": "qurIP6", "to_name": "qurIpv6Addr", "to_type": 13}, {"from_name": "qurType", "to_name": "qurType", "to_type": 9}, {"from_name": "", "to_name": "ttl", "to_type": 9}, {"from_name": "gatewayAddr", "to_name": "gwAddr", "to_type": 13}, {"from_name": "exc_dstport", "to_name": "excDstPort", "to_type": 9, "rule_name": "unr.port.dst", "tll": 1}, {"from_name": "exc_srcport", "to_name": "excSrcPort", "to_type": 9, "rule_name": "unr.port.src", "tll": 1}, {"from_name": "exc_proto", "to_name": "excProt", "to_type": 9, "rule_name": "unr.proto", "tll": 1}, {"from_name": "exc_dstaddr", "to_name": "excDstAddr", "to_type": 13, "rule_name": "unr.dst", "tll": 1}, {"from_name": "exc_srcaddr", "to_name": "excSrcAddr", "to_type": 13, "rule_name": "unr.src", "tll": 1}, {"from_name": "transmit_time", "to_name": "transTimeStamp", "to_type": 15}, {"from_name": "receive_time", "to_name": "recvTimeStamp", "to_type": 15}, {"from_name": "origin_time", "to_name": "origTimeStamp", "to_type": 15}, {"from_name": "payload", "to_name": "dataCon", "to_type": 13, "rule_name": "payload.hex", "tll": 1}, {"from_name": "seq", "to_name": "echoSeqNum", "to_type": 9, "rule_name": "seq", "tll": 1}, {"from_name": "code", "to_name": "infoCode", "to_type": 9, "rule_name": "code", "tll": 1}, {"from_name": "type", "to_name": "msgType", "to_type": 9, "rule_name": "type", "tll": 1}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}