{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "common", "to_proto_name": "common", "rule_proto_name": "common", "field": [{"from_name": "srcMacOui", "to_name": "dstMac<PERSON>ui", "to_type": 13, "rule_name": "mac.oui.dst", "tll": 1}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 13, "rule_name": "ctime", "tll": 1}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 13, "rule_name": "outer.ip.proto", "tll": 1}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 13, "rule_name": "outer.port.dst", "tll": 1}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 13, "rule_name": "outer.port.src", "tll": 1}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13, "rule_name": "outer.ipv6.dst", "tll": 1}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13, "rule_name": "outer.ipv6.src", "tll": 1}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 13, "rule_name": "outer.ip.dst", "tll": 1}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 13, "rule_name": "outer.ip.src", "tll": 1}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 13, "rule_name": "outer.ip.version", "tll": 1}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 13, "rule_name": "ip.asn.dst", "tll": 1}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13, "rule_name": "ip.isp.dst", "tll": 1}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 13, "rule_name": "ip.latitude.dst", "tll": 1}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 13, "rule_name": "ip.longitude.dst", "tll": 1}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13, "rule_name": "ip.city.dst", "tll": 1}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13, "rule_name": "ip.state.dst", "tll": 1}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13, "rule_name": "ip.country.dst", "tll": 1}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 13, "rule_name": "ip.asn.src", "tll": 1}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13, "rule_name": "ip.isp.src", "tll": 1}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 13, "rule_name": "ip.latitude.src", "tll": 1}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 13, "rule_name": "ip.longitude.src", "tll": 1}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13, "rule_name": "ip.city.src", "tll": 1}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13, "rule_name": "ip.state.src", "tll": 1}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13, "rule_name": "ip.country.src", "tll": 1}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 13}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13, "rule_name": "mac.dst", "tll": 1}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13, "rule_name": "mac.src", "tll": 1}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 13, "rule_name": "vlan.id2", "tll": 1}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 13, "rule_name": "vlan.id1", "tll": 1}, {"from_name": "lable4", "to_name": "lable4", "to_type": 13, "rule_name": "mpls.lable4", "tll": 1}, {"from_name": "lable3", "to_name": "lable3", "to_type": 13, "rule_name": "mpls.lable3", "tll": 1}, {"from_name": "lable2", "to_name": "lable2", "to_type": 13, "rule_name": "mpls.lable2", "tll": 1}, {"from_name": "lable1", "to_name": "lable1", "to_type": 13, "rule_name": "mpls.lable1", "tll": 1}, {"from_name": "utags", "to_name": "utags", "to_type": 13, "rule_name": "utags", "tll": 1}, {"from_name": "atags", "to_name": "atags", "to_type": 13, "rule_name": "atags", "tll": 1}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13, "rule_name": "ttags", "tll": 1}, {"from_name": "etags", "to_name": "etags", "to_type": 13, "rule_name": "etags", "tll": 1}, {"from_name": "payLen", "to_name": "payLen", "to_type": 13, "rule_name": "ip.databytes", "tll": 1}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 13, "rule_name": "ip.packets", "tll": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13, "rule_name": "ip.uproto", "tll": 1}, {"from_name": "protType", "to_name": "protType", "to_type": 13, "rule_name": "ip.bproto", "tll": 1}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13, "rule_name": "ip.proto_path", "tll": 1}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13, "rule_name": "ipv6.dst", "tll": 1}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13, "rule_name": "ipv6.src", "tll": 1}, {"from_name": "protNum", "to_name": "protNum", "to_type": 13, "rule_name": "ip.proto", "tll": 1}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 13, "rule_name": "port.dst", "tll": 1}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 13, "rule_name": "port.src", "tll": 1}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 13, "rule_name": "ip.dst", "tll": 1}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 13, "rule_name": "ip.src", "tll": 1}, {"from_name": "ttl", "to_name": "ttl", "to_type": 13, "rule_name": "ip.ttl", "tll": 1}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 13, "rule_name": "ip.version", "tll": 1}, {"from_name": "comDur", "to_name": "comDur", "to_type": 13, "rule_name": "ip.time_len", "tll": 1}, {"from_name": "endTime", "to_name": "endTime", "to_type": 13, "rule_name": "ip.time.end", "tll": 1}, {"from_name": "begTime", "to_name": "begTime", "to_type": 13, "rule_name": "ip.time.beg", "tll": 1}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13, "rule_name": "cable.linename2", "tll": 1}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13, "rule_name": "cable.linename1", "tll": 1}]}