{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "l2tp", "to_proto_name": "l2tp", "rule_proto_name": "l2tp", "field": [{"from_name": "", "to_name": "advMsg", "to_type": 13}, {"from_name": "", "to_name": "cauMsg", "to_type": 9}, {"from_name": "", "to_name": "cauCode", "to_type": 9}, {"from_name": "", "to_name": "maxBps", "to_type": 9}, {"from_name": "", "to_name": "minBps", "to_type": 9}, {"from_name": "Private_group_id", "to_name": "priGrpID", "to_type": 13}, {"from_name": "", "to_name": "ocrpSFraType", "to_type": 9}, {"from_name": "", "to_name": "ocrpAFraType", "to_type": 9}, {"from_name": "", "to_name": "occnSFraType", "to_type": 9}, {"from_name": "", "to_name": "occnAFraType", "to_type": 9}, {"from_name": "", "to_name": "iccnSFraType", "to_type": 9}, {"from_name": "", "to_name": "iccnAFraType", "to_type": 9}, {"from_name": "Rx_connect_speed", "to_name": "rxConSpe", "to_type": 13}, {"from_name": "Tx_connect_speed", "to_name": "txConSpe", "to_type": 13}, {"from_name": "", "to_name": "subAddr", "to_type": 13}, {"from_name": "Physical_channel", "to_name": "phyCHID", "to_type": 9}, {"from_name": "", "to_name": "dBeaType", "to_type": 9}, {"from_name": "", "to_name": "aBeaType", "to_type": 9}, {"from_name": "Call_serial_number", "to_name": "callSN", "to_type": 9}, {"from_name": "", "to_name": "ocrqASID", "to_type": 9}, {"from_name": "", "to_name": "ocrpASID", "to_type": 9}, {"from_name": "", "to_name": "icrqASID", "to_type": 9}, {"from_name": "", "to_name": "icrpASID", "to_type": 9}, {"from_name": "", "to_name": "cdnASID", "to_type": 9}, {"from_name": "", "to_name": "errMsg", "to_type": 13}, {"from_name": "", "to_name": "errCode", "to_type": 9}, {"from_name": "", "to_name": "resCode", "to_type": 9}, {"from_name": "Vendor_name_sccrp", "to_name": "rpvenName", "to_type": 13}, {"from_name": "Vendor_name_sccrq", "to_name": "rqvenName", "to_type": 13}, {"from_name": "", "to_name": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "", "to_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "", "to_name": "tieBrek", "to_type": 13}, {"from_name": "", "to_name": "cnChaResp", "to_type": 13}, {"from_name": "", "to_name": "rpChaResp", "to_type": 13}, {"from_name": "", "to_name": "rpChallenge", "to_type": 13}, {"from_name": "", "to_name": "rqChallenge", "to_type": 13}, {"from_name": "", "to_name": "rpReWinSize", "to_type": 9}, {"from_name": "", "to_name": "rqReWinSize", "to_type": 9}, {"from_name": "", "to_name": "rpDBeaCap", "to_type": 13}, {"from_name": "", "to_name": "rpABeaCap", "to_type": 13}, {"from_name": "", "to_name": "rqDBeaCap", "to_type": 13}, {"from_name": "", "to_name": "rqABeaCap", "to_type": 13}, {"from_name": "", "to_name": "stopccnATID", "to_type": 9}, {"from_name": "Assigned_tunnel_id_sccrp", "to_name": "rpAssTunID", "to_type": 9}, {"from_name": "Assigned_tunnel_id_sccrq", "to_name": "rqAssTunID", "to_type": 9}, {"from_name": "", "to_name": "rpSFraCapRP", "to_type": 13}, {"from_name": "", "to_name": "rpAFraCapRP", "to_type": 13}, {"from_name": "", "to_name": "rqSFraCapRQ", "to_type": 13}, {"from_name": "", "to_name": "rqAFraCapRQ", "to_type": 13}, {"from_name": "Host_name", "to_name": "rphostNameRP", "to_type": 13}, {"from_name": "Host_name", "to_name": "rqhostName", "to_type": 13}, {"from_name": "", "to_name": "rpRev", "to_type": 9}, {"from_name": "", "to_name": "rpVer", "to_type": 9}, {"from_name": "", "to_name": "rq<PERSON>ev", "to_type": 9}, {"from_name": "", "to_name": "rqVer", "to_type": 9}, {"from_name": "version", "to_name": "ver", "to_type": 9}, {"from_name": "Chap_challenge", "to_name": "challenge", "to_type": 13}, {"from_name": "Vendor_name", "to_name": "venName", "to_type": 13}, {"from_name": "Calling_number", "to_name": "calliNum", "to_type": 13}, {"from_name": "Called_number", "to_name": "calle<PERSON><PERSON>", "to_type": 13}, {"from_name": "message_type", "to_name": "msgType", "to_type": 9}, {"from_name": "session_id", "to_name": "sesID", "to_type": 9}, {"from_name": "tunnel_id", "to_name": "tunID", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}