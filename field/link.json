{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "link", "to_proto_name": "link", "rule_proto_name": "link", "field": [{"from_name": "payLenRatio", "to_name": "payLenRatio", "to_type": 13, "tll": 1}, {"from_name": "sesBytesRatio", "to_name": "sesBytesRatio", "to_type": 13, "tll": 1}, {"from_name": "sesBytes", "to_name": "sesBytes", "to_type": 13, "rule_name": "ip.bytes", "tll": 1}, {"from_name": "downSesBytes", "to_name": "downSesbytes", "to_type": 13, "rule_name": "ip.bytes.dst", "tll": 1}, {"from_name": "upSesBytes", "to_name": "upSesBytes", "to_type": 13, "rule_name": "ip.bytes.src", "tll": 1}, {"from_name": "downLinkTcpOpts", "to_name": "downLinkTcpOpts", "to_type": 13, "rule_name": "tcp.options.dst", "tll": 1}, {"from_name": "upLinkTcpOpts", "to_name": "upLinkTcpOpts", "to_type": 13, "rule_name": "tcp.options.src", "tll": 1}, {"from_name": "downLinkSynTcpWins", "to_name": "downLinkSynTcpWins", "to_type": 13, "rule_name": "tcp.winsize.dst", "tll": 1}, {"from_name": "upLinkSynTcpWins", "to_name": "upLinkSynTcpWins", "to_type": 13, "rule_name": "tcp.winsize.src", "tll": 1}, {"from_name": "downLinkPayLenSet", "to_name": "downLinkPayLenSet", "to_type": 13, "rule_name": "trans.paylen.set.dst", "tll": 1}, {"from_name": "upLinkPayLenSet", "to_name": "upLinkPayLenSet", "to_type": 13, "rule_name": "trans.paylen.set.src", "tll": 1}, {"from_name": "downLinkTransPayHex", "to_name": "downLinkTransPayHex", "to_type": 13, "tll": 1}, {"from_name": "upLinkTransPayHex", "to_name": "upLinkTransPayHex", "to_type": 13, "tll": 1}, {"from_name": "downLinkStream", "to_name": "downLinkStream", "to_type": 13, "rule_name": "ip.stream.dst", "tll": 1}, {"from_name": "upLinkStream", "to_name": "upLinkStream", "to_type": 13, "rule_name": "ip.stream.src", "tll": 1}, {"from_name": "", "to_name": "stream", "to_type": 13, "rule_name": "ip.stream", "tll": 1}, {"from_name": "downLinkDesBytes", "to_name": "downLinkDesBytes", "to_type": 13, "rule_name": "ip.desiredbytes.dst", "tll": 1}, {"from_name": "upLinkDesBytes", "to_name": "upLinkDesBytes", "to_type": 13, "rule_name": "ip.desiredbytes.src", "tll": 1}, {"from_name": "downLinkChecksum", "to_name": "downLinkChecksum", "to_type": 13, "tll": 1}, {"from_name": "upLinkChecksum", "to_name": "upLinkChecksum", "to_type": 13, "tll": 1}, {"from_name": "tcpFlagsSynAckCnt", "to_name": "tcpFlagsSynAckCnt", "to_type": 13, "rule_name": "tcp.flags.syn_ack.cnt", "tll": 1}, {"from_name": "tcpFlagsNSCnt", "to_name": "tcpFlagsNSCnt", "to_type": 13, "tll": 1}, {"from_name": "tcpFlagsCwrCnt", "to_name": "tcpFlagsCwrCnt", "to_type": 13, "tll": 1}, {"from_name": "tcpFlagsEceCnt", "to_name": "tcpFlagsEceCnt", "to_type": 13, "tll": 1}, {"from_name": "tcpFlagsUrgCnt", "to_name": "tcpFlagsUrgCnt", "to_type": 13, "rule_name": "tcp.flags.urg.cnt", "tll": 1}, {"from_name": "tcpFlagsAckCnt", "to_name": "tcpFlagsAckCnt", "to_type": 13, "rule_name": "tcp.flags.ack.cnt", "tll": 1}, {"from_name": "tcpFlagsPshCnt", "to_name": "tcpFlagsPshCnt", "to_type": 13, "rule_name": "tcp.flags.psh.cnt", "tll": 1}, {"from_name": "tcpFlagsRstCnt", "to_name": "tcpFlagsRstCnt", "to_type": 13, "rule_name": "tcp.flags.rst.cnt", "tll": 1}, {"from_name": "tcpFlagsSynCnt", "to_name": "tcpFlagsSynCnt", "to_type": 13, "rule_name": "tcp.flags.syn.cnt", "tll": 1}, {"from_name": "tcpFlagsFinCnt", "to_name": "tcpFlagsFinCnt", "to_type": 13, "rule_name": "tcp.flags.fin.cnt", "tll": 1}, {"from_name": "appDirec", "to_name": "appDirec", "to_type": 13, "rule_name": "ip.direction", "tll": 1}, {"from_name": "firTtlBySrv", "to_name": "firTtlBySrv", "to_type": 13, "rule_name": "ip.ttl.dst", "tll": 1}, {"from_name": "firTtlByCli", "to_name": "firTtlByCli", "to_type": 13, "rule_name": "ip.ttl.src", "tll": 1}, {"from_name": "downLinkSmaPktInt", "to_name": "downLinkSmaPktInt", "to_type": 13, "tll": 1}, {"from_name": "downLinkBigPktInt", "to_name": "downLinkBigPktInt", "to_type": 13, "tll": 1}, {"from_name": "downLinkSmaPktLen", "to_name": "downLinkSmaPktLen", "to_type": 13, "tll": 1}, {"from_name": "downLinkBigPktLen", "to_name": "downLinkBigPktLen", "to_type": 13, "tll": 1}, {"from_name": "downLinkPktNum", "to_name": "downLinkPktNum", "to_type": 13, "rule_name": "ip.packets.dst", "tll": 1}, {"from_name": "upLinkSmaPktInt", "to_name": "upLinkSmaPktInt", "to_type": 13, "tll": 1}, {"from_name": "upLinkBigPktInt", "to_name": "upLinkBigPktInt", "to_type": 13, "tll": 1}, {"from_name": "upLinkSmaPktLen", "to_name": "upLinkSmaPktLen", "to_type": 13, "tll": 1}, {"from_name": "upLinkBigPktLen", "to_name": "upLinkBigPktLen", "to_type": 13, "tll": 1}, {"from_name": "upLinkPktNum", "to_name": "upLinkPktNum", "to_type": 13, "rule_name": "ip.packets.src", "tll": 1}, {"from_name": "downPayLen", "to_name": "downPayLen", "to_type": 13, "rule_name": "ip.databytes.dst", "tll": 1}, {"from_name": "upPayLen", "to_name": "upPayLen", "to_type": 13, "rule_name": "ip.databytes.src", "tll": 1}]}