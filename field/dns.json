{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "dns", "to_proto_name": "dns", "rule_proto_name": "dns", "field": [{"from_name": "DNSTEX", "to_name": "DNSTEX", "to_type": 13, "rule_name": "txt", "tll": 1}, {"from_name": "DNSSPF", "to_name": "DNSSPF", "to_type": 13, "rule_name": "sfp", "tll": 1}, {"from_name": "nameSrvCountry", "to_name": "nameSrvCountry", "to_type": 13, "tll": 1}, {"from_name": "nameSrvAsn", "to_name": "nameSrvAsn", "to_type": 13, "tll": 1}, {"from_name": "nameSrvIPCnt", "to_name": "nameSrvIPCnt", "to_type": 9, "tll": 1}, {"from_name": "nameSrvIp", "to_name": "nameSrvIp", "to_type": 9, "rule_name": "namesrv.ip", "tll": 1}, {"from_name": "nameSrvHostCnt", "to_name": "nameSrvHostCnt", "to_type": 9, "tll": 1}, {"from_name": "nameSrvHost", "to_name": "nameSrvHost", "to_type": 13, "rule_name": "namesrv.host", "tll": 1}, {"from_name": "mailSrvCountry", "to_name": "mailSrvCountry", "to_type": 13, "tll": 1}, {"from_name": "mailSrvAsn", "to_name": "mailSrvAsn", "to_type": 13, "tll": 1}, {"from_name": "mailSrvIPCnt", "to_name": "mailSrvIPCnt", "to_type": 9, "rule_name": "mailsrv.ip.cnt", "tll": 1}, {"from_name": "mailSrvIp", "to_name": "mailSrvIp", "to_type": 9, "rule_name": "mailsrv.ip", "tll": 1}, {"from_name": "mailSrvHostcnt", "to_name": "mailSrvHostcnt", "to_type": 9, "rule_name": "mailsrv.host.cnt", "tll": 1}, {"from_name": "mailSrvHost", "to_name": "mailSrvHost", "to_type": 13, "rule_name": "mailsrv.host", "tll": 1}, {"from_name": "aipCountry", "to_name": "aipCountry", "to_type": 13, "tll": 1}, {"from_name": "aipAsn", "to_name": "aipAsn", "to_type": 13, "tll": 1}, {"from_name": "AipCnt", "to_name": "AipCnt", "to_type": 9, "tll": 1}, {"from_name": "<PERSON><PERSON>", "to_name": "<PERSON><PERSON>", "to_type": 9, "rule_name": "aip", "tll": 1}, {"from_name": "additional_RRs", "to_name": "addCnt", "to_type": 9}, {"from_name": "authorization_RRs", "to_name": "autCnt", "to_type": 9}, {"from_name": "ansIPv6", "to_name": "ansIPv6", "to_type": 13, "rule_name": "answer.ipv6", "tll": 1}, {"from_name": "ansCnameCnt", "to_name": "ansCnameCnt", "to_type": 9, "tll": 1}, {"from_name": "ansCname", "to_name": "ansCname", "to_type": 13, "rule_name": "answer.cname", "tll": 1}, {"from_name": "answer_RRs", "to_name": "ansCnt", "to_type": 9, "tll": 1}, {"from_name": "addAnsRes", "to_name": "addAnsRes", "to_type": 13}, {"from_name": "addAnsType", "to_name": "addAnsType", "to_type": 9}, {"from_name": "authAnsRes", "to_name": "authAnsRes", "to_type": 13}, {"from_name": "authAnsType", "to_name": "authAnsType", "to_type": 9}, {"from_name": "ansRes", "to_name": "ansRes", "to_type": 13}, {"from_name": "ansTypes", "to_name": "ansTypes", "to_type": 13, "rule_name": "answer.types", "tll": 1}, {"from_name": "An_name00", "to_name": "ansQue", "to_type": 13, "rule_name": "answer.query", "tll": 1}, {"from_name": "flags", "to_name": "srvFlag", "to_type": 9}, {"from_name": "identification", "to_name": "traID", "to_type": 9}, {"from_name": "Qd_name00", "to_name": "queName", "to_type": 13}, {"from_name": "Qd_type00", "to_name": "queType", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}