{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "eigrp", "to_proto_name": "eigrp", "rule_proto_name": "eigrp", "field": [{"from_name": "", "to_name": "rouOpc", "to_type": 13}, {"from_name": "", "to_name": "rouTid", "to_type": 13}, {"from_name": "", "to_name": "rou<PERSON><PERSON>", "to_type": 13}, {"from_name": "", "to_name": "comList", "to_type": 13}, {"from_name": "routeReplicatedFlag", "to_name": "opaRepl", "to_type": 13}, {"from_name": "routeActiveFlag", "to_name": "opaAct", "to_type": 13}, {"from_name": "candDefaultFlag", "to_name": "opaCd", "to_type": 13}, {"from_name": "srcWithdrawFlag", "to_name": "opaSrc", "to_type": 13}, {"from_name": "", "to_name": "metEne", "to_type": 13}, {"from_name": "", "to_name": "met<PERSON>en", "to_type": 13}, {"from_name": "", "to_name": "metJit", "to_type": 13}, {"from_name": "", "to_name": "metMtu", "to_type": 13}, {"from_name": "", "to_name": "peeTerm", "to_type": 13}, {"from_name": "", "to_name": "mulSeq", "to_type": 13}, {"from_name": "", "to_name": "seqProtAddrs", "to_type": 13}, {"from_name": "", "to_name": "authData", "to_type": 13}, {"from_name": "", "to_name": "authType", "to_type": 13}, {"from_name": "", "to_name": "virID", "to_type": 13}, {"from_name": "", "to_name": "extMet", "to_type": 9}, {"from_name": "", "to_name": "adminTag", "to_type": 9}, {"from_name": "", "to_name": "rouTag", "to_type": 9}, {"from_name": "", "to_name": "scaBW", "to_type": 9}, {"from_name": "", "to_name": "extFlag", "to_type": 9}, {"from_name": "ExtProtoID", "to_name": "extProtID", "to_type": 9}, {"from_name": "OrigAS", "to_name": "oriASNum", "to_type": 10}, {"from_name": "OrigRoutID", "to_name": "originRou", "to_type": 9}, {"from_name": "Destination", "to_name": "dst", "to_type": 9}, {"from_name": "PrefixLength", "to_name": "perLen", "to_type": 9}, {"from_name": "Load", "to_name": "load", "to_type": 9}, {"from_name": "Reliability", "to_name": "reliability", "to_type": 9}, {"from_name": "HopCount", "to_name": "hopCou", "to_type": 9}, {"from_name": "BW", "to_name": "BW", "to_type": 10}, {"from_name": "Delay", "to_name": "delay", "to_type": 10}, {"from_name": "NextHop", "to_name": "nextHop", "to_type": 10}, {"from_name": "Type", "to_name": "rouType", "to_type": 9}, {"from_name": "TLVVersion", "to_name": "TLVSofVer", "to_type": 9}, {"from_name": "", "to_name": "tidList", "to_type": 13}, {"from_name": "SoftVersion", "to_name": "softwVer", "to_type": 13}, {"from_name": "HoldTime", "to_name": "holdTime", "to_type": 13}, {"from_name": "ParamK", "to_name": "parK", "to_type": 13}, {"from_name": "AS", "to_name": "ASNum", "to_type": 10}, {"from_name": "Ack", "to_name": "ACK", "to_type": 10}, {"from_name": "Seq", "to_name": "seq", "to_type": 10}, {"from_name": "Flag", "to_name": "flag", "to_type": 10}, {"from_name": "Opcode", "to_name": "opCode", "to_type": 9}, {"from_name": "Version", "to_name": "ver", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}