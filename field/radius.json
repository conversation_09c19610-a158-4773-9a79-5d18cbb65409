{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "radius", "to_proto_name": "radius", "rule_proto_name": "radius", "field": [{"from_name": "Acct_Authentic", "to_name": "acctAuth", "to_type": 9}, {"from_name": "Termination_Action", "to_name": "terCas", "to_type": 13}, {"from_name": "Login_IPv6_Host", "to_name": "loginIPv6Host", "to_type": 13}, {"from_name": "NAS_IPv6_Address", "to_name": "NASIPv6Addr", "to_type": 13}, {"from_name": "Login_LAT_Port", "to_name": "loginLATPort", "to_type": 13}, {"from_name": "Login_LAT_Group", "to_name": "loginLATGro", "to_type": 13}, {"from_name": "Login_LAT_Node", "to_name": "loginLATNod", "to_type": 13}, {"from_name": "Login_LAT_Service", "to_name": "loginLATSvc", "to_type": 13}, {"from_name": "Calling_Station_Id", "to_name": "calliStaID", "to_type": 13}, {"from_name": "Called_Station_Id", "to_name": "calleStaID", "to_type": 13}, {"from_name": "Vendor_Specific", "to_name": "venSpe", "to_type": 13}, {"from_name": "Framed_IPX_Network", "to_name": "fraIPXNet", "to_type": 9}, {"from_name": "Framed_Route", "to_name": "fraRout", "to_type": 13}, {"from_name": "NAS_Port_Type", "to_name": "NASPortType", "to_type": 13}, {"from_name": "NAS_Identifier", "to_name": "NASID", "to_type": 13}, {"from_name": "NAS_Port_Id", "to_name": "NASPorID", "to_type": 13}, {"from_name": "Callback_Id", "to_name": "calID", "to_type": 13}, {"from_name": "Callback_Number", "to_name": "calNum", "to_type": 13}, {"from_name": "Login_TCP_Port", "to_name": "logTcpPort", "to_type": 9}, {"from_name": "Login_Service", "to_name": "logSvc", "to_type": 9}, {"from_name": "Login_IP_Host", "to_name": "logIPHost", "to_type": 10}, {"from_name": "Framed_IP_Netmask", "to_name": "fraIPNet", "to_type": 10}, {"from_name": "Framed_IP_Address", "to_name": "fraIPAddr", "to_type": 10}, {"from_name": "Framed_Protocol", "to_name": "fra<PERSON><PERSON>", "to_type": 9}, {"from_name": "Service_Type", "to_name": "svcType", "to_type": 9}, {"from_name": "NAS_Port", "to_name": "NASPort", "to_type": 9}, {"from_name": "NAS_IP_Address", "to_name": "NASIPAddr", "to_type": 10}, {"from_name": "CHAP_Password", "to_name": "chapPwd", "to_type": 13}, {"from_name": "User_Password", "to_name": "pwd", "to_type": 13}, {"from_name": "User_Name", "to_name": "usrName", "to_type": 13}, {"from_name": "Authenticator", "to_name": "authenticator", "to_type": 13}, {"from_name": "Code", "to_name": "code", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}