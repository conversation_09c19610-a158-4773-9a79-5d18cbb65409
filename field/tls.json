{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "ssl_n", "to_proto_name": "ssl_tls", "rule_proto_name": "tls", "field": [{"from_name": "CertificatesNums", "to_name": "srvCertNum", "to_type": 9, "tll": 1}, {"from_name": "", "to_name": "cliCertNum", "to_type": 9, "tll": 1}, {"from_name": "ServerCertificateSignature", "to_name": "srvCertHashes", "to_type": 13, "rule_name": "cert.hashes.s", "tll": 1}, {"from_name": "ClientCertificateSignature", "to_name": "cliCertHashes", "to_type": 13, "rule_name": "cert.hashes.c", "tll": 1}, {"from_name": "", "to_name": "fullText", "to_type": 13, "tll": 1}, {"from_name": "", "to_name": "sessSecFlag", "to_type": 13}, {"from_name": "certPath", "to_name": "certPath", "to_type": 13, "rule_name": "cert.path", "tll": 1}, {"from_name": "", "to_name": "certIntactFlag", "to_type": 13}, {"from_name": "", "to_name": "JoyFp", "to_type": 13}, {"from_name": "", "to_name": "certNonFlag", "to_type": 13}, {"from_name": "", "to_name": "STARTTLS", "to_type": 13}, {"from_name": "JA3S", "to_name": "JOYS", "to_type": 13, "tll": 1}, {"from_name": "JA3C", "to_name": "JOY", "to_type": 13, "tll": 1}, {"from_name": "", "to_name": "SigHashAlg", "to_type": 13}, {"from_name": "", "to_name": "sigAlg", "to_type": 13}, {"from_name": "", "to_name": "SigAlgType", "to_type": 13}, {"from_name": "DHEPubkey", "to_name": "DHEPubKey", "to_type": 13}, {"from_name": "DHEPubKeyLength", "to_name": "DHEPubKeyLen", "to_type": 13}, {"from_name": "DHESignature", "to_name": "DHESig", "to_type": 13}, {"from_name": "RSASignature", "to_name": "RSASig", "to_type": 13}, {"from_name": "RSAExponentLength", "to_name": "RSAExpLen", "to_type": 13}, {"from_name": "RSAModulusLength", "to_name": "RSAModLen", "to_type": 13}, {"from_name": "", "to_name": "greaseFlag", "to_type": 13}, {"from_name": "RSASignatureHashAlgorithm", "to_name": "RSASigHash", "to_type": 13}, {"from_name": "DHESignatureHashAlgorithm", "to_name": "DHESigHash", "to_type": 13}, {"from_name": "ECDHSignatureHashAlgorithm", "to_name": "ECDHISigHash", "to_type": 13}, {"from_name": "ClientCipherSuiteCnt", "to_name": "cipSuiNum", "to_type": 9, "rule_name": "ciphersuits.cnt", "tll": 1}, {"from_name": "ServerCipherSuite", "to_name": "srvCipSui", "to_type": 13, "rule_name": "cipher", "tll": 1}, {"from_name": "newSessTicketData", "to_name": "ticDat", "to_type": 13}, {"from_name": "", "to_name": "namLen", "to_type": 13}, {"from_name": "", "to_name": "namType", "to_type": 13}, {"from_name": "ECDHSignatureLength", "to_name": "ECDHPubKeyLen", "to_type": 13}, {"from_name": "ServerExtensionsLength", "to_name": "srvExtLen", "to_type": 13}, {"from_name": "ClientExtensionsLength", "to_name": "cliExtLen", "to_type": 13}, {"from_name": "EncrypedPubkeyLength", "to_name": "encPubKeyLen", "to_type": 13}, {"from_name": "EncrypedPubkey", "to_name": "enc<PERSON><PERSON><PERSON><PERSON>", "to_type": 13}, {"from_name": "ClientKexLength", "to_name": "clikeyExcLen", "to_type": 13}, {"from_name": "DHEg", "to_name": "DHEGLen", "to_type": 13}, {"from_name": "DHEp", "to_name": "DHEPLen", "to_type": 13}, {"from_name": "ECDHSignature", "to_name": "ECDHSig", "to_type": 13}, {"from_name": "ECDHCurveType", "to_name": "ECDHCurType", "to_type": 13}, {"from_name": "ServerKexLength", "to_name": "srvKeyExcLen", "to_type": 13}, {"from_name": "ServerSessionIDLength", "to_name": "srvSesIDLen", "to_type": 13}, {"from_name": "ClientSessionIDLength", "to_name": "cliSesIDLen", "to_type": 13}, {"from_name": "", "to_name": "ttags", "to_type": 13}, {"from_name": "", "to_name": "etags", "to_type": 13}, {"from_name": "", "to_name": "ecPoiForByServ", "to_type": 13}, {"from_name": "", "to_name": "ecGroupsCli", "to_type": 13}, {"from_name": "CertificatesNums", "to_name": "srvCertCnt", "to_type": 9}, {"from_name": "Client_Certificate_length", "to_name": "cliCertCnt", "to_type": 9}, {"from_name": "", "to_name": "AuthTag", "to_type": 1}, {"from_name": "ServerSessionTick", "to_name": "srvSessTicket", "to_type": 13, "rule_name": "sessionticket.s", "tll": 1}, {"from_name": "ClientSessionTick", "to_name": "cliSessTicket", "to_type": 13, "rule_name": "sessionticket.c", "tll": 1}, {"from_name": "JA3S", "to_name": "srvJA3", "to_type": 13, "rule_name": "ja3s", "tll": 1}, {"from_name": "JA3C", "to_name": "cliJA3", "to_type": 13, "rule_name": "ja3", "tll": 1}, {"from_name": "extGrease", "to_name": "cliExtGrease", "to_type": 1, "rule_name": "ext.grease", "tll": 1}, {"from_name": "ServerExtensions", "to_name": "srvExt", "to_type": 13, "rule_name": "ext.types.s", "tll": 1}, {"from_name": "ClientExtensions", "to_name": "cliExt", "to_type": 13, "rule_name": "ext.types.c", "tll": 1}, {"from_name": "Server<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_name": "srvHandSkLen", "to_type": 9, "rule_name": "handshake.len.s", "tll": 1}, {"from_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to_name": "cliHandSkLen", "to_type": 9, "rule_name": "handshake.len.c", "tll": 1}, {"from_name": "ServerExtensionsLength", "to_name": "srvExtCnt", "to_type": 9, "tll": 1}, {"from_name": "ClientExtensionsLength", "to_name": "cliExtCnt", "to_type": 9, "tll": 1}, {"from_name": "CertificateVerifyLength", "to_name": "cliCertLen", "to_type": 13}, {"from_name": "ServerGMTUnixTime", "to_name": "srvGMTUniTime", "to_type": 15, "rule_name": "gmttime.s"}, {"from_name": "ClientECDHPubkey", "to_name": "cliEllCurDHPubKey", "to_type": 13}, {"from_name": "ECDHPubkey", "to_name": "srvEllCurDHPubKey", "to_type": 13}, {"from_name": "srvExtECGroups", "to_name": "srvEllCur", "to_type": 9, "rule_name": "ext.ec_groups.s", "tll": 1}, {"from_name": "srvExtECPoiFor", "to_name": "srvEllCurPoiFor", "to_type": 9, "rule_name": "ext.ec_format.s", "tll": 1}, {"from_name": "ClientECDHNamedCurve", "to_name": "cliEllCur", "to_type": 9, "rule_name": "ext.ec_groups.c", "tll": 1}, {"from_name": "ClientECDHCurveType", "to_name": "cliEllCurPoiFor", "to_type": 9, "rule_name": "ext.ec_format.c", "tll": 1}, {"from_name": "ext_type", "to_name": "extTypeInSSL", "to_type": 9}, {"from_name": "", "to_name": "cli<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_type": 13}, {"from_name": "RSAPreSharedKey", "to_name": "preMasKeyEncryByRSA", "to_type": 13}, {"from_name": "DHEPubkey", "to_name": "srvDHPub<PERSON>ey", "to_type": 13}, {"from_name": "ServerKeyExDHGen_g", "to_name": "DHGenOfSrvKeyExc", "to_type": 13}, {"from_name": "ServerKeyExDHMod_p", "to_name": "DHModOfSrvKeyExc", "to_type": 13}, {"from_name": "RSAExponent", "to_name": "RSAExpOfSrvKeyExc", "to_type": 10}, {"from_name": "RSAModulus", "to_name": "RSAModOfSrvKeyExc", "to_type": 13}, {"from_name": "Client_Certificate_length", "to_name": "cliCertLen", "to_type": 9, "tll": 1}, {"from_name": "ClientCertificateTypes", "to_name": "certResType", "to_type": 9}, {"from_name": "CertificatesLength", "to_name": "srvCertLen", "to_type": 9, "tll": 1}, {"from_name": "ServerCompressionMethod", "to_name": "srvComprMet", "to_type": 13, "rule_name": "compmethod.s", "tll": 1}, {"from_name": "ServerSessionID", "to_name": "srvSesID", "to_type": 13, "rule_name": "sessionid.s", "tll": 1}, {"from_name": "ServerRandomBytes", "to_name": "srvRand", "to_type": 13, "rule_name": "handshake.random.s", "tll": 1}, {"from_name": "ServerNameAttr", "to_name": "srvNameAttr", "to_type": 9, "tll": 1}, {"from_name": "ServerName", "to_name": "srvName", "to_type": 13, "rule_name": "sni", "tll": 1}, {"from_name": "ServerProtocolVersion", "to_name": "srvVer", "to_type": 9, "rule_name": "version.s", "tll": 1}, {"from_name": "ClientCompressionMethods", "to_name": "cliComMet", "to_type": 13, "rule_name": "compmethod.c", "tll": 1}, {"from_name": "ClientCipherSuites", "to_name": "cliCipSui", "to_type": 9, "rule_name": "cyphersuits", "tll": 1}, {"from_name": "ClientSessionID", "to_name": "cliSesID", "to_type": 13, "rule_name": "sessionid.c", "tll": 1}, {"from_name": "ClientRandomBytes", "to_name": "cliRand", "to_type": 13, "rule_name": "handshake.random.c", "tll": 1}, {"from_name": "ClientGMTUnixTime", "to_name": "cliGMTUniTime", "to_type": 15, "rule_name": "gmttime.c", "tll": 1}, {"from_name": "ClientProtocolVersion", "to_name": "cli<PERSON>er", "to_type": 9, "rule_name": "version.c", "tll": 1}, {"from_name": "HandshakeType", "to_name": "handShaType", "to_type": 9}, {"from_name": "AlertDescription", "to_name": "aleDes", "to_type": 9}, {"from_name": "AlertLevel", "to_name": "<PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "ContentType", "to_name": "conType", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}