{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "rsvp", "to_proto_name": "rsvp", "rule_proto_name": "rsvp", "field": [{"from_name": "flowMaxPackUint", "to_name": "", "to_type": 9}, {"from_name": "flowMinPolicedUint", "to_name": "", "to_type": 9}, {"from_name": "flowPeakDataRate", "to_name": "", "to_type": 9}, {"from_name": "flowBucketSize", "to_name": "", "to_type": 9}, {"from_name": "flowBucketRate", "to_name": "", "to_type": 9}, {"from_name": "", "to_name": "recordSrcAddr", "to_type": 13}, {"from_name": "explicitRouteSrcAddress", "to_name": "exolicitSrcAddr", "to_type": 13}, {"from_name": "minPathLatency", "to_name": "minLaten", "to_type": 15}, {"from_name": "tspecBucketSize", "to_name": "buckSize", "to_type": 9}, {"from_name": "sender_template_lsp_id", "to_name": "lspID", "to_type": 9}, {"from_name": "session_flags", "to_name": "ses<PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "holdPriority", "to_name": "priHold", "to_type": 9}, {"from_name": "setupPriority", "to_name": "priSetup", "to_type": 9}, {"from_name": "request_label_label", "to_name": "l3pid", "to_type": 9}, {"from_name": "eror_value", "to_name": "err<PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "error_code", "to_name": "errCode", "to_type": 9}, {"from_name": "error_flags", "to_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "error_node_address", "to_name": "errNoAddr", "to_type": 13}, {"from_name": "timevalues_value", "to_name": "frfInterv", "to_type": 15}, {"from_name": "style_option_vector", "to_name": "sty", "to_type": 9}, {"from_name": "style_flags", "to_name": "styFlags", "to_type": 9}, {"from_name": "", "to_name": "dsum", "to_type": 9}, {"from_name": "", "to_name": "csum", "to_type": 9}, {"from_name": "", "to_name": "dtot", "to_type": 9}, {"from_name": "", "to_name": "ctoc", "to_type": 9}, {"from_name": "minMTU", "to_name": "comMtu", "to_type": 9}, {"from_name": "minBandwidth", "to_name": "bw", "to_type": 9}, {"from_name": "tspecMaxPackUint", "to_name": "max<PERSON><PERSON><PERSON><PERSON>", "to_type": 9}, {"from_name": "tspecMinPolicedUint", "to_name": "minPunit", "to_type": 9}, {"from_name": "tspecPeakDataRate", "to_name": "peakDRate", "to_type": 9}, {"from_name": "check", "to_name": "chk", "to_type": 9}, {"from_name": "version", "to_name": "ver", "to_type": 9}, {"from_name": "lspTE", "to_name": "TE", "to_type": 13}, {"from_name": "", "to_name": "rSrcAddr", "to_type": 13}, {"from_name": "label_label", "to_name": "label", "to_type": 9}, {"from_name": "tspecBucketRate", "to_name": "rate", "to_type": 9}, {"from_name": "<PERSON><PERSON><PERSON>", "to_name": "sesName", "to_type": 13}, {"from_name": "hop_neighbor_address", "to_name": "nexHop", "to_type": 10}, {"from_name": "", "to_name": "intDstAddr", "to_type": 10}, {"from_name": "", "to_name": "intLocAddr", "to_type": 10}, {"from_name": "session_extended_tunnel_id", "to_name": "extTunID", "to_type": 9}, {"from_name": "session_tunnel_id", "to_name": "tunID", "to_type": 9}, {"from_name": "session_dst_address", "to_name": "tunDstAddr", "to_type": 10}, {"from_name": "sender_template_src_address", "to_name": "tunSrcAddr", "to_type": 10}, {"from_name": "", "to_name": "msgID", "to_type": 13}, {"from_name": "ttl", "to_name": "senTTL", "to_type": 9}, {"from_name": "type", "to_name": "msgType", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}