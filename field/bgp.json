{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "bgp", "to_proto_name": "bgp", "rule_proto_name": "bgp", "field": [{"from_name": "UpdateMPUnreachNLRI", "to_name": "mpUnreachNLRI", "to_type": 13, "rule_name": "mp_unreach.nlri", "tll": 1}, {"from_name": "UpdateMPUnreachNLRISafi", "to_name": "mpUnreachSAFI", "to_type": 13, "rule_name": "mp_unreach.safi", "tll": 1}, {"from_name": "UpdateMPUnreachNLRIAfi", "to_name": "mpUnreachAFI", "to_type": 13, "rule_name": "mp_unreach.afi", "tll": 1}, {"from_name": "UpdateMPReachNLRI", "to_name": "mpReach", "to_type": 13, "rule_name": "mp_reach.nlri", "tll": 1}, {"from_name": "UpdateMPReachNexthop", "to_name": "mpReachNextHop", "to_type": 13, "rule_name": "mp_reach.next_hop", "tll": 1}, {"from_name": "UpdateMPReachNLRISafi", "to_name": "mpReachSAFI", "to_type": 13, "rule_name": "mp_reach.safi", "tll": 1}, {"from_name": "UpdateMPReachNLRIAfi", "to_name": "mpReachAFI", "to_type": 13, "rule_name": "mp_reach.afi", "tll": 1}, {"from_name": "", "to_name": "NLRIPre", "to_type": 13}, {"from_name": "", "to_name": "NLRIPreMAS", "to_type": 13}, {"from_name": "", "to_name": "ipv6Prefix", "to_type": 13}, {"from_name": "", "to_name": "ipv4Prefix", "to_type": 13}, {"from_name": "", "to_name": "VPNlabSta", "to_type": 13}, {"from_name": "", "to_name": "rou<PERSON>is", "to_type": 13}, {"from_name": "", "to_name": "prefixLen", "to_type": 13}, {"from_name": "", "to_name": "typeCode", "to_type": 13}, {"from_name": "", "to_name": "extAreaId", "to_type": 13}, {"from_name": "", "to_name": "locAdmin", "to_type": 13}, {"from_name": "", "to_name": "extMetType", "to_type": 13}, {"from_name": "", "to_name": "extRouType", "to_type": 13}, {"from_name": "", "to_name": "extRouID", "to_type": 13}, {"from_name": "", "to_name": "extSubType", "to_type": 13}, {"from_name": "ext_UpdateCommunitiesTypeHigh", "to_name": "extTypeHigh", "to_type": 13, "rule_name": "typehigh", "tll": 1}, {"from_name": "UpdateCommunitiesValue", "to_name": "comVal", "to_type": 13, "rule_name": "commval", "tll": 1}, {"from_name": "UpdateCommunitiesAs", "to_name": "comAS", "to_type": 13, "rule_name": "commas", "tll": 1}, {"from_name": "", "to_name": "errType", "to_type": 13}, {"from_name": "", "to_name": "gateway", "to_type": 13}, {"from_name": "", "to_name": "atoAgg", "to_type": 13}, {"from_name": "", "to_name": "dstBorAS", "to_type": 13}, {"from_name": "", "to_name": "srcBorAS", "to_type": 13}, {"from_name": "", "to_name": "nexHopIpv6", "to_type": 13}, {"from_name": "OpenCapabilityTypes", "to_name": "capType", "to_type": 13, "rule_name": "capa.type", "tll": 1}, {"from_name": "OpenParamType", "to_name": "parType", "to_type": 13, "rule_name": "parm.type", "tll": 1}, {"from_name": "RouteRefreshAFI", "to_name": "afi", "to_type": 9, "rule_name": "afi", "tll": 1}, {"from_name": "NotificationErrData", "to_name": "errData", "to_type": 13, "rule_name": "err_data", "tll": 1}, {"from_name": "NotificationMIEC", "to_name": "errSubCode", "to_type": 9, "rule_name": "err_subcode", "tll": 1}, {"from_name": "NotificationMAEC", "to_name": "errCode", "to_type": 9, "rule_name": "err_code", "tll": 1}, {"from_name": "OpenHoldtime", "to_name": "holdTime", "to_type": 9, "rule_name": "holdtime", "tll": 1}, {"from_name": "UpdateNetworkLayerRechability", "to_name": "nlri", "to_type": 13, "rule_name": "nlri", "tll": 1}, {"from_name": "UpdateWithDrawnRoutes", "to_name": "with<PERSON>raw", "to_type": 13, "rule_name": "withdrawn", "tll": 1}, {"from_name": "UpdateMPUnReachability", "to_name": "MVNLURIPreAndMas", "to_type": 13}, {"from_name": "UpdateMPReachability", "to_name": "MVNLRIPreAndMas", "to_type": 13}, {"from_name": "RouteRefreshSAFI", "to_name": "SAFI", "to_type": 9, "rule_name": "safi", "tll": 1}, {"from_name": "UpdateClusterList", "to_name": "cluIDList", "to_type": 13, "rule_name": "cluster", "tll": 1}, {"from_name": "UpdateOriginatorId", "to_name": "originID", "to_type": 10, "rule_name": "origin_id", "tll": 1}, {"from_name": "UpdateAggregatorIp", "to_name": "aggAddr", "to_type": 13, "rule_name": "aggr_ip", "tll": 1}, {"from_name": "UpdateAggregatorAs", "to_name": "aggAS", "to_type": 13, "rule_name": "aggr_as", "tll": 1}, {"from_name": "ext_UpdateCommunities", "to_name": "extComName", "to_type": 13, "rule_name": "ext_comm", "tll": 1}, {"from_name": "UpdateCommunities", "to_name": "comName", "to_type": 13, "rule_name": "comm", "tll": 1}, {"from_name": "UpdateLocalPref", "to_name": "locPre", "to_type": 10, "rule_name": "localpref", "tll": 1}, {"from_name": "UpdateMed", "to_name": "MED", "to_type": 10, "rule_name": "med", "tll": 1}, {"from_name": "UpdateNextHop", "to_name": "nexHop", "to_type": 10, "rule_name": "next_hop", "tll": 1}, {"from_name": "UpdateAsPath", "to_name": "ASPath", "to_type": 13, "rule_name": "as_path", "tll": 1}, {"from_name": "Update<PERSON><PERSON><PERSON>", "to_name": "origin", "to_type": 9, "rule_name": "origin", "tll": 1}, {"from_name": "OpenAuthenticationData", "to_name": "authData", "to_type": 13, "rule_name": "auth_data", "tll": 1}, {"from_name": "OpenAuthenticationCode", "to_name": "authCode", "to_type": 9, "rule_name": "auth_code", "tll": 1}, {"from_name": "OpenIdentifier", "to_name": "rouID", "to_type": 10, "rule_name": "id", "tll": 1}, {"from_name": "OpenMyas", "to_name": "ASNum", "to_type": 10, "rule_name": "asn", "tll": 1}, {"from_name": "OpenVersion", "to_name": "ver", "to_type": 9, "rule_name": "version", "tll": 1}, {"from_name": "BgpType", "to_name": "msgType", "to_type": 9, "rule_name": "msg_type", "tll": 1}, {"from_name": "marker", "to_name": "marker", "to_type": 13, "rule_name": "marker", "tll": 1}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}