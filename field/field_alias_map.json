{"desc": "左边是客户字段全称, 右边是阅维字段全称，仅限筛选字段", "convert": {"ip.databytes.src": "link.ip.databytes.src", "ip.databytes.dst": "link.ip.databytes.dst", "ip.packets.src": "link.ip.packets.src", "trans.packetlen.src.max": "link.trans.packetlen.src.max", "trans.packetlen.src.min": "link.trans.packetlen.src.min", "trans.packinter.src.max": "link.trans.packinter.src.max", "trans.packinter.src.min": "link.trans.packinter.src.min", "ip.packets.dst": "link.ip.packets.dst", "trans.packetlen.dst.max": "link.trans.packetlen.dst.max", "trans.packetlen.dst.min": "link.trans.packetlen.dst.min", "trans.packinter.dst.max": "link.trans.packinter.dst.max", "trans.packinter.dst.min": "link.trans.packinter.dst.min", "ip.ttl.src": "link.ip.ttl.src", "ip.ttl.dst": "link.ip.ttl.dst", "ip.direction": "link.ip.direction", "tcp.flags.fin.cnt": "link.tcp.flags.fin.cnt", "tcp.flags.syn.cnt": "link.tcp.flags.syn.cnt", "tcp.flags.rst.cnt": "link.tcp.flags.rst.cnt", "tcp.flags.psh.cnt": "link.tcp.flags.psh.cnt", "tcp.flags.ack.cnt": "link.tcp.flags.ack.cnt", "tcp.flags.urg.cnt": "link.tcp.flags.urg.cnt", "tcp.flags.ece.cnt": "link.tcp.flags.ece.cnt", "tcp.flags.cwr.cnt": "link.tcp.flags.cwr.cnt", "tcp.flags.ns.cnt": "link.tcp.flags.ns.cnt", "tcp.flags.syn_ack.cnt": "link.tcp.flags.syn_ack.cnt", "ip.checksum.src": "link.ip.checksum.src", "ip.checksum.dst": "link.ip.checksum.dst", "ip.desiredbytes.src": "link.ip.desiredbytes.src", "ip.desiredbytes.dst": "link.ip.desiredbytes.dst", "ip.stream": "link.ip.stream", "ip.stream.src": "link.ip.stream.src", "ip.stream.dst": "link.ip.stream.dst", "trans.payload.src.hex": "link.trans.payload.src.hex", "trans.payload.dst.hex": "link.trans.payload.dst.hex", "trans.paylen.set.src": "link.trans.paylen.set.src", "trans.paylen.set.dst": "link.trans.paylen.set.dst", "tcp.winsize.src": "link.tcp.winsize.src", "tcp.winsize.dst": "link.tcp.winsize.dst", "tcp.options.src": "link.tcp.options.src", "tcp.options.dst": "link.tcp.options.dst", "ip.bytes.src": "link.ip.bytes.src", "ip.bytes.dst": "link.ip.bytes.dst", "ip.bytes": "link.ip.bytes", "ip.bytes.ratio": "link.ip.bytes.ratio", "ip.databytes.ratio": "link.ip.databytes.ratio", "cable.linename1": "common.cable.linename1", "cable.linename2": "common.cable.linename2", "ip.time.beg": "common.ip.time.beg", "ip.time.end": "common.ip.time.end", "ip.time_len": "common.ip.time_len", "ip.version": "common.ip.version", "ip.src": "common.ip.src", "ip.dst": "common.ip.dst", "ip.ttl": "common.ip.ttl", "port.src": "common.port.src", "port.dst": "common.port.dst", "ip.proto": "common.ip.proto", "ipv6.src": "common.ipv6.src", "ipv6.dst": "common.ipv6.dst", "ip.proto_path": "common.ip.proto_path", "ip.bproto": "common.ip.bproto", "ip.uproto": "common.ip.uproto", "ip.packets": "common.ip.packets", "ip.databytes": "common.ip.databytes", "etags": "common.etags", "ttags": "common.ttags", "atags": "common.atags", "utags": "common.utags", "apls.lable1": "common.apls.lable1", "apls.lable2": "common.apls.lable2", "apls.lable3": "common.apls.lable3", "apls.lable4": "common.apls.lable4", "vlan.id1": "common.vlan.id1", "vlan.id2": "common.vlan.id2", "mpls.lable1": "common.mpls.lable1", "mpls.lable2": "common.mpls.lable2", "mpls.lable3": "common.mpls.lable3", "mpls.lable4": "common.mpls.lable4", "mac.src": "common.mac.src", "mac.dst": "common.mac.dst", "ip.country.src": "common.ip.country.src", "ip.state.src": "common.ip.state.src", "ip.city.src": "common.ip.city.src", "ip.longitude.src": "common.ip.longitude.src", "ip.latitude.src": "common.ip.latitude.src", "ip.isp.src": "common.ip.isp.src", "ip.asn.src": "common.ip.asn.src", "ip.country.dst": "common.ip.country.dst", "ip.state.dst": "common.ip.state.dst", "ip.city.dst": "common.ip.city.dst", "ip.longitude.dst": "common.ip.longitude.dst", "ip.latitude.dst": "common.ip.latitude.dst", "ip.isp.dst": "common.ip.isp.dst", "ip.asn.dst": "common.ip.asn.dst", "outer.ip.version": "common.outer.ip.version", "outer.ip.src": "common.outer.ip.src", "outer.ip.dst": "common.outer.ip.dst", "outer.ipv6.src": "common.outer.ipv6.src", "outer.ipv6.dst": "common.outer.ipv6.dst", "outer.port.src": "common.outer.port.src", "outer.port.dst": "common.outer.port.dst", "outer.ip.proto": "common.outer.ip.proto", "etime": "common.etime", "mac.oui.src": "common.mac.oui.src", "mac.oui.dst": "common.mac.oui.dst"}}