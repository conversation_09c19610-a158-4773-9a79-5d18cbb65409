{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "smb", "to_proto_name": "smb", "rule_proto_name": "smb", "field": [{"from_name": "clusterShift", "to_name": "clusterShift", "to_type": 13}, {"from_name": "chunkShift", "to_name": "chunkShift", "to_type": 13}, {"from_name": "compressUnitShift", "to_name": "compressUnitShift", "to_type": 13}, {"from_name": "compressFormat", "to_name": "compressFormat", "to_type": 13}, {"from_name": "compressFileSize", "to_name": "compressFileSize", "to_type": 13}, {"from_name": "dltPending", "to_name": "dltPending", "to_type": 13}, {"from_name": "numofLinks", "to_name": "numofLinks", "to_type": 13}, {"from_name": "endofFile", "to_name": "endofFile", "to_type": 13}, {"from_name": "nameofFileSys", "to_name": "nameofFileSys", "to_type": 13}, {"from_name": "lenofFileSys", "to_name": "lenofFileSys", "to_type": 13}, {"from_name": "maxLenofFileSys", "to_name": "maxLenofFileSys", "to_type": 13}, {"from_name": "fileSysAttrs", "to_name": "fileSysAttrs", "to_type": 13}, {"from_name": "", "to_name": "lastChangTime", "to_type": 13}, {"from_name": "", "to_name": "deviceCharacter", "to_type": 13}, {"from_name": "deviceType", "to_name": "deviceType", "to_type": 13}, {"from_name": "bytesPersec", "to_name": "bytesPersec", "to_type": 13}, {"from_name": "secAllocPerUnit", "to_name": "secAllocPerUnit", "to_type": 13}, {"from_name": "total_freeallocUnits", "to_name": "total_freeallocUnits", "to_type": 13}, {"from_name": "", "to_name": "total_allocUnits", "to_type": 13}, {"from_name": "", "to_name": "volCrtTime", "to_type": 13}, {"from_name": "volLabel", "to_name": "volLabel", "to_type": 13}, {"from_name": "volSerNbr", "to_name": "volSerNbr", "to_type": 13}, {"from_name": "unitAva", "to_name": "unitAva", "to_type": 13}, {"from_name": "unit", "to_name": "unit", "to_type": 13}, {"from_name": "", "to_name": "secUnit", "to_type": 13}, {"from_name": "fileSys", "to_name": "fileSys", "to_type": 13}, {"from_name": "", "to_name": "lcking", "to_type": 13}, {"from_name": "", "to_name": "unlcking", "to_type": 13}, {"from_name": "", "to_name": "unlckOffsetInbyts", "to_type": 13}, {"from_name": "", "to_name": "rspEchData", "to_type": 13}, {"from_name": "reqEchData", "to_name": "reqEchData", "to_type": 13}, {"from_name": "", "to_name": "stateFlags", "to_type": 13}, {"from_name": "", "to_name": "ntactionFlags", "to_type": 13}, {"from_name": "", "to_name": "actionFlags", "to_type": 13}, {"from_name": "freeUnits", "to_name": "freeUnits", "to_type": 13}, {"from_name": "blksize", "to_name": "blksize", "to_type": 13}, {"from_name": "blkPerunit", "to_name": "blkPerunit", "to_type": 13}, {"from_name": "totalUnits", "to_name": "totalUnits", "to_type": 13}, {"from_name": "", "to_name": "srvService", "to_type": 13}, {"from_name": "", "to_name": "srvNativeOs", "to_type": 13}, {"from_name": "", "to_name": "serverPrimaryDom", "to_type": 13}, {"from_name": "", "to_name": "crtAction", "to_type": 13}, {"from_name": "", "to_name": "priority", "to_type": 13}, {"from_name": "compltn<PERSON><PERSON>er", "to_name": "compltn<PERSON><PERSON>er", "to_type": 13}, {"from_name": "watchTree", "to_name": "watchTree", "to_type": 13}, {"from_name": "cliPrimaryDom", "to_name": "cliPrimaryDom", "to_type": 13}, {"from_name": "chall", "to_name": "chall", "to_type": 13}, {"from_name": "srvtimeZone", "to_name": "srvtimeZone", "to_type": 13}, {"from_name": "systime", "to_name": "systime", "to_type": 13}, {"from_name": "capabilities", "to_name": "capabilities", "to_type": 13}, {"from_name": "sesskey", "to_name": "sesskey", "to_type": 13}, {"from_name": "maxRawSz", "to_name": "maxRawSz", "to_type": 13}, {"from_name": "maxBuffSz", "to_name": "maxBuffSz", "to_type": 13}, {"from_name": "maxNumVcs", "to_name": "maxNumVcs", "to_type": 13}, {"from_name": "secMod", "to_name": "secMod", "to_type": 13}, {"from_name": "dialectIndx", "to_name": "dialectIndx", "to_type": 13}, {"from_name": "dialectStr", "to_name": "dialectStr", "to_type": 13}, {"from_name": "natFileSys", "to_name": "natFileSys", "to_type": 13}, {"from_name": "", "to_name": "clientService", "to_type": 13}, {"from_name": "", "to_name": "getExtAttrLst", "to_type": 13}, {"from_name": "lastWrtDate", "to_name": "lastWrtDate", "to_type": 13}, {"from_name": "lastAccTime", "to_name": "lastAccTime", "to_type": 13}, {"from_name": "lastAccDate", "to_name": "lastAccDate", "to_type": 13}, {"from_name": "crtTime", "to_name": "crtTime", "to_type": 13}, {"from_name": "crtDate", "to_name": "crtDate", "to_type": 13}, {"from_name": "", "to_name": "newFileNm", "to_type": 13}, {"from_name": "oldFileNm", "to_name": "oldFileNm", "to_type": 13}, {"from_name": "", "to_name": "EaerrOffset", "to_type": 13}, {"from_name": "numLckRng", "to_name": "numLckRng", "to_type": 13}, {"from_name": "numUnLckRng", "to_name": "numUnLckRng", "to_type": 13}, {"from_name": "lckType", "to_name": "lckType", "to_type": 13}, {"from_name": "offset", "to_name": "offset", "to_type": 13}, {"from_name": "name", "to_name": "name", "to_type": 13}, {"from_name": "", "to_name": "sid", "to_type": 13}, {"from_name": "subCommand", "to_name": "subCommand", "to_type": 13}, {"from_name": "lastWrtTime", "to_name": "lastWrtTime", "to_type": 13}, {"from_name": "lastAcc", "to_name": "lastAcc", "to_type": 13}, {"from_name": "", "to_name": "extAttrErrOff", "to_type": 13}, {"from_name": "", "to_name": "actionTaken", "to_type": 13}, {"from_name": "oplockLev", "to_name": "oplockLev", "to_type": 13}, {"from_name": "shareAcc", "to_name": "shareAcc", "to_type": 13}, {"from_name": "desireAcc", "to_name": "desireAcc", "to_type": 13}, {"from_name": "", "to_name": "rootDirFid", "to_type": 13}, {"from_name": "", "to_name": "openRsts", "to_type": 13}, {"from_name": "isDir", "to_name": "isDir", "to_type": 13}, {"from_name": "", "to_name": "nmpipeStats", "to_type": 13}, {"from_name": "", "to_name": "rsrcType", "to_type": 13}, {"from_name": "timeout", "to_name": "timeout", "to_type": 13}, {"from_name": "<PERSON><PERSON><PERSON>", "to_name": "<PERSON><PERSON><PERSON>", "to_type": 13}, {"from_name": "", "to_name": "openMod", "to_type": 13}, {"from_name": "", "to_name": "mod", "to_type": 13}, {"from_name": "", "to_name": "crttm", "to_type": 13}, {"from_name": "", "to_name": "lastMODFD", "to_type": 13}, {"from_name": "FID", "to_name": "FID", "to_type": 13}, {"from_name": "", "to_name": "accMode", "to_type": 13}, {"from_name": "unicodePass", "to_name": "unicodePass", "to_type": 13}, {"from_name": "", "to_name": "oemPass", "to_type": 13}, {"from_name": "pathPwd", "to_name": "pathPwd", "to_type": 13, "rule_name": "pwd", "tll": 1}, {"from_name": "accntnm", "to_name": "accntnm", "to_type": 13}, {"from_name": "hdrSecFeature", "to_name": "hdrSecFeature", "to_type": 13}, {"from_name": "hdrFlag2", "to_name": "hdrFlag2", "to_type": 13}, {"from_name": "hdrFlags", "to_name": "hdrFlags", "to_type": 13}, {"from_name": "hdrSrvStatus", "to_name": "hdrSrvStatus", "to_type": 13}, {"from_name": "hdrTID", "to_name": "hdrTID", "to_type": 13}, {"from_name": "hdrCommand", "to_name": "hdrCommand", "to_type": 13}, {"from_name": "hdrPID", "to_name": "hdrPID", "to_type": 13}, {"from_name": "", "to_name": "share", "to_type": 13}, {"from_name": "user", "to_name": "user", "to_type": 13, "rule_name": "user", "tll": 1}, {"from_name": "fileNameCnt", "to_name": "fileNameCnt", "to_type": 9, "rule_name": "fn.cnt", "tll": 1}, {"from_name": "", "to_name": "srvDomCnt", "to_type": 9}, {"from_name": "", "to_name": "fileExtAtt", "to_type": 10}, {"from_name": "fileAtt", "to_name": "fileAtt", "to_type": 10}, {"from_name": "fileSize", "to_name": "fileSize", "to_type": 10, "rule_name": "filesize", "tll": 1}, {"from_name": "fileName", "to_name": "fileName", "to_type": 13, "rule_name": "fn", "tll": 1}, {"from_name": "authType", "to_name": "authType", "to_type": 13, "rule_name": "auth_type", "tll": 1}, {"from_name": "ClientSB_Hostname", "to_name": "cliHost", "to_type": 13, "rule_name": "host.c", "tll": 1}, {"from_name": "ServerSB_Version", "to_name": "ver", "to_type": 13, "rule_name": "version", "tll": 1}, {"from_name": "ServerNativeOS", "to_name": "natOS", "to_type": 13, "rule_name": "os", "tll": 1}, {"from_name": "ServerName", "to_name": "hostName", "to_type": 13, "rule_name": "host.s", "tll": 1}, {"from_name": "ServerDomainName", "to_name": "srvDom", "to_type": 13, "rule_name": "domain", "tll": 1}, {"from_name": "path", "to_name": "path", "to_type": 13, "rule_name": "path", "tll": 1}, {"from_name": "dir", "to_name": "dir", "to_type": 13, "rule_name": "dir", "tll": 1}, {"from_name": "accRghts", "to_name": "accRghts", "to_type": 13, "rule_name": "accRghts", "tll": 1}, {"from_name": "infoFileNm", "to_name": "infoFileNm", "to_type": 13, "rule_name": "infoFileNm", "tll": 1}, {"from_name": "streamInfo", "to_name": "streamInfo", "to_type": 13, "rule_name": "streamInfo", "tll": 1}, {"from_name": "usrID", "to_name": "usrID", "to_type": 9}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 13}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "srcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}