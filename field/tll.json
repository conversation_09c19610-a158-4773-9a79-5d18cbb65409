{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "tll", "to_proto_name": "tll", "rule_proto_name": "tll", "field": [{"from_name": "direction", "to_name": "direction", "to_type": 9}, {"from_name": "octetsline_s2c", "to_name": "octetsline_s2c", "to_type": 9, "rule_name": "octetsline_s2c"}, {"from_name": "octetsline_c2s", "to_name": "octetsline_c2s", "to_type": 9, "rule_name": "octetsline_c2s"}, {"from_name": "pktline_s2c", "to_name": "pktline_s2c", "to_type": 9, "rule_name": "pktline_s2c"}, {"from_name": "pktline_c2s", "to_name": "pktline_c2s", "to_type": 9, "rule_name": "pktline_c2s"}, {"from_name": "linkno4_s2c", "to_name": "linkno4_s2c", "to_type": 9, "rule_name": "linkno4_s2c"}, {"from_name": "linkno3_s2c", "to_name": "linkno3_s2c", "to_type": 9, "rule_name": "linkno3_s2c"}, {"from_name": "linkno2_s2c", "to_name": "linkno2_s2c", "to_type": 9, "rule_name": "linkno2_s2c"}, {"from_name": "linkno1_s2c", "to_name": "linkno1_s2c", "to_type": 9, "rule_name": "linkno1_s2c"}, {"from_name": "linkno4_c2s", "to_name": "linkno4_c2s", "to_type": 9, "rule_name": "linkno4_c2s"}, {"from_name": "linkno3_c2s", "to_name": "linkno3_c2s", "to_type": 9, "rule_name": "linkno3_c2s"}, {"from_name": "linkno2_c2s", "to_name": "linkno2_c2s", "to_type": 9, "rule_name": "linkno2_c2s"}, {"from_name": "linkno1_c2s", "to_name": "linkno1_c2s", "to_type": 9, "rule_name": "linkno1_c2s"}, {"from_name": "linkno_count", "to_name": "linkno_count", "to_type": 9, "rule_name": "linkno_cnt"}, {"from_name": "octetsall_s2c", "to_name": "octetsall_s2c", "to_type": 9, "rule_name": "octetsall_s2c"}, {"from_name": "octetsall_c2s", "to_name": "octetsall_c2s", "to_type": 9, "rule_name": "octetsall_c2s"}, {"from_name": "pktall_s2c", "to_name": "pktall_s2c", "to_type": 9, "rule_name": "pktall_s2c"}, {"from_name": "pktall_c2s", "to_name": "pktall_c2s", "to_type": 9, "rule_name": "pktall_c2s"}, {"from_name": "endtime", "to_name": "endtime", "to_type": 9, "rule_name": "endtime"}, {"from_name": "starttime", "to_name": "starttime", "to_type": 9, "rule_name": "starttime"}, {"from_name": "networkid", "to_name": "networkid", "to_type": 9, "rule_name": "networkid"}, {"from_name": "protocol", "to_name": "protocol", "to_type": 9, "rule_name": "protocol"}, {"from_name": "srcMacOui", "to_name": "srcMacOui", "to_type": 13}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "dstISP", "to_name": "dstISP", "to_type": 13}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}