{"desc": "from 字段代表内部字段, to 代表 输出字段， rule 代表规则下发字段, tll 代表是否为通联字段", "from_proto_name": "email", "to_proto_name": "email", "rule_proto_name": "email", "field": [{"from_name": "", "to_name": "rcvWit", "to_type": 13, "rule_name": "with", "tll": 1}, {"from_name": "", "to_name": "ByDom", "to_type": 13, "rule_name": "by.domain", "tll": 1}, {"from_name": "", "to_name": "ByAsn", "to_type": 13, "rule_name": "by.asn", "tll": 1}, {"from_name": "", "to_name": "ByCountry", "to_type": 13, "rule_name": "by.country", "tll": 1}, {"from_name": "", "to_name": "usrAge", "to_type": 13, "rule_name": "useragent", "tll": 1}, {"from_name": "", "to_name": "conType", "to_type": 13, "rule_name": "content_type", "tll": 1}, {"from_name": "", "to_name": "ver", "to_type": 13, "rule_name": "version", "tll": 1}, {"from_name": "", "to_name": "senderDom", "to_type": 13, "rule_name": "sender.domain", "tll": 1}, {"from_name": "", "to_name": "received", "to_type": 13, "rule_name": "received", "tll": 1}, {"from_name": "", "to_name": "content", "to_type": 13, "rule_name": "content", "tll": 1}, {"from_name": "", "to_name": "resentSrvAge", "to_type": 13, "rule_name": "resent.agent", "tll": 1}, {"from_name": "", "to_name": "body", "to_type": 13, "rule_name": "body", "tll": 1}, {"from_name": "", "to_name": "bodyURL", "to_type": 13, "rule_name": "body.url", "tll": 1}, {"from_name": "xMailerCnt", "to_name": "xMaiCnt", "to_type": 13, "tll": 1}, {"from_name": "subjectCnt", "to_name": "subj.cnt", "to_type": 13, "tll": 1}, {"from_name": "rcvrEmailCnt", "to_name": "rcvrEmailCnt", "to_type": 13, "tll": 1}, {"from_name": "osVer", "to_name": "osVer", "to_type": 13, "rule_name": "imap.os_ver", "tll": 1}, {"from_name": "os", "to_name": "os", "to_type": 13, "rule_name": "imap.os", "tll": 1}, {"from_name": "ver", "to_name": "ver", "to_type": 13, "rule_name": "imap.version", "tll": 1}, {"from_name": "vendor", "to_name": "vendor", "to_type": 13, "rule_name": "imap.vendor", "tll": 1}, {"from_name": "name", "to_name": "name", "to_type": 13, "rule_name": "imap.name", "tll": 1}, {"from_name": "bodyTexCha", "to_name": "bodyTexCha", "to_type": 13, "rule_name": "body.charset", "tll": 1}, {"from_name": "bodyTraEnc", "to_name": "bodyTraEnc", "to_type": 13, "rule_name": "body.encoding", "tll": 1}, {"from_name": "bodyLen", "to_name": "bodyLen", "to_type": 13, "rule_name": "body_len", "tll": 1}, {"from_name": "resentDate", "to_name": "resentDate", "to_type": 13, "rule_name": "resent.date", "tll": 1}, {"from_name": "resentTo", "to_name": "resentTo", "to_type": 13, "rule_name": "resent.to", "tll": 1}, {"from_name": "resentFrom", "to_name": "resentFrom", "to_type": 13, "rule_name": "resent.from", "tll": 1}, {"from_name": "rcptToDomCnt", "to_name": "rcptToDomCnt", "to_type": 13, "tll": 1}, {"from_name": "rcptToDom", "to_name": "rcptToDom", "to_type": 13, "rule_name": "rcptto.domain", "tll": 1}, {"from_name": "rcptTo", "to_name": "rcptTo", "to_type": 13, "rule_name": "rcptto", "tll": 1}, {"from_name": "rcvrDom", "to_name": "rcvrDom", "to_type": 13, "rule_name": "receiver.domain"}, {"from_name": "mailFromDomCnt", "to_name": "mailFromDomCnt", "to_type": 13, "tll": 1}, {"from_name": "mailFromDom", "to_name": "mailFromDom", "to_type": 13, "rule_name": "mailfrom.dom", "tll": 1}, {"from_name": "mailFrom", "to_name": "mailFrom", "to_type": 13, "rule_name": "mailfrom", "tll": 1}, {"from_name": "count", "to_name": "count", "to_type": 13, "rule_name": "cnt", "tll": 1}, {"from_name": "Command", "to_name": "Command", "to_type": 13, "rule_name": "cmd", "tll": 1}, {"from_name": "startTLS", "to_name": "startTLS", "to_type": 13, "rule_name": "starttls", "tll": 1}, {"from_name": "xOriIP", "to_name": "xOriIP", "to_type": 13, "rule_name": "x_ori_ip", "tll": 1}, {"from_name": "deliveredTo", "to_name": "deliveredTo", "to_type": 13, "rule_name": "deliveredto", "tll": 1}, {"from_name": "host", "to_name": "host", "to_type": 13, "rule_name": "host", "tll": 1}, {"from_name": "contentLen", "to_name": "contentLen", "to_type": 13}, {"from_name": "charset", "to_name": "charset", "to_type": 13}, {"from_name": "contentWithHtml", "to_name": "contentWithHtml", "to_type": 13}, {"from_name": "realTo", "to_name": "realTo", "to_type": 13}, {"from_name": "realFrom", "to_name": "realFrom", "to_type": 13}, {"from_name": "pwd", "to_name": "pwd", "to_type": 13, "rule_name": "pwd", "tll": 1}, {"from_name": "login", "to_name": "login", "to_type": 13, "rule_name": "login", "tll": 1}, {"from_name": "mimeVerCnt", "to_name": "mimeVerCnt", "to_type": 13, "tll": 1}, {"from_name": "mime<PERSON>er", "to_name": "mime<PERSON>er", "to_type": 13, "rule_name": "mime_version", "tll": 1}, {"from_name": "msgIDCnt", "to_name": "msgIDCnt", "to_type": 13, "tll": 1}, {"from_name": "msgID", "to_name": "msgID", "to_type": 13, "rule_name": "msg_id", "tll": 1}, {"from_name": "headSetCnt", "to_name": "headSetCnt", "to_type": 13, "tll": 1}, {"from_name": "headSet", "to_name": "headSet", "to_type": 13, "rule_name": "header", "tll": 1}, {"from_name": "attConSize", "to_name": "attConSize", "to_type": 13, "rule_name": "att_len", "tll": 1}, {"from_name": "attMD5Cnt", "to_name": "attMD5Cnt", "to_type": 13, "tll": 1}, {"from_name": "attMD5", "to_name": "attMD5", "to_type": 13, "rule_name": "att_md5", "tll": 1}, {"from_name": "attTypeCnt", "to_name": "attTypeCnt", "to_type": 13, "tll": 1}, {"from_name": "attType", "to_name": "attType", "to_type": 13, "rule_name": "att_con_type", "tll": 1}, {"from_name": "attFileNameCnt", "to_name": "attFileNameCnt", "to_type": 13, "tll": 1}, {"from_name": "attFileName", "to_name": "attFileName", "to_type": 13, "rule_name": "att_fn", "tll": 1}, {"from_name": "emaInd", "to_name": "emaInd", "to_type": 13, "rule_name": "index", "tll": 1}, {"from_name": "conTypeCnt", "to_name": "conTypeCnt", "to_type": 13, "tll": 1}, {"from_name": "bodyTypeCnt", "to_name": "bodyTypeCnt", "to_type": 13, "tll": 1}, {"from_name": "bodyType", "to_name": "bodyType", "to_type": 13, "rule_name": "body.type", "tll": 1}, {"from_name": "conTexCha", "to_name": "conTexCha", "to_type": 15}, {"from_name": "conTraEnc", "to_name": "conTraEnc", "to_type": 15}, {"from_name": "xMai", "to_name": "xMai", "to_type": 13, "rule_name": "x_mailer", "tll": 1}, {"from_name": "subj", "to_name": "subj", "to_type": 13, "rule_name": "subject", "tll": 1}, {"from_name": "SMTPSrvAge", "to_name": "SMTPSrvAge", "to_type": 13, "rule_name": "smtp_srv_agent", "tll": 1}, {"from_name": "SMTPSrv", "to_name": "SMTPSrv", "to_type": 13, "rule_name": "smtp_srv", "tll": 1}, {"from_name": "loginSrv", "to_name": "loginsrv", "to_type": 13, "rule_name": "login_srv", "tll": 1}, {"from_name": "emaProtType", "to_name": "emaProtType", "to_type": 13, "rule_name": "proto_type", "tll": 1}, {"from_name": "date", "to_name": "date", "to_type": 13, "rule_name": "date", "tll": 1}, {"from_name": "repTo", "to_name": "repTo", "to_type": 13, "rule_name": "re", "tll": 1}, {"from_name": "BCC", "to_name": "BCC", "to_type": 13, "rule_name": "bcc", "tll": 1}, {"from_name": "CCAli", "to_name": "CCAli", "to_type": 13, "rule_name": "cc.alias", "tll": 1}, {"from_name": "CC", "to_name": "CC", "to_type": 13, "rule_name": "cc", "tll": 1}, {"from_name": "ByIP", "to_name": "ByIP", "to_type": 13, "rule_name": "by.ip", "tll": 1}, {"from_name": "rcvrAli", "to_name": "rcvrAli", "to_type": 13, "rule_name": "receiver.alias", "tll": 1}, {"from_name": "rcvrEmail", "to_name": "rcvrEmail", "to_type": 13, "rule_name": "receiver", "tll": 1}, {"from_name": "FromCountry", "to_name": "FromCountry", "to_type": 13, "rule_name": "from.country", "tll": 1}, {"from_name": "FromAsn", "to_name": "FromAsn", "to_type": 13, "rule_name": "from.asn", "tll": 1}, {"from_name": "FromDomCnt", "to_name": "FromDomCnt", "to_type": 13, "tll": 1}, {"from_name": "FromDom", "to_name": "FromDom", "to_type": 13, "rule_name": "from.domain", "tll": 1}, {"from_name": "FromIpCnt", "to_name": "FromIpCnt", "to_type": 13, "tll": 1}, {"from_name": "FromIp", "to_name": "FromIp", "to_type": 13, "rule_name": "from.ip", "tll": 1}, {"from_name": "sender<PERSON><PERSON>", "to_name": "sender<PERSON><PERSON>", "to_type": 13, "rule_name": "sender.alias", "tll": 1}, {"from_name": "senderEmail", "to_name": "senderEmail", "to_type": 13, "rule_name": "sender", "tll": 1}, {"from_name": "dstMac<PERSON>ui", "to_name": "dstMac<PERSON>ui", "to_type": 13}, {"from_name": "captureTime", "to_name": "captureTime", "to_type": 15}, {"from_name": "outTransProto", "to_name": "outTransProto", "to_type": 9}, {"from_name": "outDstPort", "to_name": "outDstPort", "to_type": 9}, {"from_name": "outSrcPort", "to_name": "outSrcPort", "to_type": 9}, {"from_name": "outer.ipv6.dst", "to_name": "outer.ipv6.dst", "to_type": 13}, {"from_name": "outer.ipv6.src", "to_name": "outer.ipv6.src", "to_type": 13}, {"from_name": "outDstAddr", "to_name": "outDstAddr", "to_type": 9}, {"from_name": "outSrcAddr", "to_name": "outSrcAddr", "to_type": 9}, {"from_name": "outAddrType", "to_name": "outAddrType", "to_type": 9}, {"from_name": "dstASN", "to_name": "dstASN", "to_type": 9}, {"from_name": "dstLatitude", "to_name": "dstLatitude", "to_type": 12}, {"from_name": "dstLongitude", "to_name": "dstLongitude", "to_type": 12}, {"from_name": "dstCity", "to_name": "dstCity", "to_type": 13}, {"from_name": "dstState", "to_name": "dstState", "to_type": 13}, {"from_name": "dstCountry", "to_name": "dstCountry", "to_type": 13}, {"from_name": "srcASN", "to_name": "srcASN", "to_type": 9}, {"from_name": "srcISP", "to_name": "srcISP", "to_type": 13}, {"from_name": "srclatitude", "to_name": "srcLatitude", "to_type": 12}, {"from_name": "srcLongitude", "to_name": "srcLongitude", "to_type": 12}, {"from_name": "SrcCity", "to_name": "srcCity", "to_type": 13}, {"from_name": "srcState", "to_name": "srcState", "to_type": 13}, {"from_name": "srcCountry", "to_name": "srcCountry", "to_type": 13}, {"from_name": "tunnelID", "to_name": "tunnelID", "to_type": 9}, {"from_name": "dstMac", "to_name": "dstMac", "to_type": 13}, {"from_name": "srcMac", "to_name": "srcMac", "to_type": 13}, {"from_name": "vlanID2", "to_name": "vlanID2", "to_type": 9}, {"from_name": "vlanID1", "to_name": "vlanID1", "to_type": 9}, {"from_name": "lable4", "to_name": "lable4", "to_type": 9}, {"from_name": "lable3", "to_name": "lable3", "to_type": 9}, {"from_name": "lable2", "to_name": "lable2", "to_type": 9}, {"from_name": "lable1", "to_name": "lable1", "to_type": 9}, {"from_name": "utags", "to_name": "utags", "to_type": 13}, {"from_name": "atags", "to_name": "atags", "to_type": 13}, {"from_name": "ttags", "to_name": "ttags", "to_type": 13}, {"from_name": "etags", "to_name": "etags", "to_type": 13}, {"from_name": "streamId", "to_name": "streamId", "to_type": 10}, {"from_name": "payLen", "to_name": "payLen", "to_type": 10}, {"from_name": "pktNum", "to_name": "pktNum", "to_type": 9}, {"from_name": "strDirec", "to_name": "strDirec", "to_type": 9}, {"from_name": "intFlag", "to_name": "intFlag", "to_type": 1}, {"from_name": "mulRouFlag", "to_name": "mulRouFlag", "to_type": 1}, {"from_name": "protName", "to_name": "protName", "to_type": 13}, {"from_name": "protType", "to_name": "protType", "to_type": 13}, {"from_name": "protInfo", "to_name": "protInfo", "to_type": 13}, {"from_name": "dstAddrV6", "to_name": "dstAddrV6", "to_type": 13}, {"from_name": "srcAddrV6", "to_name": "srcAddrV6", "to_type": 13}, {"from_name": "protNum", "to_name": "protNum", "to_type": 9}, {"from_name": "dstPort", "to_name": "dstPort", "to_type": 9}, {"from_name": "srcPort", "to_name": "srcPort", "to_type": 9}, {"from_name": "dstAddr", "to_name": "dstAddr", "to_type": 10}, {"from_name": "srcAddr", "to_name": "srcAddr", "to_type": 10}, {"from_name": "ipVer", "to_name": "ipVer", "to_type": 9}, {"from_name": "secdegpro", "to_name": "secdegpro", "to_type": 13}, {"from_name": "filesecdeg", "to_name": "filesecdeg", "to_type": 13}, {"from_name": "mdsecdeg", "to_name": "mdsecdeg", "to_type": 13}, {"from_name": "stortime", "to_name": "stortime", "to_type": 15}, {"from_name": "guid", "to_name": "guid", "to_type": 10}, {"from_name": "taskID", "to_name": "taskID", "to_type": 13}, {"from_name": "unitID", "to_name": "unitID", "to_type": 13}, {"from_name": "siteID", "to_name": "siteID", "to_type": 13}, {"from_name": "meanID", "to_name": "meanID", "to_type": 13}, {"from_name": "comDur", "to_name": "comDur", "to_type": 9}, {"from_name": "endTime", "to_name": "endTime", "to_type": 15}, {"from_name": "begTime", "to_name": "begTime", "to_type": 15}, {"from_name": "lineName2", "to_name": "lineName2", "to_type": 13}, {"from_name": "lineName1", "to_name": "lineName1", "to_type": 13}]}